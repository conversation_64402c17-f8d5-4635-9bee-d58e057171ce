<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './ScmCustSelect/Basic.vue'
import CodeExample from './ScmCustSelect/Code.vue'
import MultipleExample from './ScmCustSelect/Multiple.vue'
import SearchTypeExample from './ScmCustSelect/SearchType.vue'
import PageSizeExample from './ScmCustSelect/PageSize.vue'
import PaginationExample from './ScmCustSelect/Pagination.vue'
import NoPaginationExample from './ScmCustSelect/NoPagination.vue'
import OrgCustExample from './ScmCustSelect/OrgCust.vue'
import OrgPartnerNoPaginationExample from './ScmCustSelect/OrgPartnerNoPagination.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('noPagination')
</script>

<template>
  <div>
    <Paragraph
      >供应商选择组件，支持根据输入内容搜索供应商，并可以选择匹配的供应商。支持按名称、编码、五笔码(qsCode1)和拼音码(qsCode2)搜索。组件默认会加载第一页数据，可通过pageSize属性设置每页显示条数。支持滚动加载和按钮加载两种分页方式。也支持禁用分页，一次性加载所有数据并使用前端过滤。新增支持机构供应商API，通过orgPartner属性启用，可调用orgPartnerFindAllApi获取机构供应商数据，支持根据custName、custCode、qsCode1、qsCode2四个属性进行过滤，qsCode1、qsCode2搜索时会自动转大写匹配。</Paragraph
    >

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="noPagination" tab="非分页加载">
        <NoPaginationExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="code" tab="显示编码">
        <CodeExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="multiple" tab="多选模式">
        <MultipleExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="searchType" tab="搜索类型">
        <SearchTypeExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="pageSize" tab="分页设置">
        <PageSizeExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="pagination" tab="分页加载">
        <PaginationExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="orgCust" tab="机构供应商">
        <OrgCustExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="orgPartnerNoPagination" tab="机构供应商非分页">
        <OrgPartnerNoPaginationExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
