import { wrapCodeExample } from '@/utils/codeUtils'

// 滚动加载
export const scrollPaginationUsage = wrapCodeExample(`<template>
  <!-- 滚动加载 -->
  <ScmCustSelect
    v-model="custId"
    :pageSize="10"
    enablePagination
    paginationMode="scroll"
    :maxPages="5"
    style="width: 100%"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const custId = ref()
</script>`)

// 按钮加载
export const buttonPaginationUsage = wrapCodeExample(`<template>
  <!-- 按钮加载 -->
  <ScmCustSelect
    v-model="custId"
    :pageSize="10"
    enablePagination
    paginationMode="button"
    :maxPages="3"
    style="width: 100%"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const custId = ref()
</script>`)

// 手动控制加载
export const manualPaginationUsage = wrapCodeExample(`<template>
  <div>
    <!-- 禁用自动分页加载 -->
    <ScmCustSelect
      ref="scmCustSelectRef"
      v-model="custId"
      :pageSize="10"
      :enablePagination="false"
      style="width: 100%"
    />
    
    <div style="margin-top: 16px">
      <a-button @click="loadMore" :disabled="allDataLoaded">
        {{ loadingMore ? '加载中...' : '手动加载更多' }}
      </a-button>
      
      <div v-if="allDataLoaded" style="margin-top: 8px; color: #999">
        已加载全部数据
      </div>
    </div>
  </div>
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref, reactive } from 'vue'

// 单选值
const custId = ref()
const scmCustSelectRef = ref()
const loadingMore = ref(false)
const allDataLoaded = ref(false)

// 手动加载更多
const loadMore = async () => {
  if (loadingMore.value || allDataLoaded.value) return
  
  loadingMore.value = true
  try {
    // 调用组件的loadMore方法
    await scmCustSelectRef.value.loadMore()
    // 更新状态
    allDataLoaded.value = scmCustSelectRef.value.getAllDataLoaded()
  } finally {
    loadingMore.value = false
  }
}
</script>`)

// 引入组件
export const importCode = `import { ScmCustSelect } from '@mh-wm/scm-cust'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/scm-cust": "^1.0.0",
    "@mh-wm/util": "^1.0.4"
  }
}`
