import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 选择指定组织机构下的部门 -->
  <OrgDept
    v-model="deptId"
    :orgId="orgId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDept } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)
// 部门ID
const deptId = ref()
</script>`)

// 当前用户
export const currentUserUsage = wrapCodeExample(`<template>
  <!-- 不指定组织机构ID，使用当前用户token中的orgId -->
  <OrgDept
    v-model="currentUserDeptId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDept } from '@mh-hip/org'
import { ref } from 'vue'

// 部门ID
const currentUserDeptId = ref()
</script>`)

// 多选模式
export const multipleUsage = wrapCodeExample(`<template>
  <!-- 多选模式 -->
  <OrgDept
    v-model="deptIds"
    :orgId="orgId"
    multiple
    style="width: 100%"
  />
</template>

<script setup>
import { OrgDept } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)
// 多选值 - 使用deptCode数组
const deptIds = ref([])
</script>`)

// 医生对应部门
export const clinicianDeptUsage = wrapCodeExample(`<template>
  <!-- 根据医生ID查询对应的部门 -->
  <OrgDept
    v-model="clinicianDeptId"
    :clinicianId="clinicianId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDept } from '@mh-hip/org'
import { ref } from 'vue'

// 医生ID
const clinicianId = ref(10002)
// 部门ID
const clinicianDeptId = ref()
</script>`)

// 组织机构和医生
export const orgAndClinicianUsage = wrapCodeExample(`<template>
  <!-- 同时指定组织机构ID和医生ID -->
  <OrgDept
    v-model="bothDeptId"
    :orgId="orgId"
    :clinicianId="clinicianId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDept } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)
// 医生ID
const clinicianId = ref(10002)
// 部门ID
const bothDeptId = ref()
</script>`)

// 当前用户部门
export const ownUsage = wrapCodeExample(`<template>
  <!-- 显示当前用户的部门 -->
  <OrgDept
    v-model="ownDeptId"
    :own="true"
    :orgId="orgId"
    :clinicianId="clinicianId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDept } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)
// 医生ID
const clinicianId = ref(10002)
// 部门ID
const ownDeptId = ref()
</script>`)

// 引入组件
export const importCode = `import { OrgDept } from '@mh-hip/org'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-hip/org": "^1.0.8"
  }
}`
