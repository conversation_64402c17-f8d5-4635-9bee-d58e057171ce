<template>
  <div class="test-container">
    <h1>测试 WmDeptArtSelect 组件</h1>
    <p>请打开浏览器控制台查看是否还有 AutoComplete 警告</p>
    
    <div class="test-section">
      <h3>WmDeptArtSelect 组件测试</h3>
      <div class="form-item">
        <label>科室编码：</label>
        <input v-model="deptCode" placeholder="请输入科室编码" style="margin-right: 10px;" />
        <button @click="updateDeptCode">更新科室编码</button>
      </div>
      
      <div class="form-item">
        <label>品种选择：</label>
        <WmDeptArtSelect
          :deptCode="deptCode"
          :searchType="1"
          :showCost="true"
          @selected="handleArtSelected"
          style="width: 500px;"
        />
      </div>
      
      <div class="result-section">
        <h4>选中的品种信息：</h4>
        <pre>{{ JSON.stringify(selectedArt, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { WmDeptArtSelect } from '@mh-inpatient-hsd/selector'

// 响应式数据
const deptCode = ref('001')
const selectedArt = ref(null)

// 方法
const updateDeptCode = () => {
  deptCode.value = deptCode.value === '001' ? '002' : '001'
}

const handleArtSelected = (art: any) => {
  console.log('选中的品种:', art)
  selectedArt.value = art
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.form-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.form-item label {
  width: 100px;
  font-weight: bold;
}

.result-section {
  margin-top: 20px;
  padding: 15px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.result-section pre {
  background-color: #fff;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  overflow-x: auto;
}

input {
  padding: 5px 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

button {
  padding: 5px 15px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #40a9ff;
}
</style>
