<script setup lang="ts">
import { ReqComponent } from '@mh-wm/req-component'
import { Card, Typography, Divider, Button, message, Alert } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { validationUsage, importCode, packageJsonCode } from '../code/ReqComponentCode'

const { Title, Paragraph, Text } = Typography

// 仓库编码
const deptCode = ref('000013')

// 添加品种回调
const handleAddArt = (formData: any) => {
  // 提示添加成功
  message.success(`成功添加品种: ${formData.artData.artName}`)
  console.log('添加品种成功，表单数据：', formData)
}
</script>

<template>
  <Card title="表单验证" class="mb-16px">
    <div mb-16px>
      <Title :level="4">表单验证示例</Title>
      <Text type="secondary">组件内置表单验证功能</Text>
      <Divider style="margin: 8px 0" />
      
      <Alert
        message="表单验证规则"
        description="组件内置了以下验证规则：1. 生产日期和有效期至必须填写，且格式为YYYYMMDD；2. 整包数量和拆零数量至少填写一个；3. 必须先选择品种才能提交表单。"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />
      
      <div class="component-container">
        <ReqComponent
          :deptCode="deptCode"
          @addArt="handleAddArt"
        />
      </div>
      
      <div class="validation-tips">
        <Title :level="5">验证说明</Title>
        <ul>
          <li>
            <Text strong>品种选择验证：</Text>
            <Text>必须先选择一个品种才能提交表单，否则会提示"请先选择一个品种"</Text>
          </li>
          <li>
            <Text strong>日期格式验证：</Text>
            <Text>生产日期和有效期至必须填写，且格式为YYYYMMDD，否则会提示"生产日期和有效期至不能为空"</Text>
          </li>
          <li>
            <Text strong>数量验证：</Text>
            <Text>整包数量和拆零数量至少填写一个，否则会提示"请输入有效的数量"</Text>
          </li>
          <li>
            <Text strong>拆零验证：</Text>
            <Text>如果品种不可拆零（splittable !== 1），则不会显示拆零数量字段</Text>
          </li>
        </ul>
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="validationUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.component-container {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.validation-tips {
  margin-top: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.validation-tips ul {
  padding-left: 16px;
  margin: 0;
}

.validation-tips li {
  margin-bottom: 8px;
}

.validation-tips li:last-child {
  margin-bottom: 0;
}
</style>
