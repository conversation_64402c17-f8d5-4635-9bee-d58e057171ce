<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

import BaseReadRecipeExample from './examples/BaseReadRecipeExample.vue'

const { Paragraph } = Typography
const activeKey = ref('basic')
</script>

<template>
  <Card title="ReadRecipe 处方查询组件" class="mb-16px">
    <Paragraph>BaseReadRecipe组件是HSD业务域的核心组件，专门用于医疗处方的展示和交互。
      支持西药、中药、治疗处方类型，提供弹窗模式和内联模式两种显示方式，
      符合医疗行业标准的处方单样式设计。</Paragraph>
    <BaseReadRecipeExample />
  </Card>
</template>

<style scoped>
:deep(.ant-tabs-left > .ant-tabs-content-holder) {
  border-left: 1px solid #f0f0f0;
  padding-left: 16px;
}
:deep(.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab) {
  padding: 8px 16px;
}
:deep(.ant-card-body) {
  padding: 24px;
}
</style>
