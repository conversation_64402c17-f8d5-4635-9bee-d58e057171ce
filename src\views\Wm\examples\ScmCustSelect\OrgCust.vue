<script setup lang="ts">
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { Card, Typography, Divider, Switch } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { orgCustUsage, importCode, packageJsonCode } from '../code/ScmCustSelectCode'

const { Title } = Typography

// 机构供应商选择值
const orgCustId = ref<number>()

// 是否使用机构供应商API
const useOrgPartner = ref<boolean>(true)
</script>

<template>
  <Card title="机构供应商" class="mb-16px">
    <div mb-16px>
      <Title :level="4">使用机构供应商API</Title>
      <div mb-16px>
        <div mb-8px>
          <span mr-8px>使用机构供应商API：</span>
          <Switch v-model:checked="useOrgPartner" />
          <span ml-8px>{{ useOrgPartner ? '是' : '否' }}</span>
        </div>
      </div>

      <ScmCustSelect
        v-model="orgCustId"
        :orgPartner="useOrgPartner"
        style="width: 100%"
        placeholder="请输入机构供应商名称/编码"
      />
      <div mt-8px>选中的供应商ID: {{ orgCustId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        当<code>orgPartner</code>属性为<code>true</code>时，组件会调用机构供应商API（/clinics_wm/orgpartner/orgPartnerPage和/clinics_wm/orgpartner/findAll）。
      </div>

      <div mt-16px>
        <Title :level="5">使用说明：</Title>
        <ol class="usage-list">
          <li>通过<code>:orgPartner="true"</code>启用机构供应商API</li>
          <li>当<code>orgPartner</code>为<code>true</code>时，组件内部会自动调用机构供应商相关API</li>
          <li>当<code>orgPartner</code>为<code>false</code>或不传时，组件会使用普通供应商API</li>
          <li>其他属性和事件与普通供应商选择组件完全相同</li>
          <li>可以通过<code>v-model</code>双向绑定选中的供应商ID</li>
        </ol>
      </div>

      <div mt-16px>
        <Title :level="5">使用示例：</Title>
        <div class="example-block">
          <pre><code>// 基础用法
&lt;ScmCustSelect
  v-model="orgCustId"
  :orgPartner="true"
  style="width: 200px"
/&gt;

// 带更多配置的机构供应商选择
&lt;ScmCustSelect
  v-model="orgCustId"
  :orgPartner="true"
  showField="custCode"
  placeholder="请输入机构供应商编码"
  style="width: 300px"
/&gt;

// 机构供应商多选模式
&lt;ScmCustSelect
  v-model="orgCustIds"
  :orgPartner="true"
  multiple
  style="width: 100%"
/&gt;</code></pre>
        </div>
      </div>
    </div>

    <Divider />

    <CodeDemoVue :code="orgCustUsage" title="机构供应商示例" />
    <CodeDemoVue :code="importCode" title="引入组件" language="javascript" />
    <CodeDemoVue :code="packageJsonCode" title="package.json依赖配置" language="json" />
  </Card>
</template>

<style scoped>
.tip-text {
  color: #666;
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 50%;
  background-color: #1890ff;
  color: #fff;
  font-style: normal;
  margin-right: 4px;
}

.usage-list {
  margin-left: 20px;
  line-height: 1.8;
}

.usage-list li {
  margin-bottom: 8px;
}

.usage-list code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  color: #d56161;
}

.example-block {
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 16px;
  margin-top: 8px;
  overflow: auto;
}

.example-block pre {
  margin: 0;
}

.example-block code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
</style>
