<script setup lang="ts">
import { TrackCode } from '@mh-wm/track-code'
import { Card, Typography, Divider, Input, Button, Space, Alert } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { importCode, packageJsonCode, customHotkeyUsage } from '../code/TrackCodeCustomHotkeyCode'

const { Title, Paragraph } = Typography

// 溯源码ID
const wbSeqid = ref<string>('12345')

// 自定义热键
const customHotkey = ref<string>('F5')

// 是否处于录入状态
const isRecording = ref(false)

// 成功回调
const handleSuccess = (data: any) => {
  console.log('溯源码录入成功', data)
}

// 开始录入按键
const startRecording = () => {
  isRecording.value = true
  // 添加全局键盘事件监听
  window.addEventListener('keydown', recordKey)
}

// 停止录入按键
const stopRecording = () => {
  isRecording.value = false
  // 移除全局键盘事件监听
  window.removeEventListener('keydown', recordKey)
}

// 录入按键
const recordKey = (event: KeyboardEvent) => {
  // 阻止默认行为
  event.preventDefault()

  // 获取按键名称
  let keyName = event.key

  // 对于功能键，使用特殊处理
  if (
    event.key === 'F1' ||
    event.key === 'F2' ||
    event.key === 'F3' ||
    event.key === 'F4' ||
    event.key === 'F5' ||
    event.key === 'F6' ||
    event.key === 'F7' ||
    event.key === 'F8' ||
    event.key === 'F9' ||
    event.key === 'F10' ||
    event.key === 'F11' ||
    event.key === 'F12'
  ) {
    keyName = event.key
  } else if (event.key === 'Escape') {
    keyName = 'Esc'
  } else if (event.key === ' ') {
    keyName = 'Space'
  } else if (event.key.length === 1) {
    // 对于字母和数字，转为大写
    keyName = event.key.toUpperCase()
  }

  // 设置自定义热键
  customHotkey.value = keyName

  // 停止录入
  stopRecording()
}
</script>

<template>
  <Card title="自定义热键" class="mb-16px">
    <div mb-16px>
      <Title :level="4">自定义热键</Title>
      <Paragraph>溯源码录入组件支持自定义热键，可以通过hotkey属性设置。</Paragraph>

      <Alert type="info" show-icon style="margin-bottom: 16px">
        <template #message>
          <div>
            当前设置的热键是: <strong>{{ customHotkey }}</strong>
          </div>
          <div>您可以点击下方的"点击录入按键"按钮，然后按下任意键来设置新的热键。</div>
        </template>
      </Alert>

      <Space direction="vertical" style="width: 100%">
        <div>
          <span style="margin-right: 8px">溯源码ID:</span>
          <Input v-model:value="wbSeqid" style="width: 200px" placeholder="请输入溯源码ID" />
        </div>

        <div>
          <TrackCode :wbSeqid="wbSeqid" :hotkey="customHotkey" @success="handleSuccess" />
          <span style="margin-left: 8px; color: #666; font-size: 13px"> 点击按钮或按 {{ customHotkey }} 键将打开溯源码录入弹窗 </span>
        </div>

        <div style="margin-top: 16px">
          <span style="margin-right: 8px">自定义热键:</span>
          <Input v-model:value="customHotkey" placeholder="请点击录入按键" readonly style="width: 200px; margin-right: 8px" />
          <Button @click="startRecording" :disabled="isRecording">
            {{ isRecording ? '正在录入...' : '点击录入按键' }}
          </Button>
        </div>
      </Space>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="customHotkeyUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
