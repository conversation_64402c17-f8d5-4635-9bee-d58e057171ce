<script setup lang="ts">
import { PharmacySelect } from '@mh-wm/pharmacy'
import { Card, Typography, Divider, Switch } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { currentUserUsage, importCode, packageJsonCode } from '../code/PharmacySelectCode'

const { Title } = Typography

// 当前用户的药房ID
const userPharmacyId = ref<string>()

// 是否只显示当前用户的药房
const isOwn = ref(true)
</script>

<template>
  <Card title="仅显示当前用户有权限的药房" class="mb-16px">
    <div flex items-center mb-16px>
      <span mr-8px>是否只显示当前用户的药房：</span>
      <Switch v-model:checked="isOwn" />
      <span ml-8px>{{ isOwn ? '是' : '否' }}</span>
      <span ml-16px class="tip-text">
        <i class="tip-icon">i</i>
        当开启此选项时，会查询当前用户有权限的药房列表
      </span>
    </div>

    <div mb-16px>
      <Title :level="4">当前用户的药房</Title>
      <PharmacySelect v-model="userPharmacyId" :own="isOwn" w-200px />
      <div mt-8px>选中的药房ID: {{ userPharmacyId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        当own为true时，会调用list API获取当前用户有权限的药房列表；当own为false时，会调用findAll API获取所有药房列表。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="currentUserUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
