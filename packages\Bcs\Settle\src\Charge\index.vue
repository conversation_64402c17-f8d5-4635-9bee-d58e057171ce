<script lang="ts" setup>
import { View } from '@idmy/antd'
import { add, Data, Dialog, Format, Modal, subtract, useLoading } from '@idmy/core'
import { SelfFeePaymentType } from '@mh-bcs/pay'
import { CashType, chargeInjectKey, getCash, MiTrans, MiTransStatusEnum, payCancel, sumUnpaidAmount } from '@mh-bcs/util'
import { Space } from 'ant-design-vue'
import { isNil } from 'lodash-es'
import type { PropType } from 'vue'
import Cancel from './Cancel.vue'
import CashPayment from './CashPayment.vue'
import Discount from './Discount.vue'
import Finish from './Finish.vue'
import MiFund from './MiFund/index.vue'
import MiTransInfo from './MiFund/MiTransInfo.vue'

import Prepaid from './Prepaid/index.vue'
import Tip from './Tip.vue'
import UnsettleAmt from './UnsettleAmt.vue'

const props = defineProps({
  autoFinish: { type: Boolean as PropType<boolean> },
  card: { type: Object as PropType<Data> },
  cashId: { type: Number as PropType<number>, required: true },
  cashType: { type: String as PropType<CashType>, required: true },
  midway: { type: Boolean as PropType<boolean> },
  showMiFund: { type: Boolean as PropType<boolean>, default: true },
  visitId: { type: Number as PropType<number> },
})

const cpRef = ref()
const onLoadCashPayment = () => cpRef.value?.onLoad()
const prepaidRef = ref()
const onLoadPrepaid = () => prepaidRef.value?.onLoad()

const miTrans = reactive<MiTrans>({
  acctPayAmt: undefined,
  fundPayAmt: undefined,
  multiAidAmt: undefined,
  miTransId: undefined,
  transStatus: undefined,
})
const midway = ref(props.midway)
const finished = ref(false)
const totalAmount = ref(0)
const cashUnpaidAmount = ref(0)
const prepaid = ref(0)
const loaded = ref(false)
const endDate = ref()
const insuranceTypeId = ref()
const medTypeId = ref()
const diseaseCode = ref()
const cash = ref({})
const [onLoad] = useLoading(async () => {
  const tmp = await getCash(props.cashId)
  cash.value = tmp
  medTypeId.value = tmp.medTypeId
  insuranceTypeId.value = tmp.insuranceTypeId
  diseaseCode.value = tmp.diseaseCode
  finished.value = tmp.status === 'OK'
  midway.value = tmp.midway
  endDate.value = tmp.endDate
  totalAmount.value = subtract(tmp.amount, tmp.refundedAmount)
  cashUnpaidAmount.value = await sumUnpaidAmount(props.cashId)
  if (ctx.cashType === 'INPATIENT') {
    await onLoadPrepaid()
    await nextTick()
  }
  await onLoadCashPayment()
  await nextTick()
  loaded.value = true
}, true)

const unpaidAmount = computed(() => {
  let out
  if (miTrans.transStatus === MiTransStatusEnum.preSettle) {
    out = subtract(cashUnpaidAmount.value, add(miTrans.fundPayAmt, miTrans.acctPayAmt, miTrans.multiAidAmt))
  } else {
    out = cashUnpaidAmount.value
  }
  return subtract(out, prepaid.value)
})

const isZero = computed(() => unpaidAmount.value === 0)

const payAmount = ref(0)
const paying = ref(false)
watch(
  () => unpaidAmount.value,
  val => {
    if (!paying.value) {
      payAmount.value = val ?? 0
    }
  }
)

watch(
  () => payAmount.value,
  val => {
    if (isNil(val)) {
      payAmount.value = 0
    }
  }
)

Modal.setClose(async () => {
  if (!finished.value) {
    if (props.cashType === 'OUTPATIENT' || props.cashType === 'REG') {
      return await new Promise(resolve => {
        Modal.b.open({
          component: Cancel,
          width: 2,
          title: '取消原因',
          props: {
            cashId: props.cashId,
            cashType: props.cashType,
          },
          onClose: async (isOk: boolean) => resolve(isOk),
        })
      })
    } else {
      try {
        await Dialog.confirm({
          title: '是否取消本次结算？',
          content: '取消后下次结算将会重新计算结算金额。',
          cancelText: '取消重结',
          okText: '保留稍后处理',
        })
        await Modal.close()
      } catch {
        await payCancel(props.cashId, '', false, props.cashType)
      }
    }
  }
})

const autoFinish = ref(props.autoFinish)
const paymentType = ref(props.cashType === 'INPATIENT' ? 'PREPAID' : 'CASH')
const miFundCashPayment = ref()
const ctx = reactive({
  allowPayZero: props.showMiFund,
  autoFinish,
  card: props.card,
  cashId: props.cashId,
  cashType: props.cashType,
  insuranceTypeId,
  medTypeId,
  diseaseCode,
  finished,
  finishing: false,
  isZero,
  miFundCashPayment,
  miTrans,
  midway,
  onLoad,
  payAmount,
  paying,
  paymentType,
  preSettleLoading: false,
  prepaid,
  totalAmount,
  unpaidAmount,
  visit: {},
  visitId: props.visitId,
})
provide(chargeInjectKey, ctx)
</script>

<template>
  <Tip :totalAmount="totalAmount" />
  <div class="mb-8px!" flex justify-between>
    <Space :size="24" items-center text-16px>
      <Discount :discount="add(cash.discounted, cash.derated)" />
      <Format :value="totalAmount" prefix="应收总额：" type="Currency" />
      <Prepaid v-if="ctx.cashType === 'INPATIENT'" ref="prepaidRef" />
      <Format :prefix="unpaidAmount >= 0 ? `待收金额：` : `待退金额：`" :value="unpaidAmount" flex items-center type="Currency" value-class="error font-bold text-22px" />
      <UnsettleAmt v-if="midway && visitId" :endDate="endDate" :visitId="visitId" />
    </Space>
    <Space size="large" text-16px>
      <Finish :loaded="loaded" />
    </Space>
  </div>
  <MiFund v-if="loaded && showMiFund" />
  <div flex>
    <div class="f1">
      <CashPayment ref="cpRef" />
    </div>
    <div class="f1">
      <SelfFeePaymentType :combine="false" />
    </div>
  </div>
  <slot />
  <MiTransInfo v-if="miTrans.miTransId" />
  <View v-if="cfg.tenant.debug" title="调试">
    {{ ctx }}
  </View>
</template>
