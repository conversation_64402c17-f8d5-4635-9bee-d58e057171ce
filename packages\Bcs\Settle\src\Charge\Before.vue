<script lang="ts" setup>
import type { Data } from '@idmy/core'
import { Api, Format, Message, Modal, useLoading } from '@idmy/core'
import type { CashType } from '@mh-bcs/util'
import { beforePay, sumLeEndDateUnpaidAmt } from '@mh-bcs/util'
import { Alert, Button, DatePicker, Space } from 'ant-design-vue'
import dayjs from 'dayjs'
import Pay from './index.vue'
import UnsettleAmt from './UnsettleAmt.vue'

const props = defineProps({
  autoFinish: Boolean,
  billIds: { type: Array as PropType<number[]> },
  card: { type: Object as PropType<Data> },
  cashId: { type: Number as PropType<number> },
  cashType: { type: String as PropType<CashType>, default: 'OUTPATIENT' },
  endDate: { type: Number as PropType<number> },
  midway: { type: Boolean as PropType<boolean> },
  showMiFund: { type: Boolean as PropType<boolean>, default: true },
  visitId: { type: Number as PropType<number> },
})

const error = ref()
const cashId = ref(props.cashId)
const endDate = ref(props.endDate ? dayjs() : dayjs(props.endDate))
const [onOk, loading] = useLoading(async () => {
  if (!props.cashId) {
    const msg = Message.loading({ content: '正在创建结算单……', duration: 0 })
    try {
      error.value = null
      cashId.value = await beforePay({
        billIds: props.billIds as number[],
        cashType: props.cashType,
        endDate: Number(dayjs(endDate.value).format('YYYYMMDD')),
        midway: props.midway,
        visitId: props.visitId,
      })
    } catch (e) {
      error.value = e
    } finally {
      msg()
    }
  }
  const id = cashId.value ?? props.cashId
  const title = Modal.state.title
  if (!title.includes(id)) {
    Modal.setTitle(`收费#${id ?? props.visitId} ${title.split('收费').pop()}`)
  }
}, !(props.midway && !props.cashId))
</script>

<template>
  <div v-if="midway && !cashId" flex-between>
    <Api v-slot="{ output: amt }" :input="{ endDate }" :load="() => sumLeEndDateUnpaidAmt(visitId, endDate)" input-watch type="Number">
      <Space>
        <span>中途结算截止日期：</span>
        <DatePicker v-model:value="endDate" :allow-clear="false" format="YYYY-MM-DD" placeholder="截至日期" />
        <Space size="large">
          <Format :value="amt" prefix="本次中途结算金额：" type="Currency" value-class="primary text-22px b" />
          <UnsettleAmt :endDate="endDate" :visitId="visitId" />
        </Space>
      </Space>
      <Button :disabled="amt <= 0" :loading="loading" type="primary" @click="onOk"> 开始结算 </Button>
    </Api>
  </div>
  <div v-else>
    <Alert v-if="error" :message="error" show-icon type="error" />
    <Pay v-else-if="cashId" :autoFinish="autoFinish" :card="card" :cashId="cashId" :cashType="cashType" :endDate="endDate" :midway="midway" :showMiFund="showMiFund" :visitId="visitId">
      <slot v-if="cashId" :cashId="cashId" />
    </Pay>
  </div>
</template>
