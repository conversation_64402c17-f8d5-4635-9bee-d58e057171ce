<script setup lang="ts">
import * as antDesignVue from 'ant-design-vue'
const { Descriptions, Input, DatePicker, Select, Button, message } = antDesignVue
import { ref, reactive, watch, computed } from 'vue'
import { EditOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
// import { RelationshipSelect } from '@mh-hip/relationship'
import type { PropType } from 'vue'
import dayjs from 'dayjs'

interface HisFileResponse {
  code: number
  msg: string
  data: HisFileInfo
}

interface HisFileInfo {
  patientId: number
  patientName: string
  genderId: number
  createdTime: string
  disabled: number
  telNo: string
  livingAddr: string
  livingZonecode: string
  companyTel: string
  lastUpdated: string
  countryCode: string
  nationalityCode: string
  birthDate: number
  idcertNo: string
  certTypeId: number
  contactorName: string
  contactorTel: string
  relationshipId: number
  genderName: string
  certTypeName: string
  relationshipName: string
  ageOfYears: number
  ageOfDays: number
  familyZoneCodeLs: string[]
  livingZoneCodeLs: string[]
  companyZoneCodeLs: string[]
}

const props = defineProps({
  info: {
    type: Object as PropType<HisFileInfo>,
    required: true
  }
})

const emit = defineEmits(['update:info', 'save'])

// 编辑模式
const isEditing = ref(false)
// 编辑数据
const editingData = reactive<Partial<HisFileInfo>>({})

// 格式化出生日期
const formatBirthDate = (date: number) => {
  if (!date) return '-'
  const dateStr = date.toString()
  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
}

// 性别选项
const genderOptions = [
  { value: 1, label: '男' },
  { value: 2, label: '女' }
]

// 证件类型选项
const certTypeOptions = [
  { value: 1, label: '居民身份证' },
  { value: 2, label: '军官证' },
  { value: 3, label: '护照' },
  { value: 4, label: '其他' }
]

/*
// 关系选项 - 已由RelationshipSelect组件提供
const relationshipOptions = [
  { value: 1, label: '本人' },
  { value: 2, label: '父母' },
  { value: 3, label: '子女' },
  { value: 4, label: '配偶' },
  { value: 5, label: '其他亲属' },
  { value: 6, label: '朋友' },
  { value: 7, label: '其他' }
]
*/

// 格式化的出生日期对象
const birthDateMoment = computed(() => {
  if (!editingData.birthDate) return null
  const dateStr = editingData.birthDate.toString()
  const year = dateStr.slice(0, 4)
  const month = dateStr.slice(4, 6)
  const day = dateStr.slice(6, 8)
  return dayjs(`${year}-${month}-${day}`)
})

// 开始编辑
const startEdit = () => {
  // 深拷贝当前数据
  Object.assign(editingData, JSON.parse(JSON.stringify(props.info)))
  isEditing.value = true
}

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false
}

// 保存编辑
const saveEdit = () => {
  isEditing.value = false
  // 通知父组件更新数据
  emit('update:info', editingData)
  emit('save', editingData)
  message.success('保存成功')
}

// 处理日期变更
const handleDateChange = (date: any) => {
  if (date) {
    const year = date.year()
    const month = (date.month() + 1).toString().padStart(2, '0')
    const day = date.date().toString().padStart(2, '0')
    editingData.birthDate = parseInt(`${year}${month}${day}`)
  } else {
    editingData.birthDate = 0
  }
}

// 监听props.info变化，更新编辑数据
watch(
  () => props.info,
  (newVal) => {
    if (!isEditing.value && newVal) {
      Object.assign(editingData, JSON.parse(JSON.stringify(newVal)))
    }
  },
  { immediate: true, deep: true }
)
</script>

<template>
  <div class="his-file-info">
    <!-- 非编辑模式下的信息展示 -->
    <template v-if="!isEditing">
      <div class="edit-actions" v-if="info">
        <a-button 
          type="link"
          @click="startEdit"
          class="edit-icon-button"
        >
          <edit-outlined />
        </a-button>
      </div>
      
      <Descriptions :column="2" size="small">
        <Descriptions.Item label="姓名">{{ info.patientName || '-' }}</Descriptions.Item>
        <Descriptions.Item label="性别">{{ info.genderName || '-' }}</Descriptions.Item>
        <Descriptions.Item label="年龄">{{ info.ageOfYears ? `${info.ageOfYears}岁` : '-' }}</Descriptions.Item>
        <Descriptions.Item label="出生日期">{{ formatBirthDate(info.birthDate) }}</Descriptions.Item>
        <Descriptions.Item label="证件类型">{{ info.certTypeName || '-' }}</Descriptions.Item>
        <Descriptions.Item label="证件号码">{{ info.idcertNo || '-' }}</Descriptions.Item>
        <Descriptions.Item label="联系电话">{{ info.telNo || '-' }}</Descriptions.Item>
        <Descriptions.Item label="联系人">{{ info.contactorName || '-' }}</Descriptions.Item>
        <Descriptions.Item label="联系人电话">{{ info.contactorTel || '-' }}</Descriptions.Item>
        <Descriptions.Item label="与患者关系">{{ info.relationshipName || '-' }}</Descriptions.Item>
        <Descriptions.Item label="居住地址" :span="2">{{ info.livingAddr || '-' }}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{{ info.createdTime || '-' }}</Descriptions.Item>
        <Descriptions.Item label="更新时间">{{ info.lastUpdated || '-' }}</Descriptions.Item>
      </Descriptions>
    </template>
    
    <!-- 编辑模式下的表单 -->
    <template v-else>
      <div class="edit-actions" v-if="info">
        <a-button 
          type="link"
          @click="saveEdit"
          class="save-icon-button"
        >
          <check-outlined />
        </a-button>
        <a-button 
          type="link"
          @click="cancelEdit"
          class="cancel-icon-button"
        >
          <close-outlined />
        </a-button>
      </div>
      
      <div class="edit-form">
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">姓名</div>
            <a-input v-model:value="editingData.patientName" size="small" />
          </div>
          <div class="form-item">
            <div class="form-label">性别</div>
            <a-select v-model:value="editingData.genderId" size="small" style="width: 100%">
              <a-select-option v-for="option in genderOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">出生日期</div>
            <a-date-picker 
              v-model:value="birthDateMoment" 
              size="small" 
              @change="handleDateChange" 
              format="YYYY-MM-DD"
              style="width: 100%"
            />
          </div>
          <div class="form-item">
            <div class="form-label">年龄</div>
            <a-input :value="info.ageOfYears ? `${info.ageOfYears}岁` : '-'" size="small" disabled />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">证件类型</div>
            <a-select v-model:value="editingData.certTypeId" size="small" style="width: 100%">
              <a-select-option v-for="option in certTypeOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </div>
          <div class="form-item">
            <div class="form-label">证件号码</div>
            <a-input v-model:value="editingData.idcertNo" size="small" />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">联系电话</div>
            <a-input v-model:value="editingData.telNo" size="small" />
          </div>
          <div class="form-item">
            <div class="form-label">联系人</div>
            <a-input v-model:value="editingData.contactorName" size="small" />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">联系人电话</div>
            <a-input v-model:value="editingData.contactorTel" size="small" />
          </div>
          <div class="form-item">
            <div class="form-label">与患者关系</div>
            <!-- <RelationshipSelect v-model:value="editingData.relationshipId" size="small" style="width: 100%" /> -->
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item" style="width: 100%">
            <div class="form-label">居住地址</div>
            <a-input v-model:value="editingData.livingAddr" size="small" />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-item">
            <div class="form-label">创建时间</div>
            <a-input :value="info.createdTime || '-'" size="small" disabled />
          </div>
          <div class="form-item">
            <div class="form-label">更新时间</div>
            <a-input :value="info.lastUpdated || '-'" size="small" disabled />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
.his-file-info {
  position: relative;
  
  .edit-actions {
    position: absolute;
    top: -8px;
    right: 0;
    z-index: 1;
    display: flex;
    gap: 5px;
    
    .edit-icon-button {
      color: #1890ff;
      padding: 4px 8px;
      
      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
        border-radius: 4px;
      }
    }
    
    .save-icon-button {
      color: #52c41a;
      padding: 4px 8px;
      
      &:hover {
        background-color: rgba(82, 196, 26, 0.1);
        border-radius: 4px;
      }
    }
    
    .cancel-icon-button {
      color: #ff4d4f;
      padding: 4px 8px;
      
      &:hover {
        background-color: rgba(255, 77, 79, 0.1);
        border-radius: 4px;
      }
    }
  }
  
  :deep(.ant-descriptions) {
    .ant-descriptions-item {
      padding-bottom: 12px;
      
      .ant-descriptions-item-label {
        color: #666;
        width: 100px;
        vertical-align: middle;
      }
      
      .ant-descriptions-item-content {
        color: #333;
        vertical-align: middle;
      }
    }
  }
  
  .edit-form {
    padding: 8px 0;
    
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 12px;
      
      .form-item {
        flex: 1;
        
        .form-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 4px;
        }
      }
    }
  }
}
</style> 