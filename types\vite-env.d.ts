
declare module '*.vue' {
  import type { DefineComponent } from 'vue'

  const component: DefineComponent<NonNullable<unknown>, NonNullable<unknown>, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_APP_KEY: string;
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_ALIAS: string;
  readonly VITE_SECURITY_ENCRYPT: string;
  readonly VITE_SECURITY_SECRET: string;
  readonly VITE_ROOT: string;
  readonly VITE_PERMISSION: string;
  readonly VITE_ROLE: string;
  readonly VITE_MIN_ROLE: string;
  readonly VITE_LOGIN_URL: string;
  readonly VITE_APP_URL_DEFAULT: string;
  readonly VITE_APP_URL_oauth: string;
  readonly VITE_APP_URL_dmc: string;
  readonly VITE_APP_URL_amc: string;
  readonly VITE_APP_URL_idm: string;
  readonly VITE_APP_URL_tas: string;
  readonly VITE_APP_URL_inv: string;
  readonly VITE_APP_URL_bcs: string;
  readonly VITE_APP_URL_sys: string;
  readonly VITE_APP_URL_hip: string;
  readonly VITE_APP_URL_hsd: string;
  readonly VITE_APP_URL_inpatientHsd: string;
  readonly VITE_APP_URL_wm: string;
  readonly VITE_APP_URL_eam: string;
  readonly VITE_APP_URL_mi: string;
  readonly VITE_APP_URL_miLocal: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

interface ImportMetaEnv {
  readonly VITE_APP_KEY: string;
  readonly VITE_APP_TITLE: string;
}
