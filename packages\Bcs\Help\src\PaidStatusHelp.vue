<script lang="ts" setup>
const opened = ref(false)
import { Collapse, CollapsePanel, Modal, Tag } from 'ant-design-vue'
</script>

<template>
<span class="cp" @click="opened = !opened">支付状态<a>?</a></span>
<Modal v-model:open="opened" :footer="null" destroy-on-close title="支付状态说明" width="800px">
  <div class="trnas-status-help">
    <div>
      <Tag class="bgc-error">待支付</Tag>
      当患者完成就诊后，医生会开具一份包含所需支付的所有费用项目的清单（即划价单）。此时，该划价单处于“待支付”状态，意味着患者还没有完成付款。收费员需要等待患者到窗口或通过电子方式来完成支付。
    </div>
    <div>
      <Tag :bordered="false" class="bgc-dark-orange">支付中</Tag>
      一旦患者开始进行支付流程，比如使用银行卡、移动支付等方式提交了支付请求，但系统尚未确认收到款项时，划价单的状态就会显示为“支付中”。在这个过程中，收费员可能需要留意是否有支付失败的情况发生，并准备提供必要的帮助或指导。
    </div>
    <div>
      <Tag :bordered="false" class="bgc-success">已支付</Tag>
      当患者的支付被成功处理并验证无误后，划价单将更新为“已支付”状态。这意味着所有列出的费用都已经被全额支付完毕，收费员可以根据这个状态来进行下一步的操作，如打印收据等。
    </div>
    <div>
      <Tag :bordered="false" class="bgc-cyan">已退费</Tag>
      如果由于某种原因（例如重复计费、服务取消等）需要退还给患者部分或全部已收取的费用，那么相应的划价单会被标记为“已退费”。收费员需根据规定流程办理退款手续，并确保退款准确无误地返还给患者。
    </div>
    <div>
      <Tag :bordered="false" class="bgc-gray">已取消</Tag>
      若在患者支付前决定不接受某项服务或治疗，或者发现有错误需要重新开单，则原划价单可能会被标记为“已取消”。在这种情况下，收费员应当告知患者情况，并按照指示重新开具正确的划价单或处理其他相关事宜。
    </div>
    <Collapse>
      <CollapsePanel header="支付状态和交易状态的区别">
        <strong>范围不同：</strong>
        <ul>
          <li>支付状态更侧重于描述一个完整的支付流程中划价单所处的不同阶段。</li>
          <li>交易状态则专注于每次具体的支付尝试的结果，它反映了每一次独立的财务交互情况。</li>
        </ul>
        <strong>关注点不同：</strong>
        <ul>
          <li>支付状态（针对划价单），交易状态（针对交易系统）</li>
          <li>对于收费员而言，了解支付状态可以帮助他们掌握整体的账务处理进度，比如哪些患者还没有付款、哪些正在处理中等。</li>
          <li>
            而交易状态则更多地用于监控每一笔具体支付行为的成功与否，特别是在出现异常情况时能迅速定位问题所在并采取相应措施。
          </li>
        </ul>
      </CollapsePanel>
    </Collapse>
  </div>
</Modal>

</template>

<style lang="less" scoped>
.trnas-status-help {
  ul {
    margin-top: 4px;
  }

  div {
    margin-bottom: 4px;
  }
}
</style>
