<script setup lang="ts">
import { FeeCatAll, FeeCatDict } from '@mh-hip/fee-cat'
import { But<PERSON> } from 'ant-design-vue'

const ct = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'.toLowerCase().split(';')[0]
console.info(ct.toLowerCase().split(';')[0].trim())

const select = ref()
const radio = ref()
const checkbox = reactive<any[]>([])
const exportExcel = async () => {
  try {
    return await http.post(
      '/hsd/wmBillDetail/expWmBillDetail',
      {
        applyTimeRange: ['2025-03-25T07:48:51.349Z', '2025-03-31T07:48:51.349Z'],
        absByType: true,
        sectionId: 38,
        billStatus: 5,
        startDate: '2025-03-25',
        endDate: '2025-03-31',
      },
      { responseType: 'blob', appKey: 'inpatientHsd' }
    )
  } catch (e) {
    console.info(e)
  }
}
</script>

<template>
  <Button @click="exportExcel">导出</Button>
  <FeeCatDict v-model="select" type="Select" w-200px mr-8px />
  <FeeCatDict v-model="radio" type="Radio" w-200px mr-8px />
  <FeeCatDict v-model="checkbox" multiple type="Checkbox" w-400px />
  <div h-8px />
  <FeeCatAll />
</template>
