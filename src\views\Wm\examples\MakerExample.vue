<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './Maker/Basic.vue'
import FormExample from './Maker/Form.vue'
import MethodsExample from './Maker/Methods.vue'
import ValidationExample from './Maker/Validation.vue'
import MakerDeptArtExample from './Maker/MakerDeptArt.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>
      采购制单组件，用于库房管理系统中快速录入制单信息。该组件提供了一个完整的表单界面，支持品种选择、信息填写和数据验证。支持回车键顺序导航表单字段，自动填充品种相关信息（包装单位、拆零单位等），支持日期格式验证（YYYYMMDD），当品种信息为空时自动弹出机构商品设置窗口。
    </Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="form" tab="在表单中使用">
        <FormExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="methods" tab="组件方法">
        <MethodsExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="validation" tab="表单验证">
        <ValidationExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="makerdeptart" tab="品种选择组件">
        <MakerDeptArtExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
