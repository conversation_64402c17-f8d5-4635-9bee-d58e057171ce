<script lang="ts" setup>
import { Data, Format, useLoading } from '@idmy/core'
import { BillDetailGroup } from '@mh-bcs/bill'
import { CashPaymentAllByCashId } from '@mh-bcs/cash-payment'
import { listAllCashes } from '@mh-bcs/util'
import { Collapse, CollapsePanel, Space } from 'ant-design-vue'
import Cash from './Cash.vue'

import Header from './Header.vue'

const { cashId, load } = defineProps({
  cashId: { type: Number as PropType<number>, required: true },
  load: { type: Function, required: true },
})

const cashes = ref<Data[]>([])
useLoading(async () => {
  cashes.value = await listAllCashes(cashId)
}, true)

const activeKey = ref(cashId)
</script>

<template>
  <Header v-if="cashes.length" :data="cashes" :load="load" />
  <Collapse v-model:activeKey="activeKey" :bordered="true">
    <CollapsePanel v-for="(cash, idx) in cashes" :key="cash.cashId">
      <template #header>
        <Space>
          <Format :value="cash.createdAt" type="Datetime" />
          <template v-if="cash.actionType === 'BLUE'"> 开始收费#{{ cash.cashId }} </template>
          <template v-else> 第{{ idx }}次退款#{{ cash.cashId }} </template>
        </Space>
      </template>
      <template #extra>
        <Format :value="cash.amount" type="Currency" />
      </template>
      <Cash :cashId="cash.cashId" />
      <CashPaymentAllByCashId :cashId="cash.cashId" />
      <BillDetailGroup :cashId="cash.cashId" :cashType="cash.cashType" />
    </CollapsePanel>
  </Collapse>
</template>
