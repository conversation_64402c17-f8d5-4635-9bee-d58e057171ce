﻿const MAIN_JS = 'CLodopfuncs.js'
const URL_WS1 = `ws://localhost:8000/${MAIN_JS}` // ws用8000/18000
const URL_WS2 = `ws://localhost:18000/${MAIN_JS}`
const URL_HTTP1 = `http://localhost:8000/${MAIN_JS}` // http用8000/18000
const URL_HTTP2 = `http://localhost:18000/${MAIN_JS}`
const URL_HTTP3 = `https://localhost.lodop.net:8443/${MAIN_JS}` // https用8000/8443
const SCRIPT = 'script'

const checkOrTryHttp = () => {
  if (globalThis.getCLodop) return true
  const head = document.head || document.getElementsByTagName('head')[0] || document.documentElement
  const js2 = document.createElement(SCRIPT)
  const js1 = document.createElement(SCRIPT)
  const js3 = document.createElement(SCRIPT)
  js2.src = URL_HTTP2
  js1.src = URL_HTTP1
  js3.src = URL_HTTP3
  js1.onerror = () => {
    if (globalThis.location.protocol === 'https:') {
      head.insertBefore(js3, head.firstChild)
    } else {
      head.insertBefore(js2, head.firstChild)
    }
  }
  js2.onerror = js3.onerror = () => {
    const js = document.createElement(SCRIPT)
    js.src = `/${MAIN_JS}`
    document.head.insertBefore(js, document.head.firstChild)
  }
  head.insertBefore(js1, head.firstChild)
}

const loadCLodop = () => {
  if (!globalThis.WebSocket && globalThis.MozWebSocket) {
    globalThis.WebSocket = globalThis.MozWebSocket
  }
  try {
    const ws1 = new WebSocket(URL_WS1)
    ws1.onopen = () => setTimeout(checkOrTryHttp, 500)
    // eslint-disable-next-line no-eval
    ws1.onmessage = e => globalThis.getCLodop || eval(e.data)
    ws1.onerror = () => {
      const ws2 = new WebSocket(URL_WS2)
      ws2.onopen = () => setTimeout(checkOrTryHttp, 500)
      // eslint-disable-next-line no-eval
      ws2.onmessage = e => globalThis.getCLodop || eval(e.data)
      ws2.onerror = () => checkOrTryHttp()
    }
  } catch (e) {
    checkOrTryHttp()
  }
}
checkOrTryHttp()
loadCLodop()
