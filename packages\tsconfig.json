{"compilerOptions": {"declaration": true, "emitDeclarationOnly": true, "preserveSymlinks": false, "target": "esnext", "module": "esnext", "outDir": "dist", "allowJs": true, "strict": false, "jsx": "preserve", "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "sourceMap": true, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "baseUrl": ".", "types": ["node", "vite/client"]}, "include": ["auto-imports.d.ts", "./**/*.ts", "./**/*.tsx", "./**/*.vue"], "exclude": ["node_modules", "dist"]}