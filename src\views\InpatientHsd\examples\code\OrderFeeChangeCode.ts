import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { AddArt, RefundOrderExecFee } from '@mh-inpatient-hsd/order-fee-change'
import '@mh-inpatient-hsd/order-fee-change/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/order-fee-change": "^1.0.0",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 医嘱执行增加条目
export const addArtUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleAddArt">打开医嘱执行增加条目弹窗</Button>
  <AddArt ref="addArtRef" :section-id="sectionId" @add="handleClose" />
</template>

<script setup>
import { AddArt } from '@mh-inpatient-hsd/order-fee-change'
import '@mh-inpatient-hsd/order-fee-change/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const addArtRef = ref()

// 模拟数据
const sectionId = 40
const orderId = "OE2025051500001" // 医嘱执行单号
const lineNo = 1 // 行号
const orderLineno = 1 // 医嘱行号

// 打开医嘱执行增加条目弹窗
const handleVisibleAddArt = () => {
  addArtRef.value.open(orderId, lineNo, orderLineno)
}

// 关闭回调
const handleClose = () => {
  message.success('操作成功')
}
</script>`)

// 医嘱执行退费
export const refundOrderExecFeeUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleRefundOrderExecFee">打开医嘱执行退费弹窗</Button>
  <RefundOrderExecFee ref="refundOrderExecFeeRef" @refunded="handleClose" />
</template>

<script setup>
import { RefundOrderExecFee } from '@mh-inpatient-hsd/order-fee-change'
import '@mh-inpatient-hsd/order-fee-change/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const refundOrderExecFeeRef = ref()

// 模拟数据
const orderId = "OE2025051500001" // 医嘱执行单号
const lineNo = 1 // 行号

// 打开医嘱执行退费弹窗
const handleVisibleRefundOrderExecFee = () => {
  refundOrderExecFeeRef.value.open(orderId, lineNo)
}

// 关闭回调
const handleClose = () => {
  message.success('操作成功')
}
</script>`)
