import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 供应商选择 -->
  <ScmCustSelect
    v-model="custId"
    style="width: 200px"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const custId = ref()
</script>`)

// 显示供应商编码
export const codeUsage = wrapCodeExample(`<template>
  <!-- 显示供应商编码 -->
  <ScmCustSelect
    v-model="custId"
    showField="custCode"
    style="width: 200px"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const custId = ref()
</script>`)

// 多选模式
export const multipleUsage = wrapCodeExample(`<template>
  <!-- 多选模式 -->
  <ScmCustSelect
    v-model="custIds"
    multiple
    style="width: 100%"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 多选值
const custIds = ref([])
</script>`)

// 搜索类型
export const searchTypeUsage = wrapCodeExample(`<template>
  <!-- 按五笔码搜索 -->
  <ScmCustSelect
    v-model="custId"
    searchType="wubi"
    placeholder="请输入供应商五笔码"
    style="width: 200px"
  />

  <!-- 按拼音码搜索 -->
  <ScmCustSelect
    v-model="custId"
    searchType="pinyin"
    placeholder="请输入供应商拼音码"
    style="width: 200px"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const custId = ref()
</script>`)

// 分页设置
export const pageSizeUsage = wrapCodeExample(`<template>
  <!-- 设置每页显示条数和是否默认加载 -->
  <ScmCustSelect
    v-model="custId"
    :pageSize="10"
    :loadFirstPage="true"
    style="width: 100%"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值
const custId = ref()
</script>`)

// 非分页加载
export const noPaginationUsage = wrapCodeExample(`<template>
  <!-- 禁用分页，使用前端过滤 -->
  <ScmCustSelect
    v-model="custId"
    :enablePagination="false"
    style="width: 100%"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 单选值，设置默认值
const custId = ref(126)
</script>`)

// 机构供应商
export const orgCustUsage = wrapCodeExample(`<template>
  <!-- 使用机构供应商API -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    style="width: 200px"
    placeholder="请输入机构供应商名称/编码"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 机构供应商选择值
const orgCustId = ref()

// 说明：
// 1. 通过:orgPartner="true"启用机构供应商API
// 2. 当orgPartner为true时，组件内部会自动调用机构供应商相关API
// 3. 当orgPartner为false或不传时，使用普通供应商API
// 4. 其他属性和事件与普通供应商选择组件完全相同
</script>`)

// 机构供应商非分页模式
export const orgPartnerNoPaginationUsage = wrapCodeExample(`<template>
  <!-- 机构供应商非分页模式 - 基础用法 -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    :enablePagination="false"
    style="width: 300px"
    placeholder="请输入机构供应商名称/编码/五笔/拼音"
  />

  <!-- 机构供应商非分页模式 - 多选 -->
  <ScmCustSelect
    v-model="orgCustIds"
    :orgPartner="true"
    :enablePagination="false"
    multiple
    style="width: 100%"
    placeholder="请选择多个机构供应商"
  />

  <!-- 机构供应商非分页模式 - 指定搜索类型 -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    :enablePagination="false"
    searchType="pinyin"
    placeholder="请输入拼音搜索机构供应商"
    style="width: 300px"
  />

  <!-- 机构供应商非分页模式 - 显示编码优先 -->
  <ScmCustSelect
    v-model="orgCustId"
    :orgPartner="true"
    :enablePagination="false"
    showField="custCode"
    style="width: 300px"
  />
</template>

<script setup>
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { ref } from 'vue'

// 机构供应商选择值
const orgCustId = ref()
const orgCustIds = ref([])

// 说明：
// 1. orgPartner="true" - 使用机构供应商API (orgPartnerFindAllApi)
// 2. enablePagination="false" - 禁用分页，一次性加载所有数据
// 3. 组件会根据 custName、custCode、qsCode1、qsCode2 四个字段进行前端过滤
// 4. qsCode1、qsCode2 是拼音首字母和拼音的大写，搜索时会自动转大写匹配
// 5. searchType 可以指定搜索特定字段：all/name/code/wubi/pinyin
// 6. showField 可以指定显示格式：custName(名称优先)/custCode(编码优先)
</script>`)

// 引入组件
export const importCode = `import { ScmCustSelect } from '@mh-wm/scm-cust'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/scm-cust": "^1.0.0",
    "@mh-wm/util": "^1.0.4"
  }
}`
