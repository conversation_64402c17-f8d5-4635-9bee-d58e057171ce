<script setup lang="ts">
import { WmCountCreate } from '@mh-wm/count'
import { Card, Typography, Divider, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { createUsage, importCode, packageJsonCode } from '../code/CountCreateCode'

const { Title, Paragraph } = Typography

// 创建盘点单成功回调
const handleCreateSuccess = (countId: number) => {
  message.success(`创建盘点单成功，盘点单ID: ${countId}`)
}

// 取消创建盘点单回调
const handleCreateCancel = () => {
  console.log('取消创建盘点单')
}
</script>

<template>
  <Card title="创建盘点单" class="mb-16px">
    <div mb-16px>
      <Title :level="4">基础用法</Title>
      <Paragraph>
        点击按钮打开创建盘点单对话框，可以选择盘点仓库、盘点人，添加盘点品种。
      </Paragraph>
      
      <div class="demo-container">
        <WmCountCreate
          buttonText="新建盘点"
          buttonType="primary"
          :onlyShowWithStock="true"
          @success="handleCreateSuccess"
          @cancel="handleCreateCancel"
        />
      </div>
      
      <Divider />
      <CodeDemoVue :usage="createUsage" :importCode="importCode" :packageJson="packageJsonCode" />
    </div>
  </Card>
</template>

<style scoped>
.demo-container {
  margin: 16px 0;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}
</style>
