<script setup lang="ts">
import { CardReaderButton } from '@mh-mi/card-reader'
import { Card, Typography, Divider, Switch, Input, InputNumber, Form, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { fullParamsUsage, importCode, packageJsonCode } from '../code/CardReaderCode'

const { Title } = Typography

// 表单数据
const formState = ref({
  businessTypeId: '104',
  medicalType: 21,
  visitId: undefined,
  hostUrl: '',
  isGetMI: true,
  isMIOpenSelect: true,
  isMIShowMT: true
})

// 其他参数
const params = ref({})

// 读卡成功回调
const onSuccess = (data: any) => {
  console.log('读卡成功', data)
}
</script>

<template>
  <Card title="完整参数示例" class="mb-16px">
    <div mb-16px>
      <Title :level="4">参数配置</Title>
      <Form :model="formState" layout="vertical">
        <Row :gutter="[16, 0]">
          <Col :span="12">
            <Form.Item label="业务类型ID (businessTypeId)" required>
              <Input v-model:value="formState.businessTypeId" placeholder="请输入业务类型ID" />
            </Form.Item>
          </Col>
          <Col :span="12">
            <Form.Item label="医疗类型 (medicalType)" required>
              <InputNumber v-model:value="formState.medicalType" style="width: 100%" placeholder="请输入医疗类型" />
            </Form.Item>
          </Col>
        </Row>
        <Row :gutter="[16, 0]">
          <Col :span="12">
            <Form.Item label="就诊ID (visitId)">
              <InputNumber v-model:value="formState.visitId" style="width: 100%" placeholder="请输入就诊ID" />
            </Form.Item>
          </Col>
          <Col :span="12">
            <Form.Item label="请求接口 (hostUrl)">
              <Input v-model:value="formState.hostUrl" placeholder="请输入请求接口" />
            </Form.Item>
          </Col>
        </Row>
        <Row :gutter="[16, 0]">
          <Col :span="8">
            <Form.Item label="是否调用医保 (isGetMI)">
              <Switch v-model:checked="formState.isGetMI" />
            </Form.Item>
          </Col>
          <Col :span="8">
            <Form.Item label="是否选择医保险种 (isMIOpenSelect)">
              <Switch v-model:checked="formState.isMIOpenSelect" />
            </Form.Item>
          </Col>
          <Col :span="8">
            <Form.Item label="是否显示慢特信息 (isMIShowMT)">
              <Switch v-model:checked="formState.isMIShowMT" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>

    <Divider />

    <div mb-16px>
      <Title :level="4">读卡按钮</Title>
      <CardReaderButton 
        :business-type-id="formState.businessTypeId"
        :medical-type="formState.medicalType"
        :visit-id="formState.visitId"
        :params="params"
        :host-url="formState.hostUrl"
        :is-get-m-i="formState.isGetMI"
        :is-m-i-open-select="formState.isMIOpenSelect"
        :is-m-i-show-m-t="formState.isMIShowMT"
        @success="onSuccess"
      />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="fullParamsUsage"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
