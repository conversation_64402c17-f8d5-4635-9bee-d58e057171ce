<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form'
import type { TableColumnType } from 'ant-design-vue'
import { Button, Col, Form, FormItem, Modal, Row, Table, Select, SelectOption, Badge, InputNumber, Radio, RadioGroup, Checkbox, Popconfirm, message } from 'ant-design-vue'
import { ArtSelect } from '@mh-inpatient-hsd/selector'
import { findSectionRouteConsumableLsByIdApi, saveSectionRouteConsumableApi, delSectionRouteConsumableApi, getDictData } from '@mh-hip/util'

const props = defineProps({
  sectionId: {
    type: Number,
    default: null
  },
  sectionName: {
    type: String,
    default: null
  }
})

const columns: TableColumnType[] = [
  {
    title: '条目编号',
    dataIndex: 'artId',
    align: 'center',
    width: 80
  },
  {
    title: '耗材',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '适用儿童',
    dataIndex: 'forChildren',
    align: 'center',
    width: 80
  },
  {
    title: '适用成人',
    dataIndex: 'forAdult',
    align: 'center',
    width: 80
  },
  {
    title: '倍乘频次',
    dataIndex: 'multiplyByTimes',
    align: 'center',
    width: 80
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unit',
    width: 60
  },
  {
    title: '数量',
    dataIndex: 'total',
    align: 'right',
    width: 80
  },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    width: 60
  }
]

const visible = ref(false);
const formRef = ref()
const formModel = ref<any>({})
const stConfirmLoading = ref<boolean>(false)
const dataSource = ref<any>([])
const routeTypeLs = ref<any>([])
const personal_child = 1
const personal_adult = 2
const personal_all = 3

const artSelectRef = ref<InstanceType<typeof ArtSelect>>()
const open = () => {
  formModel.value.routeId = null
  clearFormModel()
  getDataSource()
  getRouteTypeLs()
  nextTick(() => {
    artSelectRef.value?.init()
  })
  visible.value = true
}

const getRouteTypeLs = async () => {
  routeTypeLs.value = []
  getDictData('routetype').then((data: any) => {
    if (data) {
      routeTypeLs.value = data
    }
  })
  // routeTypeLs.value = data.filter(item => item.feedMethod === 20)
}

const getDataSource = async () => {
  dataSource.value = []
  if (!formModel.value.routeId) {
    return
  }
  findSectionRouteConsumableLsByIdApi({ sectionId: props.sectionId, routeId: formModel.value.routeId }).then((data: any) => {
    if (data) {
      dataSource.value = data
    }
  })
}

const clearFormModel = () => {
  formModel.value.artId = null
  formModel.value.total = null
  formModel.value.unitType = null
  formModel.value.packUnit = null
  formModel.value.cellUnit = null
  formModel.value.personalRange = null
  formModel.value.multiplyByTimes = null
}

const rules: Record<string, Rule[]> = {
  total: [
    { required: true, message: '请设置数量', trigger: 'change' }
  ],
  unitType: [
    { required: true, message: '请选择单位类型', trigger: 'change' }
  ],
  personalRange: [
    { required: true, message: '请选择适用范围', trigger: 'change' }
  ]
}

function handleCancel() {
  visible.value = false
}

// 耗材选择
function handleArtSelect (art: any) {
  clearFormModel()
  if (art) {
    formModel.value.artId = art.artId
    formModel.value.packUnit = art.packUnit
    formModel.value.cellUnit = art.cellUnit
    formModel.value.total = 1
    // && art.artTypeId !== 14
    if (art.stockReq === 1) {
      if (formModel.value.cellUnit) {
        formModel.value.unitType = 1
        formModel.value.unit = formModel.value.cellUnit
      } else if (formModel.value.packUnit) {
        formModel.value.unitType = 2
        formModel.value.unit = formModel.value.packUnit
      }
    } else {
      if (formModel.value.packUnit) {
        formModel.value.unitType = 2
        formModel.value.unit = formModel.value.packUnit
      } else if (formModel.value.cellUnit) {
        formModel.value.unitType = 1
        formModel.value.unit = formModel.value.cellUnit
      }
    }
    formModel.value.multiplyByTimes = true
    formModel.value.personalRange = personal_all
  }
}

const handleAdd = () => {
  if (!formModel.value.personalRange) {
    message.error('请选择适用范围')
    return
  }
  stConfirmLoading.value = true
  const forChildren = formModel.value.personalRange === personal_child  || formModel.value.personalRange === personal_all ? 1 : 0
  const forAdult = formModel.value.personalRange === personal_adult  || formModel.value.personalRange === personal_all ? 1 : 0
  const params = {
    sectionId: props.sectionId,
    routeId: formModel.value.routeId,
    artId: formModel.value.artId,
    total: formModel.value.total,
    unitType: formModel.value.unitType,
    forChildren,
    forAdult,
    multiplyByTimes: formModel.value.multiplyByTimes ? 1 : 0
  }
  saveSectionRouteConsumableApi(params).then(() => {
    message.success('病区给药绑定耗材添加完成')
    clearFormModel()
    getDataSource()
    artSelectRef.value?.init()
  }).finally(() => {
    stConfirmLoading.value = false
  })
}
const handleDelete = (record: any) => {
  stConfirmLoading.value = true
  const params = {
    sectionId: props.sectionId,
    routeId: formModel.value.routeId,
    artId: record.artId
  }
  delSectionRouteConsumableApi(params).then(() => {
    message.success('病区给药绑定耗材删除完成')
    getDataSource()
  }).finally(() => {
    stConfirmLoading.value = false
  })
}

function filterOption (input: any, option: any) {
  return (
      option.text.toLowerCase().indexOf(input.toLowerCase()) >= 0
  )
}

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" :title="`${props.sectionName ? props.sectionName : ''}  病区给药绑定耗材`" width="1100px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Button type="dashed" @click="handleCancel">
        关闭
      </Button>
    </template>
    <div style="height: calc(100vh - 170px); overflow: auto;">
      <Form ref="formRef" :model="formModel" :rules="rules" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <Row>
          <Col :span="8">
            <Form-item label="给药途径" name="routeId">
              <Select
                show-search
                placeholder="请选择给药途径"
                style="width: 100%"
                :filter-option="filterOption"
                v-model:value="formModel.routeId"
                @change="getDataSource">
                <Select-option v-for="item in routeTypeLs" :key="item.routeId" :value="item.routeId" :text="item.routeName">
                  {{ item.routeName }}
                </Select-option>
              </Select>
            </Form-item>
          </Col>
          <Col :span="8">
            <Form-item label="适用范围" name="personalRange">
              <Select
                show-search
                placeholder="请选择适用范围"
                style="width: 100%"
                v-model:value="formModel.personalRange">
                <Select-option :value="personal_child">
                  儿童
                </Select-option>
                <Select-option :value="personal_adult">
                  成人
                </Select-option>
                <Select-option :value="personal_all">
                  全部
                </Select-option>
              </Select>
            </Form-item>
          </Col>
          <Col :span="8">
            <Form-item label="倍乘频次" name="multiplyByTimes">
              <Checkbox v-model:checked="formModel.multiplyByTimes"/>
            </Form-item>
          </Col>
        </Row>
        <Row>
          <Col :span="11">
            <Form-item label="条目" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
              <art-select ref="artSelectRef" :searchType="2" @selected="handleArtSelect"/>
            </Form-item>
          </Col>
          <Col :span="4">
            <Form-item label="数量" name="total">
              <Input-number v-model:value="formModel.total" placeholder="请设置数量" :precision="4" :min="0" :max="99999999" style="width: 100%;"/>
            </Form-item>
          </Col>
          <Col :span="7">
            <Form-item label="单位" name="unitType">
              <Radio-group v-model:value="formModel.unitType">
                <Radio v-if="formModel.packUnit" :value="2">
                  {{ formModel.packUnit }}(包装)
                </Radio>
                <Radio v-if="formModel.cellUnit" :value="1">
                  {{ formModel.cellUnit }}(拆零)
                </Radio>
              </Radio-group>
            </Form-item>
          </Col>
          <Col :span="2" style="text-align: right">
            <Button @click="handleAdd" :loading="stConfirmLoading" type="primary">
              添加
            </Button>
          </Col>
        </Row>
        <Table :rowKey="(record: any) => record.artId" :columns="columns" :data-source="dataSource">
          <template #bodyCell="{ text, column, record }">
            <template v-if="column.dataIndex === 'artDesc'">
              <div>
                <span style="font-weight: bold;">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
              </div>
              <div v-if="record.producer">{{ record.producer }}</div>
            </template>
            <template v-else-if="['forChildren', 'forAdult', 'multiplyByTimes'].includes(column.dataIndex)">
              <template v-if="text === 1">
                <Badge status="success" />
              </template>
              <template v-else><span> </span></template>
            </template>
            <template v-else-if="column.dataIndex === 'unit'">
              <span v-if="record.unitType === 1">{{ record.cellUnit }}</span>
              <span v-else-if="record.unitType === 2">{{ record.packUnit }}</span>
              <span v-else-if="record.unitType === 3">{{ record.doseUnit }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'action'">
              <Popconfirm title="确定删除吗？" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
                <a c-error>
                  删除
                </a>
              </Popconfirm>
            </template>
          </template>
        </Table>
      </Form>
    </div>
  </Modal>
</template>
