import { Data, http, Modal } from '@idmy/core'

export async function prepaidUncharged(visitId: number, amount: number) {
  return await http.post('/api/bcs/prepaidbalance/uncharged', { visitId, amount }, { appKey: 'bcs' })
}

export async function getPrepaidBalanceByVisitId(visitId: number) {
  return await http.post('/api/bcs/pay/getPrepaidBalanceByPatientIdOrVisitId', { visitId }, { appKey: 'bcs' })
}

export function openPrepaidRefund(visitId: number) {
  return new Promise(resolve => {
    Modal.b.open({
      //component: Refund,
      title: '预交金退款',
      width: 2,
      props: {
        visitId,
      },
      onClose: () => resolve(),
    })
  })
}

export function pageDepositAccount(params: Data) {
  return http.post('/api/bcs/deposit/DepositAccount/page', params, { appKey: 'bcs' })
}

export function depositRefund(paymentType: number, amount: number) {
  return http.post('/api/bcs/deposit/DepositPay/refund', { paymentType, amount }, { appKey: 'bcs' })
}

export function listPlusPaymentTypesAmt(accountId: number) {
  return http.post('/api/bcs/deposit/DepositAccount/listPlusPaymentTypesAmt', { accountId }, { appKey: 'bcs' })
}
