<script setup lang="ts">
import { WholeBox } from '@mh-wm/whole-box'
import { Card, Typography, Divider, Button, message, Table, Space, Tag, Input, Select } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, ExportOutlined } from '@ant-design/icons-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, computed, h } from 'vue'
import { tableIntegration, apiIntegration } from '@/views/Wm/examples/code/WholeBoxCode'

const { Title, Paragraph } = Typography

// WholeBox组件引用
const wholeBoxRef = ref()

// 表格数据和状态
const loading = ref(false)
const searchText = ref('')
const selectedRowKeys = ref([])
const filterStatus = ref('all')

// 模拟库存数据
const stockData = ref([
  {
    artId: 1001,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    producer: '华北制药集团有限责任公司',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 15,
    deptTotalCells: 8,
    totalPacks: 5,
    totalCells: 3,
    batchNo: 'B20240101',
    expDate: '20251201',
    status: 'normal'
  },
  {
    artId: 1002,
    artName: '头孢克肟片',
    artSpec: '100mg',
    producer: '齐鲁制药有限公司',
    packCells: 6,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 8,
    deptTotalCells: 2,
    totalPacks: 3,
    totalCells: 4,
    batchNo: 'B20240102',
    expDate: '20251202',
    status: 'low'
  },
  {
    artId: 1003,
    artName: '布洛芬缓释胶囊',
    artSpec: '300mg',
    producer: '中美史克制药有限公司',
    packCells: 20,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 12,
    deptTotalCells: 15,
    totalPacks: 5,
    totalCells: 18,
    batchNo: 'B20240103',
    expDate: '20251203',
    status: 'normal'
  },
  {
    artId: 2001,
    artName: '一次性注射器',
    artSpec: '5ml',
    producer: '山东威高集团医用高分子制品股份有限公司',
    packCells: 100,
    cellUnit: '支',
    packUnit: '盒',
    deptTotalPacks: 3,
    deptTotalCells: 25,
    totalPacks: 1,
    totalCells: 50,
    batchNo: 'D20240101',
    expDate: '20261201',
    status: 'normal'
  },
  {
    artId: 3001,
    artName: '胰岛素笔芯',
    artSpec: '3ml',
    producer: '诺和诺德(中国)制药有限公司',
    packCells: 5,
    cellUnit: '支',
    packUnit: '盒',
    deptTotalPacks: 0,
    deptTotalCells: 0,
    totalPacks: 0,
    totalCells: 0,
    batchNo: 'S20240101',
    expDate: '20251201',
    status: 'empty'
  }
])

// 过滤后的数据
const filteredData = computed(() => {
  let data = stockData.value

  // 文本搜索过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    data = data.filter(item => 
      item.artName.toLowerCase().includes(search) ||
      item.artSpec.toLowerCase().includes(search) ||
      item.producer.toLowerCase().includes(search) ||
      item.batchNo.toLowerCase().includes(search)
    )
  }

  // 状态过滤
  if (filterStatus.value !== 'all') {
    data = data.filter(item => item.status === filterStatus.value)
  }

  return data
})

// 表格列配置
const columns = [
  {
    title: '药品信息',
    key: 'artInfo',
    width: 200,
    customRender: ({ record }) => {
      return h('div', { class: 'art-info' }, [
        h('div', { class: 'art-name' }, record.artName),
        h('div', { class: 'art-spec' }, record.artSpec),
        h('div', { class: 'art-producer' }, record.producer)
      ])
    }
  },
  {
    title: '包装规格',
    key: 'packSpec',
    width: 120,
    align: 'center',
    customRender: ({ record }) => {
      return h(Tag, { color: 'blue' }, () => 
        `${record.packCells}${record.cellUnit}/${record.packUnit}`
      )
    }
  },
  {
    title: '仓库总库存',
    key: 'deptStock',
    width: 120,
    align: 'center',
    customRender: ({ record }) => {
      return h('div', { class: 'stock-info' }, [
        h('div', { class: 'stock-packs' }, [
          h('strong', record.deptTotalPacks),
          record.packUnit
        ]),
        record.deptTotalCells > 0 && h('div', { class: 'stock-cells' }, [
          h('strong', record.deptTotalCells),
          record.cellUnit
        ])
      ])
    }
  },
  {
    title: '批次库存',
    key: 'batchStock',
    width: 120,
    align: 'center',
    customRender: ({ record }) => {
      return h('div', { class: 'stock-info' }, [
        h('div', { class: 'stock-packs' }, [
          h('strong', record.totalPacks),
          record.packUnit
        ]),
        record.totalCells > 0 && h('div', { class: 'stock-cells' }, [
          h('strong', record.totalCells),
          record.cellUnit
        ])
      ])
    }
  },
  {
    title: '批次信息',
    key: 'batchInfo',
    width: 150,
    customRender: ({ record }) => {
      return h('div', { class: 'batch-info' }, [
        h('div', { class: 'batch-no' }, `批次: ${record.batchNo}`),
        h('div', { class: 'exp-date' }, `效期: ${formatDate(record.expDate)}`)
      ])
    }
  },
  {
    title: '库存状态',
    key: 'stockStatus',
    width: 100,
    align: 'center',
    customRender: ({ record }) => {
      const statusConfig = getStatusConfig(record.status)
      return h(Tag, { color: statusConfig.color }, () => statusConfig.text)
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    align: 'center',
    fixed: 'right',
    customRender: ({ record }) => {
      return h(Space, [
        h(Button, {
          type: 'primary',
          size: 'small',
          onClick: () => handleSplitPack(record)
        }, () => '拆零盒整'),
        h(Button, {
          size: 'small',
          onClick: () => viewDetails(record)
        }, () => '详情')
      ])
    }
  }
]

// 分页配置
const pagination = {
  current: 1,
  pageSize: 10,
  total: computed(() => filteredData.value.length),
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
}

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: any[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: any) => ({
    disabled: record.status === 'empty'
  })
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr || dateStr.length !== 8) return dateStr
  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
}

// 获取状态配置
const getStatusConfig = (status: string) => {
  switch (status) {
    case 'normal': return { color: 'green', text: '库存充足' }
    case 'low': return { color: 'orange', text: '库存不足' }
    case 'empty': return { color: 'red', text: '缺货' }
    default: return { color: 'default', text: '未知' }
  }
}

// 搜索处理
const handleSearch = () => {
  // 搜索逻辑已在computed中处理
  message.success(`找到 ${filteredData.value.length} 条记录`)
}

// 刷新数据
const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据刷新成功')
  }, 1000)
}

// 导出数据
const exportData = () => {
  if (filteredData.value.length === 0) {
    message.warning('没有数据可导出')
    return
  }

  const csvContent = [
    ['药品名称', '规格', '生产厂家', '包装规格', '仓库库存', '批次库存', '批次号', '有效期', '状态'].join(','),
    ...filteredData.value.map(record => [
      record.artName,
      record.artSpec,
      record.producer,
      `${record.packCells}${record.cellUnit}/${record.packUnit}`,
      `${record.deptTotalPacks}${record.packUnit}${record.deptTotalCells}${record.cellUnit}`,
      `${record.totalPacks}${record.packUnit}${record.totalCells}${record.cellUnit}`,
      record.batchNo,
      formatDate(record.expDate),
      getStatusConfig(record.status).text
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `库存数据_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()

  message.success('数据导出成功')
}

// 批量拆零盒整
const batchSplitPack = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要操作的记录')
    return
  }

  const selectedRecords = stockData.value.filter(item => 
    selectedRowKeys.value.includes(item.artId)
  )

  message.info(`开始批量拆零盒整，共 ${selectedRecords.length} 条记录`)
  
  let index = 0
  const processNext = () => {
    if (index < selectedRecords.length) {
      const record = selectedRecords[index]
      message.info(`正在处理: ${record.artName}`)
      handleSplitPack(record)
      index++
      setTimeout(processNext, 1500)
    } else {
      message.success('批量拆零盒整完成！')
      selectedRowKeys.value = []
    }
  }

  processNext()
}

// 拆零盒整操作
const handleSplitPack = (record: any) => {
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(record)
  }
}

// 查看详情
const viewDetails = (record: any) => {
  message.info(`查看 ${record.artName} 的详细信息`)
  // 这里可以打开详情模态框或跳转到详情页面
}

// 拆零盒整成功回调
const handleSplitPackSuccess = (data: any) => {
  message.success('拆零盒整操作成功！')
  // 这里可以刷新表格数据
  refreshData()
}
</script>

<template>
  <Card title="表格集成 - 拆零盒整组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">库存管理表格</Title>
      <Paragraph>
        在数据表格中集成拆零盒整功能，实现完整的库存管理系统。
        支持搜索、筛选、分页、批量操作等功能。
      </Paragraph>

      <!-- 操作工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <Button type="primary" @click="refreshData" :loading="loading">
            <ReloadOutlined />
            刷新数据
          </Button>
          <Button @click="exportData">
            <ExportOutlined />
            导出数据
          </Button>
          <Button 
            @click="batchSplitPack"
            :disabled="selectedRowKeys.length === 0"
          >
            批量拆零盒整 ({{ selectedRowKeys.length }})
          </Button>
        </div>
        
        <div class="toolbar-right">
          <Space>
            <Select
              v-model:value="filterStatus"
              style="width: 120px"
              placeholder="状态筛选"
            >
              <Select.Option value="all">全部状态</Select.Option>
              <Select.Option value="normal">库存充足</Select.Option>
              <Select.Option value="low">库存不足</Select.Option>
              <Select.Option value="empty">缺货</Select.Option>
            </Select>
            
            <Input.Search
              v-model:value="searchText"
              placeholder="搜索药品名称、规格、厂家或批次号"
              style="width: 300px"
              @search="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </Input.Search>
          </Space>
        </div>
      </div>

      <!-- 库存数据表格 -->
      <Table
        :columns="columns"
        :data-source="filteredData"
        :pagination="pagination"
        :loading="loading"
        :row-selection="rowSelection"
        row-key="artId"
        size="middle"
        bordered
        :scroll="{ x: 1200 }"
      >
        <template #emptyText>
          <div class="empty-table">
            <p>暂无库存数据</p>
            <p>请检查搜索条件或联系管理员</p>
          </div>
        </template>
      </Table>

      <Divider />

      <!-- 代码示例 -->
      <div class="code-section">
        <Title :level="5">代码示例</Title>
        
        <CodeDemoVue 
          title="表格集成示例"
          :code="tableIntegration"
          description="在数据表格中集成拆零盒整功能的完整示例"
        />
        
        <CodeDemoVue 
          title="API集成示例"
          :code="apiIntegration"
          description="API调用监控和错误处理的集成示例"
        />
      </div>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @split-pack-success="handleSplitPackSuccess"
    />
  </Card>
</template>

<style scoped>
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.art-info {
  .art-name {
    font-weight: 600;
    color: #262626;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .art-spec {
    color: #666;
    font-size: 12px;
    margin-bottom: 2px;
  }

  .art-producer {
    color: #999;
    font-size: 11px;
    line-height: 1.2;
  }
}

.stock-info {
  .stock-packs {
    margin-bottom: 2px;
    font-size: 14px;
  }

  .stock-cells {
    color: #666;
    font-size: 12px;
  }
}

.batch-info {
  .batch-no {
    font-size: 12px;
    margin-bottom: 2px;
  }

  .exp-date {
    color: #666;
    font-size: 11px;
  }
}

.empty-table {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.code-section {
  margin-top: 24px;
}

:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: #e6f7ff;
  }
}

:deep(.ant-table-selection-column) {
  width: 60px;
}
</style>
