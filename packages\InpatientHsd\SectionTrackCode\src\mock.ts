// 病区绑定追溯码组件的模拟数据

import type { SectionFeeTrackCodeSummaryResponse } from './types'

// 模拟API响应数据
export const mockSectionFeeTrackCodeSummaryData: SectionFeeTrackCodeSummaryResponse = {
  artSummaryList: [
    {
      artId: 1063936,
      keyStr: "1063936_1",
      artName: "注射用头孢曲松钠",
      artSpec: " 1g*10瓶",
      producer: "国药集团致君(深圳)制药有限公司",
      packCells: 10,
      packUnit: "盒",
      cellUnit: "瓶",
      unitType: 1, // 拆零
      isDisassembled: 0,
      totalAmount: 5,
      unitName: "瓶",
      visitSummaryList: [
        {
          visitId: "86234",
          patientName: "张三",
          bedNo: "9",
          feeDetailList: [
            {
              keyStr: "1929715815310036992-4",
              execSeqid: "1929715815310036992",
              lineNo: 4,
              visitId: 86234,
              oeNo: 80,
              artId: 1063936,
              total: 3.0,
              unitType: 1,
              bseqid: "328360",
              billLineNo: 10,
              isDisassembled: 0,
              usedTrackCodeDetails: [
                {
                  trackCode: "83794211263916924482",
                  usedAmount: 3
                }
              ]
            }
          ]
        },
        {
          visitId: "85148",
          patientName: "李允纬",
          bedNo: "7",
          feeDetailList: [
            {
              keyStr: "1929715812558573568-4",
              execSeqid: "1929715812558573568",
              lineNo: 4,
              visitId: 85148,
              oeNo: 39,
              artId: 1063936,
              total: 2.0,
              unitType: 1,
              bseqid: "328359",
              billLineNo: 10,
              isDisassembled: 0
            }
          ]
        }
      ]
    },
    {
      artId: 1063937,
      keyStr: "1063937_2",
      artName: "阿莫西林胶囊",
      artSpec: "0.25g*24粒",
      producer: "华北制药股份有限公司",
      packCells: 24,
      packUnit: "盒",
      cellUnit: "粒",
      unitType: 2, // 整包
      isDisassembled: 0,
      totalAmount: 2,
      unitName: "盒",
      visitSummaryList: [
        {
          visitId: "86235",
          patientName: "王五",
          bedNo: "12",
          feeDetailList: [
            {
              keyStr: "1929715815310036993-5",
              execSeqid: "1929715815310036993",
              lineNo: 5,
              visitId: 86235,
              oeNo: 81,
              artId: 1063937,
              total: 1.0,
              unitType: 2,
              bseqid: "328361",
              billLineNo: 11,
              isDisassembled: 0
            }
          ]
        },
        {
          visitId: "85149",
          patientName: "赵六",
          bedNo: "15",
          feeDetailList: [
            {
              keyStr: "1929715812558573569-5",
              execSeqid: "1929715812558573569",
              lineNo: 5,
              visitId: 85149,
              oeNo: 40,
              artId: 1063937,
              total: 1.0,
              unitType: 2,
              bseqid: "328362",
              billLineNo: 12,
              isDisassembled: 0
            }
          ]
        }
      ]
    },
    {
      artId: 1063938,
      keyStr: "1063938_1",
      artName: "葡萄糖注射液",
      artSpec: "5%*250ml",
      producer: "石家庄四药有限公司",
      packCells: 1,
      packUnit: "瓶",
      cellUnit: "瓶",
      unitType: 1, // 拆零
      isDisassembled: 1, // 标记为拆零上报
      totalAmount: 8,
      unitName: "瓶",
      visitSummaryList: [
        {
          visitId: "86236",
          patientName: "孙七",
          bedNo: "18",
          feeDetailList: [
            {
              keyStr: "1929715815310036994-6",
              execSeqid: "1929715815310036994",
              lineNo: 6,
              visitId: 86236,
              oeNo: 82,
              artId: 1063938,
              total: 4.0,
              unitType: 1,
              bseqid: "328363",
              billLineNo: 13,
              isDisassembled: 1
            }
          ]
        },
        {
          visitId: "85150",
          patientName: "周八",
          bedNo: "21",
          feeDetailList: [
            {
              keyStr: "1929715812558573570-6",
              execSeqid: "1929715812558573570",
              lineNo: 6,
              visitId: 85150,
              oeNo: 41,
              artId: 1063938,
              total: 4.0,
              unitType: 1,
              bseqid: "328364",
              billLineNo: 14,
              isDisassembled: 1
            }
          ]
        }
      ]
    }
  ],
  pharmacyTrackCodeSums: [
    {
      artId: 1063936,
      trackCode: "80026021263916924484",
      totalPacks: 0,
      totalCells: 5.0,
      usedTotalPacks: 0,
      usedTotalCells: 0,
      curTotalPacks: 0,
      curTotalCells: 5.0
    },
    {
      artId: 1063936,
      trackCode: "80026021263916924485",
      totalPacks: 0,
      totalCells: 3.0,
      usedTotalPacks: 0,
      usedTotalCells: 0,
      curTotalPacks: 0,
      curTotalCells: 3.0
    },
    {
      artId: 1063938,
      trackCode: "80026021263916924486",
      totalPacks: 0,
      totalCells: 8.0,
      usedTotalPacks: 0,
      usedTotalCells: 0,
      curTotalPacks: 0,
      curTotalCells: 8.0
    }
  ]
}

// 模拟API延迟
export const mockApiDelay = (ms: number = 1000) => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 模拟API调用
export const mockSectionFeeTrackCodeSummaryApi = async (params: {
  sectionId: number
  bsnDate: string
}): Promise<SectionFeeTrackCodeSummaryResponse> => {
  console.log('模拟API调用:', params)
  
  // 模拟网络延迟
  await mockApiDelay(800)
  
  // 根据不同的病区ID返回不同的数据
  if (params.sectionId === 38) {
    return mockSectionFeeTrackCodeSummaryData
  }
  
  // 其他病区返回空数据
  return {
    artSummaryList: [],
    pharmacyTrackCodeSums: []
  }
}

// 生成随机追溯码
export const generateRandomTrackCode = (): string => {
  const prefix = '8002602' // 固定前7位
  const suffix = Math.random().toString().slice(2, 15) // 随机后缀
  return prefix + suffix.padEnd(13, '0')
}

// 生成测试用的追溯码列表
export const generateTestTrackCodes = (count: number = 5): string[] => {
  return Array.from({ length: count }, () => generateRandomTrackCode())
}
