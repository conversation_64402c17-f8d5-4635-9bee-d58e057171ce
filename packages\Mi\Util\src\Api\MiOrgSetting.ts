/**
 * 机构医保参数配置表
 */
export interface MiOrgSetting {
  /**
   * 机构id
   */
  orgId: number

  /**
   * 通信端点类型(1-中心侧,2-客户侧)
   */
  endpointType: number

  /**
   * 通信端点协议(REST/MQ)
   */
  endpointProtocol: string

  /**
   * 通信端点地址
   */
  endpointUrl: string

  /**
   * MQ发送队列名称
   */
  mqSendQueue: string

  /**
   * MQ接收队列名称
   */
  mqRecvQueue: string
}

/**
 * 通信端点类型枚举
 */
export enum EndpointType {
  /**
   * 中心侧
   */
  CENTER = 1,

  /**
   * 客户侧
   */
  CLIENT = 2,
}

/**
 * 获取当前机构的医保参数配置
 */
export function currentMiOrgSetting(params: Record<string, any> = {}) {
  return http.post(`/mcisp/miorgsetting/current`, params, { appKey: 'mi', ignoreError: true })
}
