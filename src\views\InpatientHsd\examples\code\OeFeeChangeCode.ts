import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { AddArt as OeAddArt, ChangeTotal, RefundOe } from '@mh-inpatient-hsd/oe-fee-change'
import '@mh-inpatient-hsd/oe-fee-change/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/oe-fee-change": "^1.0.0",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 医嘱增加条目
export const oeAddArtUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleOeAddArt">打开医嘱增加条目弹窗</Button>
  <OeAddArt ref="oeAddArtRef" :section-id="sectionId" @page="handleClose" />
</template>

<script setup>
import { AddArt as OeAddArt } from '@mh-inpatient-hsd/oe-fee-change'
import '@mh-inpatient-hsd/oe-fee-change/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const oeAddArtRef = ref()

// 模拟数据
const sectionId = 40
const execSeqid = "OE2025051500001" // 医嘱执行流水号

// 打开医嘱增加条目弹窗
const handleVisibleOeAddArt = () => {
  oeAddArtRef.value.open(execSeqid, 1)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>`)

// 医嘱数量变更
export const changeTotalUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleChangeTotal">打开医嘱数量变更弹窗</Button>
  <ChangeTotal ref="changeTotalRef" :section-id="sectionId" @page="handleClose" />
</template>

<script setup>
import { ChangeTotal } from '@mh-inpatient-hsd/oe-fee-change'
import '@mh-inpatient-hsd/oe-fee-change/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const changeTotalRef = ref()

// 模拟数据
const sectionId = 40
const execSeqid = "OE2025051500001" // 医嘱执行流水号

// 模拟医嘱数量变更记录
const changeTotalRecord = {
  execSeqid: execSeqid,
  lineNo: 1,
  artId: 1044295,
  artName: "盐酸氨溴索口服溶液",
  artSpec: "100ml:300mg/瓶",
  producer: "江苏汉晨药业有限公司",
  unit: "瓶",
  price: 35.8,
  total: 1,
  amount: 35.8,
  stockReq: 1
}

// 打开医嘱数量变更弹窗
const handleVisibleChangeTotal = () => {
  changeTotalRef.value.open(changeTotalRecord)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>`)

// 医嘱退费
export const refundOeUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleRefundOe">打开医嘱退费弹窗</Button>
  <RefundOe ref="refundOeRef" :section-id="sectionId" @page="handleClose" />
</template>

<script setup>
import { RefundOe } from '@mh-inpatient-hsd/oe-fee-change'
import '@mh-inpatient-hsd/oe-fee-change/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const refundOeRef = ref()

// 模拟数据
const sectionId = 40
const execSeqid = "OE2025051500001" // 医嘱执行流水号
const bsnDate = "20250515" // 业务日期

// 打开医嘱退费弹窗
const handleVisibleRefundOe = () => {
  refundOeRef.value.open(execSeqid, 1, bsnDate)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>`)
