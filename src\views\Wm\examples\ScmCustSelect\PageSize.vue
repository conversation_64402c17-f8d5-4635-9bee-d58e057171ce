<script setup lang="ts">
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { Card, Typography, Divider, InputNumber, Switch } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { pageSizeUsage, importCode, packageJsonCode } from '../code/ScmCustSelectCode'

const { Title } = Typography

// 单选值
const custId = ref<number>()

// 每页显示条数
const pageSize = ref<number>(10)

// 是否默认加载第一页
const loadFirstPage = ref<boolean>(true)
</script>

<template>
  <Card title="分页设置" class="mb-16px">
    <div mb-16px>
      <Title :level="4">分页参数设置</Title>
      <div mb-16px>
        <div mb-8px>
          <span mr-8px>每页显示条数：</span>
          <InputNumber v-model:value="pageSize" :min="1" :max="50" />
        </div>
        <div mb-8px>
          <span mr-8px>默认加载第一页：</span>
          <Switch v-model:checked="loadFirstPage" />
          <span ml-8px>{{ loadFirstPage ? '是' : '否' }}</span>
        </div>
      </div>
      
      <ScmCustSelect 
        v-model="custId" 
        :pageSize="pageSize"
        :loadFirstPage="loadFirstPage"
        style="width: 100%" 
      />
      <div mt-8px>选中的供应商ID: {{ custId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        可以设置每页显示的条数，以及是否在组件初始化时自动加载第一页数据
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="pageSizeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
