<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个组件示例
import CardReaderExample from './examples/CardReaderExample.vue'
import DipExample from './examples/DipExample.vue'

const { Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('cardreader')
</script>

<template>
  <Card title="医保相关组件" class="mb-16px">
    <Paragraph>医保相关组件，包括CardReader（医保读卡器）和Dip（事前事中分析）组件。</Paragraph>

    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane key="cardreader" tab="医保读卡器">
        <CardReaderExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="dip" tab="事前事中分析">
        <DipExample />
      </Tabs.TabPane>
    </Tabs>
  </Card>
</template>

<style scoped>
/* 全局样式可以放在这里 */
:deep(.ant-tabs-left > .ant-tabs-content-holder) {
  border-left: 1px solid #f0f0f0;
  padding-left: 16px;
}

:deep(.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab) {
  padding: 8px 16px;
}

:deep(.ant-card-body) {
  padding: 24px;
}
</style>
