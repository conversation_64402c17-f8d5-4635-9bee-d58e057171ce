<script setup lang="ts">
import { reactive, ref, watch, nextTick } from 'vue'
import { BaseAutoComplete } from "@mh-base/core"
import { deptArtPageApi } from "@mh-wm/util"

const props = defineProps({
  // 查询范围： 无：所有，1：catType.in(201,301)，2：catType.eq(301)+stockReq=1, 3:查机构项目物价, 4:artType.in(14,26,29)（材料、护理、输氧）, 5:stockReq=1+catType.ne 301过滤耗材, 6:stockReq=1 药品+耗材
  searchType: {
    type: Number,
    default: null
  },
  deptCode: {
    type: String,
    default: null
  },
  showCost: {
    type: Boolean,
    default: true
  }
})
const emit = defineEmits(['selected'])

const artDataModel = reactive({
  columns: [
    { title: '条目ID', dataIndex: 'artId', width: 100, align: 'right' },
    { title: '品名', dataIndex: 'artName', width: 150 },
    { title: '规格', dataIndex: 'artSpec', width: 100 },
    { title: '生产厂家', dataIndex: 'producer', width: 150 },
    { title: '医保编码', dataIndex: 'miCode', width: 150 },
    { title: '库存包装数', dataIndex: 'totalPacks', width: 80, align: 'right' },
    { title: '包装单位', dataIndex: 'packUnit', width: 75, align: 'center' },
    { title: '库存拆零数', dataIndex: 'totalCells', width: 80, align: 'right' },
    { title: '拆零单位', dataIndex: 'cellUnit', width: 75, align: 'center' },
    { title: '允许拆零', dataIndex: 'splittable', width: 80, align: 'center' },
    { title: '包装成本均价', dataIndex: 'avgCostPrice', width: 100, align: 'right' },
    { title: '包装销售均价', dataIndex: 'avgSalePrice', width: 100, align: 'right' }
  ],
  searchFormModel: {
    order: 'asc',
    sidx: 't_article.Art_Type_ID, t_art_subtype.Display_Order, t_dept_art.Total_Packs desc, t_dept_art.Art_ID',
    artName: '',
    deptCode: null as string | null,
    showCost: true,
    disabled: 0,
    S_IN_t_article__Cat_Type_ID: null as string | null,
    S_EQ_t_article__Cat_Type_ID: null as number | null,
    S_EQ_t_article__Stock_Req: null as number | null,
    S_IN_t_article__Art_Type_ID: null as string | null,
    S_NE_t_article__Cat_Type_ID: null as number | null
  } as any,
  options: [],
  pagination: {
    pageSize: 10, // 每页条数
    pageNum: 1, // 当前页码
    pages: 0, // 总页数
    total: 0, // 总条数
    showTotal: (total: number) => `共：${total} 条`,
    onChange: async (current: number, pageSize: number) => {
      artDataModel.pagination.pageSize = pageSize
      artDataModel.pagination.pageNum = current
      await artDataModel.loadOptions()
    }
  },
  loadOptions: async () => {
    artDataModel.searchFormModel.deptCode = props.deptCode
    artDataModel.searchFormModel.showCost = props.showCost
    artDataModel.searchFormModel.disabled = 0
    if (props.searchType) {
      if (props.searchType === 1) {
        artDataModel.searchFormModel.S_IN_t_article__Cat_Type_ID = '201,301'
      } else if (props.searchType === 2) {
        artDataModel.searchFormModel.S_EQ_t_article__Cat_Type_ID = 301
        artDataModel.searchFormModel.S_EQ_t_article__Stock_Req = 1
      } else if (props.searchType === 4) {
        artDataModel.searchFormModel.S_IN_t_article__Art_Type_ID = '14,26,29'
      } else if (props.searchType === 5) {
        artDataModel.searchFormModel.S_NE_t_article__Cat_Type_ID = 301
        artDataModel.searchFormModel.S_EQ_t_article__Stock_Req = 1
      } else if (props.searchType === 5) {
        artDataModel.searchFormModel.S_NE_t_article__Cat_Type_ID = 301
        artDataModel.searchFormModel.S_EQ_t_article__Stock_Req = 1
      }
    }
    const { list, pageNum, pages, total } = await deptArtPageApi({
      ...artDataModel.searchFormModel,
      ...artDataModel.pagination
    })
    artDataModel.options = list
    artDataModel.pagination.pageNum = Number(pageNum)
    artDataModel.pagination.pages = Number(pages)
    artDataModel.pagination.total = Number(total)
  },
  onSearch: async (value: string) => {
    artDataModel.pagination.pageNum = 1
    artDataModel.searchFormModel.artName = value
    await artDataModel.loadOptions()
  },
  onSelect: (item: any) => {
    if (item) {
      artDataModel.searchFormModel.artName = item.artName +  (item.artSpec ? ' ' + item.artSpec : '') + (item.producer? ' ' + item.producer: '')
      emit('selected', item)
    }
  },
})

const init = () => {
  artDataModel.searchFormModel.artName = undefined
  artDataModel.options = []
}

watch(() => props.deptCode, () => {
  init()
}, {
  immediate: true,
  deep: true,
})
const keywordRef = ref()

const focus = () => {
  keywordRef?.value.$refs().focus()
}

// 设置显示值（用于回填数据）
const setValue = (artData: any) => {
  if (artData) {
    // 构造显示文本：品种名称 + 规格 + 生产厂家
    const displayText = artData.artName +
      (artData.artSpec ? ' ' + artData.artSpec : '') +
      (artData.producer ? ' ' + artData.producer : '')

    console.log('WmDeptArtSelect setValue 设置显示文本:', displayText)
    console.log('WmDeptArtSelect setValue 接收到的artData:', artData)

    // 设置显示文本
    artDataModel.searchFormModel.artName = displayText

    // 确保界面更新
    nextTick(() => {
      console.log('WmDeptArtSelect setValue 设置后的artName:', artDataModel.searchFormModel.artName)
    })
  }
}

defineExpose({
  init,
  focus,
  setValue
})
</script>

<template>
  <div>
    <base-auto-complete
      style="width: 100%"
      ref="keywordRef"
      v-model:value="artDataModel.searchFormModel.artName"
      keyColumn="artId"
      :columns="artDataModel.columns"
      :options="artDataModel.options"
      :pagination="artDataModel.pagination"
      @onSearch="artDataModel.onSearch"
      @onSelect="artDataModel.onSelect"
    />
  </div>
</template>
