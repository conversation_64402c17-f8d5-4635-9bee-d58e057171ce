import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <div>
    <!-- 采购制单录入组件 -->
    <div class="maker-form-section">
      <h4>采购制单录入</h4>
      <p v-if="formEditingId" style="color: #fa8c16;">
        当前正在编辑记录，修改完成后点击"添加"按钮保存更改。
        <a-button type="link" size="small" @click="cancelEdit">取消编辑</a-button>
      </p>
      <Maker
        ref="makerComponentRef"
        @addArt="handleAddArt"
      />
    </div>

    <!-- 已添加品种列表 -->
    <div class="added-items-section">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h4>已添加品种列表 ({{ addedItems.length }})</h4>
        <a-space>
          <a-button @click="setTestData" type="primary" ghost>加载测试数据</a-button>
          <a-button v-if="addedItems.length > 0" @click="exportData">导出数据</a-button>
          <a-button v-if="addedItems.length > 0" @click="clearAll" danger>清空所有</a-button>
        </a-space>
      </div>

      <!-- 批量操作工具栏 -->
      <div v-if="addedItems.length > 0" style="margin-bottom: 16px; padding: 12px; background: #fafafa; border-radius: 6px;">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-button v-if="hasSelected" @click="deleteSelected" danger size="small">批量删除</a-button>
          <a-button v-if="selectedRowKeys.length > 0" @click="selectedRowKeys = []" size="small">取消选择</a-button>
        </a-space>
      </div>

      <a-table
        v-if="addedItems.length > 0"
        :dataSource="addedItems"
        :columns="columns"
        rowKey="id"
        :scroll="{ x: 1500 }"
        :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onChange: onSelectChange,
          type: 'checkbox'
        }"
      />

      <div v-else style="text-align: center; padding: 40px; color: #999;">
        <div style="margin-bottom: 16px;">暂无数据，请使用上方表单添加品种</div>
        <a-button @click="setTestData" type="primary" ghost>或点击加载测试数据</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Maker } from '@mh-wm/maker'
import { ref, computed, h } from 'vue'
import { message, Button, Space } from 'ant-design-vue'

// 已添加的品种列表
const addedItems = ref([])

// 编辑状态管理
const formEditingId = ref('')
const makerComponentRef = ref()

// 批量选择
const selectedRowKeys = ref([])
const hasSelected = computed(() => selectedRowKeys.length > 0)

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys
}

// 添加品种回调
const handleAddArt = (formData) => {
  console.log('添加品种成功，表单数据：', formData)

  // 如果是编辑状态，更新现有记录
  if (formEditingId.value) {
    const index = addedItems.value.findIndex(item => item.id === formEditingId.value)
    if (index !== -1) {
      addedItems.value[index] = {
        ...addedItems.value[index],
        ...formData,
        id: formEditingId.value // 保持原有ID
      }
      message.success('品种信息更新成功')
      formEditingId.value = '' // 清空编辑状态
      return
    }
  }

  // 新增记录
  const newItem = {
    id: Date.now().toString(),
    ...formData,
    // 从artData中提取品种信息
    artName: formData.artData?.artName || '',
    artSpec: formData.artData?.artSpec || '',
    producer: formData.artData?.producer || '',
  }

  addedItems.value.push(newItem)
  message.success('品种添加成功')
}

// 编辑记录
const edit = (record) => {
  formEditingId.value = record.id
  if (makerComponentRef.value) {
    makerComponentRef.value.setFormData(record)
  }
  message.info('请在上方表单中修改品种信息，然后点击添加按钮保存')
}

// 取消编辑
const cancelEdit = () => {
  formEditingId.value = ''
  if (makerComponentRef.value) {
    makerComponentRef.value.clearFormData()
  }
  message.info('已取消编辑')
}

// 删除记录
const deleteRecord = (record) => {
  const index = addedItems.value.findIndex(item => item.id === record.id)
  if (index !== -1) {
    addedItems.value.splice(index, 1)
    message.success('删除成功')
  }
}

// 复制记录
const copyRecord = (record) => {
  const copiedRecord = {
    ...record,
    id: Date.now().toString(),
    batchNo: record.batchNo + '_copy',
  }
  addedItems.value.push(copiedRecord)
  message.success('记录复制成功')
}

// 批量删除
const deleteSelected = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的记录')
    return
  }

  addedItems.value = addedItems.value.filter(item =>
    !selectedRowKeys.value.includes(item.id)
  )
  selectedRowKeys.value = []
  message.success('批量删除成功')
}

// 清空所有数据
const clearAll = () => {
  addedItems.value = []
  if (makerComponentRef.value) {
    makerComponentRef.value.clearFormData()
  }
  formEditingId.value = ''
  message.success('已清空所有数据')
}

// 设置测试数据
const setTestData = () => {
  const testData = [
    {
      id: 'test1',
      artData: {
        artId: 1001,
        artName: '阿莫西林胶囊',
        artSpec: '0.25g*24粒',
        producer: '哈药集团制药总厂'
      },
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '哈药集团制药总厂',
      originPlace: '中国',
      batchNo: '*********',
      dateManufactured: '20240101',
      expiry: '20261231',
      packPrice: 15.50,
      totalPacks: 10,
      totalCells: 5
    }
    // ... 更多测试数据
  ]
  addedItems.value = testData
  message.success('测试数据已加载')
}

// 导出数据
const exportData = () => {
  if (addedItems.value.length === 0) {
    message.warning('没有数据可导出')
    return
  }

  const dataStr = JSON.stringify(addedItems.value, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = \`maker-data-\${new Date().getTime()}.json\`
  link.click()
  URL.revokeObjectURL(url)
  message.success('数据导出成功')
}

// 表格列定义
const columns = [
  {
    title: '品名',
    dataIndex: 'artName',
    key: 'artName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 120,
  },
  {
    title: '生产厂家',
    dataIndex: 'producer',
    key: 'producer',
    width: 120,
  },
  {
    title: '原产地',
    dataIndex: 'originPlace',
    key: 'originPlace',
    width: 100,
  },
  {
    title: '生产批号',
    dataIndex: 'batchNo',
    key: 'batchNo',
    width: 120,
  },
  {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    key: 'dateManufactured',
    width: 100,
  },
  {
    title: '有效期至',
    dataIndex: 'expiry',
    key: 'expiry',
    width: 100,
  },
  {
    title: '整包单价',
    dataIndex: 'packPrice',
    key: 'packPrice',
    width: 100,
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ record }) => {
      return \`\${record.totalPacks || 0} \${record.packUnit || ''}\`
    }
  },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    key: 'totalCells',
    width: 100,
    customRender: ({ record }) => {
      if (record.packCells && record.packCells > 1) {
        return \`\${record.totalCells || 0} \${record.cellUnit || ''}\`
      }
      return '-'
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    customRender: ({ record }) => {
      return h(Space, {}, [
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => edit(record)
        }, '编辑'),
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => copyRecord(record)
        }, '复制'),
        h(Button, {
          type: 'link',
          size: 'small',
          danger: true,
          onClick: () => deleteRecord(record)
        }, '删除')
      ])
    }
  }
]
</script>

<!--
组件内置了以下验证规则：
1. 生产日期和有效期至必须填写，且格式为YYYYMMDD
2. 整包数量和拆零数量至少填写一个
3. 必须先选择品种才能提交表单
4. 如果品种不可拆零（splittable !== 1），则不会显示拆零数量字段
5. 当品种详细信息为空时，会自动弹出机构商品设置窗口
-->`)

// 组件方法使用
export const methodsUsage = wrapCodeExample(`<template>
  <div>
    <Maker
      ref="makerRef"
      @addArt="handleAddArt"
    />

    <div class="action-buttons">
      <a-button @click="clearForm">清空表单</a-button>
      <a-button @click="getSelectedData">获取选中数据</a-button>
      <a-button @click="setFormData">设置表单数据</a-button>
    </div>
  </div>
</template>

<script setup>
import { Maker } from '@mh-wm/maker'
import { ref } from 'vue'
import { message } from 'ant-design-vue'

const makerRef = ref()

// 清空表单
const clearForm = () => {
  makerRef.value.clearFormData()
  message.success('表单已清空')
}

// 获取选中的品种数据
const getSelectedData = () => {
  const selectedData = makerRef.value.getSelectedArtData()
  console.log('当前选中的品种数据:', selectedData)
  if (selectedData) {
    message.success('已获取选中数据，请查看控制台')
  } else {
    message.warning('当前没有选中的品种')
  }
}

// 设置表单数据（用于编辑）
const setFormData = () => {
  const mockData = {
    artData: {
      artId: 1001,
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '哈药集团制药总厂',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 24,
      splittable: 1
    },
    originPlace: '中国',
    batchNo: '*********',
    dateManufactured: '20240101',
    expiry: '20261231',
    packPrice: 15.50,
    totalPacks: 10,
    totalCells: 5
  }

  makerRef.value.setFormData(mockData)
  message.success('表单数据已设置')
}

const handleAddArt = (formData) => {
  console.log('添加品种:', formData)
}
</script>`)

// 表单中使用
export const formUsage = wrapCodeExample(`<template>
  <a-form :model="formState" layout="vertical">
    <a-row :gutter="16">
      <a-col :span="12">
        <a-form-item label="申请单号" name="reqNo">
          <a-input v-model:value="formState.reqNo" />
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="申请日期" name="reqDate">
          <a-date-picker v-model:value="formState.reqDate" style="width: 100%" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 采购制单录入组件 -->
    <a-form-item label="品种录入">
      <Maker
        ref="makerRef"
        @addArt="handleAddArt"
      />
    </a-form-item>

    <!-- 已添加品种列表 -->
    <a-form-item label="申请品种列表">
      <a-table
        :dataSource="formState.items"
        :columns="columns"
        rowKey="id"
        :pagination="false"
      />
    </a-form-item>

    <a-form-item>
      <a-button type="primary" @click="handleSubmit">提交申请</a-button>
      <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { Maker } from '@mh-wm/maker'
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'

// 组件引用
const makerRef = ref()

// 表单数据
const formState = reactive({
  reqNo: 'REQ' + Date.now(),
  reqDate: dayjs(),
  items: [],
  remark: ''
})

// 添加品种
const handleAddArt = (formData) => {
  const newItem = {
    id: Date.now().toString(),
    ...formData,
    artName: formData.artData?.artName || '',
    artSpec: formData.artData?.artSpec || '',
    producer: formData.artData?.producer || ''
  }

  formState.items.push(newItem)
  message.success('品种添加成功')
}

// 提交表单
const handleSubmit = () => {
  if (formState.items.length === 0) {
    message.warning('请至少添加一个品种')
    return
  }

  console.log('提交申请:', formState)
  message.success('申请提交成功')
}

// 重置表单
const handleReset = () => {
  formState.items = []
  makerRef.value?.clearFormData()
  message.success('表单已重置')
}
</script>`)

// 引入组件
export const importCode = `import { Maker } from '@mh-wm/maker'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/maker": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}`

// 打包发布指令
export const publishCommands = `# 在项目根目录下执行以下命令打包并发布组件

# 正式版本
pnpm publish:component Wm/Maker

# 测试版本
pnpm publish:test-component Wm/Maker

# 开发版本
pnpm publish:dev-component Wm/Maker

# 安装组件
pnpm add @mh-wm/maker`

// 打包流程说明
export const buildProcess = `打包命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库`

// 编辑功能示例
export const editFunctionUsage = wrapCodeExample(`<template>
  <div>
    <!-- 采购制单录入组件 -->
    <Maker
      ref="makerRef"
      @addArt="handleAddArt"
    />

    <!-- 已添加品种列表 -->
    <div class="added-items-section">
      <h4>已添加品种列表</h4>
      <a-table
        :dataSource="addedItems"
        :columns="columns"
        rowKey="id"
        :scroll="{ x: 1400 }"
      />
    </div>
  </div>
</template>`)
