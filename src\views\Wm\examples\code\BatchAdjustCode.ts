import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 批号调整组件 -->
  <BatchAdjust ref="batchAdjustRef" @close="handleClose" />
</template>

<script setup>
import { BatchAdjust } from '@mh-wm/batch-adjust'
import { ref } from 'vue'

const batchAdjustRef = ref()

// 打开批号调整弹窗
const openBatchAdjust = (deptCode, art) => {
  batchAdjustRef.value.init(deptCode, art)
}

// 关闭回调
const handleClose = () => {
  console.log('批号调整完成')
}
</script>`)

// 引入组件
export const importCode = `import { BatchAdjust } from '@mh-wm/batch-adjust'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/batch-adjust": "^1.0.4",
    "@mh-wm/util": "^1.0.4"
  }
}`
