<script setup lang="ts">
import { ref, reactive } from 'vue'
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Divider,
  Typography,
  message,
  Space,
  Tag
} from 'ant-design-vue'

const { Title, Text } = Typography

// 引用BillsTrackCode组件
const billsTrackCodeRef = ref()

// 表单数据
const formState = reactive({
  title: '多单据追溯码采集示例',
  wbSeqIdLs: [242629, 242628, 242627, 242626, 242625, 242624, 242623, 242882, 242883, 242884, 242881],
  onlyAddRecognizedTrackCode: false,
  enableOnlyAddRecognizedTrackCodeOption: false
})

// 表单引用
const formRef = ref()

// 打开多单据扫码组件
const openBillsTrackCode = () => {
  formRef.value.validate().then(() => {
    billsTrackCodeRef.value.open(formState.wbSeqIdLs)
  }).catch(error => {
    console.log('表单验证失败:', error)
    message.error('请填写必要的参数')
  })
}

// 处理成功事件
const handleSuccess = (data) => {
  console.log('多单据扫码录入成功:', data)
  message.success(`多单据扫码录入成功，处理了 ${data.length} 个单据`)
}

// 处理取消事件
const handleCancel = () => {
  console.log('取消多单据扫码录入')
}

// 表单验证规则
const rules = {
  wbSeqIdLs: [{ required: true, message: '请输入单据ID列表' }]
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
}

// 使用默认值
const useDefaultValues = () => {
  formState.title = '多单据追溯码采集示例'
  formState.wbSeqIdLs = [242629, 242628, 242627, 242626, 242625, 242624, 242623, 242882, 242883, 242884, 242881]
  formState.onlyAddRecognizedTrackCode = false
  formState.enableOnlyAddRecognizedTrackCodeOption = true
}

// 添加单据ID
const addBillId = () => {
  const newId = Math.floor(Math.random() * 900000) + 100000
  formState.wbSeqIdLs.push(newId)
}

// 删除单据ID
const removeBillId = (index: number) => {
  formState.wbSeqIdLs.splice(index, 1)
}

// 更新单据ID
const updateBillId = (index: number, value: number) => {
  formState.wbSeqIdLs[index] = value
}
</script>

<template>
  <div class="bills-track-code-example">
    <Title :level="2">多单据追溯码扫描组件示例</Title>

    <Card title="组件说明" style="margin-bottom: 24px">
      <Text>
        多单据追溯码扫描组件用于处理多个单据的批量追溯码采集。与单一处方不同，该组件会自动将多个单据中相同 artId 的药品进行汇总，按拆零/整包类型分别处理。
      </Text>
      <br><br>
      <Text strong>主要特性：</Text>
      <ul>
        <li>支持多个单据的批量追溯码采集</li>
        <li>智能药品汇总：按条目类别和拆零/整包类型自动汇总</li>
        <li>原始明细映射：API调用时自动拆分回原始的 wbSeqid 和 lineNo</li>
        <li>支持所有 RecipeTrackCode 的核心功能</li>
      </ul>
    </Card>

    <Title :level="3">参数设置</Title>
    <Form
      :model="formState"
      :rules="rules"
      ref="formRef"
      layout="vertical"
      class="parameter-form"
    >
      <Form.Item label="标题" name="title">
        <Input v-model:value="formState.title" placeholder="请输入标题" />
      </Form.Item>

      <Form.Item label="单据ID列表" name="wbSeqIdLs">
        <div class="bill-ids-container">
          <div v-for="(billId, index) in formState.wbSeqIdLs" :key="index" class="bill-id-item">
            <InputNumber
              :value="billId"
              @change="(value) => updateBillId(index, value)"
              placeholder="请输入单据ID"
              style="width: 200px; margin-right: 8px"
            />
            <Button
              type="text"
              danger
              @click="removeBillId(index)"
              :disabled="formState.wbSeqIdLs.length <= 1"
            >
              删除
            </Button>
          </div>
          <Button type="dashed" @click="addBillId" style="width: 200px">
            + 添加单据ID
          </Button>
        </div>
        <div class="form-item-description">
          当前共 {{ formState.wbSeqIdLs.length }} 个单据：
          <Tag v-for="id in formState.wbSeqIdLs" :key="id" style="margin: 2px">{{ id }}</Tag>
        </div>
      </Form.Item>

      <Form.Item label="是否只添加识别追溯码" name="onlyAddRecognizedTrackCode">
        <Switch v-model:checked="formState.onlyAddRecognizedTrackCode" />
        <div class="form-item-description">设置为true时，只有能够匹配到药品的追溯码才会被添加</div>
      </Form.Item>

      <Form.Item label="是否启用'只添加识别追溯码'选项" name="enableOnlyAddRecognizedTrackCodeOption">
        <Switch v-model:checked="formState.enableOnlyAddRecognizedTrackCodeOption" />
        <div class="form-item-description">设置为true时，在追溯码输入框旁边显示"只添加识别追溯码"复选框</div>
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" @click="openBillsTrackCode">打开多单据扫码组件</Button>
          <Button @click="resetForm">重置</Button>
          <Button type="dashed" @click="useDefaultValues">使用默认值</Button>
        </Space>
      </Form.Item>
    </Form>

    <Divider />

    <Title :level="3">JSON格式参数</Title>
    <pre class="json-display">{
  "title": "{{ formState.title }}",
  "wbSeqIdLs": {{ JSON.stringify(formState.wbSeqIdLs) }},
  "onlyAddRecognizedTrackCode": {{ formState.onlyAddRecognizedTrackCode }},
  "enableOnlyAddRecognizedTrackCodeOption": {{ formState.enableOnlyAddRecognizedTrackCodeOption }}
}</pre>

    <Divider />

    <Title :level="3">与 RecipeTrackCode 的主要区别</Title>
    <div class="comparison-table">
      <table>
        <thead>
          <tr>
            <th>特性</th>
            <th>RecipeTrackCode</th>
            <th>BillsTrackCode</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>处理范围</td>
            <td>单一处方</td>
            <td>多个单据</td>
          </tr>
          <tr>
            <td>入参</td>
            <td>(title, wbSeqid, visitId, isView)</td>
            <td>(wbSeqIdLs)</td>
          </tr>
          <tr>
            <td>数据汇总</td>
            <td>无需汇总</td>
            <td>按 artId + 拆零/整包类型汇总</td>
          </tr>
          <tr>
            <td>API调用</td>
            <td>单个 wbSeqid</td>
            <td>数组 wbSeqids</td>
          </tr>
          <tr>
            <td>左侧列表</td>
            <td>直接显示明细</td>
            <td>显示汇总后的数据</td>
          </tr>
          <tr>
            <td>追溯码关联</td>
            <td>lineNo</td>
            <td>wbSeqid + lineNo</td>
          </tr>
        </tbody>
      </table>
    </div>

    <Divider />

    <Title :level="3">打包与发布</Title>
    <div class="code-example">
      <pre>
// 在项目根目录下执行以下命令打包并发布组件
pnpm publish:component Wm/BillsTrackCode

// 该命令会执行以下操作：
// 1. 编译组件源码
// 2. 生成类型声明文件
// 3. 打包CSS样式
// 4. 生成组件包到dist目录
// 5. 更新package.json中的版本号
// 6. 将组件发布到内部npm仓库
      </pre>
    </div>

    <Divider />

    <Title :level="3">使用方法</Title>
    <div class="code-example">
      <pre>
// 安装组件
npm install @mh-wm/bills-track-code
// 或
pnpm add @mh-wm/bills-track-code

// 引入组件和样式
import { BillsTrackCode } from '@mh-wm/bills-track-code'
import '@mh-wm/bills-track-code/index.css'

// 在模板中使用
&lt;BillsTrackCode
  ref="billsTrackCodeRef"
  :onlyAddRecognizedTrackCode="false"
  :enableOnlyAddRecognizedTrackCodeOption="true"
  @success="handleSuccess"
  @cancel="handleCancel"
/&gt;

// 在方法中调用
const openBillsTrackCode = () => {
  billsTrackCodeRef.value.open([123456, 123457, 123458])
}
      </pre>
    </div>

    <!-- 多单据扫码组件 -->
    <BillsTrackCode
      ref="billsTrackCodeRef"
      :onlyAddRecognizedTrackCode="formState.onlyAddRecognizedTrackCode"
      :enableOnlyAddRecognizedTrackCodeOption="formState.enableOnlyAddRecognizedTrackCodeOption"
      @success="handleSuccess"
      @cancel="handleCancel"
    />
  </div>
</template>

<style scoped>
.bills-track-code-example {
  padding: 20px;
}

.parameter-form {
  max-width: 800px;
}

.bill-ids-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.bill-id-item {
  display: flex;
  align-items: center;
}

.json-display {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: monospace;
  overflow-x: auto;
}

.code-example {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow: auto;
}

.code-example pre {
  margin: 0;
  font-family: monospace;
}

.form-item-description {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}

.comparison-table {
  overflow-x: auto;
}

.comparison-table table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.comparison-table th,
.comparison-table td {
  border: 1px solid #d9d9d9;
  padding: 8px 12px;
  text-align: left;
}

.comparison-table th {
  background-color: #fafafa;
  font-weight: 600;
}

.comparison-table tr:nth-child(even) {
  background-color: #fafafa;
}
</style>
