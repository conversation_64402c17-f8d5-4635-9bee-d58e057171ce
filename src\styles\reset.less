@import "./motion.css";
html {
  --text-color: rgba(0,0,0,.85);
  --text-color-1: rgba(0,0,0,.45);
  --text-color-2: rgba(0,0,0,.2);
  --bg-color: #fff;
  --hover-color:rgba(0,0,0,.025);
  --bg-color-container: #f5f5f5;
  --c-shadow: 2px 0 8px 0 rgba(29,35,41,.05);
}

html.dark{
  --text-color: rgba(229, 224, 216, 0.85);
  --text-color-1: rgba(229, 224, 216, 0.45);
  --text-color-2: rgba(229, 224, 216, 0.45);
  --bg-color: rgb(36, 37, 37);
  --hover-color:rgb(42, 44, 55);
  --bg-color-container: rgb(42, 44, 44);
  --c-shadow: rgba(13, 13, 13, 0.65) 0 2px 8px 0;
}

body{
  color: var(--text-color);
  background-color: var(--bg-color);
  text-rendering: optimizeLegibility;
  overflow: hidden;
}

#app, body, html{
  height: 100%;
}

#app{
  overflow-x: hidden;
}
*, :after, :before{
  box-sizing: border-box;
}

.ant-form-item {
  margin-bottom: 10px;
}

.content {
  background-color: #eff1f9;
  min-height: 300px;
}
.art-select-container {
  padding: 0;
  max-width: calc(100% - 300px);
  overflow: auto;
  .custom-art-header {
    position: fixed !important;
    z-index: 200 !important;
    background: #F0F0F0 !important;
    color: #000000 !important;
    font-weight: 700 !important;
    max-width: calc(100% - 300px);
  }
  .rc-virtual-list-holder {
    max-height: 400px !important;
  }
  .ant-select-dropdown .ant-select-item-option .ant-select-item-option-content {
    align-items: center;
    white-space: pre-wrap;
    display: flex;
  }
  .ant-select-item-option:nth-child(2n) {
    background: rgba(0, 0, 0, 0.04);
  }
  .ant-select-item-option-active {
    background: var(--pro-ant-color-primary) !important;
  }
  .ant-select-item-option-active .ant-select-item-option-content, .ant-select-item-option-active .ant-select-item-option-content > div{
    color: #fff !important;
  }

  .custom-art-check {
    width: 40px;
    float: left;
    text-align: center;
  }
  .custom-art-name {
    width: 200px;
    float: left;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-spec {
    width: 140px;
    float: left;
    text-align: center;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-price {
    width: 80px;
    float: left;
    padding-right: 10px;
    text-align: center;
  }
  .custom-art-producer {
    width: 250px;
    float: left;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-other {
    width: 80px;
    float: left;
    padding-right: 10px;
    text-align: center;
  }
  .custom-art-footer {
    position: absolute !important;
    bottom: 0;
    width: 100%;
    background: #FFFFFF !important;
    border-radius: 0;
  }
}
.cancel-row {

  color: #A6A6A6 !important;

  .surely-table-cell-content {
    text-decoration-color: #807d7d;
    text-decoration-line: line-through;
    text-decoration-style: solid;
    text-decoration-thickness: auto;
  }
}
.summary-row {
  color: #000000 !important;
  font-weight: bold;
}

.pointer {
  cursor: pointer;
}

// 字体
@import url('./font/iconfont.css');
// 表格
@import url('@surely-vue/table/dist/index.less');

html, body, span, div, h1, p, a, li {
  font-family: '微软雅黑', '宋体', -apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji' !important;
}

// surely-table 删除表格 水印
.surely-table {
  --surely-table-border-color: #ddd !important;
  --surely-table-row-hover-bg: #FFFFB2 !important;

  &-header {
    border-radius: 0 !important;
  }
  // Unlicensed Product   带有滚动条的table
  .surely-table-horizontal-scroll ~ div:last-child {
    visibility: hidden !important;
    &.surely-table-summary {
      visibility: visible !important;
    }
  }
  .surely-table-unselectable {
    // Powered by Surely Vue
    .surely-table-body-viewport-container + div {
      // 使用CSS的clip属性来隐藏元素。这样可以根据元素的尺寸和位置来裁剪显示区域，将其设为与元素相同大小的矩形，即可隐藏该元素。
      clip: rect(0, 0, 0, 0) !important;
    }
    // Unlicensed Product   不带带有滚动条的table
    + div {
      // visibility: hidden !important;
      text-indent: -5000px !important;
    }
  }
  // 合计 删除水印
  .surely-table-summary.surely-table-summary-fixed-bottom + div {
    clip: rect(0, 0, 0, 0) !important;
    text-indent: -5000px !important;
  }

  // &.surely-table-bordered {
  //   @border-color: #e0e0e0;
  //   border-color: @border-color;
  //   .surely-table-cell:not(.surely-table-body-cell-range-single-cell):not(.surely-table-body-cell-range-selected){
  //     border-right-color: @border-color;
  //     // border-right: 0;
  //   }
  //   .surely-table-row, .surely-table-header-cell {
  //     border-bottom-color: @border-color;
  //     // border-bottom: 0;
  //   }
  // }
}
// 整体大框架样式
.ant-pro-basicLayout-content {

    margin: 0 !important;
    .user-tabs {
      margin-top: 8px !important;
    }
    .base-base-content {
     
    }
    .base-base-tree {
      
    }
    .ant-divider-horizontal {
      margin:  10px 0 !important;
    }
    .ant-input {
    }
}
// 全局 modal
.base-modal {
  .ant-modal-header {
    padding-bottom: 12px;
    position: relative;
    margin-bottom: 12px;
    &::after {
      display: block;
      content: "";
      width: calc(100% + 48px);
      height: 1px;
      position: absolute;
      left: -24px;
      bottom: 0;
      background-color: #E0E0E0;
    }
  }
  .ant-modal-close {
    top: 20px;
  }
}
.full-base-modal {
  .ant-modal {
    max-width: 100%;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
  }
  .ant-modal-body {
    flex: 1;
  }
}
// 全局表单
.ant-btn-link {
  color: var(--pro-ant-color-primary) !important;
}
.ant-btn-link.ant-btn-dangerous, 
.ant-btn-link.ant-btn-dangerous:active, 
.ant-btn-link.ant-btn-dangerous:focus, 
.ant-btn-link.ant-btn-dangerous:hover {
  color: #ff0004 !important;
}
.ant-form-item{
  margin-bottom: 10px !important;
}
// 按钮 输入框 的样式调整
.ant-btn-primary {
  box-shadow: 0 1px 0 rgba(#02777F, 1);
}
.ant-btn-default:disabled {
  border-color: rgba(0, 0, 0, 0.02);
  border: 0 none;
}
.ant-select-single:not(.ant-select-customize-input) .ant-select-selector, .ant-input-affix-wrapper, .ant-input, .ant-input-number, .ant-picker, .ant-btn-default, .ant-input-group .ant-input-group-addon {
  box-shadow: 0 1px 0 rgba(#C5C5C5, 1);
  border-color: #E2E2E2;
  border-bottom-color: #fff;
}
.ant-input-group .ant-input-group-addon {
  border-bottom-color: rgba(#000, 0.02);
}
.ant-input-affix-wrapper {
  .ant-input { box-shadow: none; }
}
.ant-select-single .ant-select-selector {
  border-radius: 2px !important;
}
// 左侧树 优化左侧空白宽度
.ant-tree-directory {
  .ant-tree-switcher {
    &.ant-tree-switcher-noop {
      width: 0 !important;
    }
    &.ant-tree-switcher_close, &.ant-tree-switcher_open {
      width: 8px !important;
    }
  }
}

// 内容模块下 
.ant-descriptions.ant-descriptions-bordered.ant-descriptions-small {
  .ant-descriptions-item-label, .ant-descriptions-item-content {
    padding: 4px 8px 4px 16px !important;
  }
  // form表单的样式优化
  .ant-descriptions-item-label {
    white-space: nowrap;
    .required {
      position: relative;
      display: inline;
      &::before {
        display: inline-block;
        margin-inline-end: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: "*";
        position: absolute;
        top: 50%;
        left: -8px;
        transform: translateY(-50%)
      }
    }
  }
  .ant-descriptions-item-content {
    .ant-form-item {
      margin-bottom: 0 !important;
      .ant-form-item-explain.ant-form-item-explain-connected.ant-form-show-help{
          position: absolute !important;
          bottom: -4px;
          left: 50%;
          transform: translateX(-50%);
          z-index: 2;
          background-color: #fff;
          width: auto;
          height: 16px;
          line-height: 16px;
          font-size: 12px;
          padding: 0 6px;
          border-radius: 2px;
      }
    }
    .ant-select {
      min-width: 160px !important;
      max-width: 100% !important;
    }
  }
} 
// 表格模块
.surely-table, .ant-table-wrapper .ant-table .ant-table-title, .ant-table-wrapper .ant-table .ant-table-header, .ant-table-wrapper table {
  border-radius: 0 !important;
}
.ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:first-child, .ant-table-wrapper .ant-table-container table>thead>tr:first-child >*:last-child, .ant-table-wrapper .ant-table-container {
  border-start-start-radius: 0 !important;
  border-start-end-radius: 0 !important
}
.surely-table .surely-table-row.surely-table-row-selected, .ant-table-wrapper .ant-table-tbody >tr.ant-table-row-selected >td {
  background-color: #eaf8f8 !important;
}

.ant-table.ant-table-small.ant-table-bordered {
  .ant-table-cell {
    padding: 4px 8px !important;
  }
}

.surely-table-body .surely-table-body-cell-range-selected.surely-table-body-cell-range-single-cell {
  border-color: var(--pro-ant-color-primary) !important;
}

.surely-table-body .surely-table-body-cell-range-selected:not(.surely-table-body .surely-table-body-cell-inline-edit) {
  background-color: #fff !important;
}
.surely-table-header-cell-title-inner, .ant-table-thead .ant-table-cell  {
  text-align: center !important;
}

// 表格 分组样式
.b-line-s, .b-line-c, .b-line-e, .b-r-line-s, .b-r-line-c, .b-r-line-e {
  position: absolute;
  top: 0;
  left: 8px;
  display: block;
  height: 100%;
  width: 10px;
  text-indent: -100px;
  overflow: hidden;
  &::before, &::after, .before, .after {
    position: absolute;
    left: 0;
    content: '';
    display: inline-block;
    width: 2px;
    height: 50%;
    background-color: #EA741E;
  }
  .before, .after {
    background-color: #000000;
    display: block;
  }
  &::before, .before {
    top: 0;
  }
  &::after, .after {
    bottom: 0;
  }
  &.sample-1 {
    &::before, &::after, .before, .after {
      background-color: #64a6e1;
    }
  }
  &.sample-2 {
    &::before, &::after, .before, .after {
      background-color: #0d0d0d;
    }
  }
  &.sample-3 {
    &::before, &::after, .before, .after {
      background-color: #fc2435;
    }
  }
  &.sample-4 {
    &::before, &::after, .before, .after {
      background-color: #ffa04d;
    }
  }
  &.sample-5 {
    &::before, &::after, .before, .after {
      background-color: #007130;
    }
  }
  &.sample-6 {
    &::before, &::after, .before, .after {
      background-color: #a491f3;
    }
  }
  &.sample-7 {
    &::before, &::after, .before, .after {
      background-color: #c5c5c5;
    }
  }
  &.sample-8 {
    &::before, &::after, .before, .after {
      background-color: #009B9B;
    }
  }
}
.b-line-s,  .b-r-line-s{
  &::before, .before{
    width: 10px;
    height: 2px;
    top: 50%
  }
}
.b-line-e, .b-r-line-e {
  &::after, .after{
    width: 10px;
    height: 2px;
    top: 50%
  }
}
.b-line-txt {
  padding-left: 15px;
}
.b-r-line-s, .b-r-line-c, .b-r-line-e {
  left: auto;
  right: 8px;

  &::after, &::before,  .before, .after  {
    left: auto;
    right: 0;
  }
}
.b-r-line-s {
  &::after, .after  {
    left: auto;
    right: 0;
  }
}
.b-r-line-e {
  &::before, .before {
    left: auto;
    right: 0;
  }
}
.b-r-line-txt {
  padding-right: 15px;
}


// 指令 无权限
.v-lock-auth {
  position: relative;
  .lock-blur {
    filter: blur(3px);
  }
  .lock-mask {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    position: absolute;
    overflow: hidden;
    z-index: 99;
    background-color: rgba(#fff, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 15px;
    font-weight: bold;
    .icon-lock {
      font-size: 20px;
      margin-right: 6px;
      font-weight: 100;
    }
  }
}
.v-drag-grid-v, .v-drag-grid-h {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  overflow: hidden;
  color: rgba(#555, 0.3);
  cursor: row-resize;
  z-index: 99;
  &::before {
    content: "";
    display: inline-block;
    width: 100%;
    height: 1px;
    background-color: #cecece;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
  }
}
.v-drag-grid-h {
  // transform: rotate(90deg);
  height: 100%;
  width: 24px;
  min-height: 100px;
  cursor: col-resize;
  i {
    transform: rotate(90deg);
  }
  &::before {
    // height: 100%;
    width: 1px;
    height: 100%;
  }
}

// 全局 loading

.loading-wrapper {
  @color: rgba(#fff, .7); // var(--pro-ant-color-primary);
  .chase-wrapper .chase-item::before {
    background-color: @color !important;
  }
  .text {
    color: @color !important;
  }
}

// auto-complete
.ant-select-dropdown {
  .rc-virtual-list {
    &-holder {
      // max-height: 400px !important;
    }

    .custom-art-header {
      position: fixed !important;
      z-index: 200 !important;
      background: #F0F0F0 !important;
      color: #000000 !important;
      font-weight: 700 !important;
    }
    .custom-art-check {
      width: 40px;
      float: left;
      text-align: center;
    }
    .custom-art-name {
      width: 180px;
      float: left;
      padding-right: 10px;
      white-space: break-spaces;
      word-break: break-word;
    }
    .custom-art-spec {
      width: 140px;
      float: left;
      text-align: center;
      padding-right: 10px;
    }
    .custom-art-price {
      width: 80px;
      float: left;
      padding-right: 10px;
      text-align: center;
    }
    .custom-art-producer {
      width: 250px;
      float: left;
      padding-right: 10px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .custom-art-other {
      width: 80px;
      float: left;
      padding-right: 10px;
      text-align: center;
    }
    .custom-art-footer {
      position: absolute !important;
      bottom: 0;
      width: 100%;
      background: #FFFFFF !important;
      border-radius: 0;
    }

  }
  .ant-select-item-option .ant-select-item-option-content {
    align-items: center;
    white-space: pre-wrap;
    display: flex;
  }
  .ant-select-item-option:nth-child(2n) {
    background: rgba(0, 0, 0, 0.04);
  }
  .ant-select-item-option-active {
    background: var(--pro-ant-color-primary) !important;
  }
  .ant-select-item-option-active .ant-select-item-option-content, .ant-select-item-option-active .ant-select-item-option-content > div{
    color: #fff !important;
  }
}




