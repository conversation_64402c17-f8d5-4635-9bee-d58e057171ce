<script setup lang="ts">
import * as antDesignVue from 'ant-design-vue'
const {
  Button: AButton,
  Card: ACard,
  Space: ASpace,
  Form: AForm,
  FormItem: AFormItem,
  Input: AInput,
  notification,
  Tabs: ATabs,
  TabPane: ATabPane,
  Table: ATable,
  Row: ARow,
  Col: ACol,
  Divider: ADivider,
} = antDesignVue
import { shallowRef, watch, ref, reactive, nextTick } from 'vue'
import { getConfig, readCard, getByIdcertNo, getPersonInfo, INSU_TYPE, formatCertType, formatGender } from '@mh-mi/util'
import { ReloadOutlined } from '@ant-design/icons-vue'
import CardInfo from './CardInfo.vue'
import InsuTypeList from './InsuTypeList.vue'
import MiBaseInfo from './MiBaseInfo.vue'
import MiDiseaseInfo from './MiDiseaseInfo.vue'
import MiSignInfo from './MiSignInfo.vue'
import HisFileInfo from './HisFileInfo.vue'
import type { PropType } from 'vue'
import type { CardModelType, CardInfoType, BaseInfoType } from './types'

defineOptions({
  name: 'CardReaderModal',
  inheritAttrs: false,
})

const props = defineProps({
  // 业务类型ID
  businessTypeId: {
    type: String,
    required: true,
  },
  // 医疗类型
  medicalType: {
    type: Number,
    default: 21,
  },
  // 就诊ID
  visitId: {
    type: Number,
    default: undefined,
  },
  // 其他参数
  params: {
    type: Object,
    default: () => ({}),
  },
  // 请求接口
  hostUrl: {
    type: String,
    default: '',
  },
  // 是否调用医保
  isGetMI: {
    type: Boolean,
    default: true,
  },
  // 医保调用后 是否选择医保险种
  isMIOpenSelect: {
    type: Boolean,
    default: true,
  },
  // 医保调用后 是否显示慢特信息
  isMIShowMT: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['success', 'cancel', 'confirm-insu-type'])

// 当前选中的卡类型
const activeCardType = ref('')
// 读卡状态
const cardTypeLoading = ref('')
// 当前选中的标签页
const activeTabKey = ref('2')
// 是否显示创建档案按钮
const showCreateFile = ref(false)
// 读卡状态
const readState = ref('init')
// 是否有配置数据
const hasConfigData = ref(true)

// 表单相关
const formRef = ref()
const requireds = { rules: { required: true, message: '' } }

// MiDiseaseInfo组件引用
const miDiseaseInfoRef = ref()
// MiSignInfo组件引用
const miSignInfoRef = ref()

// 读卡返回信息
const cardInfo = reactive<CardInfoType>({
  mdtrt_cert_type: '',
  mdtrt_cert_no: '',
  card_sn: null,
  psn_cert_type: '',
  certno: '',
  psn_cert_no: '',
  psn_name: '',
  age: '',
  birthday: '',
  gender: '',
  nation: '',
  address: '',
  insuplc_admdvs: null,
  extra_props: null,
})

// 医保险种对象
const insuinfoModel = reactive({
  open: false,
  list: [],
  mtList: [],
})

// 医保基础信息
const miBaseInfo = reactive<BaseInfoType>({
  psn_no: '', // 人员编号
  psn_name: '', // 姓名
  gend: '', // 性别
  brdy: '', // 出生日期
  psn_cert_type: '', // 证件类型
  psn_cert_no: '', // 证件号码
  tel: '', // 联系电话
  addr: '', // 联系地址
  insutype: '', // 险种
  balc: '', // 余额
})

// 当前选中的险种
const selectedInsuType = ref(null)

// 患者ID
const patientId = ref<number>()
// 就诊ID
const visitId = ref<number>(props.visitId)

// 卡片模型
const cardModel = reactive<CardModelType>({
  list: [],
  // 传入参数
  sParams: {},
  // 读卡数据
  readCard: {
    psn_cert_type: '',
    psn_cert_no: '',
    psn_name: '',
    psn_sex: '',
    psn_nation: '',
    psn_age: '',
    psn_address: '',
    psn_phone: '',
    insuplc_admdvs: '',
    card_sn: '',
    mdtrt_cert_type: '',
    mdtrt_cert_no: '',
    certno: '',
    age: '',
    birthday: '',
    gender: '',
    nation: '',
    address: '',
    read_time: '',
    extra_props: null,
  },
  // 医保数据
  miData: {},
  // 患者信息
  patientInfo: {},
})

// 档案信息
const patientFileInfo = ref<any>(null)
// 是否显示查看档案按钮
const showViewFile = ref(false)

// 是否最小化医保信息
const isMiInfoMinimized = ref(false)

// 最小化医保信息
const minimizeMiInfo = () => {
  isMiInfoMinimized.value = true
}

// 还原医保信息
const restoreMiInfo = () => {
  isMiInfoMinimized.value = false
}

// 更新档案信息
const updatePatientFileInfo = (data: any) => {
  patientFileInfo.value = data
}

// 保存档案信息
const savePatientFileInfo = async (data: any) => {
  try {
    console.log('保存档案信息:', data)
    // TODO: 调用保存档案信息的API
    notification.success({
      message: '档案信息保存成功',
    })
  } catch (error) {
    console.error('保存档案信息失败:', error)
    notification.error({
      message: '档案信息保存失败',
    })
  }
}

// 获取档案信息
const getPatientFile = async (certTypeId: number, idcertNo: string) => {
  try {
    console.log('getPatientFile', certTypeId, idcertNo)
    const res = await getByIdcertNo(certTypeId, idcertNo)
    if (res) {
      patientFileInfo.value = res
      showViewFile.value = true
      showCreateFile.value = false
    } else {
      patientFileInfo.value = null
      showViewFile.value = false
      showCreateFile.value = true
    }
  } catch (error) {
    console.error('获取档案信息失败:', error)
    patientFileInfo.value = null
    showViewFile.value = false
    showCreateFile.value = true
  }
}

// 清除卡信息
const clearCardInfo = () => {
  Object.keys(cardInfo).forEach(key => {
    if (key === 'card_sn' || key === 'insuplc_admdvs') {
      cardInfo[key as keyof typeof cardInfo] = null
    } else {
      cardInfo[key as keyof typeof cardInfo] = ''
    }
  })
  // 清空医保信息
  Object.keys(miBaseInfo).forEach(key => {
    miBaseInfo[key as keyof typeof miBaseInfo] = ''
  })
  insuinfoModel.list = []
  insuinfoModel.mtList = []
  selectedInsuType.value = null
  // 清空档案信息
  patientFileInfo.value = null
  showViewFile.value = false
  showCreateFile.value = false
}

// 监听 props.visitId 变化
watch(
  () => props.visitId,
  val => {
    if (val !== undefined) {
      visitId.value = val
    }
  }
)

// 初始化状态
const initState = async () => {
  try {
    // 获取读卡配置
    const res = await getConfig()
    if (res) {
      // 按照 code 排序，确保电子医保、身份证、社保卡的顺序
      const sortedData = [...res].sort((a, b) => parseInt(a.code) - parseInt(b.code))
      cardModel.list = sortedData.map(item => ({
        code: item.code,
        name: item.name,
        cardReader: item.cardReader,
        readCard: item.readCard,
        getPersonInfo: item.getPersonInfo,
        others: item.Others,
      }))

      // 清空所有状态
      clearCardInfo()
      activeTabKey.value = '2'
      cardTypeLoading.value = ''

      // 重置卡类型选中状态
      activeCardType.value = ''
    } else {
      hasConfigData.value = false
      notification.error({
        message: '未获取到读卡配置',
      })
    }
  } catch (error) {
    console.error('获取读卡配置失败:', error)
    hasConfigData.value = false
    notification.error({
      message: '获取读卡配置失败',
    })
  }
}

// 初始化
watch(
  () => true,
  val => {
    if (val) {
      initState()
    }
  },
  { immediate: true }
)

// 更新 insuplc_admdvs 值
const updateInsuplcAdmdvs = () => {
  // 确保使用选中险种的 insuplc_admdvs 更新 readCard
  if (insuinfoModel.list && insuinfoModel.list.length > 0) {
    const selectedItem = insuinfoModel.list.find((item: any) => item.selected)
    console.log('选中的险种信息：', selectedItem)
    if (selectedItem && selectedItem.insuplc_admdvs) {
      console.log('更新前 insuplc_admdvs:', cardModel.readCard.insuplc_admdvs)
      console.log('将要设置的 insuplc_admdvs:', selectedItem.insuplc_admdvs)

      // 直接设置值
      cardModel.readCard.insuplc_admdvs = selectedItem.insuplc_admdvs

      // 手动检查更新后的值
      console.log('更新后 insuplc_admdvs:', cardModel.readCard.insuplc_admdvs)
    } else {
      console.warn('未找到选中的险种或选中的险种没有 insuplc_admdvs 属性')
    }
  } else {
    console.warn('险种列表为空，无法更新 insuplc_admdvs')
  }
}

// 关闭弹窗
const handleCancel = () => {
  // 关闭前更新 insuplc_admdvs
  updateInsuplcAdmdvs()
  emit('cancel')
}

// 获取证件类型名称
const getCardTypeName = (type: string) => {
  const typeNum = parseInt(type) % 10
  switch (typeNum) {
    case 1:
      return '电子医保'
    case 2:
      return '身份证'
    case 3:
      return '社保卡'
    default:
      return '其他'
  }
}

// 选择卡类型
const selectCardType = async (code: string) => {
  activeCardType.value = code
  clearCardInfo()

  // 重置医保信息卡片显示状态
  isMiInfoMinimized.value = false

  // 开始读卡
  cardTypeLoading.value = code
  try {
    readState.value = 'pending'

    // 构建读卡参数
    const params = {
      business_type_id: props.businessTypeId,
      visit_id: props.visitId,
      card_sn: '',
      mdtrt_cert_no: '',
      mdtrt_cert_type: code,
      medical_type: props.medicalType,
      org_id: props.params.org_id,
      psn_cert_no: '',
      psn_name: '',
      user_id: props.params.user_id,
    }

    // 保存传入参数
    cardModel.sParams = params

    // 调用读卡方法
    const res = (await readCard(params)) as any
    if (res) {
      cardModel.readCard = res

      // 显示读卡信息
      Object.keys(res).forEach(key => {
        if (key in cardInfo) {
          // 特殊处理证件类型
          if (key === 'mdtrt_cert_type') {
            cardInfo[key] = getCardTypeName(res[key])
          } else {
            cardInfo[key] = res[key] || ''
          }
        }
      })

      // 获取档案信息
      if (res.psn_cert_no) {
        await getPatientFile(res.psn_cert_type, res.psn_cert_no)
      }

      // 不调用医保就返回结果
      if (!props.isGetMI) {
        onReadCardSuccess()
      } else {
        // 获取当前选中卡片的配置
        const currentCardConfig = cardModel.list.find(item => item.code === activeCardType.value)
        // 直接获取医保信息
        readState.value = 'infoing'
        await onGetMedicalInfo(currentCardConfig)
      }
    } else {
      readState.value = 'init'
      notification.error({
        message: '读卡失败',
      })
    }
  } catch (error) {
    console.error('读卡失败:', error)
    readState.value = 'init'
    notification.error({
      message: '读卡失败',
    })
  } finally {
    cardTypeLoading.value = ''
  }
}

// 读卡方法
const onReadCard = async () => {
  if (props.params.business_type_id === '') {
    notification.error({
      message: '未配置 business_type_id',
    })
    return
  } else if (props.params.user_id === '') {
    notification.error({
      message: '未配置 user_id',
    })
    return
  }

  readState.value = 'pending'
  // TODO: 实现读卡逻辑
}

// 医保参数
const miParams = ref<any>(null)

// 获取医保信息
const onGetMedicalInfo = async (cardConfig?: any) => {
  try {
    readState.value = 'infoing'

    // 检查卡片配置中是否有 getPersonInfo 对象
    if (!cardConfig?.getPersonInfo) {
      console.log('当前卡片配置中没有 getPersonInfo 对象，不获取医保信息')
      readState.value = 'success'
      onReadCardSuccess()
      return
    }

    // 构建参数
    const params = {
      card_info: { ...cardModel.readCard },
      business_type_id: props.businessTypeId,
      medical_type: props.medicalType,
      visit_id: props.visitId,
      mdtrt_cert_type: cardModel.readCard.psn_cert_type,
      psn_name: cardModel.readCard.psn_name,
      psn_cert_no: cardModel.readCard.psn_cert_no,
      mdtrt_cert_no: cardModel.readCard.psn_cert_no,
      card_sn: cardModel.readCard.card_sn || '',
      insuplc_admdvs: cardModel.readCard.insuplc_admdvs || '',
    }

    // 保存医保参数
    miParams.value = params

    // 调用获取医保信息接口，传入卡片配置
    const res = (await getPersonInfo(params, cardConfig)) as any
    if (res) {
      cardModel.miData = res as any
      // 更新基础信息
      if (res.baseinfo) {
        Object.keys(miBaseInfo).forEach(key => {
          if (key in res.baseinfo) {
            miBaseInfo[key] = res.baseinfo[key] || ''
          }
        })
      }

      // 更新险种列表
      if (res.insuinfo && Array.isArray(res.insuinfo)) {
        // 如果后端返回的数据中没有 selected 属性，则添加默认值
        const processedInsuinfo = res.insuinfo.map((item: any, index: number) => {
          // 如果没有key，则根据险种类型和索引生成唯一key
          if (!item.key) {
            item.key = `${item.insutype} - ${index}`
          }

          // 如果未设置 selected 属性，则默认第一个设为选中状态
          if (item.selected === undefined) {
            item.selected = index === 0
          }

          return item
        })

        insuinfoModel.list = processedInsuinfo

        // 设置默认选中的险种
        const selectedItem = processedInsuinfo.find((item: any) => item.selected)
        if (selectedItem) {
          selectedInsuType.value = selectedItem
          miBaseInfo.insutype = INSU_TYPE[selectedItem.insutype] || ''
          miBaseInfo.balc = selectedItem.balc || ''
        }
      }

      readState.value = 'success'

      // 获取完医保信息后，调用 onSuccess 方法确保更新 insuplc_admdvs
      console.log('获取医保信息成功，不需要立即调用 onReadCardSuccess')
    } else {
      readState.value = 'init'
      notification.error({
        message: '获取医保信息失败',
      })
    }
  } catch (error) {
    console.error('获取医保信息失败:', error)
    readState.value = 'init'
    notification.error({
      message: '获取医保信息失败',
    })
  }
}

// 处理险种选择
const handleInsuTypeSelected = (data: any) => {
  selectedInsuType.value = data.value

  // 更新基础信息中的险种和余额
  if (data.value) {
    miBaseInfo.insutype = INSU_TYPE[data.value.insutype] || ''
    miBaseInfo.balc = data.value.balc || ''

    // 更新险种列表中的 selected 属性
    if (insuinfoModel.list && insuinfoModel.list.length > 0) {
      insuinfoModel.list.forEach((item: any) => {
        // 根据 key 或组合键值判断是否是选中的险种
        if (item.key === data.value.key || (item.insutype === data.value.insutype && item.insuplc_admdvs === data.value.insuplc_admdvs)) {
          item.selected = true
        } else {
          item.selected = false
        }
      })
    }
  }
}

// 确认险种
const confirmInsuType = () => {
  // 如果险种列表为空，直接关闭
  // if (insuinfoModel.list.length === 0) {
  //   handleCancel()
  //   return
  // }
  if (insuinfoModel.list.length > 0) {
    // 更新 insuplc_admdvs
    updateInsuplcAdmdvs()
  }
  // 构建返回数据（当有险种列表时）
  const result = {
    params: miParams.value || props.params,
    readCard: cardModel.readCard,
    miData: {
      baseinfo: miBaseInfo,
      insuinfo: insuinfoModel.list,
      idetinfo: cardModel.miData?.idetinfo || [],
    },
  }

  // 触发确认险种事件
  emit('confirm-insu-type', result)
}

// 选择险种
const onHandleSelected = () => {
  onReadCardSuccess()
}

// 读卡成功回调（原 onSuccess）
const onReadCardSuccess = () => {
  readState.value = 'success'
  // 暂时不做任何处理
}

// 创建档案
const handleCreateFile = () => {
  antDesignVue.Modal.confirm({
    title: '提示',
    content: '是否确认创建患者档案？',
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        // TODO: 实现创建档案逻辑
        notification.success({
          message: '创建档案成功',
        })
        showCreateFile.value = false
        minimizeMiInfo()
      } catch (error) {
        console.error('创建档案失败:', error)
      }
    },
  })
}

// 查看档案
const handleViewFile = () => {
  minimizeMiInfo()
}

// 获取结果数据
const getResult = () => {
  const params: any = {
    params: miParams.value || props.params,
    readCard: cardModel.readCard,
    miData: cardModel.miData || {
      baseinfo: miBaseInfo,
      insuinfo: insuinfoModel.list,
      idetinfo: [],
    },
  }
  return params
}

// 刷新慢病信息
const refreshDiseaseInfo = () => {
  if (miDiseaseInfoRef.value && typeof miDiseaseInfoRef.value.getDiseaseInfo === 'function') {
    miDiseaseInfoRef.value.getDiseaseInfo()
  }
}

// 刷新签约信息
const refreshSignInfo = () => {
  if (miSignInfoRef.value && typeof miSignInfoRef.value.getSignInfo === 'function') {
    miSignInfoRef.value.getSignInfo()
  }
}

// 点击医保信息按钮的处理函数
const handleGetMedicalInfo = () => {
  // 获取当前选中卡片的配置
  const currentCardConfig = cardModel.list.find(item => item.code === activeCardType.value)
  onGetMedicalInfo(currentCardConfig)
}

// 暴露方法给父组件
defineExpose({
  getResult,
  onReadCardSuccess,
  confirmInsuType,
  insuinfoModel,
})
</script>

<template>
  <div class="super-read-content">
    <!-- 读卡方式选择区域 -->
    <div class="card-type-container" v-if="hasConfigData">
      <div class="card-type-buttons">
        <a-space>
          <a-button
            v-for="item in cardModel.list"
            :key="item.code"
            :type="activeCardType === item.code ? 'primary' : 'default'"
            :loading="cardTypeLoading === item.code"
            :disabled="cardTypeLoading !== ''"
            @click="selectCardType(item.code)"
          >
            {{ item.name }}
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="16">
        <!-- 左侧：卡信息 -->
        <a-col :span="8">
          <a-card title="卡信息" class="info-card" :bordered="false">
            <template #extra>
              <a-button v-if="showViewFile" type="link" @click="handleViewFile"> 查看档案 </a-button>
              <a-button v-if="showCreateFile" type="link" danger @click="handleCreateFile"> 创建档案 </a-button>
            </template>
            <card-info :info="cardInfo" />
          </a-card>
        </a-col>

        <!-- 右侧：医保信息/HIS档案 -->
        <a-col :span="16">
          <!-- 医保信息卡片 -->
          <a-card v-if="!isMiInfoMinimized" title="医保信息" class="info-card" :bordered="false">
            <!-- 医保信息按钮 -->
            <template #extra>
              <a-button type="primary" size="small" :loading="readState === 'infoing'" :disabled="readState === 'init' || readState === 'pending'" @click="handleGetMedicalInfo"> 医保信息 </a-button>
            </template>

            <!-- 标签页内容 -->
            <a-tabs v-model:activeKey="activeTabKey" class="mi-tabs">
              <!-- 基础信息标签页 -->
              <a-tab-pane key="1" tab="基础信息">
                <mi-base-info :info="miBaseInfo" />
              </a-tab-pane>

              <!-- 慢病信息标签页 -->
              <a-tab-pane key="2">
                <template #tab>
                  <span style="display: flex; align-items: center">
                    慢病信息
                    <a-button type="link" style="padding: 0 0 0 5px; margin: 0; font-size: 14px" @click.stop="refreshDiseaseInfo">
                      <template #icon><reload-outlined /></template>
                    </a-button>
                  </span>
                </template>
                <mi-disease-info
                  ref="miDiseaseInfoRef"
                  :psn-no="miBaseInfo.psn_no"
                  :patient-id="patientId"
                  :visit-id="visitId"
                  :psn-cert-type="cardInfo.psn_cert_type"
                  :psn-cert-no="cardInfo.psn_cert_no"
                />
              </a-tab-pane>

              <!-- 签约信息标签页 -->
              <a-tab-pane key="3">
                <template #tab>
                  <span style="display: flex; align-items: center">
                    签约信息
                    <a-button type="link" style="padding: 0 0 0 5px; margin: 0; font-size: 14px" @click.stop="refreshSignInfo">
                      <template #icon><reload-outlined /></template>
                    </a-button>
                  </span>
                </template>
                <mi-sign-info ref="miSignInfoRef" :psn-no="miBaseInfo.psn_no" :patient-id="patientId" :visit-id="visitId" :psn-cert-type="cardInfo.psn_cert_type" :psn-cert-no="cardInfo.psn_cert_no" />
              </a-tab-pane>
            </a-tabs>
          </a-card>

          <!-- HIS档案卡片 -->
          <a-card v-if="isMiInfoMinimized" title="HIS档案" class="info-card" :bordered="false">
            <template #extra>
              <a-button type="link" @click="restoreMiInfo"> 返回医保信息 </a-button>
            </template>
            <his-file-info :info="patientFileInfo" @update:info="updatePatientFileInfo" @save="savePatientFileInfo" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 医保险种选择表格 -->
      <div v-if="insuinfoModel.list.length" class="insu-type-section">
        <insu-type-list :dataSource="insuinfoModel.list" @selected="handleInsuTypeSelected" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.super-read-content {
  min-height: 500px;
  display: flex;
  flex-direction: column;
  padding: 10px 0 20px;
}

.card-type-container {
  margin-bottom: 5px;
  text-align: center;
}

.main-content {
  flex: 1;
}

.insu-type-section {
  margin-top: 16px;
}

.minimized-mi-info {
  position: absolute;
  top: 0;
  right: 8px;
  z-index: 10;
}

.his-file-content {
  min-height: 300px;
}

.info-card {
  width: 100%;
  height: 100%;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
  position: relative;

  :deep(.ant-card-head) {
    height: 48px;
    line-height: 48px;
    padding: 0 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #fafafa;

    .ant-card-head-title {
      padding: 0;
      font-size: 16px;
      font-weight: 500;
      line-height: 48px;
      height: 48px;
    }

    .ant-card-extra {
      padding: 0;
      height: 48px;
      line-height: 48px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

.mi-info-empty {
  text-align: center;
  color: #999;
  padding: 30px 0;
}

.insu-table-container {
  margin-bottom: 5px;
}
</style>
