import { Data, http } from '@idmy/core'

export function listFullPaymentsByCashId(cashId: number): Promise<Data[]> {
  return http.post('/api/bcs/CashPayment/listFullByCashId/' + cashId, null, { appKey: 'bcs' })
}

export function listAllPaymentsByCashId(cashId: number): Promise<Data[]> {
  return http.post('/api/bcs/CashPayment/listAllByCashId/' + cashId, null, { appKey: 'bcs' })
}

export function listPrevPaymentsByCashId(cashId: number): Promise<Data[]> {
  return http.post('/api/bcs/CashPayment/listPrevByCashId/' + cashId, null, { appKey: 'bcs' })
}
