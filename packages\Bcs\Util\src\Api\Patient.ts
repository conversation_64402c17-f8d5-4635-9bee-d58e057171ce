export function getPatient(patientId: number) {
  return http.post('/hip-base/patient/get', { patientId }, { appKey: 'hip' })
}

/**
 * 获取患者信息
 * @param patientId
 */
export function patientInfoApi(patientId: number) {
  return http.post('patientApi.patientInfo', { patientId: patientId }, { appKey: 'hip' })
}

/**
 * 获取患者信息
 */
export function getPatientIdByIdcertNo(certTypeId: number, idcertNo: string) {
  return http.post('patientApi.getPatientIdByIdcertNo', { certTypeId, idcertNo }, { appKey: 'hip' })
}

