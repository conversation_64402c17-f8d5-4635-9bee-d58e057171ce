/**
 * 转义HTML标签，将<>等特殊字符转换为HTML实体
 * @param code 需要转义的代码
 * @returns 转义后的代码
 */
export function escapeHtml(code: string): string {
  return code
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * 包装代码示例，自动转义HTML标签
 * @param code 原始代码
 * @returns 转义后的代码
 */
export function wrapCodeExample(code: string): string {
  return escapeHtml(code);
}
