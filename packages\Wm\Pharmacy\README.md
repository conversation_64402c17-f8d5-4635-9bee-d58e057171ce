# Pharmacy组件

药房相关组件，包括：

1. PharmacySelect：药房选择组件，支持选择所有药房或仅当前用户有权限的药房

## 特性

- 基于ant-design-vue的Select组件实现
- 支持单选和多选模式
- 支持显示所有药房或仅显示当前用户有权限的药房
- 支持根据业务类型过滤药房列表
- 支持搜索过滤
- 当own属性变化时自动刷新数据

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-wm/util`: 工具库，提供API调用

## 安装

```bash
# 安装组件
pnpm publish:component Wm/Pharmacy
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/pharmacy": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { PharmacySelect } from '@mh-wm/pharmacy'
```

### PharmacySelect 基本用法

```vue
<template>
  <!-- 基础用法 - 显示所有药房 -->
  <PharmacySelect v-model="pharmacyId" style="width: 200px" />

  <!-- 仅显示当前用户有权限的药房 -->
  <PharmacySelect v-model="userPharmacyId" :own="true" style="width: 200px" />

  <!-- 多选模式 -->
  <PharmacySelect v-model="pharmacyIds" multiple />
  
  <!-- 根据业务类型过滤 -->
  <PharmacySelect v-model="purchasePharmacyId" :bsnType="1" />
  
  <!-- 用于处方发药的药房 -->
  <PharmacySelect v-model="dispensingPharmacyId" :forRecipeDispensing="true" />
</template>

<script setup>
import { PharmacySelect } from '@mh-wm/pharmacy'
import { ref } from 'vue'

// 单选值
const pharmacyId = ref()
const userPharmacyId = ref()
const purchasePharmacyId = ref()
const dispensingPharmacyId = ref()

// 多选值
const pharmacyIds = ref([])
</script>
```

## 组件属性

### PharmacySelect 属性

| 属性名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ----- | ---- |
| v-model | String/Array | - | 绑定值，多选时为数组 |
| own | Boolean | false | 是否仅显示当前用户有权限的药房 |
| multiple | Boolean | false | 是否多选 |
| bsnType | Number | 0 | 业务类型，用于过滤药房列表 |
| forRecipeDispensing | Boolean | false | 是否用于处方发药 |
| forSectionReq | Boolean | false | 是否用于科室请领 |
| forMoveSource | Boolean | false | 是否用于库存调拨源 |
| forPharmacy | Boolean | false | 是否为药房 |
| forWms | Boolean | false | 是否为仓库 |
| isOperator | Boolean | false | 是否用于采购入库 |
| isChecker | Boolean | false | 是否用于验收 |
| isScheduler | Boolean | false | 是否用于排班 |

## API说明

### PharmacySelect 组件

组件内部使用了两个不同的API：

1. 当`own=false`时，调用`/clinics_wm/deptcustmap/findAll`获取所有药房
2. 当`own=true`时，调用`/clinics_wm/deptcustmap/list`获取当前用户有权限的药房

当`own`属性值或其他过滤条件发生变化时，组件会自动重新加载对应的药房列表。

### 业务类型说明

业务类型(bsnType)对应的值：
- 1: 采购入库
- 2: 销售出库
- 3: 库存调拨

当设置业务类型时，组件会根据业务类型自动过滤药房列表。例如，当bsnType=1时，只会显示支持采购入库的药房。
