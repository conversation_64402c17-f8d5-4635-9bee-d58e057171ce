<script setup lang="ts">
import { Card, Typography } from 'ant-design-vue'
import ScmCustSelectExample from './examples/ScmCustSelectExample.vue'

const { Title } = Typography
</script>

<template>
  <div class="scm-cust-select-page">
    <Card title="ScmCustSelect 供应商选择组件" class="mb-16px">
      <ScmCustSelectExample />
    </Card>
  </div>
</template>

<style scoped>
.scm-cust-select-page {
  padding: 20px;
}
</style>
