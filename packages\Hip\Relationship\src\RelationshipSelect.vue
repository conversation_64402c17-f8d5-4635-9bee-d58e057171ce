<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Data } from '@idmy/core'
import { allRelationships } from '@mh-hip/util'
import { Select } from 'ant-design-vue'
import type { SelectProps } from 'ant-design-vue'

const loading = ref(false)
const options = ref<Data[]>([])

// 继承 Select 组件的所有属性，同时添加 modelValue
type Props = SelectProps & {
  modelValue?: string | number
}

const props = withDefaults(defineProps<Props>(), {
  filterOption: (input: string, option: any) => 
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase()),
  showSearch: true,
  placeholder: '请选择关系',
  style: { width: '100%' }
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | number): void
}>()

const value = computed({
  get: () => props.modelValue,
  set: (val: string | number) => emit('update:modelValue', val)
})

const loadData = async () => {
  loading.value = true
  try {
    const data = await allRelationships(true)
    options.value = data
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<template>
  <Select
    v-model:value="value"
    v-bind="$props"
    :loading="loading"
    :options="options"
    show-search
    placeholder="请选择关系"
    :filter-option="(input: string, option: any) => 
      (option?.label ?? '').toLowerCase().includes(input.toLowerCase())"
  />
</template>

<style scoped lang="less"></style>