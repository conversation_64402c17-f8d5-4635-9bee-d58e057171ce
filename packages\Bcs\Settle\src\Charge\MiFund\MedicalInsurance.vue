<script lang="ts" setup>
import { Dict } from '@idmy/antd'
import { type Data, Format, format, useLoading } from '@idmy/core'
import { allInsuranceTypes, listPatientMiDiseases, updateMedType, updateMiDisease, updatePsnTypeId } from '@mh-bcs/util'
import { Select, Space, Tag } from 'ant-design-vue'
import dayjs from 'dayjs'
import { isEmpty, sortBy } from 'lodash-es'

const { data, patientId } = defineProps({
  data: { type: Array, required: true },
  patientId: { type: Number, required: true },
  miTransStatus: { type: Number, required: true },
})

const model = defineModel<any>({})

const map = ref<any>({})
useLoading(async () => {
  const arr: any[] = await allInsuranceTypes()
  map.value = arr.reduce((acc, row) => {
    if (!acc[row.medTypeId]) {
      acc[row.medTypeId] = []
    }
    acc[row.medTypeId].push(row.insuranceTypeId)
    return acc
  }, {})
}, true)

const diseases = ref<any[]>([])
watch(
  () => data,
  () => {
    listPatientMiDiseases(patientId).then(rows => {
      diseases.value = rows
    })
  },
  { immediate: true }
)

const its = computed(() => {
  const mtIts = map.value[model.value.medTypeId] ?? []
  let arr: any[] = data?.filter((row: any) => mtIts.includes(Number(row.insutype))) ?? []
  arr = arr.map((row: Data) => ({
    id: Number(row.insutype),
    balance: Number(row.balc),
    company: row.emp_name,
    label: format(Number(row.insutype), 'Dict', 'InsuranceType'),
    psnType: row.psn_type,
    pausInsuDate: row.paus_insu_date,
    insuplcAdmdvs: row.insuplc_admdvs,
    value: `${row.insutype}:${row.insuplc_admdvs}`,
    expired: dayjs().isAfter(row.paus_insu_date),
  }))
  return sortBy(arr, 'balance').reverse()
})

const selectedIt = ref<string>('')

function onSelect(it: string) {
  selectedIt.value = it
  const tmp = its.value.find((item: Data) => item.value === it)
  model.value.insuranceTypeId = tmp?.id
  model.value.insuplcAdmdvs = tmp?.insuplcAdmdvs
  model.value.psnType = tmp?.psnType
}

async function onChangeMedType() {
  model.value.diseaseCode = undefined
  model.value.diseaseName = undefined
}

function onChangeDisease(row: Data) {
  if (diseases.value.length) {
    model.value.diseaseName = diseases.value.find(item => item.diseaseCode === model.value.diseaseCode)?.diseaseName
  } else {
    model.value.diseaseName = row.name
  }
}

watch(
  () => model.value.diseaseCode,
  async () => {
    await updateMiDisease(model.value.cashId, model.value.diseaseCode, model.value.diseaseName)
  }
)

watch(
  [() => model.value.medTypeId, () => its.value],
  () => {
    if (its.value.length) {
      const arr = its.value.filter(row => row.selected || !row.expired)
      if (arr.length) {
        onSelect(arr[0].value)
      } else {
        onSelect(its.value[0].value)
      }
    } else {
      onSelect('')
    }
    model.value.medTypeId && updateMedType(model.value.cashId, model.value.medTypeId)
    model.value.psnType && updatePsnTypeId(model.value.visitId, model.value.psnType)
  },
  { immediate: true }
)

function medicalTypeFilter(row: Data) {
  if (model.value.cashType === 'OUTPATIENT') {
    return row.data.forOpc === 1 && map.value[row.id]
  } else if (model.value.cashType === 'INPATIENT') {
    return row.data.forIpc === 1 && map.value[row.id]
  } else {
    return false
  }
}
</script>

<template>
  <Space v-if="!isEmpty(map)">
    <Dict v-model="model.medTypeId" :clearable="false" :disabled="miTransStatus >= 1" :filter="medicalTypeFilter" class="w-120px!" clazz="MedType" @change="onChangeMedType" />
    <Select v-model:value="selectedIt" :disabled="its.length === 0 || miTransStatus >= 1" :options="its" class="w-200px!" placeholder="医保险种" popupClassName="w-450px!" @select="onSelect">
      <template #option="item">
        <div class="wp100">
          <div class="f1" flex justify-between>
            {{ item.label }}
            <Format :value="item.balance" class="text-16px font-bold" component="div" type="Currency" />
          </div>
          <div text-12px flex justify-between>
            <div>{{ item.company }}</div>
            <div v-if="item.pausInsuDate">
              <Tag v-if="item.expired" color="red" size="small">已过期</Tag>
              {{ item.pausInsuDate }}
            </div>
          </div>
        </div>
      </template>
    </Select>
    <Select
      v-if="diseases.length && model.medTypeId === 14"
      v-model:value="model.diseaseCode"
      :field-names="{ value: 'diseaseCode', label: 'diseaseName' }"
      :options="diseases"
      class="w-160px!"
      placeholder="医保病种"
      popupClassName="w-300px!"
      @change="onChangeDisease"
    >
      <template #option="item"> {{ item.diseaseName }} - {{ item.diseaseCode }} </template>
    </Select>
    <Dict v-else-if="model.medTypeId === 14" v-model="model.diseaseCode" :clearable="false" cache class="w-220px!" clazz="MiDisease" placeholder="医保病种" @change="onChangeDisease" />
  </Space>
</template>
