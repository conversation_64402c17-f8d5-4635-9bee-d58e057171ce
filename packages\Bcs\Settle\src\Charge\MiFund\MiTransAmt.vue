<script lang="ts" setup>
import { Format } from '@idmy/core'
import { ChargeContext, chargeInjectKey } from '@mh-bcs/util'
import { Space } from 'ant-design-vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})
</script>

<template>
  <Space v-if="ctx.miTrans?.miTransId">
    <Format :value="ctx.miTrans.fundPayAmt ?? 0" prefix="统筹：" type="Currency" value-class="primary font-bold text-16px" />
    <Format v-if="ctx.miTrans.acctPayAmt" :value="ctx.miTrans.acctPayAmt ?? 0" prefix="个账：" type="Currency" value-class="primary font-bold text-16px" />
    <Format v-if="ctx.miTrans.multiAidAmt" :value="ctx.miTrans.multiAidAmt ?? 0" prefix="共济：" type="Currency" value-class="primary font-bold text-16px" />
  </Space>
</template>
