<script lang="ts" setup>
import type { Data } from '@idmy/core'
import { add, Api, Format, subtract } from '@idmy/core'
import { countCash } from '@mh-bcs/util'
import { Alert, Space } from 'ant-design-vue'

const apiRef = ref()
const onLoad = (args: any) => apiRef.value.onLoad(args)
defineExpose({
  onLoad,
})
</script>

<template>
  <div class="mb-8px">
    <Api ref="apiRef" v-slot="{ output: cash }: Data" :immediate="false" :load="countCash" first spin type="Object">
      <Alert>
        <template #message>
          <span font-bold>合计：</span>
          <Space size="large">
            <template v-if="cfg.tenant.enableDiscount">
              <Format :value="cash.totalAmount ?? 0" prefix="总费用：" type="Currency" />
              <Format :value="add(cash.discounted, cash.derated)" prefix="优惠：" type="Currency" />
            </template>
            <Format :value="cash.amount ?? 0" prefix="收费：" type="Currency" />
            <Format :value="cash.refundedAmount ?? 0" prefix="退费：" type="Currency" />
            <Format :value="add(cash.amount, cash.refundedAmount)" prefix="实收（收费-退费）：" type="Currency" />
            <Format :value="add(cash.miFundAmt, cash.miAcctAmt, cash.familyAcctAmt)" prefix="医保：" type="Currency" />
            <Format :value="cash.miFundAmt ?? 0" prefix="统筹：" type="Currency" />
            <Format :value="cash.miAcctAmt ?? 0" prefix="个账：" type="Currency" />
            <Format :value="cash.familyAcctAmt ?? 0" prefix="共济：" type="Currency" />
            <Format :value="subtract(add(cash.amount, cash.refundedAmount), add(cash.miFundAmt, cash.miAcctAmt, cash.familyAcctAmt))" prefix="自费：" type="Currency" />
            <Format :value="cash.billCount ?? 0" prefix="划价单：" type="Integer" />
          </Space>
        </template>
      </Alert>
    </Api>
  </div>
</template>
