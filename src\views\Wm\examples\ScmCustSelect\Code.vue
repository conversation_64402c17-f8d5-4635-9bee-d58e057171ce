<script setup lang="ts">
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { Card, Typography, Divider } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { codeUsage, importCode, packageJsonCode } from '../code/ScmCustSelectCode'

const { Title } = Typography

// 单选值
const custId = ref<number>()
</script>

<template>
  <Card title="显示供应商编码" class="mb-16px">
    <div mb-16px>
      <Title :level="4">供应商选择（显示编码）</Title>
      <ScmCustSelect v-model="custId" showField="custCode" style="width: 100%" />
      <div mt-8px>选中的供应商ID: {{ custId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        显示格式为：供应商编码 - 供应商名称
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="codeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
