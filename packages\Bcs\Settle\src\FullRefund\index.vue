<script lang="ts" setup>
import { View } from '@idmy/antd'
import { cfg, Data, useLoading } from '@idmy/core'
import { Button } from 'ant-design-vue'
import { PropType } from 'vue'
import RefundBefore from '../Refund/Before.vue'
import Bill from './Bill.vue'

const { cashId } = defineProps({
  cashId: { type: Number as PropType<number>, required: true },
})

const [onOk, okLoading] = useLoading(async (reds: Data[]) => {
  const redBillIds = reds.map(i => i.billId)
  const onClose: any = Modal.state.onClose
  Modal.open({
    component: RefundBefore,
    props: { blueCashId: cashId, redBillIds },
    title: `退费`,
    width: cfg.setting?.partialRefundDisabled ? 4 : 5,
    onClose: () => {
      onClose()
    },
  })
})
</script>

<template>
  <Bill v-if="!cfg.setting?.partialRefundDisabled" v-slot="{ reds, blues }: any" :cashId="cashId" class="mb-24px">
    <Button :disabled="!reds.length" :loading="okLoading" type="primary" @click="onOk(reds)"> 开始退费 </Button>
  </Bill>
  <template v-else>
    <Bill v-slot="{ reds, blues }: any" :cashId="cashId" class="mb-24px">
      <Button :disabled="!reds.length || reds.length !== blues.length" :loading="okLoading" type="primary" @click="onOk(reds)"> 开始退费 </Button>
    </Bill>
    <View line title="为什么必须进行全额退费？">
      <div class="error"><strong>医保政策要求：</strong>根据医保的规定，退费必须全额进行，以确保支付的准确性和透明度。</div>
      <div><strong>全额退费：</strong>全额退费是指对已开具的划价单进行的全额冲销操作。这意味着原划价单的全部金额被冲掉，但收费单仍然保留为原单。</div>
      <div><strong>数据保持：</strong>当进行全额退费时，会生成相应的退费单，确保原划价单的金额被完全冲销。</div>
      <div><strong>合规性与透明度：</strong>通过全额退费的方式，确保了操作的合规性，同时增强了财务记录的透明度，让用户能够清楚了解退款的处理方式。</div>
      <div><strong>用户体验：</strong>这种处理方式简化了退款流程，确保用户能够快速获得退款，同时维护了系统的高效运作。</div>
      <div><strong>部分退费的影响：</strong>如果需要进行部分退费，必须先进行全额退费操作，然后由医生重新开单。</div>
    </View>
  </template>
</template>
