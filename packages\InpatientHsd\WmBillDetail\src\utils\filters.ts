import dayjs from 'dayjs'

const filters = {
  dateFormatMDHM(dataStr: any, pattern = 'MM-DD HH:mm'): string {
    if (!dataStr) {
      return ''
    }
    return dayjs(dataStr).format(pattern)
  },
  formatAge(ageOfYears: number, ageOfDays: number): string {
    let year = ageOfYears ? ageOfYears : 0
    let month = ageOfDays ? Math.floor(ageOfDays / 30) : 0
    let day = ageOfDays ? ageOfDays : 0
    if (year > 6) {
      return year + '岁'
    } else if (year > 0) {
      return year + '岁' + month + '月'
    } else if (month > 0 && day > 0) {
      return month + '月' + day + '天'
    } else if (month > 0) {
      return month + '月'
    } else {
      return day + '天'
    }
  },
  isNumber(value: any): boolean {
    const regex = /^-?\d+(\.\d+)?$/
    return regex.test(value)
  },
  formatAmount(value: number | string | undefined): string {
    if (!value) {
      return ''
    }
    if (typeof value !== 'number' && typeof value !== 'string' || isNaN(Number(value))) {
      return '0'; // 如果值无效，返回0或其他默认值
    }

    const numberValue = Number(value);
    // 先将数字转换为字符串并保留两位小数（如果需要的话）
    const formattedString = numberValue.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ' ');

    // 如果需要添加货币符号或其他装饰，可以在这里添加
    let decoratedAmount = `¥ ${formattedString}`; // 示例：添加货币符号¥，并将小数点替换为逗号（可选）
    const integerPart = decoratedAmount.split('.')[1];
    if (!integerPart || integerPart === '00') {
      decoratedAmount = decoratedAmount.split('.')[0];
    }

    return decoratedAmount;
  },
}

export default filters
