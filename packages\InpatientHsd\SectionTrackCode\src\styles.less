// 病区绑定追溯码组件样式

.section-track-code-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
}

.section-track-code-header {
  flex-shrink: 0;
  background: #fafafa;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  border: 1px solid #d9d9d9;

  .compact-form {
    .ant-form-item {
      margin-bottom: 0;
      margin-right: 8px;
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  margin-bottom: 16px;
}

.table-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  flex-shrink: 0;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }
}

.table-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-inline {
  flex: 1;
  margin: 0 12px;
  display: flex;
  justify-content: center;
}

.table-count {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

// 搜索容器
.search-container {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.drug-list-container,
.patient-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  max-height: calc(100vh - 280px); // 最大高度，确保滚动条出现
}

.drug-list-container {
  max-height: calc(100vh - 260px); // 药品列表高度，搜索框已内联
  min-height: 400px; // 最小高度确保滚动条显示
}

.patient-list-container {
  max-height: calc(100vh - 240px); // 患者列表高度
  min-height: 400px; // 最小高度确保滚动条显示
}

// 药房追溯码区域
.pharmacy-codes-section {
  margin-bottom: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fafafa;

  .table-header {
    padding: 8px 12px;
    border-bottom: 1px solid #e8e8e8;
  }
}

.pharmacy-codes-list {
  max-height: 120px;
  overflow-y: auto;
  padding: 8px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

.pharmacy-code-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  margin-bottom: 4px;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 3px;
  font-size: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .code-text {
    font-family: monospace;
    flex: 1;
    margin-right: 8px;
  }

  .code-amount {
    color: #52c41a;
    font-weight: 500;
    margin-right: 8px;
    min-width: 40px;
  }
}

.drug-cards-wrapper,
.patients-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// 紧凑药品卡片样式
.drug-card-compact {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  position: relative; // 为右上角标签提供定位基准

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 1px 4px rgba(24, 144, 255, 0.2);
  }

  &--selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
  }

  &--completed {
    border-color: #52c41a;
    background-color: #f6ffed;
  }

  // 右上角药房标签
  &__pharmacy-corner {
    position: absolute;
    top: 4px;
    right: 4px;
    z-index: 1;
  }

  &__main {
    margin-bottom: 4px;
    padding-right: 60px; // 为右上角标签留出空间
  }

  &__name {
    font-weight: 500;
    color: #262626;
    line-height: 1.3;
    font-size: 13px;
  }

  &__producer {
    color: #8c8c8c;
    font-size: 11px;
    line-height: 1.2;
    margin-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &__info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  &__type {
    flex-shrink: 0;
  }

  &__amount {
    color: #1890ff;
    font-weight: 500;
    font-size: 12px;
  }

  &__bound {
    color: #52c41a;
    font-size: 11px;
  }

  &__status {
    flex-shrink: 0;
  }
}

// 患者网格布局
.patients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
  padding: 8px;
}

// 紧凑患者卡片样式
.patient-card-compact {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  min-height: 60px;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 1px 4px rgba(24, 144, 255, 0.2);
  }

  &--selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
  }

  &--completed {
    border-color: #52c41a;
    background-color: #f6ffed;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
  }

  &__name {
    font-weight: 500;
    color: #262626;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
  }

  &__amount {
    color: #1890ff;
    font-weight: 500;
    font-size: 11px;
    margin-left: 4px;
    flex-shrink: 0;
  }

  &__codes {
    margin-top: 4px;
  }

  &__code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 4px;
    background: #fafafa;
    border-radius: 2px;
    margin-bottom: 2px;
    font-size: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    .code-text {
      font-family: monospace;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 10px;
      line-height: 1.2;

      .code-prefix-consistent {
        color: #52c41a;
        font-weight: bold;
        background: rgba(82, 196, 26, 0.1);
        padding: 1px 2px;
        border-radius: 2px;
      }

      .code-prefix-inconsistent {
        color: #fa8c16;
        font-weight: bold;
        background: rgba(250, 140, 22, 0.1);
        padding: 1px 2px;
        border-radius: 2px;
      }

      .code-prefix-default {
        color: #666;
        font-weight: bold;
        background: rgba(102, 102, 102, 0.1);
        padding: 1px 2px;
        border-radius: 2px;
      }

      .code-suffix {
        color: #262626;
      }
    }

    .code-amount {
      color: #52c41a;
      font-weight: 500;
      margin: 0 6px;
      font-size: 10px;
    }

    .code-unbind {
      color: #ff4d4f;
      cursor: pointer;
      padding: 2px 6px;
      border-radius: 3px;
      font-weight: bold;
      font-size: 12px;
      line-height: 1;
      min-width: 20px;
      text-align: center;
      background: rgba(255, 77, 79, 0.1);

      &:hover {
        background: #fff2f0;
        color: #ff1f1f;
        transform: scale(1.1);
      }
    }

    .code-used {
      color: #52c41a;
      font-weight: bold;
      font-size: 12px;
      padding: 2px 6px;
    }
  }
}

// 追溯码录入区域样式
.section-track-code-input-area {
  flex-shrink: 0;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  margin-top: 12px;
  border: 1px solid #d9d9d9;
  /* 固定在底部 */
  order: 2;
}

.current-art-info {
  margin-bottom: 8px;
}

.track-code-form-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.track-code-form {
  display: flex;
  flex-wrap: wrap;
  flex: 1;

  :deep(.ant-form-item) {
    margin-right: 16px;
    margin-bottom: 0;
  }
}

.action-buttons-container {
  margin-left: 20px;
  display: flex;
  justify-content: flex-end;
  min-width: 320px;

  .button-group {
    margin-left: 10px;

    &:first-child {
      margin-left: 0;
    }
  }
}

// 动画效果
@keyframes slideOutAndDown {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-20px) translateY(0);
    opacity: 0.7;
  }
  100% {
    transform: translateX(-20px) translateY(100px);
    opacity: 0;
  }
}

// 一键分配按钮样式
.auto-allocate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  &:focus {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }
}

// 表格容器样式，确保表格能够正确填充空间
:deep(.ant-row) {
  flex: 1;
  display: flex;
  overflow: hidden;
}

:deep(.ant-col) {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    :deep(.ant-row) {
      flex-direction: column;
    }

    :deep(.ant-col) {
      width: 100% !important;
      margin-bottom: 16px;
    }
  }
}

// 滚动条样式
.drug-list-container,
.patient-list-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}
