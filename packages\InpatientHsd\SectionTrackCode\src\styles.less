// 病区绑定追溯码组件样式

.section-track-code-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
}

.section-track-code-header {
  flex-shrink: 0;
  background: #fafafa;
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
  border: 1px solid #d9d9d9;

  .ant-form-item {
    margin-bottom: 0;
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  margin-bottom: 16px;
}

.table-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  flex-shrink: 0;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
  }
}

.table-header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-count {
  font-size: 12px;
  color: #666;
}

.drug-list-container,
.patient-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

.drug-cards-wrapper,
.patients-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

// 药品卡片样式
.drug-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }

  &--selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
  }

  &--completed {
    border-color: #52c41a;
    background-color: #f6ffed;
    
    .drug-card__status {
      color: #52c41a;
      font-weight: bold;
    }

    // 完成动画效果
    &.drug-card--animating {
      animation: slideOutAndDown 0.8s ease-in-out forwards;
    }
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  &__title {
    font-weight: 500;
    color: #262626;
    line-height: 1.4;
  }

  &__status {
    font-size: 12px;
    color: #8c8c8c;
  }

  &__info {
    font-size: 12px;
    color: #595959;
    line-height: 1.5;
  }

  &__amount {
    margin-top: 8px;
    font-size: 14px;
    color: #1890ff;
    font-weight: 500;
  }
}

// 患者卡片样式
.patient-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }

  &--selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
  }

  &--completed {
    border-color: #52c41a;
    background-color: #f6ffed;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
  }

  &__name {
    font-weight: 500;
    color: #262626;
  }

  &__bed {
    font-size: 12px;
    color: #8c8c8c;
  }

  &__amount {
    font-size: 14px;
    color: #1890ff;
    font-weight: 500;
  }

  &__track-codes {
    margin-top: 8px;
  }

  &__track-code-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    background: #fafafa;
    border-radius: 4px;
    margin-bottom: 4px;
    font-size: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .track-code {
      font-family: monospace;
      color: #1890ff;
    }

    .amount {
      color: #52c41a;
      font-weight: 500;
    }

    .unbind-btn {
      color: #ff4d4f;
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 2px;

      &:hover {
        background: #fff2f0;
      }
    }
  }
}

// 追溯码录入区域样式
.track-code-input-area {
  flex-shrink: 0;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  border: 1px solid #d9d9d9;
  /* 固定在底部 */
  order: 2;
}

.current-art-info {
  margin-bottom: 8px;
}

.track-code-form-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.track-code-form {
  display: flex;
  flex-wrap: wrap;
  flex: 1;

  :deep(.ant-form-item) {
    margin-right: 16px;
    margin-bottom: 0;
  }
}

.action-buttons-container {
  margin-left: 20px;
  display: flex;
  justify-content: flex-end;
  min-width: 240px;

  .button-group {
    margin-left: 10px;

    &:first-child {
      margin-left: 0;
    }
  }
}

// 动画效果
@keyframes slideOutAndDown {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-20px) translateY(0);
    opacity: 0.7;
  }
  100% {
    transform: translateX(-20px) translateY(100px);
    opacity: 0;
  }
}

// 一键分配按钮样式
.auto-allocate-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  &:focus {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }
}

// 表格容器样式，确保表格能够正确填充空间
:deep(.ant-row) {
  flex: 1;
  display: flex;
  overflow: hidden;
}

:deep(.ant-col) {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    :deep(.ant-row) {
      flex-direction: column;
    }

    :deep(.ant-col) {
      width: 100% !important;
      margin-bottom: 16px;
    }
  }
}

// 滚动条样式
.drug-list-container,
.patient-list-container {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}
