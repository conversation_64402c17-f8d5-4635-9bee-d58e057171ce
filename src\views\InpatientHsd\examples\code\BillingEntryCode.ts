import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { BillingEntryModal, BillingEntry } from '@mh-inpatient-hsd/billing-entry'
import '@mh-inpatient-hsd/billing-entry/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/billing-entry": "^1.0.1",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 基础用法 - 内嵌模式
export const basicUsage = wrapCodeExample(`<template>
  <billing-entry 
    ref="billingEntryRef" 
    :section-id="sectionId" 
    :visit-id="visitInfo.visitId" 
    :gender-id="visitInfo.genderId" 
    :default-freq-code="'st'" 
    @oe-change="handleOeChange" 
  />
</template>

<script setup>
import { BillingEntry } from '@mh-inpatient-hsd/billing-entry'
import '@mh-inpatient-hsd/billing-entry/index.css'
import { ref } from 'vue'

const billingEntryRef = ref()

// 模拟数据
const visitInfo = ref({
  visitId: 123456,
  genderId: 1
})

const sectionId = ref(40)

// 医嘱变更回调
const handleOeChange = () => {
  console.log('医嘱已更新')
}
</script>`)

// 弹窗模式
export const modalUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleBillingEntryModal">打开计费医嘱弹窗</Button>
  
  <billing-entry-modal
    ref="billingEntryModalRef"
    :in-operating="-1"
    :section-id="sectionId"
    :gender-id="visitInfo.genderId"
    :default-freq-code="'st'"
    @oe-change="handleOeChange"
  />
</template>

<script setup>
import { BillingEntryModal } from '@mh-inpatient-hsd/billing-entry'
import '@mh-inpatient-hsd/billing-entry/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const billingEntryModalRef = ref()

// 模拟数据
const visitInfo = ref({
  visitId: 123456,
  genderId: 1
})

const sectionId = ref(40)

// 打开计费医嘱弹窗
const handleVisibleBillingEntryModal = () => {
  billingEntryModalRef.value.open(visitInfo.value.visitId)
}

// 医嘱变更回调
const handleOeChange = () => {
  message.success('医嘱已更新')
}
</script>`)




