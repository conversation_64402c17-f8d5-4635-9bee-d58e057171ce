<script lang="ts" setup>
import { appConfig } from '@idmy/core'
import { Breadcrumb, BreadcrumbItem, Layout, LayoutContent, LayoutHeader, LayoutSider } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import HeaderRight from '../HeaderRight/index.vue'
import { refreshKey } from '../index'
import Menu from '../Menu/index.vue'
import OrgSelect from '../Org/OrgSelect.vue'
import Tabs from '../Tabs/index.vue'

defineProps({
  showSider: { type: Boolean, default: true },
  closeable: { type: Boolean, default: true },
  contentClass: { type: String, default: 'p-16px m-8px' },
  nav: { type: Object as PropType<VNode> },
})

const route = useRoute()

// 侧边栏折叠状态
const collapsed = ref(false)

// 生成面包屑数据
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    title: item.meta.title,
    path: item.path,
  }))
})
</script>
<template>
  <Layout class="layout-container">
    <!-- 顶部导航 -->
    <LayoutHeader class="layout-header">
      <div class="logo">
        <h1>{{ appConfig.app.title }}</h1>
      </div>
      <OrgSelect />
      <component :is="nav" />
      <div class="header-right">
        <HeaderRight />
      </div>
    </LayoutHeader>

    <Layout class="layout-main">
      <!-- 左侧菜单 -->
      <LayoutSider v-model:collapsed="collapsed" :width="220" class="layout-sider" collapsible v-if="showSider">
        <Menu :collapsed="collapsed" />
      </LayoutSider>

      <!-- 主内容区 -->
      <Layout class="layout-content-wrapper">
        <!-- 导航区域 -->
        <div class="layout-nav">
          <!-- 标签页 -->
          <Tabs class="layout-tabs" :closeable="closeable" />

          <!-- 面包屑 -->
          <Breadcrumb class="layout-breadcrumb">
            <BreadcrumbItem v-for="item in breadcrumbs.reverse()" :key="item.path">{{ item.title }}</BreadcrumbItem>
            <BreadcrumbItem key="index">{{ appConfig.app.title }}</BreadcrumbItem>
          </Breadcrumb>
        </div>
        <!-- 内容区 -->
        <LayoutContent class="layout-content" :style="$route.meta.style" :class="contentClass">
          <router-view :key="refreshKey" />
        </LayoutContent>
      </Layout>
    </Layout>
  </Layout>
</template>

<style lang="less" scoped>
.layout-container {
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
}

.layout-header {
  background: var(--primary-color) !important;
  height: 44px !important;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .logo {
    display: flex;
    align-items: center;
    padding-left: 24px;

    h1 {
      margin: 0;
      color: #fff;
      font-weight: 600;
      font-size: 18px;
    }
  }

  .header-right {
    flex: 1;
    height: 100%;
    display: flex;
    flex-flow: row-reverse;
  }
}

.layout-main {
  height: calc(100vh - 44px);
}

.layout-sider {
  background: #fff;
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);

  :deep(.ant-layout-sider-children) {
    display: flex;
    flex-direction: column;
  }

  :deep(.ant-layout-sider-trigger) {
    background: #fff;
    color: #333;
    border-top: 1px solid #f0f0f0;

    &:hover {
      color: var(--primary-color);
    }
  }
}

.layout-content-wrapper {
  background: #eee;
  padding: 0;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.layout-nav {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 32px;
  padding: 0;
  background: #fff;
  border-bottom: none;
  flex-shrink: 0;
}

.layout-tabs {
  flex: 1;
  margin-bottom: 0;
  height: 100%;
}

.layout-breadcrumb {
  margin-bottom: 0;
  flex-shrink: 0;
  padding: 0 12px;
  height: 32px;
  line-height: 32px;
  background: #fff;

  :deep(.ant-breadcrumb-link) {
    color: #666;
    font-size: 13px;
  }

  :deep(.ant-breadcrumb-separator) {
    color: #999;
  }
}

.layout-content {
  flex: 1;
  background: #fff;
  overflow: auto;
}
</style>
