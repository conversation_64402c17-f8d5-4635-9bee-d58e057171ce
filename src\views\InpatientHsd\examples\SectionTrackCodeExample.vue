<!--病区绑定追溯码组件示例-->
<template>
  <div class="section-track-code-example">
    <div class="example-header">
      <h1>病区绑定追溯码组件示例</h1>
      <p class="description">
        病区绑定追溯码组件用于处理病区药品追溯码的绑定和分配，支持病区自行扫码绑定和药房预绑定追溯码分配两种模式。
      </p>
    </div>

    <div class="example-content">
      <div class="example-section">
        <h2>基本用法</h2>
        <p>传入病区ID，组件会自动加载该病区的药品和患者数据。</p>
        
        <div class="example-demo">
          <SectionTrackCode
            :sectionId="sectionId"
            @success="handleSuccess"
            @cancel="handleCancel"
          />
        </div>
      </div>

      <div class="example-section">
        <h2>参数配置</h2>
        <div class="param-config">
          <a-form layout="inline">
            <a-form-item label="病区ID">
              <a-input-number
                v-model:value="sectionId"
                :min="1"
                :max="999"
                placeholder="请输入病区ID"
                style="width: 120px;"
              />
            </a-form-item>
            <a-form-item>
              <a-button type="primary" @click="updateSectionId">
                更新病区
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>

      <div class="example-section">
        <h2>功能特性</h2>
        <div class="features">
          <a-row :gutter="16">
            <a-col :span="8">
              <a-card title="双重追溯码来源" size="small">
                <p>支持病区自行扫码绑定和药房预绑定追溯码分配两种模式</p>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card title="智能一键分配" size="small">
                <p>药房预绑定的拆零追溯码支持一键智能分配给患者</p>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card title="可视化界面" size="small">
                <p>左侧药品卡片，右侧患者列表，直观展示绑定状态</p>
              </a-card>
            </a-col>
          </a-row>
          <a-row :gutter="16" style="margin-top: 16px;">
            <a-col :span="8">
              <a-card title="实时状态更新" size="small">
                <p>药品和患者完成状态实时更新，支持动画效果</p>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card title="追溯码验证" size="small">
                <p>支持19-27位追溯码验证，自动识别无效码</p>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card title="解绑保护" size="small">
                <p>已使用的追溯码不允许解绑，保护数据完整性</p>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </div>

      <div class="example-section">
        <h2>使用说明</h2>
        <div class="usage-guide">
          <a-steps direction="vertical" size="small">
            <a-step title="选择日期" description="在上方日期选择器中选择要处理的业务日期" />
            <a-step title="查询数据" description="点击查询按钮加载该日期的药品和患者数据" />
            <a-step title="选择药品" description="在左侧药品列表中点击选择要处理的药品" />
            <a-step title="选择患者" description="在右侧患者列表中点击选择要绑定追溯码的患者" />
            <a-step title="录入追溯码" description="在下方输入框中扫描或手动输入追溯码" />
            <a-step title="一键分配" description="对于药房预绑定的追溯码，可使用一键分配功能" />
          </a-steps>
        </div>
      </div>

      <div class="example-section">
        <h2>API 数据结构</h2>
        <div class="api-structure">
          <a-collapse>
            <a-collapse-panel key="1" header="sectionFeeTrackCodeSummaryApi 返回数据结构">
              <pre><code>{{apiDataStructure}}</code></pre>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>

      <div class="example-section">
        <h2>事件日志</h2>
        <div class="event-log">
          <a-textarea
            v-model:value="eventLog"
            :rows="6"
            readonly
            placeholder="事件日志将在这里显示..."
          />
          <div style="margin-top: 8px;">
            <a-button size="small" @click="clearLog">清空日志</a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import SectionTrackCode from '@mh-inpatient-hsd/section-track-code'

// 响应式数据
const sectionId = ref(38)
const eventLog = ref('')

// API数据结构示例
const apiDataStructure = `{
  "artSummaryList": [
    {
      "artId": 1063936,
      "keyStr": "1063936_1",
      "artName": "注射用头孢曲松钠",
      "artSpec": " 1g*10瓶",
      "producer": "国药集团致君(深圳)制药有限公司",
      "packCells": 10,
      "packUnit": "盒",
      "cellUnit": "瓶",
      "unitType": 1,
      "isDisassembled": 0,
      "totalAmount": 5,
      "unitName": "瓶",
      "visitSummaryList": [
        {
          "visitId": "86234",
          "patientName": "张三",
          "bedNo": "9",
          "feeDetailList": [
            {
              "keyStr": "1929715815310036992-4",
              "execSeqid": "1929715815310036992",
              "lineNo": 4,
              "visitId": 86234,
              "oeNo": 80,
              "artId": 1063936,
              "total": 3.0,
              "unitType": 1,
              "bseqid": "328360",
              "billLineNo": 10,
              "isDisassembled": 0,
              "usedTrackCodeDetails": [
                {
                  "trackCode": "83794211263916924482",
                  "usedAmount": 3
                }
              ]
            }
          ]
        }
      ]
    }
  ],
  "pharmacyTrackCodeSums": [
    {
      "artId": 1063936,
      "trackCode": "80026021263916924484",
      "totalPacks": 0,
      "totalCells": 5.0,
      "usedTotalPacks": 0,
      "usedTotalCells": 0,
      "curTotalPacks": 0,
      "curTotalCells": 5.0
    }
  ]
}`

// 方法
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  eventLog.value += `[${timestamp}] ${message}\n`
}

const handleSuccess = (data: any) => {
  message.success('追溯码绑定成功')
  addLog(`追溯码绑定成功: ${JSON.stringify(data)}`)
}

const handleCancel = () => {
  message.info('取消操作')
  addLog('用户取消操作')
}

const updateSectionId = () => {
  message.info(`病区ID已更新为: ${sectionId.value}`)
  addLog(`病区ID更新为: ${sectionId.value}`)
}

const clearLog = () => {
  eventLog.value = ''
  message.info('日志已清空')
}

// 初始化日志
addLog('病区绑定追溯码组件示例页面已加载')
</script>

<style scoped>
.section-track-code-example {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.example-header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.example-header h1 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 28px;
  font-weight: 600;
}

.description {
  margin: 0;
  color: #595959;
  font-size: 16px;
  line-height: 1.6;
}

.example-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.example-section {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.example-section h2 {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 20px;
  font-weight: 500;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.example-demo {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.param-config {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.features .ant-card {
  height: 100%;
}

.features .ant-card-body {
  padding: 16px;
}

.features .ant-card-head-title {
  font-size: 14px;
  font-weight: 500;
}

.features p {
  margin: 0;
  color: #595959;
  font-size: 13px;
  line-height: 1.5;
}

.usage-guide {
  background: #f9f9f9;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.api-structure pre {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
}

.event-log {
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.event-log .ant-input {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

@media (max-width: 768px) {
  .section-track-code-example {
    padding: 16px;
  }
  
  .example-header,
  .example-section {
    padding: 16px;
  }
  
  .example-header h1 {
    font-size: 24px;
  }
  
  .description {
    font-size: 14px;
  }
}
</style>
