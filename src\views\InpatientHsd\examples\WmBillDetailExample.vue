<script setup lang="ts">
import { index as WmBillDetail } from '@mh-inpatient-hsd/wm-bill-detail'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from './code/WmBillDetailCode'

const { Title, Paragraph } = Typography

// 引用
const wmBillDetailRef = ref()

// 模拟数据
const sectionId = 40
const artId = 1044295
const startDate = '2025-03-01'
const endDate = '2025-03-31'

// 打开病区库存台账弹窗
const handleVisibleWmBillDetail = () => {
  wmBillDetailRef.value.open(sectionId, artId, startDate, endDate)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>

<template>
  <Card title="病区库存台账组件" class="mb-16px">
    <div mb-16px>
      <Title :level="4">病区库存台账组件</Title>
      <Paragraph>用于查看病区物品的库存变动记录。</Paragraph>

      <Button type="primary" @click="handleVisibleWmBillDetail">打开病区库存台账弹窗</Button>
      <WmBillDetail
        ref="wmBillDetailRef"
        @close="handleClose"
      />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
