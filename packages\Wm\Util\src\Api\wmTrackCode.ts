import { http } from '@idmy/core'

/**
 * 处方追溯码相关API
 */
export function trackCodePageApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/page', params, { appKey: 'wm' })
}
export function trackCodeListByWbSeqIdsApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/findLsByWbSeqIds', params, { appKey: 'wm' })
}
export function trackCodeAddCodeApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/addCode', params, { appKey: 'wm', ignoreError: true })
}
export function trackCodeDelCodeApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/delCode', params, { appKey: 'wm', ignoreError: true })
}
export function trackCodeDelCodeByWbSeqIdApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/delCodeByWbSeqId', params, { appKey: 'wm', ignoreError: true })
}
export function trackCodeAllConfirmByWbSeqIdApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/allConfirmByWbSeqId', params, { appKey: 'wm', ignoreError: true })
}
export function trackCodeSaveBillApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/saveBill', params, { appKey: 'wm' })
}
export function trackCodeSubmitBillApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/submitBill', params, { appKey: 'wm' })
}
export function trackCodeSetNoTrackCodeApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/setNoTrackCode', params, { appKey: 'wm' })
}
export function trackCodeClearNoTrackCodeApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/clearNoTrackCode', params, { appKey: 'wm' })
}
export function trackCodeSetDisassembledApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/setDisassembled', params, { appKey: 'wm' })
}
export function trackCodeClearDisassembledApi(params: any) {
  return http.post('/clinics_wm/wmtrackcode/clearDisassembled', params, { appKey: 'wm' })
}
