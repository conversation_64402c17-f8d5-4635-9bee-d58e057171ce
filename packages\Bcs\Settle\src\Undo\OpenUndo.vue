<script lang="ts" setup>
import { Data } from '@idmy/core'
import Undo from './index.vue'

const { data } = defineProps({
  data: { type: Object as PropType<Data>, required: true },
})

const emit = defineEmits(['ok'])

const [openUndo] = useLoading(() =>
  Modal.open({
    component: Undo,
    width: 1,
    title: '重新结算',
    props: { cashId: data.cashId },
    onClose: (isOk: boolean) => {
      isOk && emit('ok')
    },
  })
)
</script>
<template>
  <template v-if="!data.checkId && data.amount > 0">
    <a v-if="data.status === 'OK' && data.actionType === 'BLUE'" class="color-primary" @click="openUndo">重新结算</a>
  </template>
</template>
