<script setup lang="ts">
import { index as VisitForm } from '@mh-inpatient-hsd/visit-form'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from './code/VisitFormCode'

const { Title, Paragraph } = Typography

// 引用
const visitFormRef = ref()

// 模拟数据
const visitId = 84503

// 打开患者信息表单弹窗
const handleVisibleVisitForm = () => {
  visitFormRef.value.open(visitId)
}

// 提交回调
const handleSubmit = (data) => {
  message.success('提交成功')
  console.log('提交数据:', data)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>

<template>
  <Card title="患者信息表单组件" class="mb-16px">
    <div mb-16px>
      <Title :level="4">患者信息表单组件</Title>
      <Paragraph>用于编辑患者基本信息。</Paragraph>
      
      <Button type="primary" @click="handleVisibleVisitForm">打开患者信息表单弹窗</Button>
      <VisitForm 
        ref="visitFormRef" 
        @submit="handleSubmit"
        @close="handleClose"
      />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
