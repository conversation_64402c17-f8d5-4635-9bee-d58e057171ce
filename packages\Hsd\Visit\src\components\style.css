.ant-card:after,.ant-card:before {
  content: " ";
  display: table;
  clear: both
}
.ant-card {
  background-color: #ffffff;
  :deep(.ant-card-body) {
    background-color: #ffffff;
    padding: 10px 10px;
  }
  &:hover {
    .but_operation {
      display: block;
    }
  }
}
.custom-doubleline {
  margin-top: 4px;
  padding-top: 1px;
  height: 1px;
  border-top: 2px solid #000000;
}
.bline-dotted{
  background-color: #fff;
  padding-bottom: 2px;
  border-top: 4px solid #818181 !important;
  border-bottom: 1px solid #818181 !important;
  margin-bottom: 10px;
  margin-top:10px;
}
.bline-solid{
  border-bottom:1px solid #818181;
  margin:5px 0 10px 0;
}
.custom-xyzz-tags{
  display: inline-block;
  background-color: #08979c;
  color: #ffffff;
  border-radius: 100%;
  width: 24px;
  height: 24px;
  text-align: center;
}
.custom-zyzz-tags{
  display: inline-block;
  background-color: #9e6b31;
  color: #ffffff;
  border-radius: 100%;
  width: 24px;
  height: 24px;
  text-align: center;
}
.zd-con{
  display: block;
  width: 100%;
}
.zd-main{
  color: var(--pro-ant-color-primary);
  font-size: 14px;
}
.alink2{
  display: inline-block;
  color: #ffffff;
  font-size: 12px;
  background-color: #A6A6A6;
  border-radius: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  vertical-align: middle;
  &:hover{
    color: #ffffff;
    background-color: #cccccc;
  }
}
.custom-typename{
  text-align: center;
  font-size:12px;
  color: #000000;
  border-radius: 2px;
  line-height: 26px;
}
.custom-but {
  .but_operation {
    display: none;
    text-align: right;
    button {
      margin-left: 10px;
    }
  }
}

.custom-ref-content {
  .custom-ref-btn {
    display: none;
  }
  &:hover {
    .custom-ref-btn {
      display: block;
    }
  }
}

.custom-diag-xh {
  padding-right: 5px;
}

.del-zd {
  text-decoration: line-through;
  color: #999999;
} 