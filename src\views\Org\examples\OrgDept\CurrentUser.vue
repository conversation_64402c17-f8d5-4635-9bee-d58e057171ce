<script setup lang="ts">
import { OrgDept } from '@mh-hip/org'
import { Card, Typography, Divider } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { currentUserUsage, importCode, packageJsonCode } from '../code/OrgDeptCode'

const { Title } = Typography

// 不指定组织机构ID的部门选择
const currentUserDeptId = ref<number>()
</script>

<template>
  <Card title="不指定组织机构ID，使用当前用户token中的orgId" class="mb-16px">
    <div mb-16px>
      <Title :level="4">使用当前用户token中的orgId</Title>
      <OrgDept v-model="currentUserDeptId" w-200px />
      <div mt-8px>选中的部门ID: {{ currentUserDeptId }}</div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="currentUserUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
