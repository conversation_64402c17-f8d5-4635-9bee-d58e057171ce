export function sectionArtPageApi(params: any): Promise<any> {
  return http.post('/hsd/sectionArt/page', params, { appKey: 'inpatientHsd' })
}

export function sectionArtPageConsumableApi(params: any): Promise<any> {
  return http.post('/hsd/sectionArt/pageConsumable', params, { appKey: 'inpatientHsd' })
}

export function sectionConsumableLsApi(params: any): Promise<any> {
  return http.post('/hsd/sectionArt/sectionConsumableLs', params, { appKey: 'inpatientHsd' })
}

export function drugReqConsumableApi(params: any): Promise<any> {
  return http.post('/hsd/sectionArt/drugReqConsumable', params, { appKey: 'inpatientHsd' })
}

export function sectionStockAndWmStockApi(params: any): Promise<any> {
  return http.post('/hsd/sectionArt/sectionStockAndWmStock', params, { appKey: 'inpatientHsd' })
}
