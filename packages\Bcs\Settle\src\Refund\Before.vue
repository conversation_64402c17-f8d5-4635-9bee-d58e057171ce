<script lang="ts" setup>
import { Data, Message, useLoading } from '@idmy/core'
import { getCash, refundBefore } from '@mh-bcs/util'
import { Alert } from 'ant-design-vue'
import Index from './index.vue'

const { blueCashId, redBillIds } = defineProps({
  blueCashId: { type: Number as PropType<number>, required: true },
  redBillIds: { type: Array as PropType<number[]>, required: true },
})

const error = ref()
const redCash = ref<Data>({})
useLoading(async () => {
  const msg = Message.loading({ content: '正在创建退费单……', duration: 0 })
  try {
    error.value = null
    const redCashId = await refundBefore(blueCashId, redBillIds)
    redCash.value = await getCash(redCashId)
  } catch (e) {
    error.value = e
  } finally {
    msg()
  }
}, true)
</script>

<template>
  <Alert v-if="error" :message="error" showIcon type="error" />
  <Index v-else-if="redCash.cashId" :redCash="redCash" />
</template>
