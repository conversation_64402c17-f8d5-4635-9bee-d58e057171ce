<script setup lang="ts">
import { WmCountCreate, WmCountEdit } from '@mh-wm/count'
import { Card, Typography, Divider, Space, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { createUsage, editUsage, importCode, packageJsonCode } from '../code/CountBasicCode'

const { Title, Paragraph } = Typography

// 引用
const countEditRef = ref()

// 创建盘点单成功回调
const handleCreateSuccess = (countId: number) => {
  message.success(`创建盘点单成功，盘点单ID: ${countId}`)
  // 打开录入盘点结果对话框
  countEditRef.value.open(countId)
}

// 取消创建盘点单回调
const handleCreateCancel = () => {
  console.log('取消创建盘点单')
}

// 保存盘点结果成功回调
const handleEditSuccess = (countId: number) => {
  message.success(`保存盘点结果成功，盘点单ID: ${countId}`)
}

// 取消录入盘点结果回调
const handleEditCancel = () => {
  console.log('取消录入盘点结果')
}

// 打开录入盘点结果对话框
const openEdit = () => {
  // 这里使用一个示例ID，实际使用时应该传入真实的盘点单ID
  countEditRef.value.open(123)
}
</script>

<template>
  <Card title="基础用法" class="mb-16px">
    <div mb-16px>
      <Title :level="4">创建盘点单</Title>
      <Paragraph> 点击按钮打开创建盘点单对话框，可以选择盘点仓库、盘点人，添加盘点品种。 </Paragraph>

      <Space>
        <WmCountCreate buttonText="新建盘点" buttonType="primary" :onlyShowWithStock="true" @success="handleCreateSuccess" @cancel="handleCreateCancel" />
      </Space>

      <Divider />
      <CodeDemoVue :usage="createUsage" :importCode="importCode" :packageJson="packageJsonCode" />
    </div>

    <div mb-16px>
      <Title :level="4">录入盘点结果</Title>
      <Paragraph> 点击按钮打开录入盘点结果对话框，可以录入盘点结果。 </Paragraph>

      <Space>
        <WmCountEdit buttonText="录入盘点结果" buttonType="primary" @success="handleEditSuccess" @cancel="handleEditCancel" ref="countEditRef" />

        <Button @click="openEdit">打开录入（示例ID: 123）</Button>
      </Space>

      <Divider />
      <CodeDemoVue :usage="editUsage" :importCode="importCode" :packageJson="packageJsonCode" />
    </div>
  </Card>
</template>
