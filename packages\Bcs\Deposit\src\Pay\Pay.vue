<script setup lang="ts">
import { Modal } from '@idmy/core'
import { SelfFeePaymentType } from '@mh-bcs/pay'
import { chargeInjectKey } from '@mh-bcs/util'
import { isNil } from 'lodash-es'

const { accountId, visitId } = defineProps({
  accountId: { type: Number, required: true },
  visitId: { type: Number, required: true },
})

const isZero = ref(false)
const paying = ref(false)
const paymentType = ref('CASH')

const onLoad = (cashId?: number) => isNil(cashId) || Modal.ok(cashId)

provide(
  chargeInjectKey,
  reactive({
    cashType: 'DEPOSIT',
    isZero,
    onLoad,
    paying,
    paymentType,
    accountId,
    visitId,
  })
)
</script>

<template>
  <SelfFeePaymentType title="预交金充值方式" />
</template>
