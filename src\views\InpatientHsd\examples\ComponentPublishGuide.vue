<script setup lang="ts">
import { Card, Typography, Divider, Table } from 'ant-design-vue'

const { Title, Paragraph, Text } = Typography

// 组件打包指令数据
const columns = [
  {
    title: '组件名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '组件描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '打包指令',
    dataIndex: 'command',
    key: 'command',
  },
  {
    title: '包名',
    dataIndex: 'package',
    key: 'package',
  },
]

const data = [
  {
    key: '1',
    name: 'OeApply',
    description: '医嘱核对',
    command: 'pnpm publish:component InpatientHsd/OeApply',
    package: '@mh-inpatient-hsd/oe-apply',
  },
  {
    key: '2',
    name: 'OeExec',
    description: '医嘱执行',
    command: 'pnpm publish:component InpatientHsd/OeExec',
    package: '@mh-inpatient-hsd/oe-exec',
  },
  {
    key: '3',
    name: 'BillingEntry',
    description: '计费医嘱',
    command: 'pnpm publish:component InpatientHsd/BillingEntry',
    package: '@mh-inpatient-hsd/billing-entry',
  },
  {
    key: '4',
    name: 'OeFeeChange',
    description: '医嘱费用变更',
    command: 'pnpm publish:component InpatientHsd/OeFeeChange',
    package: '@mh-inpatient-hsd/oe-fee-change',
  },
  {
    key: '5',
    name: 'OrderFeeChange',
    description: '医嘱执行费用变更',
    command: 'pnpm publish:component InpatientHsd/OrderFeeChange',
    package: '@mh-inpatient-hsd/order-fee-change',
  },
  {
    key: '6',
    name: 'VisitInfo',
    description: '诊疗信息展示',
    command: 'pnpm publish:component InpatientHsd/VisitInfo',
    package: '@mh-inpatient-hsd/visit-info',
  },
  {
    key: '7',
    name: 'VisitForm',
    description: '诊疗信息调整',
    command: 'pnpm publish:component InpatientHsd/VisitForm',
    package: '@mh-inpatient-hsd/visit-form',
  },
  {
    key: '8',
    name: 'Selector',
    description: '选择器组件',
    command: 'pnpm publish:component InpatientHsd/Selector',
    package: '@mh-inpatient-hsd/selector',
  },
  {
    key: '9',
    name: 'SectionRouteConsumable',
    description: '病区给药途径绑定',
    command: 'pnpm publish:component InpatientHsd/SectionRouteConsumable',
    package: '@mh-inpatient-hsd/section-route-consumable',
  },
  {
    key: '10',
    name: 'WmBillDetail',
    description: '病区库存台账',
    command: 'pnpm publish:component InpatientHsd/WmBillDetail',
    package: '@mh-inpatient-hsd/wm-bill-detail',
  },
  {
    key: '11',
    name: 'Util',
    description: '工具类',
    command: 'pnpm publish:component InpatientHsd/Util',
    package: '@mh-inpatient-hsd/util',
  },
  {
    key: '12',
    name: '所有组件',
    description: '发布所有组件',
    command: 'pnpm publish:component InpatientHsd',
    package: '所有 @mh-inpatient-hsd/* 包',
  },
]
</script>

<template>
  <div class="component-publish-guide">
    <Card title="住院组件打包指令" class="mb-16px">
      <Title :level="3">住院组件打包指令</Title>
      <Paragraph>
        本页面提供了住院组件的打包指令，可以通过这些指令发布组件到npm仓库。
      </Paragraph>
      
      <Divider />
      
      <Title :level="4">组件打包指令列表</Title>
      <Table :columns="columns" :data-source="data" :pagination="false" />
      
      <Divider />
      
      <Title :level="4">使用说明</Title>
      <Paragraph>
        <Text strong>打包单个组件：</Text> 使用对应的打包指令，例如 <Text code>pnpm publish:component InpatientHsd/OeApply</Text>
      </Paragraph>
      <Paragraph>
        <Text strong>打包所有组件：</Text> 使用 <Text code>pnpm publish:component InpatientHsd</Text>
      </Paragraph>
      <Paragraph>
        <Text strong>注意事项：</Text>
        <ul>
          <li>打包前请确保组件代码已经提交到git仓库</li>
          <li>打包后会自动更新package.json中的版本号</li>
          <li>打包后会自动发布到npm仓库</li>
        </ul>
      </Paragraph>
    </Card>
  </div>
</template>

<style scoped>
.component-publish-guide {
  padding: 20px;
}
</style>
