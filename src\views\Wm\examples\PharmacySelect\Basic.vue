<script setup lang="ts">
import { PharmacySelect } from '@mh-wm/pharmacy'
import { Card, Typography, Divider } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from '../code/PharmacySelectCode'

const { Title } = Typography

// 选中的药房ID
const pharmacyId = ref<string>()
</script>

<template>
  <Card title="基础用法 - 显示所有药房" class="mb-16px">
    <div mb-16px>
      <Title :level="4">基础用法</Title>
      <PharmacySelect v-model="pharmacyId" w-200px />
      <div mt-8px>选中的药房ID: {{ pharmacyId }}</div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
