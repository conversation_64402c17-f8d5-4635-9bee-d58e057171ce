<script setup lang="ts">
// 检验报告
import { Drawer, Descriptions, DescriptionsItem, Table, Button, Empty } from 'ant-design-vue'
import { findClinicalReportApi, numericFormat, dateFormatH } from '@mh-hsd/util'

const emit = defineEmits(['close'])

const open = shallowRef<boolean>(false)
const reportLs = shallowRef<any>({})

const columns = [
  {
    title: '检验项目',
    dataIndex: 'indicatorName'
  },
  {
    title: '结果',
    dataIndex: 'resultVal',
    align: 'center'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    align: 'center'
  },
  {
    title: '参考值',
    dataIndex: 'refDesc',
    align: 'center'
  }
]

const init = async (orderId: number, orgId: number) => {
  open.value = true
  reportLs.value = []
  try {
    const res: any = await findClinicalReportApi(orderId, orgId)
    if (res.code === 0) {
      reportLs.value = res.data
    }
  } catch (e) {
    console.log(e)
  }
}

function onClose () {
  open.value = false
  emit('close')
}

defineExpose({
  init
})
</script>

<template>
  <Drawer v-model:open="open" title="检验报告详情" width="900" destroyOnClose :bodyStyle="{ padding: 0, overflowX: 'hidden' }">
    <template #extra>
      <Button @click="onClose">关闭</Button>
    </template>
    <div v-if="reportLs.length > 0" class="p-4">
      <div v-for="report in reportLs">
        <template v-if="report.pdfPathLs && report.pdfPathLs.length > 0">
          <div v-for="(pdfPath, ind) in report.pdfPathLs" :key="ind" class="w-full h-full">
            <iframe :src="pdfPath" border="0" w-full :height="750"/>
          </div>
        </template>
        <template v-else>
          <Descriptions :column="4" bordered>
            <DescriptionsItem label="检验员">{{ report.technicianName }}</DescriptionsItem>
            <DescriptionsItem label="检验日期">{{ numericFormat(report.testDate) }}</DescriptionsItem>
            <DescriptionsItem label="审核员">{{ report.qcName }}</DescriptionsItem>
            <DescriptionsItem label="审核日期">{{ dateFormatH(report.timeChecked) }}</DescriptionsItem>
          </Descriptions>
          <Table
              :row-key="(record: any) => record.diagCode"
              ref="table"
              :columns="columns"
              :data-source="report.results"
              deepWatchDataSource>
            <template #bodyCell="{ index, column }">
              <template v-if="column.dataIndex === 'serial'">
                <div>{{ index + 1 }}</div>
              </template>
            </template>
          </Table>
        </template>
      </div>
    </div>
    <div v-else class="p-4">
      <Empty description="暂无数据" />
    </div>
  </Drawer>
</template>

<style scoped lang="scss">

</style>
