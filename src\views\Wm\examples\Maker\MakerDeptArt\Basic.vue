<script setup lang="ts">
import { Card, Typography, Divider, Button, message, Table, Space } from 'ant-design-vue'
import { ref, h, computed } from 'vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'
import { basicUsage, importCode, packageJsonCode, publishCommands, buildProcess } from '@/views/Wm/examples/code/MakerDeptArtCode'

const { Title, Paragraph, Text } = Typography

// 部门编码
const deptCode = ref('000013')

// 组件引用
const makerDeptArtRef = ref()

// 已添加的品种列表
const addedItems = ref([])

// 批量选择
const selectedRowKeys = ref<string[]>([])
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys
}

// 添加品种回调
const handleAddArt = (artData: any) => {
  console.log('添加品种成功，表单数据：', artData)

  // 新增记录
  const newItem = {
    id: Date.now().toString(),
    ...artData,
    // 从artData中提取品种信息
    artName: artData.artName || '',
    artSpec: artData.artSpec || '',
    producer: artData.producer || '',
  }

  addedItems.value.push(newItem)
  message.success(`成功添加品种: ${artData.artName}`)
}

// 删除记录
const deleteRecord = (record: any) => {
  const index = addedItems.value.findIndex((item: any) => item.id === record.id)
  if (index !== -1) {
    addedItems.value.splice(index, 1)
    message.success('删除成功')
  }
}

// 复制记录
const copyRecord = (record: any) => {
  const copiedRecord = {
    ...record,
    id: Date.now().toString(), // 生成新的ID
  }

  addedItems.value.push(copiedRecord)
  message.success('记录复制成功')
}

// 清空所有数据
const clearAll = () => {
  addedItems.value = []
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.clearForm()
  }
  selectedRowKeys.value = []
  message.success('已清空所有数据')
}

// 批量删除选中的记录
const deleteSelected = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的记录')
    return
  }

  const deleteCount = selectedRowKeys.value.length
  addedItems.value = addedItems.value.filter((item: any) =>
    !selectedRowKeys.value.includes(item.id)
  )
  selectedRowKeys.value = []
  message.success(`已删除 ${deleteCount} 条记录`)
}

// 快速设置测试数据
const setTestData = () => {
  const testData = [
    {
      id: 'test1',
      artId: 1001,
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '哈药集团制药总厂',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 24,
      splittable: 1,
      totalPacks: 10,
      totalCells: 5
    },
    {
      id: 'test2',
      artId: 1002,
      artName: '头孢克肟胶囊',
      artSpec: '0.1g*12粒',
      producer: '石药集团',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 12,
      splittable: 1,
      totalPacks: 5,
      totalCells: 8
    },
    {
      id: 'test3',
      artId: 1003,
      artName: '胰岛素注射液',
      artSpec: '3ml:300IU',
      producer: '诺和诺德',
      packUnit: '支',
      cellUnit: '支',
      packCells: 1,
      splittable: 0,
      totalPacks: 20,
      totalCells: 0
    }
  ]

  addedItems.value = testData
  message.success('测试数据已加载')
}

// 导出数据
const exportData = () => {
  if (addedItems.value.length === 0) {
    message.warning('没有数据可导出')
    return
  }

  const dataStr = JSON.stringify(addedItems.value, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `maker-dept-art-data-${new Date().getTime()}.json`
  link.click()
  URL.revokeObjectURL(url)
  message.success('数据导出成功')
}

// 表格列定义
const columns = [
  {
    title: '品名',
    dataIndex: 'artName',
    key: 'artName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 120,
  },
  {
    title: '生产厂家',
    dataIndex: 'producer',
    key: 'producer',
    width: 120,
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ record }: any) => {
      return `${record.totalPacks || 0} ${record.packUnit || ''}`
    }
  },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    key: 'totalCells',
    width: 100,
    customRender: ({ record }: any) => {
      if (record.packCells && record.packCells > 1) {
        return `${record.totalCells || 0} ${record.cellUnit || ''}`
      }
      return '-'
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    customRender: ({ record }: any) => {
      return h(Space, {}, [
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => copyRecord(record)
        }, '复制'),
        h(Button, {
          type: 'link',
          size: 'small',
          danger: true,
          onClick: () => deleteRecord(record)
        }, '删除')
      ])
    }
  }
]
</script>

<template>
  <Card title="基础用法 - MakerDeptArt 品种选择组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">品种选择录入组件</Title>
      <Paragraph>
        MakerDeptArt 是一个专门用于品种选择和数量输入的组件，支持整包数量和拆零数量输入，
        具有完善的表单验证和键盘导航功能。
      </Paragraph>

      <!-- 品种选择录入组件 -->
      <div class="maker-form-section">
        <Title :level="5">品种选择录入</Title>
        <MakerDeptArt
          :deptCode="deptCode"
          :searchType="6"
          @addArt="handleAddArt"
          ref="makerDeptArtRef"
        />
      </div>

      <Divider />

      <!-- 已添加品种列表 -->
      <div class="added-items-section">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <Title :level="5" style="margin: 0;">已添加品种列表 ({{ addedItems.length }})</Title>
          <Space>
            <Button @click="setTestData" type="primary" ghost>加载测试数据</Button>
            <Button v-if="addedItems.length > 0" @click="exportData">导出数据</Button>
            <Button v-if="addedItems.length > 0" @click="clearAll" danger>清空所有</Button>
          </Space>
        </div>

        <!-- 批量操作工具栏 -->
        <div v-if="addedItems.length > 0" style="margin-bottom: 16px; padding: 12px; background: #fafafa; border-radius: 6px;">
          <Space>
            <span>已选择 {{ selectedRowKeys.length }} 项</span>
            <Button
              v-if="hasSelected"
              @click="deleteSelected"
              danger
              size="small"
            >
              批量删除
            </Button>
            <Button
              v-if="selectedRowKeys.length > 0"
              @click="selectedRowKeys = []"
              size="small"
            >
              取消选择
            </Button>
          </Space>
        </div>

        <Table
          v-if="addedItems.length > 0"
          :dataSource="addedItems"
          :columns="columns"
          rowKey="id"
          :scroll="{ x: 1200 }"
          :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            type: 'checkbox'
          }"
        />

        <div v-else style="text-align: center; padding: 40px; color: #999;">
          <div style="margin-bottom: 16px;">暂无数据，请使用上方表单添加品种</div>
          <Button @click="setTestData" type="primary" ghost>或点击加载测试数据</Button>
        </div>
      </div>

      <!-- 功能说明 -->
      <div class="feature-description">
        <Title :level="5">功能特性</Title>
        <div class="feature-list">
          <div class="feature-item">
            <Text strong>✨ 品种选择：</Text>
            <Text>支持通过输入框搜索和选择品种，自动获取品种详细信息</Text>
          </div>
          <div class="feature-item">
            <Text strong>📝 数量输入：</Text>
            <Text>支持整包数量和拆零数量输入，根据品种包装信息自动显示</Text>
          </div>
          <div class="feature-item">
            <Text strong>⌨️ 快捷操作：</Text>
            <Text>支持回车键快速添加，自动清空表单以便连续录入</Text>
          </div>
          <div class="feature-item">
            <Text strong>🔄 批量操作：</Text>
            <Text>支持记录的复制、删除等操作，支持批量选择和删除</Text>
          </div>
          <div class="feature-item">
            <Text strong>💾 数据管理：</Text>
            <Text>支持测试数据加载和数据导出功能</Text>
          </div>
          <div class="feature-item">
            <Text strong>⚙️ 自动设置：</Text>
            <Text>当品种信息为空时会自动弹出机构商品设置窗口</Text>
          </div>
        </div>
      </div>

      <div class="mt-8px tip-text">
        <i class="tip-icon">💡</i>
        点击"加载测试数据"可以快速体验组件的各项功能，包括复制、删除等操作。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="basicUsage"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>

  <!-- 打包发布指令 -->
  <Card title="打包与发布" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">组件打包指令</Title>
      <Paragraph>
        在开发完成后，需要打包组件以便发布和使用。以下是MakerDeptArt组件的打包和发布指令：
      </Paragraph>

      <div class="code-section">
        <Title :level="5">打包发布指令</Title>
        <pre class="code-block">{{ publishCommands }}</pre>
      </div>

      <div class="code-section">
        <Title :level="5">打包流程说明</Title>
        <pre class="code-block">{{ buildProcess }}</pre>
      </div>

      <div class="tips-section">
        <Title :level="5">使用说明</Title>
        <ul class="tips-list">
          <li><strong>正式版本：</strong>用于生产环境，版本号会自动递增</li>
          <li><strong>测试版本：</strong>用于测试环境，版本号带有beta标识</li>
          <li><strong>开发版本：</strong>用于开发环境，版本号带有alpha标识</li>
          <li><strong>安装组件：</strong>在其他项目中使用pnpm add命令安装组件</li>
        </ul>
      </div>

      <div class="warning-section">
        <Paragraph type="warning">
          <strong>注意：</strong>打包前请确保组件代码已经完成开发和测试，并且所有依赖项都已正确配置。
        </Paragraph>
      </div>
    </div>
  </Card>
</template>

<style scoped>
.mb-16px {
  margin-bottom: 16px;
}

.mt-8px {
  margin-top: 8px;
}

.maker-form-section {
  margin-bottom: 24px;
}

.added-items-section {
  margin-top: 24px;
}

.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  font-style: normal;
  margin-right: 8px;
  flex-shrink: 0;
}

/* 打包指令相关样式 */
.code-section {
  margin-bottom: 24px;
}

.code-block {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.45;
  color: #24292e;
  overflow-x: auto;
  white-space: pre;
  margin: 0;
}

.tips-section {
  margin-bottom: 24px;
}

.tips-list {
  margin: 8px 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.tips-list strong {
  color: #1890ff;
}

.warning-section {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 12px 16px;
  margin-top: 16px;
}

/* 功能说明样式 */
.feature-description {
  margin: 24px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.feature-list {
  margin-top: 16px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.6;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-item .ant-typography {
  margin-right: 8px;
}

.feature-item .ant-typography:last-child {
  flex: 1;
  margin-right: 0;
}
</style>
