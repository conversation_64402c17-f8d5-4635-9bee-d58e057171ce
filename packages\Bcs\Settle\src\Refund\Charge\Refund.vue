<script lang="ts" setup>
import { Data, Message, useLoading } from '@idmy/core'
import { ChargeContext, chargeInjectKey, refundInjectKey } from '@mh-bcs/util'
import { Button, InputNumber, Select, SelectOption, Space } from 'ant-design-vue'
import { head } from 'lodash-es'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})
const refundCtx = inject<any>(refundInjectKey)

const map = computed(() => refundCtx.payMap.value)

const loading = ref(false)
const onOk = async (row: Data, loaded: boolean) => {
  loading.value = true
  try {
    await ctx.onRefundPayment(row, loaded)
    if (row.paymentType === 'MI_FUND' && row.amount === 0) {
      Message.success('医保金额为零退费成功')
    }
    loaded && (await ctx.onLoad())
  } finally {
    loading.value = false
  }
}

const selectedPaymentType = ref()
watch(
  [() => ctx.unpaidAmount, () => map.value.size],
  () => {
    if (map.value.has('MI_FUND')) {
      selectedPaymentType.value = 'MI_FUND'
    } else {
      selectedPaymentType.value = head(Array.from(map.value.values()))?.paymentType
    }
  },
  { immediate: true }
)

const [onFullRefund, refunding] = useLoading(async () => {
  if (Number(ctx.unpaidAmount) < 0) {
    try {
      const values = map.value
      for (const [_, row] of values) {
        await onOk(row, false)
        await sleep(600)
      }
    } catch {
    } finally {
      await ctx.onLoad()
    }
  }
})
refundCtx.onFullRefund = onFullRefund
</script>

<template>
  <Space v-if="Number(ctx.unpaidAmount) < 0 && map.size > 0 && selectedPaymentType" class="fs16" items-center>
    <template v-if="!cfg.setting?.partialRefundDisabled">
      退费方式
      <Select v-model:value="selectedPaymentType" w-100px>
        <SelectOption v-for="row in Array.from(map.values())" :key="row.paymentType" :disabled="map.has('MI_FUND') && row.paymentType !== 'MI_FUND'" :value="row.paymentType">
          {{ row.paymentType.title }}
        </SelectOption>
      </Select>
      <InputNumber
        v-model:value="refundCtx.payMap.value.get(selectedPaymentType).amount"
        :controls="false"
        :max="cfg.setting?.partialRefundDisabled ? -Number(ctx.unpaidAmount) : undefined"
        :min="0"
        :precision="2"
        text-24px
        class="ml-8px h-36px w-134px"
        placeholder="退费金额"
      />
      <Button :disabled="loading" :loading="loading" type="primary" @click="onOk(refundCtx.payMap.value.get(selectedPaymentType), true)">确认退费</Button>
    </template>
  </Space>
</template>
