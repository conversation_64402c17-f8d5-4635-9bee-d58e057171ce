<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { WmArticlePageByOrgSelect } from '@mh-inpatient-hsd/selector'
import { orgArtInfoApi, orgCustMapInfoApi } from '@mh-wm/util'
import OrgArtPriceSetting from './org-art-price-setting.vue'

import { Button, Col, Form, FormItem, Input, InputNumber, Popover, RangePicker, Row, Tag, Tooltip } from 'ant-design-vue'
// 定义组件事件
const emit = defineEmits(['addArt'])

// 表单数据
const formState = reactive({
  deptCode: '',
  artIds: [] as number[],
  hasStock: false,
})

const orgCustMap = ref({})
const loadOrgCustMap = async () => {
  const data = await orgCustMapInfoApi({})
  orgCustMap.value = data
}

// 缓存选中的品种数据
const selectedArtData = ref(null)
const item = ref(null)
// 搜索表单数据
const searchFormModel = reactive({
  batchNo: '',
  expiry: '',
  originPlace: '',
  dateManufactured: '',
  packPrice: undefined,
  stockPacksTotal: '',
  totalPacks: undefined,
  stockCellsTotal: undefined,
  packCells: undefined,
  totalCells: '',
  cellPrice: undefined,
  packUnit: undefined,
  cellUnit: '',
  splittable: undefined,
  salePctAdd: undefined,
})

// 品种选择组件引用
const artSelectRef = ref()

// 机构商品设置弹窗相关
const orgArtPriceSettingVisible = ref(false)
const orgArtPriceSettingRef = ref()

// 添加品种
const onAddArt = () => {
  // 1. 去除品种查询校验，允许不选择品种直接录入数据
  // if (!selectedArtData.value) {
  //   message.error('请先选择一个品种')
  //   // 聚焦到品种选择框
  //   nextTick(() => {
  //     if (artSelectRef.value && typeof artSelectRef.value.focus === 'function') {
  //       artSelectRef.value.focus()
  //     }
  //   })
  //   return
  // }

  // 2. 验证生产日期不为空且格式正确
  if (!searchFormModel.dateManufactured || searchFormModel.dateManufactured.trim() === '') {
    message.error('生产日期不能为空')
    nextTick(() => {
      dateManufacturedRef.value?.focus()
    })
    return
  }
  if (!validateDateManufactured()) {
    return
  }

  // 3. 验证有效期至不为空且格式正确
  if (!searchFormModel.expiry || searchFormModel.expiry.trim() === '') {
    message.error('有效期至不能为空')
    nextTick(() => {
      expiryRef.value?.focus()
    })
    return
  }
  if (!validateExpiry()) {
    return
  }

  // 4. 验证整包单价不能为空且必须大于0
  if (searchFormModel.packPrice === undefined || searchFormModel.packPrice === null || searchFormModel.packPrice <= 0) {
    message.error('整包单价不能为空且必须大于0')
    nextTick(() => {
      packPriceRef.value?.focus()
    })
    return
  }

  // 5. 验证数量：整包数量和拆零数量至少填写一个且大于0
  const hasValidPackQuantity = searchFormModel.totalPacks !== undefined && searchFormModel.totalPacks !== null && searchFormModel.totalPacks > 0

  const hasValidCellQuantity =
    searchFormModel.splittable === 1 && searchFormModel.totalCells !== undefined && searchFormModel.totalCells !== null && searchFormModel.totalCells !== '' && Number(searchFormModel.totalCells) > 0

  if (!hasValidPackQuantity && !hasValidCellQuantity) {
    if (searchFormModel.splittable === 1) {
      message.error('整包数量和拆零数量至少填写一个且必须大于0')
    } else {
      message.error('整包数量不能为空且必须大于0')
    }
    nextTick(() => {
      totalPacksRef.value?.focus()
    })
    return
  }

  // 6. 验证生产批号不能为空
  if (!searchFormModel.batchNo || searchFormModel.batchNo.trim() === '') {
    message.error('生产批号不能为空')
    nextTick(() => {
      batchNoRef.value?.focus()
    })
    return
  }

  // 克隆当前表单数据
  const formData = {
    ...searchFormModel,
    // 添加选中的品种数据（如果有的话）
    artData: selectedArtData.value,
    // 如果没有选择品种，则使用表单中的基本信息
    artId: selectedArtData.value?.artId || null,
    artCode: selectedArtData.value?.artCode || '',
    artName: selectedArtData.value?.artName || '',
    artSpec: selectedArtData.value?.artSpec || '',
    producer: selectedArtData.value?.producer || '',
  }

  // 发射事件，返回表单数据
  emit('addArt', formData)

  // 清空表单
  clearFormData()

  // 聚焦回品种选择框，方便继续添加
  nextTick(() => {
    if (artSelectRef.value && typeof artSelectRef.value.focus === 'function') {
      artSelectRef.value.focus()
    }
  })
  console.log('添加品种成功，表单数据：', formData)
  // 返回表单数据，方便外部使用
  return formData
}
// 处理品种选择
const handleArtSelect = async (selectedArt: any) => {
  if (!selectedArt) return

  console.log('选中品种数据:', selectedArt)

  // 将选中的品种数据缓存到新变量中
  selectedArtData.value = selectedArt

  // 获取品种详细信息
  try {
    const response = await orgArtInfoApi({
      artId: selectedArtData.value.artId,
    })

    // 判断API返回值是否为空或无效
    // 检查response、response.data或直接的数据结构
    const itemData = response?.data || response

    if (itemData && Object.keys(itemData).length > 0) {
      // 有数据时，设置价格信息
      item.value = itemData
      searchFormModel.packPrice = itemData.lastBuyPrice || undefined
      if (selectedArt.cellPrice && itemData.lastBuyPrice) {
        searchFormModel.cellPrice = itemData.lastBuyPrice / selectedArt.cellPrice
      }
      // 映射 pctAdd 到 salePctAdd（API返回的是pctAdd字段）
      searchFormModel.salePctAdd = itemData.pctAdd || undefined
    } else {
      // 当item返回数据为空时，调用org-art-price-setting.vue页面进行弹窗
      console.log('品种详细信息为空，打开机构商品设置弹窗')
      item.value = null
      await loadOrgCustMap()
      orgArtPriceSettingVisible.value = true
      // 使用nextTick确保弹窗组件已经挂载后再初始化
      nextTick(() => {
        if (orgArtPriceSettingRef.value) {
          orgArtPriceSettingRef.value.init(selectedArt, (orgCustMap.value as any)?.pricingMethod || 1) // 1为定价方式参数
        }
      })
    }
  } catch (error) {
    // API调用失败时也打开弹窗
    console.error('获取品种详细信息失败:', error)
    console.log('API调用失败，打开机构商品设置弹窗')
    item.value = null
    orgArtPriceSettingVisible.value = true
    nextTick(() => {
      if (orgArtPriceSettingRef.value) {
        orgArtPriceSettingRef.value.init(selectedArt, (orgCustMap.value as any)?.pricingMethod || 1)
      }
    })
  }

  // 更新表单中的相关字段 - 包装相关信息
  searchFormModel.packUnit = selectedArt.packUnit || ''
  searchFormModel.cellUnit = selectedArt.cellUnit || ''
  searchFormModel.splittable = selectedArt.splittable !== undefined ? selectedArt.splittable : 0
  searchFormModel.packCells = selectedArt.packCells || undefined
  // 对选中的input标签进行赋值 - 库存相关信息
  searchFormModel.batchNo = selectedArt.batchNo || ''
  searchFormModel.expiry = selectedArt.expiry || ''
  searchFormModel.stockPacksTotal = selectedArt.stockPacksTotal || ''
  searchFormModel.stockCellsTotal = selectedArt.stockCellsTotal || undefined
  // 价格相关信息
  if (selectedArt.packPrice !== undefined) {
    searchFormModel.packPrice = selectedArt.packPrice
  }
  if (selectedArt.cellPrice !== undefined) {
    searchFormModel.cellPrice = selectedArt.cellPrice
  }

  // 其他可能的字段
  if (selectedArt.originPlace !== undefined) {
    searchFormModel.originPlace = selectedArt.originPlace
  }
  if (selectedArt.dateManufactured !== undefined) {
    searchFormModel.dateManufactured = selectedArt.dateManufactured
  }

  // 如果有artId，添加到artIds数组中
  if (selectedArt.artId && !formState.artIds.includes(selectedArt.artId)) {
    formState.artIds.push(selectedArt.artId)
  }

  // 聚焦到整包数量输入框，方便用户直接输入数量
  nextTick(() => {
    if (originPlaceRef.value) {
      originPlaceRef.value.focus()
    }
  })
}

// 清空表单数据
const clearFormData = () => {
  console.log('开始清空Maker组件表单数据')

  // 重置表单数据 - 将所有字段都设置为空值
  // 字符串类型字段设置为空字符串
  searchFormModel.batchNo = ''
  searchFormModel.expiry = ''
  searchFormModel.originPlace = ''
  searchFormModel.dateManufactured = ''
  searchFormModel.stockPacksTotal = ''
  searchFormModel.totalCells = ''
  searchFormModel.cellUnit = ''

  // 数字类型字段设置为undefined，让输入框显示为空
  searchFormModel.stockCellsTotal = undefined
  searchFormModel.totalPacks = undefined
  searchFormModel.packPrice = undefined
  searchFormModel.cellPrice = undefined
  searchFormModel.packUnit = undefined
  searchFormModel.splittable = undefined
  searchFormModel.packCells = undefined
  searchFormModel.salePctAdd = undefined

  // 清空品种选择组件
  if (artSelectRef.value) {
    try {
      // 尝试多种清空方法
      if (typeof artSelectRef.value.init === 'function') {
        artSelectRef.value.init()
        console.log('已调用品种选择组件的init方法')
      } else if (typeof artSelectRef.value.clear === 'function') {
        artSelectRef.value.clear()
        console.log('已调用品种选择组件的clear方法')
      } else if (typeof artSelectRef.value.reset === 'function') {
        artSelectRef.value.reset()
        console.log('已调用品种选择组件的reset方法')
      } else {
        console.warn('品种选择组件没有可用的清空方法')
      }
    } catch (error) {
      console.error('清空品种选择组件时出错:', error)
    }
  } else {
    console.warn('品种选择组件引用不存在')
  }

  // 清空缓存的品种数据
  selectedArtData.value = null

  console.log('Maker组件表单数据清空完成')
}
// 移除了deptCode参数，组件内部不再依赖外部传入的仓库编码
// 获取选中的品种数据
const getSelectedArtData = () => {
  return selectedArtData.value
}

// 设置表单数据（用于编辑时回填数据）
const setFormData = (data: any) => {
  if (!data) return

  // 设置品种数据
  if (data.artData) {
    selectedArtData.value = data.artData
    console.log('setFormData 设置的品种数据:', data.artData)
    // 设置品种选择组件的显示值
    if (artSelectRef.value && typeof artSelectRef.value.setValue === 'function') {
      artSelectRef.value.setValue(data.artData)
    }
  }

  // 设置表单字段
  searchFormModel.originPlace = data.originPlace || ''
  searchFormModel.batchNo = data.batchNo || ''
  searchFormModel.dateManufactured = data.dateManufactured || ''
  searchFormModel.expiry = data.expiry || ''
  searchFormModel.packPrice = data.packPrice || undefined
  searchFormModel.totalPacks = data.totalPacks || undefined
  searchFormModel.totalCells = data.totalCells || ''

  // 设置品种相关信息
  if (data.artData) {
    searchFormModel.packUnit = data.artData.packUnit || data.packUnit || ''
    searchFormModel.cellUnit = data.artData.cellUnit || data.cellUnit || ''
    searchFormModel.splittable = data.artData.splittable !== undefined ? data.artData.splittable : data.splittable || 0
    searchFormModel.packCells = data.artData.packCells || data.packCells || 0
    searchFormModel.salePctAdd = data.artData.salePctAdd || data.salePctAdd || undefined
  } else {
    // 如果没有artData，直接从data中获取
    searchFormModel.packUnit = data.packUnit || ''
    searchFormModel.cellUnit = data.cellUnit || ''
    searchFormModel.splittable = data.splittable || 0
    searchFormModel.packCells = data.packCells || 0
    searchFormModel.salePctAdd = data.salePctAdd || undefined
  }

  console.log('setFormData 设置后的 salePctAdd:', searchFormModel.salePctAdd)
}

// 校验8位数字日期格式 (YYYYMMDD)
const validateDateFormat = (value: string) => {
  // 检查是否为空
  if (!value || value.trim() === '') {
    message.error('日期不能为空')
    return false
  }

  // 去除空格
  const trimmedValue = value.trim()

  // 严格检查：必须是8位数字，不允许其他字符
  if (!/^\d{8}$/.test(trimmedValue)) {
    message.error('日期格式必须是8位数字(YYYYMMDD)，不允许包含字母、符号或其他字符')
    return false
  }

  // 检查长度必须严格等于8位
  if (trimmedValue.length !== 8) {
    message.error('日期必须是8位数字，当前输入长度为' + trimmedValue.length + '位')
    return false
  }

  // 检查是否包含非数字字符（双重验证）
  if (!/^[0-9]+$/.test(trimmedValue)) {
    message.error('日期只能包含数字0-9，不允许输入字母、符号或特殊字符')
    return false
  }

  // 提取年月日
  const year = parseInt(trimmedValue.substring(0, 4))
  const month = parseInt(trimmedValue.substring(4, 6))
  const day = parseInt(trimmedValue.substring(6, 8))

  // 检查年月日是否有效
  if (year < 1900 || year > 2100) {
    message.error('年份必须在1900-2100之间')
    return false
  }

  if (month < 1 || month > 12) {
    message.error('月份必须在01-12之间')
    return false
  }

  // 获取当月最大天数
  const maxDay = new Date(year, month, 0).getDate()
  if (day < 1 || day > maxDay) {
    message.error(`${year}年${month}月的日期必须在01-${maxDay.toString().padStart(2, '0')}之间`)
    return false
  }

  return true
}

// 验证生产日期
const validateDateManufactured = () => {
  if (validateDateFormat(searchFormModel.dateManufactured)) {
    return true
  }
  // 验证失败时聚焦回输入框
  nextTick(() => {
    dateManufacturedRef.value?.focus()
  })
  return false
}

// 验证有效期至
const validateExpiry = () => {
  if (validateDateFormat(searchFormModel.expiry)) {
    // 如果生产日期和有效期都有值，检查有效期是否大于生产日期
    if (searchFormModel.dateManufactured && searchFormModel.expiry) {
      if (parseInt(searchFormModel.expiry) <= parseInt(searchFormModel.dateManufactured)) {
        message.error('有效期至必须大于生产日期')
        nextTick(() => {
          expiryRef.value?.focus()
        })
        return false
      }
    }
    return true
  }
  // 验证失败时聚焦回输入框
  nextTick(() => {
    expiryRef.value?.focus()
  })
  return false
}

// 处理生产日期回车事件
const handleDateManufacturedEnter = () => {
  if (validateDateManufactured()) {
    focusNext('expiryRef')
  }
}

// 处理有效期至回车事件
const handleExpiryEnter = () => {
  if (validateExpiry()) {
    focusNext('packPriceRef')
  }
}

// 限制日期输入只能是数字
const handleDateInput = (event: Event, fieldName: string) => {
  const target = event.target as HTMLInputElement
  let value = target.value

  // 只保留数字字符
  value = value.replace(/[^\d]/g, '')

  // 限制最大长度为8位
  if (value.length > 8) {
    value = value.substring(0, 8)
  }

  // 更新对应字段的值
  if (fieldName === 'dateManufactured') {
    searchFormModel.dateManufactured = value
  } else if (fieldName === 'expiry') {
    searchFormModel.expiry = value
  }

  // 更新输入框显示值
  target.value = value
}

// 处理生产日期输入
const handleDateManufacturedInput = (event: Event) => {
  handleDateInput(event, 'dateManufactured')
}

// 处理有效期输入
const handleExpiryInput = (event: Event) => {
  handleDateInput(event, 'expiry')
}

// 处理生产日期失去焦点事件
const handleDateManufacturedBlur = () => {
  // 只有当输入框有值时才进行校验
  if (searchFormModel.dateManufactured && searchFormModel.dateManufactured.trim() !== '') {
    validateDateManufactured()
  }
}

// 处理有效期失去焦点事件
const handleExpiryBlur = () => {
  // 只有当输入框有值时才进行校验
  if (searchFormModel.expiry && searchFormModel.expiry.trim() !== '') {
    validateExpiry()
  }
}

// 处理生产日期聚焦事件 - 自动填充年份前缀
const handleDateManufacturedFocus = () => {
  // 只在第一次聚焦且输入框为空时自动填充
  if (!searchFormModel.dateManufactured || searchFormModel.dateManufactured.trim() === '') {
    const currentYear = new Date().getFullYear()
    // 如果当前年大于2021，自动补充"202"
    // 如果到了2031年或以后，就只补充"20"
    if (currentYear >= 2031) {
      searchFormModel.dateManufactured = '20'
    } else if (currentYear > 2021) {
      searchFormModel.dateManufactured = '202'
    } else {
      searchFormModel.dateManufactured = '20'
    }
  }
}

// 处理有效期聚焦事件 - 自动填充年份前缀
const handleExpiryFocus = () => {
  // 只在第一次聚焦且输入框为空时自动填充
  if (!searchFormModel.expiry || searchFormModel.expiry.trim() === '') {
    const currentYear = new Date().getFullYear()
    // 如果当前年小于2028，自动补充"202"
    // 如果到了2028年或以后，就只补充"20"
    if (currentYear >= 2028) {
      searchFormModel.expiry = '20'
    } else {
      searchFormModel.expiry = '202'
    }
  }
}

// 定义所有输入框的引用
const originPlaceRef = ref()
const batchNoRef = ref()
const dateManufacturedRef = ref()
const expiryRef = ref()
const packPriceRef = ref()
const totalPacksRef = ref()
const totalCellsRef = ref()
// 聚焦到下一个输入框
const focusNext = (refName: string) => {
  nextTick(() => {
    if (refName === 'totalCellsRef' && searchFormModel.splittable !== 1) {
      // 如果下一个是拆零数量但不可拆零，则直接提交
      onAddArt()
      return
    }
    // 根据引用名称获取对应的ref对象
    const refMap: Record<string, any> = {
      originPlaceRef: originPlaceRef,
      batchNoRef: batchNoRef,
      dateManufacturedRef: dateManufacturedRef,
      expiryRef: expiryRef,
      packPriceRef: packPriceRef,
      totalPacksRef: totalPacksRef,
      totalCellsRef: totalCellsRef,
    }

    const nextRef = refMap[refName]?.value
    if (nextRef) {
      // 处理不同类型的组件
      if (typeof nextRef.focus === 'function') {
        nextRef.focus()
      } else if (nextRef.$el && typeof nextRef.$el.focus === 'function') {
        nextRef.$el.focus()
      } else if (nextRef.input && typeof nextRef.input.focus === 'function') {
        nextRef.input.focus()
      }
    }
  })
}

// 处理机构商品设置弹窗确定回调
const handleOrgArtPriceSettingOk = async (formData: any) => {
  console.log('机构商品设置保存成功:', formData)
  // 关闭弹窗
  orgArtPriceSettingVisible.value = false

  // 保存成功后重新获取品种信息
  if (selectedArtData.value?.artId) {
    try {
      const response = await orgArtInfoApi({
        artId: selectedArtData.value.artId,
      })

      const itemData = response?.data || response
      if (itemData && Object.keys(itemData).length > 0) {
        item.value = itemData
        searchFormModel.packPrice = itemData.lastBuyPrice || 0
        if (selectedArtData.value.cellPrice && itemData.lastBuyPrice) {
          searchFormModel.cellPrice = itemData.lastBuyPrice / selectedArtData.value.cellPrice
        }
        // 设置加成比例（API返回的是pctAdd字段）
        searchFormModel.salePctAdd = itemData.pctAdd || undefined
        console.log('重新获取品种详细信息成功:', itemData)
      }
    } catch (error) {
      console.error('重新获取品种详细信息失败:', error)
    }
  }

  message.success('机构商品设置保存成功')
}

// 暴露方法给外部使用
defineExpose({
  getSelectedArtData,
  clearFormData,
  clearForm: clearFormData, // 添加clearForm别名，方便外部调用
  setFormData,
})
</script>
<template>
  <Form layout="inline" :model="searchFormModel">
    <FormItem label="品种查询" name="keyword">
      <div class="art-select-box">
        <WmArticlePageByOrgSelect
          ref="artSelectRef"
          @selected="handleArtSelect"
          :hasStock="formState.hasStock"
          style="width: 500px"
          placeholder="请选择品种"
          @keydown.enter="focusNext('originPlaceRef')"
        />
      </div>
    </FormItem>
    <FormItem label="原产地" name="originPlace">
      <Input ref="originPlaceRef" v-model:value="searchFormModel.originPlace" style="width: 220px" placeholder="请输入原产地" autocomplete="off" @keydown.enter="focusNext('batchNoRef')" />
    </FormItem>
    <FormItem label="生产批号" name="batchNo" required>
      <Input ref="batchNoRef" v-model:value="searchFormModel.batchNo" style="width: 200px" placeholder="请输入生产批号" autocomplete="off" @keydown.enter="focusNext('dateManufacturedRef')" />
    </FormItem>
    <FormItem label="生产日期" name="dateManufactured" required>
      <Input
        ref="dateManufacturedRef"
        v-model:value="searchFormModel.dateManufactured"
        style="width: 120px"
        placeholder="YYYYMMDD"
        :maxlength="8"
        autocomplete="off"
        @focus="handleDateManufacturedFocus"
        @input="handleDateManufacturedInput"
        @blur="handleDateManufacturedBlur"
        @keydown.enter="handleDateManufacturedEnter"
      />
    </FormItem>
    <FormItem label="有效期至" name="expiry" required>
      <Input
        ref="expiryRef"
        v-model:value="searchFormModel.expiry"
        style="width: 120px"
        placeholder="YYYYMMDD"
        :maxlength="8"
        autocomplete="off"
        @focus="handleExpiryFocus"
        @input="handleExpiryInput"
        @blur="handleExpiryBlur"
        @keydown.enter="handleExpiryEnter"
      />
    </FormItem>
    <FormItem label="整包单价" name="packPrice" required>
      <Input-number 
        ref="packPriceRef" 
        v-model:value="searchFormModel.packPrice" 
        :min="0"
        :step="0.000001"
        autocomplete="off"
        :formatter="(value) => {
          if (value === null || value === undefined || value === '') return ''
          const str = value.toString()
          
          // 如果字符串以.0结尾（如0.0, 1.0, 12.0），说明用户可能正在编辑，保留这个格式
          if (str.endsWith('.0') && str.split('.')[1].length === 1) {
            return str
          }
          
          // 如果字符串以.00, .000等结尾，但前面还有其他数字，也可能在编辑中
          if (/\\.0+$/.test(str) && str.split('.')[1].length <= 2) {
            return str
          }
          
          // 对于其他情况，转换为数字再转为字符串，去除尾部无意义的0
          const num = parseFloat(str)
          return isNaN(num) ? '' : num.toString()
        }"
        :parser="(value) => {
          if (!value) return undefined
          // 只保留数字、小数点和负号
          const cleaned = value.replace(/[^\d.-]/g, '')
          const num = parseFloat(cleaned)
          return isNaN(num) ? undefined : num
        }"
        placeholder="请输入单价" 
        @keydown.enter="focusNext('totalPacksRef')"
      />
    </FormItem>
    <FormItem label="整包数量" name="totalPacks" :required="!searchFormModel.splittable || searchFormModel.splittable !== 1">
      <Input-number
        ref="totalPacksRef"
        v-model:value="searchFormModel.totalPacks"
        :min="0"
        :precision="0"
        :addon-after="searchFormModel.packUnit"
        style="width: 150px"
        placeholder="请输入数量"
        autocomplete="off"
        @keydown.enter="searchFormModel.packCells && searchFormModel.packCells > 1 ? focusNext('totalCellsRef') : onAddArt()"
      />
    </FormItem>
    <FormItem label="拆零数量" name="totalCells" v-if="searchFormModel.packCells && searchFormModel.packCells > 1" :required="!searchFormModel.totalPacks || searchFormModel.totalPacks <= 0">
      <Input-number
        ref="totalCellsRef"
        v-model:value="searchFormModel.totalCells"
        style="width: 150px"
        :min="0"
        :precision="0"
        :addon-after="searchFormModel.cellUnit"
        placeholder="请输入数量"
        autocomplete="off"
        @keydown.enter="onAddArt()"
      />
    </FormItem>
    <FormItem>
      <Button @click="onAddArt">添加</Button>
    </FormItem>
  </Form>

  <!-- 机构商品设置弹窗 -->
  <OrgArtPriceSetting ref="orgArtPriceSettingRef" v-model:visible="orgArtPriceSettingVisible" @ok="handleOrgArtPriceSettingOk" width="900px" />
</template>
