/**
 * 价格格式化，保留2位小数
 * @param value 
 * @returns 
 */
export function priceFormat2(value: number | string): string {
  if (!value && value !== 0) return '-'
  const val = Number(value)
  if (isNaN(val)) return '-'
  return val.toFixed(2)
}

/**
 * 数字格式化，添加千分位
 * @param value 
 * @returns 
 */
export function formatNumberWithCommas(value: number | string): string {
  if (!value && value !== 0) return ''
  const val = Number(value)
  if (isNaN(val)) return ''
  return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
} 