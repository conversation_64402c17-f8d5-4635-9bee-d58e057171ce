import { wrapCodeExample } from '@/utils/codeUtils'
// 字典模式
export const dictUsage = wrapCodeExample(`<template>
  <DiseaseDict
    v-model="diseaseId"
  />
</template>

<script setup>
import { DiseaseDict } from '@mh-hip/disease'
import { ref } from 'vue'

// 单选值
const diseaseId = ref()
</script>`)

// 选择器模式
export const selectorUsage = wrapCodeExample(`<template>
  <DiseaseSelector
    v-model="diseaseId"
    :patientId="patientId"
    @select="handleDiseaseSelect"
  />
</template>

<script setup>
import { DiseaseSelector } from '@mh-hip/disease'
import { ref } from 'vue'

// 单选值
const diseaseId = ref()
const patientId = ref(1) // 患者ID

const handleDiseaseSelect = (disease) => {
  console.log('选择的病种:', disease)
}
</script>`)

// 弹窗模式
export const modalUsage = wrapCodeExample(`<template>
  <DiseaseSelectorModal
    v-model:visible="showModal"
    v-model="diseaseId"
    :patientId="patientId"
    @select="handleDiseaseSelect"
  />
</template>

<script setup>
import { DiseaseSelectorModal } from '@mh-hip/disease'
import { ref } from 'vue'

// 单选值
const showModal = ref(false)
const diseaseId = ref()
const patientId = ref(1) // 患者ID

const handleDiseaseSelect = (disease) => {
  console.log('选择的病种:', disease)
}
</script>`)

// 推荐模式
export const useUsage = wrapCodeExample(`<template>
   <!-- 使用弹窗方式 -->
  <template v-if="diseaseConfig.component === DiseaseSelectorModal">
    <Button @click="showModal = true">选择病种</Button>
    <component
      :is="diseaseConfig.component"
      v-model:visible="showModal"
      v-model="diseaseId"
      v-bind="diseaseConfig.props"
      @select="handleDiseaseSelect"
    />
  </template>

  <!-- 使用页面嵌入或字典方式 -->
  <template v-else>
    <component
      :is="diseaseConfig.component"
      v-model="diseaseId"
      v-bind="diseaseConfig.props"
      @select="handleDiseaseSelect"
    />
  </template>
</template>

<script setup>
import { ref } from 'vue'
import { useDiseaseSelector, DiseaseSelectorModal } from '@mh-hip/disease'

const diseaseId = ref()
const showModal = ref(false)
const patientId = ref(1) // 患者ID

// 根据需要选择模式：'modal', 'selector', 'dict'
const diseaseConfig = useDiseaseSelector({
  mode: 'modal', // 使用弹窗模式
  patientId: patientId.value,
  title: '选择患者病种'
})

const handleDiseaseSelect = (disease) => {
  console.log('选择的病种:', disease)
}
</script>`)

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-hip/disease": "^1.0.14"
  }
}`
