import { Data, emitter, getCookie, http, INITIALIZED, login } from '@idmy/core'
import { reactive, watch } from 'vue'

const CURRENT_ORG_KEY = 'Current_Org'

export const Currents = reactive({
  tenantId: 0,
  tenantName: '默认',
  id: undefined,
  name: undefined,
})

export const TENANT_CHANGE_EVENT = Symbol('tenantChange')

watch(
  () => Currents.tenantId,
  (newVal, oldVal) => {
    console.info('Currents.tenantId 改变了', oldVal, '=>', newVal)
    emitter.emit(TENANT_CHANGE_EVENT)
    if (Currents.tenantId && Currents.tenantName) {
      localStorage.setItem(
        CURRENT_ORG_KEY,
        JSON.stringify({
          label: Currents.tenantName,
          orgId: Currents.tenantId,
          orgName: Currents.tenantName,
        })
      )
    }
  }
)

const getOrg = (orgId: number): Promise<Data> => http.post('/hip-base/org/info', { orgId }, { appKey: 'hip' })

emitter.on(INITIALIZED, async () => {
  const org = JSON.parse(localStorage.getItem(CURRENT_ORG_KEY) ?? '{}')
  if (org.orgId && org.orgName) {
    Currents.tenantId = org.orgId
    Currents.tenantName = org.orgName
  } else {
    const orgId = getCookie('orgId')
    if (orgId) {
      Currents.tenantId = Number(orgId)
      const { orgName } = await getOrg(Number(orgId))
      Currents.tenantName = orgName
    }
  }

  Currents.id = login.token?.accessToken?.userId
  console.info('Currents 初始化完毕', login.token?.accessToken, Currents)
})
