import { http } from '@idmy/core'

export interface MiTrans {
  miTransId?: any
  amount?: any
  ownpayAmt?: any
  overlimitAmt?: any
  preSelfpayAmt?: any
  inScopeAmt?: any
  fundPayAmt?: any
  payLineAmt?: any
  patientPartAmt?: any
  hospitalPartAmt?: any
  acctPayAmt?: any
  cashPayAmt?: any
  acctBalance?: any
  periodStart?: any
  periodEnd?: any
  sheetUploadTime?: any
  upload_failed_list?: any
  multiAidAmt?: any
  transStatus?: any
}

export function getTrans(transId: number) {
  return http.post('/mcisp/trans/info', { transId })
}

export function getFinishedBlueByCashId(cashId: number) {
  return http.post('/mcisp/trans/getFinishedBlueByCashId', { cashId })
}
