<script setup lang="ts">
import dayjs, { Dayjs } from 'dayjs'
type RangeValue = [Dayjs, Dayjs];
import type { Rule } from 'ant-design-vue/es/form'
import { SECTION_ACC_MODE_DRUG, SECTION_ACC_MODE_EXEC, SECTION_DISCHARGE_VISIT_ID } from '@mh-inpatient-hsd/util'
import { execArtOeLsApi, execArtOeLsByDrugModeApi, preOeExecPlanFeeApi } from '@mh-inpatient-hsd/util'
import ExecRoutePlanForm from "./ExecRoutePlanForm.vue"
import ExecResultLog from './ExecResultLog.vue'
import type { TableColumnType } from 'ant-design-vue'
import { Button, Col, Form, FormItem, Modal, Row, RangePicker, Radio, RadioGroup, Checkbox, DatePicker, message } from 'ant-design-vue'

const props = defineProps({
  inOperation: {
    type: Boolean,
    default: false
  },
  sectionId: {
    type: Number,
    default: null
  },
  feedMethod: {
    type: Number,
    default: null
  },
  oeTypeId: {
    type: Number,
    default: null
  },
  useExecSectionStock: {
    type: Boolean,
    default: null
  },
  medicinesAccountingMode: {
    type: Number,
    default: Number(SECTION_ACC_MODE_EXEC),
  },
  enableExecByDay: {
    type: Boolean,
    default: true
  }
})

const columns: TableColumnType[] = [
  {
    title: '床号',
    dataIndex: 'bedNo',
    align: 'center',
    width: 70
  },
  {
    title: '姓名',
    dataIndex: 'patientName',
    align: 'center',
    width: 80
  },
  {
    title: '医嘱号',
    dataIndex: 'oeNo',
    align: 'center',
    width: 60,
  },
  {
    title: '医嘱内容',
    dataIndex: 'oeText',
    ellipsis: true,
    align: 'left',
  },
  {
    title: '申请数量',
    dataIndex: 'priceTotal',
    align: 'center',
    width: 150
  }
]

const router = useRouter()
const visible = ref(false)
const oePlanLoading = ref(false)
const confirmLoading = ref(false)
const hasTreatmentOe = ref(false)
const patientFormRef = ref()
const formModel = ref<any>({})
const planOeLs = ref<any>([])
const poLs = ref<any>([])
const selectedRows = ref<any>([])
const batchSize = 80
const maxEditSize = 10
const currentIndex = ref(0)
const firstAidStockErr = ref<string>()
const stockErr = ref<string>()
const consumableErr = ref<string>()
const execMethodLs = ref([
  {
    value: 1,
    label: '按次执行'
  }
])
const drugAccMode = computed(() => {
  return props.medicinesAccountingMode === Number(SECTION_ACC_MODE_DRUG)
})
const execResultLogRef = ref<InstanceType<typeof ExecResultLog>>()

// const emit = defineEmits(['exec', 'reqNormalArt', 'reqFirstAidArt', 'reqConsumableArt'])
const emit = defineEmits(['executed'])
const open = (oeIdLs: any, hasTreatment: boolean, initSelectedRows: any) => {
  firstAidStockErr.value = null
  stockErr.value = null
  consumableErr.value = null
  if (props.enableExecByDay) {
    execMethodLs.value = [
      {
        value: 1,
        label: '按次执行'
      },
      {
        value: 2,
        label: '按天执行'
      }
    ]
  }
  selectedRows.value = initSelectedRows
  hasTreatmentOe.value = hasTreatment
  // formModel.value.execMethod = 2
  formModel.value.execMethod = execMethodLs.value[execMethodLs.value.length - 1].value
  formModel.value.checkPassDateExec = true
  formModel.value.startDate = dayjs()
  formModel.value.endDate = dayjs()
  formModel.value.treatmentTime = dayjs()
  formModel.value.dateRange = [formModel.value.startDate, formModel.value.endDate]
  // 手术室执行医嘱取手术室的给药途径关联组套、耗材
  const params = {
    sectionId: props.sectionId,
    feedMethod: props.feedMethod,
    oeTypeId: props.oeTypeId,
    inOperation: props.inOperation,
    oeIdLs
  }
  currentIndex.value = 0
  planOeLs.value = []
  preOeExecPlanFeeApi(params).then((data: any) => {
    if (data && data.planOeLs) {
      poLs.value = data.planOeLs
      const artLs = data.artLs
      poLs.value.forEach((planOe: any) => {
        planOe.hiddenEdit = poLs.value.length > maxEditSize
        planOe.changePlan = poLs.value.length <= maxEditSize
        if (initSelectedRows && initSelectedRows.length > 0) {
          const oe = initSelectedRows.find((oe: any) => oe.visitId === planOe.visitId && oe.oeNo === planOe.oeNo)
          if (oe && oe.oeText) {
            planOe.oeText = oe.oeText
            planOe.oneDayMaxTimes = oe.oneDayMaxTimes
          }
        }
        if (planOe.planFeeLs && planOe.planFeeLs.length > 0) {
          planOe.planFeeLs.forEach((planFee: any) => {
            planFee.forFirstTimeBool = planFee.forFirstTime === 1
            planFee.forEachTimeBool = planFee.forEachTime === 1
            const art = artLs.find((art: any) => art.artId === planFee.artId)
            if (art) {
              planFee.artName = art.artName
              planFee.artSpec = art.artSpec
              planFee.producer = art.producer
            }
          })
        }
      })
      insertBatchData()
    }
    oePlanLoading.value = false
  })
  visible.value = true
  oePlanLoading.value = true
}

function insertBatchData() {
  const batch = poLs.value.slice(currentIndex.value, currentIndex.value + batchSize); // 获取当前批次的数据
  // 批量插入数据
  planOeLs.value.push(...batch);

  // 更新索引
  currentIndex.value += batchSize;

  // 如果还有数据未插入，继续调用
  if (currentIndex.value < poLs.value.length) {
    setTimeout(insertBatchData, 40);  // 每 40 毫秒插入一次数据
  }
}

const onRangeChange = (dates: RangeValue) => {
  if (dates) {
    formModel.value.startDate = dates[0]
    formModel.value.endDate = dates[1]
  } else {
    formModel.value.startDate = null
    formModel.value.endDate = null
  }
}

const rangePresets = ref([
  { label: '今明两日', value: [dayjs(), dayjs().add(1, 'd')] },
  { label: '往前2日', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '往前3日', value: [dayjs().add(-2, 'd'), dayjs()] },
  { label: '往前4日', value: [dayjs().add(-3, 'd'), dayjs()] },
  // { label: '最近5日', value: [dayjs().add(-4, 'd'), dayjs()] },
  // { label: '最近6日', value: [dayjs().add(-5, 'd'), dayjs()] },
])

const rules: Record<string, Rule[]> = {
  execMethod: [
    { required: true, message: '请选择执行方案', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择执行日期', trigger: 'change' }
  ]
}

function handleCancel() {
  visible.value = false
}

const handleExec = async () => {
  await patientFormRef.value?.validate()
  const planLs = []
  if (planOeLs.value && planOeLs.value.length > 0) {
    planOeLs.value.forEach((planOe: any) => {
      if (planOe.planFeeLs && planOe.planFeeLs.length > 0) {
        planOe.planFeeLs.forEach((planFee: any) => {
          planFee.forFirstTime = planFee.forFirstTimeBool ? 1 : 0
          planFee.forEachTime = planFee.forEachTimeBool ? 1 : 0
        })
      }
      let plan = deepClone(planOe)
      plan.keyStr = undefined
      plan.sectionId = undefined
      plan.oeText = undefined
      plan.oeDisplayOrder = undefined
      plan.bedDisplayOrder = undefined
      plan.roomDisplayOrder = undefined
      plan.bedNo = undefined
      plan.routeName = undefined
      plan.patientName = undefined
      plan.freqCode = undefined
      plan.dpm = undefined
      if (plan.planFeeLs && plan.planFeeLs.length > 0) {
        plan.planFeeLs.forEach((planFee: any) => {
          planFee.artName = undefined
          planFee.packUnit = undefined
          planFee.cellUnit = undefined
          planFee.unit = undefined
          planFee.stockReq = undefined
        })
      }
      planLs.push(plan)
    })
  }
  handleExecOe(formModel.value.execMethod, formModel.value.startDate.format('YYYYMMDD'), formModel.value.endDate.format('YYYYMMDD'),
      formModel.value.treatmentTime.format('YYYY-MM-DD HH:mm:ss'), planLs, formModel.value.checkPassDateExec ? 1 : 0)
}

function handleExecOe(execMethod: number, startDateInt: number, endDateInt: number, treatmentTime: string, planOeLs: any, checkPassDateExec: any) {
  if (startDateInt > endDateInt) {
    message.warning('起始日期不能晚于终止日期')
    return
  }
  const oeIdLs = selectedRows.value.map((item: any) => {
    return {
      visitId: item.visitId,
      oeNo: item.oeNo,
    }
  })
  const hasDischargeOeLs = selectedRows.value.filter((item: any) => item.dischargeOe)
  const noneDischargeOeLs = selectedRows.value.filter((item: any) => !item.dischargeOe)
  if (hasDischargeOeLs && hasDischargeOeLs.length > 0 && noneDischargeOeLs && noneDischargeOeLs.length > 0) {
    message.warning('出院医嘱请单独执行')
    return
  }
  else if (hasDischargeOeLs && hasDischargeOeLs.length > 0) {
    // 出院医嘱在出区执行，不在这执行
    const visitIdLs = hasDischargeOeLs.map((oe) => { return oe.visitId })
    const uniqueNumbers = [...new Set(visitIdLs)]
    if (uniqueNumbers.length > 1) {
      message.warning('一次只能执行一条诊疗记录的出院医嘱')
      return
    }
    sessionStorage.setItem(SECTION_DISCHARGE_VISIT_ID, `${visitIdLs[0]}`)
    setTimeout(() => {
      router.push({ name: 'nurseHome' })
    }, 50)
    return
  }
  const noneSt = selectedRows.value.filter((item: any) => item.stFlag && !item.stResult)
  const hasPositiveSt = selectedRows.value.filter((item: any) => item.stFlag && item.stResult === 2)
  if ((noneSt && noneSt.length > 0) || (hasPositiveSt && hasPositiveSt.length > 0)) {
    const content = `选中医嘱中存在${noneSt ? '未执行皮试医嘱' : ''}${hasPositiveSt ? '皮试阳性医嘱' : ''}是否继续执行费用？`
    Modal.confirm({
      title: '提示',
      content,
      cancelText: '取消',
      okText: '继续执行',
      onOk() {
        handleExecOeConfirm(execMethod, startDateInt, endDateInt, treatmentTime, oeIdLs, planOeLs, checkPassDateExec)
      },
    })
  }
  else {
    handleExecOeConfirm(execMethod, startDateInt, endDateInt, treatmentTime, oeIdLs, planOeLs, checkPassDateExec)
  }
}

function handleExecOeConfirm(execMethod: number, startDateInt: number, endDateInt: number, treatmentTime: string, oeIdLs: any, planOeLs: any, checkPassDateExec: any) {
  const groupOeIdLs = groupByVisitId(oeIdLs)
  if (drugAccMode.value) {
    handleExecOeConfirmDrugMode(execMethod, startDateInt, endDateInt, treatmentTime, groupOeIdLs, planOeLs, checkPassDateExec)
  } else {
    handleExecOeConfirmNormalMode(execMethod, startDateInt, endDateInt, treatmentTime, groupOeIdLs, planOeLs, checkPassDateExec)
  }
}

const groupByVisitId = (oeIdLs) => {
  return oeIdLs.reduce((acc, {visitId, oeNo}) => {
    // 查找该 visitId 是否已经存在
    let existing = acc.find(item => item.visitId === visitId);

    if (existing) {
      // 如果存在，直接把 oeNo 加入对应的 oeNoLs 数组
      existing.oeNoLs.push(oeNo);
    } else {
      // 如果不存在，新增一个新对象
      acc.push({visitId, oeNoLs: [oeNo]});
    }

    return acc;
  }, []);
}

function handleExecOeConfirmDrugMode(execMethod: number, startDateInt: number, endDateInt: number, treatmentTime: string, groupOeIdLs: any, planOeLs: any, checkPassDateExec: any) {
  confirmLoading.value = true;
  execResultLogRef.value?.clearResults();

  const modal = Modal.info({
    title: '医嘱执行结果',
    width: 1000,
    style:"top: 20px",
    okText: '确定',
    content: () => h(ExecResultLog, { ref: execResultLogRef }),
    okButtonProps: { disabled: true },
  });

  const executeNext = (index: number) => {
    if (index >= groupOeIdLs.length) {
      // 所有诊疗执行完毕
      modal.update({
        okButtonProps: { disabled: index < groupOeIdLs.length - 1 },
      });

      const eventResultLs = execResultLogRef.value?.getResults()
      if (eventResultLs.some(result => result.success)) {
        emit('executed')
      }
      if (eventResultLs.every(result => result.success)) {
        message.success('医嘱执行计费完成')
        // 延迟执行scrollToTop
        setTimeout(() => {
          execResultLogRef.value?.scrollToTop()
          modal.destroy();
        }, 1000)
      } else {
        // 延迟执行scrollToTop
        setTimeout(() => {
          execResultLogRef.value?.scrollToTop()
        }, 200)
      }
      confirmLoading.value = false;
      handleCancel()
      return;
    }

    const group = groupOeIdLs[index];
    const visitTitle = getVisitTitle(group.visitId);
    const visitPlanOeLs = planOeLs.filter((plan: any) => plan.visitId === group.visitId);
    const params = {
      sectionId: props.sectionId,
      feedMethod: props.feedMethod,
      oeTypeId: props.oeTypeId,
      startDateInt,
      endDateInt,
      execMethod,
      treatmentTime,
      groupOeIdLs: [group],
      planOeLs: visitPlanOeLs,
      checkPassDateExec,
      useExecSectionStock: props.useExecSectionStock
    };

    execArtOeLsByDrugModeApi(params).then(() => {
      execResultLogRef.value?.addResult({ title: visitTitle, message: '', success: true });
    }).catch((err: any) => {
      execResultLogRef.value?.addResult({ title: visitTitle, message: err.message, success: false });
    }).finally(() => {
      // 执行下一个诊疗
      executeNext(index + 1);
    });
  };

  // 开始执行第一个诊疗
  executeNext(0);
}

const getVisitTitle = (visitId: any) => {
  let title = ''
  if (visitId) {
    const visitOeLs = selectedRows.value.filter((item: any) => item.visitId === visitId)
    if (visitOeLs && visitOeLs.length > 0) {
      let bedMsg = visitOeLs[0].bedNo
      if (bedMsg && !bedMsg.endsWith('床')) {
        bedMsg = bedMsg + '床'
      }
      title = bedMsg + ' ' + visitOeLs[0].patientName
    }
  }
  return title
}

function handleExecOeConfirmNormalMode(execMethod: number, startDateInt: number, endDateInt: number, treatmentTime: string, groupOeIdLs: any, planOeLs: any, checkPassDateExec: any) {
  confirmLoading.value = true
  execResultLogRef.value?.clearResults();

  const modal = Modal.info({
    title: '医嘱执行结果',
    width: 1000,
    style: "top: 20px",
    okText: '确定',
    content: () => h(ExecResultLog, { ref: execResultLogRef }),
    okButtonProps: { disabled: true },
  });

  const executeNext = (index: number) => {
    if (index >= groupOeIdLs.length) {
      // 所有诊疗执行完毕
      modal.update({
        okButtonProps: { disabled: index < groupOeIdLs.length - 1 },
      });

      const eventResultLs = execResultLogRef.value?.getResults()
      if (eventResultLs.some(result => result.success)) {
        emit('executed')
      }
      if (eventResultLs.every(result => result.success)) {
        message.success('医嘱执行计费完成')
        setTimeout(() => {
          execResultLogRef.value?.scrollToTop()
          modal.destroy();
        }, 1000)
      } else {
        setTimeout(() => {
          execResultLogRef.value?.scrollToTop()
        }, 200)
      }
      confirmLoading.value = false;
      handleCancel()
      return;
    }

    const group = groupOeIdLs[index];
    const visitTitle = getVisitTitle(group.visitId);
    const visitPlanOeLs = planOeLs.filter((plan: any) => plan.visitId === group.visitId);
    const params = {
      sectionId: props.sectionId,
      feedMethod: props.feedMethod,
      oeTypeId: props.oeTypeId,
      startDateInt,
      endDateInt,
      execMethod,
      treatmentTime,
      groupOeIdLs: [group],
      planOeLs: visitPlanOeLs,
      checkPassDateExec
    };
    execArtOeLsApi(params).then(() => {
      execResultLogRef.value?.addResult({ title: visitTitle, message: '', success: true });
    }).catch((err: any) => {
      execResultLogRef.value?.addResult({ title: visitTitle, message: err.message, success: false });
    }).finally(() => {
      executeNext(index + 1);
    });
  };

  executeNext(0);
}

const deepClone = (obj: any) => {
  return JSON.parse(JSON.stringify(obj))
}

// const handleReqNormalArt = async () => {
//   emit('reqNormalArt', formModel.value.startDate.format('YYYYMMDD'), formModel.value.endDate.format('YYYYMMDD'), formModel.value.treatmentTime.format('YYYY-MM-DD HH:mm:ss'))
//   handleCancel()
// }
//
// const handleReqConsumableArt = async () => {
//   emit('reqConsumableArt')
//   handleCancel()
// }
//
// const handleReqFirstAidArt = async () => {
//   emit('reqFirstAidArt')
//   handleCancel()
// }

const disabledDate = (current: Dayjs) => {
  return current && current > dayjs().endOf('day');
}

const disabledExec = computed(() => {
  if (poLs.value && poLs.value.length > 0) {
    return !planOeLs.value || planOeLs.value.length < poLs.value.length
  }
  return false
})

defineExpose({
  open,
  handleCancel
})
</script>

<template>
  <Modal v-model:open="visible" title="医嘱执行" width="900px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Button type="dashed" @click="handleCancel">
        关闭
      </Button>
    </template>
    <div class="content">
      <Form ref="patientFormRef" :model="formModel" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <Row>
          <Col :span="12">
            <Form-item label="执行方案" name="execMethod" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }">
                <Radio-group v-model:value="formModel.execMethod" name="radioGroup">
                  <Radio v-for="item in execMethodLs" :key="item.value" :value="item.value">{{ item.label }}</Radio>
<!--                  <Radio :value="2">按天执行</Radio>-->
                </Radio-group>
            </Form-item>
          </Col>
          <Col :span="12">
            <Form-item label="提醒费用漏记日期" name="checkPassDateExec" :colon="false" :label-col="{ span: 20 }">
              <Checkbox v-model:checked="formModel.checkPassDateExec"/>
            </Form-item>
          </Col>
        </Row>
        <Row>
          <Col :span="24" class="desc-msg">
            采用按天执行方案时，系统将按医嘱的执行频次一次性产生执行日期的费用，首日、尾日不执行全天次数的，请按次执行。
          </Col>
        </Row>
        <Row>
          <Col :span="16">
            <Form-item label="治疗时间" name="treatmentTime" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }" v-show="hasTreatmentOe">
              <Date-picker v-model:value="formModel.treatmentTime" show-time :disabled-date="disabledDate" format="YYYY-MM-DD HH:mm" :allow-clear="false"/>
            </Form-item>
          </Col>
        </Row>
        <Row>
          <Col :span="16">
            <Form-item label="执行日期" name="startDate" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
              <Range-picker :presets="rangePresets" v-model:value="formModel.dateRange" @change="onRangeChange" :allow-clear="false"/>
            </Form-item>
          </Col>
          <Col :span="4">
          </Col>
          <Col :span="4">
            <Button @click="handleExec" :loading="confirmLoading" :disabled="disabledExec || oePlanLoading" type="primary">
              执行计费
            </Button>
          </Col>
        </Row>
        <Row>
          <Col :span="24" class="desc-msg" v-show="confirmLoading && !oePlanLoading">
            系统正在产生医嘱执行费用，请耐心等候.......
          </Col>
        </Row>
        <Row v-if="stockErr">
          <Col :span="21" class="stockErr">
            {{ stockErr }}
          </Col>
<!--          <Col :span="3">-->
<!--            <Button danger @click="handleReqNormalArt">申领库存</Button>-->
<!--          </Col>-->
        </Row>
        <Row v-else-if="consumableErr">
          <Col :span="21" class="stockErr">
            {{ consumableErr }}
          </Col>
<!--          <Col :span="3">-->
<!--            <Button danger @click="handleReqConsumableArt">申领库存</Button>-->
<!--          </Col>-->
        </Row>
        <Row v-else-if="firstAidStockErr">
          <Col :span="21" class="stockErr">
            {{ firstAidStockErr }}
          </Col>
<!--          <Col :span="3">-->
<!--            <Button danger @click="handleReqFirstAidArt">申领库存</Button>-->
<!--          </Col>-->
        </Row>
      </Form>
      <div v-if="planOeLs && planOeLs.length > 0">
        <div class="consumable-title"></div>
        <div v-for="planOe in planOeLs" :key="planOe.keyStr">
          <exec-route-plan-form :planOe="planOe"/>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.desc-msg {
  padding: 0px 10px 20px 10px;
}
.stockErr {
  color: red;
  padding-left: 5px;
  line-height: 32px;
}
.consumable-title {
  font-weight: bold;
  font-size: 18px;
  border-top: 1px solid #ccc;
}
.routeConsumable {
  max-height: 600px;
  overflow: auto;
}
.content {
  max-height: calc(100vh - 160px);
  overflow-y: auto;
}
</style>
