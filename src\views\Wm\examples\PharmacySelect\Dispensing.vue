<script setup lang="ts">
import { PharmacySelect } from '@mh-wm/pharmacy'
import { Card, Typography, Divider, Switch } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { dispensingUsage, importCode, packageJsonCode } from '../code/PharmacySelectCode'

const { Title } = Typography

// 处方发药药房ID
const dispensingPharmacyId = ref<string>()

// 是否用于处方发药
const forRecipeDispensing = ref(true)
</script>

<template>
  <Card title="用于处方发药的药房" class="mb-16px">
    <div flex items-center mb-16px>
      <span mr-8px>是否用于处方发药：</span>
      <Switch v-model:checked="forRecipeDispensing" />
      <span ml-8px>{{ forRecipeDispensing ? '是' : '否' }}</span>
    </div>

    <div mb-16px>
      <Title :level="4">处方发药药房</Title>
      <PharmacySelect v-model="dispensingPharmacyId" :forRecipeDispensing="forRecipeDispensing" w-200px />
      <div mt-8px>选中的药房ID: {{ dispensingPharmacyId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        当forRecipeDispensing为true时，只会显示支持处方发药的药房。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="dispensingUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
