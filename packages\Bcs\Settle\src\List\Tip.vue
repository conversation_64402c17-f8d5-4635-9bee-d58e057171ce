<script lang="ts" setup>
import { setCache, useLoading } from '@idmy/core'
import { Currents, getSql } from '@mh-base/core'
import { Alert } from 'ant-design-vue'

const hasCount = ref(0)
const { refresh } = defineProps({
  show: { type: Boolean, required: true },
  refresh: { type: Function, required: true },
})

useLoading(async () => {
  const { count } = await getSql(`select count(*) as count
                                  from microhis_bcs.t_cash
                                  where user_id = ${Currents.id}
                                    and validated_flag = 0
                                    and cash_type_id = 2
                                    and action_type = 2
                                    and org_id = ${Currents.tenantId}`)
  hasCount.value = count
}, true)

const onClose = () => {
  setCache('refundUnprocessed', false, 1000 * 60 * 5)
  refresh()
}
</script>

<template>
  <Alert
    v-if="show && hasCount"
    :message="`您存在【${hasCount}条】结算中的退费。请在下班或者交账之前处理。处理方式：【收费记录=>交易类型:退费=>结算状态:结算中=>继续退费】`"
    mb-8px
    closable
    type="warning"
    @close="onClose"
  />
</template>
