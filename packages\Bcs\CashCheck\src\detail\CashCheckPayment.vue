<script lang="ts" setup>
import { add, Data, Format, useLoading } from '@idmy/core'
import { Table } from 'ant-design-vue'
import { findGroupPaymentByCheckId } from '../services.ts'


const { checkId } = defineProps({
  checkId: { type: Number, required: true },
})

const columns: Data[] = [
  { align: 'center', dataIndex: 'paymentId', minWidth: 100, title: '支付方式' },
  { align: 'center', dataIndex: 'actualAmount', minWidth: 100, title: '实缴金额' },
]

const arr = ref<Data[]>([])
const [_, loading] = useLoading(async () => {
  arr.value = await findGroupPaymentByCheckId(checkId)
}, true)

const total = computed(() => arr.value.reduce((a, b) => add(a + b.accountAmount), 0));
const data = computed(() =>
  arr.value.map(item => ({
    ...item,
    ratio: ((item.accountAmount / total.value) * 100),
  })),
)
</script>

<template>
<div v-if="data.length" class="mb-16px">
  <div flex-center>
    <Space>
      <Format :value="data.filter((row: Data) => row.paymentType === 'CASH').reduce((a: number, b: Data) => a + b.accountAmount, 0)" prefix="应缴现金：" type="Currency" value-class="b primary fs16"/>
      <Format :value="data.filter((row: Data) => row.paymentType !== 'PREPAID').reduce((a: number, b: Data) => a + b.accountAmount, 0)" prefix="应缴总计：" type="Currency" value-class="b primary fs16"/>
      <Format :value="data.filter((row: Data) => row.paymentType === 'CASH').reduce((a: number, b: Data) => a + b.actualAmount, 0)" prefix="实缴现金：" type="Currency" value-class="b error fs16"/>
      <Format :value="data.filter((row: Data) => row.paymentType !== 'PREPAID').reduce((a: number, b: Data) => a + b.actualAmount, 0)" prefix="实缴总计：" type="Currency" value-class="b primary fs16"/>
    </Space>
  </div>
  <Table
    v-if="false"
    :bordered="false"
    :columns="columns"
    :data-source="data"
    :loading="loading"
    :pagination="false"
    :rowHeight="26"
    row-key="paymentType"
    size="small"
  >
    <template #bodyCell="{ column, record }">
      <Format
        v-if="column.dataIndex === 'paymentType'"
        :value="record.paymentType"
        params="PaymentType"
        type="Enum"
      />
      <template v-if="column.dataIndex === 'accountAmount'">
        <Format :value="record.accountAmount" type="Currency"/>
      </template>
      <template v-if="column.dataIndex === 'actualAmount'">
        <Format :value="record.actualAmount" type="Currency"/>
      </template>
      <template v-if="column.dataIndex === 'ratio'">
        <Format :value="record.ratio" suffix="%" type="Currency"/>
      </template>
    </template>
    </stable>
</div>
</template>
