<script setup lang="ts">
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { Card, Typography, Divider, Radio } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { searchTypeUsage, importCode, packageJsonCode } from '../code/ScmCustSelectCode'

const { Title } = Typography

// 单选值
const custId = ref<number>()

// 搜索类型
const searchType = ref<string>('all')

// 占位符映射
const placeholderMap = {
  all: '请输入供应商名称/编码/五笔/拼音码',
  name: '请输入供应商名称',
  code: '请输入供应商编码',
  wubi: '请输入供应商五笔码',
  pinyin: '请输入供应商拼音码'
}
</script>

<template>
  <Card title="搜索类型" class="mb-16px">
    <div mb-16px>
      <Title :level="4">搜索类型选择</Title>
      <div mb-16px>
        <Radio.Group v-model:value="searchType">
          <Radio value="all">全部</Radio>
          <Radio value="name">名称</Radio>
          <Radio value="code">编码</Radio>
          <Radio value="wubi">五笔码</Radio>
          <Radio value="pinyin">拼音码</Radio>
        </Radio.Group>
      </div>
      
      <ScmCustSelect 
        v-model="custId" 
        :searchType="searchType"
        :placeholder="placeholderMap[searchType]"
        style="width: 100%" 
      />
      <div mt-8px>选中的供应商ID: {{ custId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        可以根据不同的搜索类型进行精确搜索，支持名称、编码、五笔码和拼音码
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="searchTypeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
