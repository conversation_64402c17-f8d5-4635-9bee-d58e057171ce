<script lang="ts" setup>
import { add, Data, Format, useLoading } from '@idmy/core'
import { listMergedPaidByBlueCashId } from '@mh-bcs/util'
import { Collapse, CollapsePanel, Space } from 'ant-design-vue'
import Payment from '../Payment.vue'
import Detail from './Detail.vue'

const emits = defineEmits(['load'])

const { cash } = defineProps({
  cash: { type: Object as PropType<Data>, required: true },
  payments: { type: Array as PropType<Data[]>, required: true },
})

const rows = ref<Data[]>([])
const sumBillAmt = ref(0)
useLoading(async () => {
  rows.value = await listMergedPaidByBlueCashId(cash.cashId)
  sumBillAmt.value = rows.value.reduce((sum: number, item: Data) => add(sum, item.amount), 0)
  emits('load', rows.value, sumBillAmt.value)
}, true)

const activeKey = defineModel('activeKey')
</script>
<template>
  <Collapse v-model:activeKey="activeKey" :bordered="true" accordion>
    <CollapsePanel key="1" :header="`退费前#${cash.cashId}`">
      <template #extra>
        <Space>
          <Format :value="sumBillAmt" prefix="总金额：" type="Currency" value-class="primary b" />
        </Space>
      </template>
      <Detail :data="rows" :height="160" />
      <Payment v-if="false" :data="payments" />
    </CollapsePanel>
  </Collapse>
</template>
