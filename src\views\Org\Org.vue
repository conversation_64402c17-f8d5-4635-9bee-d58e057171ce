<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个组件示例
import OrgSelectExample from './examples/OrgSelectExample.vue'
import OrgDeptExample from './examples/OrgDeptExample.vue'
import OrgDoctorExample from './examples/OrgDoctorExample.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('orgSelect')
</script>

<template>
  <Card title="Org 组件" class="mb-16px">
    <Paragraph>组织机构相关组件，包括组织机构选择、部门选择和医生选择。</Paragraph>

    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane key="orgSelect" tab="组织机构选择">
        <OrgSelectExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="orgDept" tab="部门选择">
        <OrgDeptExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="orgDoctor" tab="医生选择">
        <OrgDoctorExample />
      </Tabs.TabPane>
    </Tabs>
  </Card>
</template>

<style scoped>
/* 全局样式可以放在这里 */
:deep(.ant-tabs-left > .ant-tabs-content-holder) {
  border-left: 1px solid #f0f0f0;
  padding-left: 16px;
}

:deep(.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab) {
  padding: 8px 16px;
}

:deep(.ant-card-body) {
  padding: 24px;
}
</style>
