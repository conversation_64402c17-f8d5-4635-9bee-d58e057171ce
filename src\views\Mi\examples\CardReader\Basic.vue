<script setup lang="ts">
import { CardReaderButton } from '@mh-mi/card-reader'
import { Card, Typography, Divider } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from '../code/CardReaderCode'

const { Title } = Typography

// 读卡成功回调
const onSuccess = (data: any) => {
  console.log('读卡成功', data)
}
</script>

<template>
  <Card title="基础用法 - 读卡按钮" class="mb-16px">
    <div mb-16px>
      <Title :level="4">读卡按钮</Title>
      <CardReaderButton 
        business-type-id="104"
        :medical-type="21"
        @success="onSuccess"
      />
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        点击按钮将打开读卡弹窗，可以进行读卡操作
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="basicUsage"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
