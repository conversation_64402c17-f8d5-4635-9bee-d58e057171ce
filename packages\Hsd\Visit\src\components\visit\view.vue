<script setup lang="ts">
import { Card, Row, Col, Button, Avatar } from 'ant-design-vue'
// 诊疗记录详情
const props = defineProps({
  visitId: {
    type: Number,
    default: 0
  },
  isRef: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['confirm'])

import { findVisitInfoApi, ageFormatYearDays, nationalityList } from '@mh-hsd/util'

const isDesensitized = shallowRef<number>(1)
const visit = ref<any>({})

function handleShowNo () {
  isDesensitized.value = isDesensitized.value === 1 ? 0 : 1
  findVisitInfo()
}

async function findVisitInfo() {
  try {
    const data: any = await findVisitInfoApi(props.visitId, isDesensitized.value)
    visit.value = data ?? {}
  } catch (err) {
  }
}

const patientNationalityName = computed(() => {
  if (visit.value.patientNationalityCode) {
    const nationality = nationalityList.find((item: any) => item.nationalityCode === visit.value.patientNationalityCode)
    if (nationality) {
      return nationality.nationalityName
    }
  }
  return ''
})

const westDiagLs = computed(() => {
  let diagLs = []
  if (visit.value && visit.value.visitDiagLs) {
    diagLs = visit.value.visitDiagLs.filter((item: any) => item.diagTypeId === 1 || item.diagTypeId === 4)
  }
  const westDiagLs: any = []
  diagLs.forEach((item: any) => {
    if (item.diagStatus !== 2) {
      westDiagLs.push(item)
    }
  })
  diagLs.forEach((item: any) => {
    if (item.diagStatus === 2) {
      westDiagLs.push(item)
    }
  })
  return westDiagLs
})

const tcmDiagLs = computed(() => {
  let diagLs = []
  if (visit.value && visit.value.visitDiagLs) {
    diagLs = visit.value.visitDiagLs.filter((item: any) => (item.diagTypeId === 2 || item.diagTypeId === 3) && item.diagStatus === 1)
  }
  const tcmDiagLs: any = []
  diagLs.forEach((item: any) => {
    if (item.diagTypeId === 2) {
      tcmDiagLs.push(item)
    }
  })
  diagLs.forEach((item: any) => {
    if (item.diagTypeId === 3) {
      tcmDiagLs.push(item)
    }
  })
  return tcmDiagLs
})

function handleVisitRef () {
  emit('confirm', 'visitRef', visit.value.visitId)
}

const pastHistory = computed(() => {
  let pastHistory = ''
  if (visit.value.pastHistory) {
    // 既往史
    const pastHistoryLs: any = visit.value.pastHistory.split(';').filter((item: any) => item !== '')
    let index = 0
    if (visit.value.visitIllnessLs) {
      visit.value.visitIllnessLs.forEach((item: any) => {
        if (pastHistory !== '') {
          pastHistory += ';'
        }
        pastHistory = pastHistory + '' + item.illnessName + '：' + pastHistoryLs[index]
        index++
      })
      if (index === pastHistoryLs.length - 1) {
        if (pastHistory !== '') {
          pastHistory += ';'
        }
        pastHistory = pastHistory + '其他：' + pastHistoryLs[index]
      }
    }
  }
  return pastHistory
})

const signUrl = computed(() => {
  return (url: string) => {
    if (url.indexOf('http') === -1 && url.indexOf('https') === -1) {
      return import.meta.env.VITE_API_USER_FILES + url
    } else {
      return url
    }
  }
})

watch(() => props.visitId, (val) => {
  if (val) {
    findVisitInfo()
  }
}, {
  immediate: true,
  deep: true
})
</script>

<template>
  <Card :bordered="false" m-b-5px>
    <h2 text-center font-bold font-size-18px>{{ visit?.orgName }}门诊电子病历</h2>
    <Row>
      <Col :span="8">
        <span m-r-10px>患者：{{ visit?.patientName }}</span>
        <span m-r-10px>{{ visit?.genderName }}</span>
        <span>{{ ageFormatYearDays(visit?.ageOfYears, visit?.ageOfDays) }}</span>
      </Col>
      <Col :span="10" text-center>
        <span v-if="visit?.companionName">陪诊人：{{ visit?.companionName }} {{ visit?.contactTel }}</span>
        <a c-primary v-if="visit?.contactTel" @click="handleShowNo">{{ isDesensitized == 1 ? '显示号码' : '隐藏号码' }}</a>
      </Col>
      <Col :span="6" text-right>门诊号：{{ visit?.visitId }}</Col>
    </Row>
    <div class="bline-dotted"></div>
    <Row>
      <Col :span="24">科&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;室：{{ visit?.deptName }}</Col>
      <Col :span="12">职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;业：{{ visit?.careerName }}</Col>
      <Col :span="12">婚&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;姻：{{ visit?.marriageStatusName }}</Col>
      <Col :span="12">出&nbsp;&nbsp;生&nbsp;&nbsp;地：{{ visit?.patientBirthPlace }}</Col>
      <Col :span="12">民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族：{{ patientNationalityName }}</Col>
      <Col :span="12">住&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;址：{{ visit?.livingAddr }}</Col>
      <Col :span="12">病史报告者：患者本人</Col>
      <Col :span="24" m-y-2px>
        <span>主&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;诉：</span>
        <span>{{ visit?.complainedAs }}</span>
      </Col>
      <Col :span="24" m-t-10px>
        <span>现&nbsp;&nbsp;病&nbsp;&nbsp;史：</span>
        <span>{{ visit?.hpiDesc }}</span>
      </Col>
      <Col :span="24" m-t-10px>
        <span>既&nbsp;&nbsp;往&nbsp;&nbsp;史：</span>
        <span>{{ pastHistory }}</span>
      </Col>
      <template v-if="visit?.ageOfYears && visit?.ageOfYears > 10">
        <Col :span="24" m-t-10px>
          <span>婚&nbsp;&nbsp;姻&nbsp;&nbsp;史：</span>
          <span>{{ visit?.maritalHistory }}</span>
        </Col>
        <Col :span="24" m-t-10px v-if="visit?.genderId === 2">
          <span>生&nbsp;&nbsp;育&nbsp;&nbsp;史：</span>
          <span>{{ visit?.childbearingHistory }}</span>
        </Col>
        <Col :span="24" m-t-10px v-if="visit?.genderId === 2">
          <span>月&nbsp;&nbsp;经&nbsp;&nbsp;史：</span>
          <span>{{ visit?.menstrualHistory }}</span>
        </Col>
      </template>
      <Col :span="24" m-t-10px>
        <span>过&nbsp;&nbsp;敏&nbsp;&nbsp;史：</span>
        <span>{{ visit?.allergicHistory }}</span>
      </Col>
      <Col :span="24" m-t-10px>
        <span>家&nbsp;&nbsp;族&nbsp;&nbsp;史：</span>
        <span>{{ visit?.familyHistory }}</span>
      </Col>
      <Col :span="24" m-t-10px class="flex">
        <span>一般检查：</span>
        <span>
          <div>
            <span v-if="visit?.temperature" m-r-20px>T：<span style="color: #666666;">{{ visit?.temperature }} ℃</span></span>
            <span v-if="visit?.pulse" m-r-20px>P：<span style="color: #666666;">{{ visit?.pulse }} 次/分</span></span>
            <span v-if="visit?.rr" m-r-20px>R：<span style="color: #666666;">{{ visit?.rr }} 次/分</span></span>
            <span v-if="visit?.rr" m-r-20px>BP：<span style="color: #666666;">{{ visit?.sbp }}/{{ visit?.dbp }} mmHg</span></span>
          </div>
          <div>
            {{ visit?.generalInspection }}
          </div>
        </span>
      </Col>
      <Col :span="24" m-t-10px>
        <span>辅助检查：</span>
        <span>{{ visit?.auxiliaryInspection }}</span>
      </Col>
      <Col :span="24" m-t-10px class="flex">
        <div>初步诊断：</div>
        <div class="flex flex-col" style="width: 80%;">
          <div v-if="westDiagLs && westDiagLs.length > 0" class="flex w-full" m-b-10px>
            <div><span class="custom-xyzz-tags">西</span></div>
            <div m-l-10px>
              <span v-for="(diag, ind) in westDiagLs" :key="ind" :class="{ 'zd-con': true, 'del-zd': diag.diagStatus === 2 }">
                <span class="custom-diag-xh">{{ ind + 1 }}.</span>
                <span>{{ diag.diagName }}<span v-if="diag.subtypeDesc" p-l-5px>{{ diag.subtypeDesc }}</span></span>
                <span v-if="ind === 0" class="zd-main">（主诊断）</span>
                <span v-if="diag.diagStatus === 0" m-r-5px class="alink2">?</span>
                <span v-else-if="diag.diagStatus === 2" class="zd-main">[排除]</span>
              </span>
            </div>
          </div>
          <div v-if="tcmDiagLs && tcmDiagLs.length > 0" class="flex w-full">
            <div><span class="custom-zyzz-tags">中</span></div>
            <div m-l-10px>
              <span v-for="(diag, ind) in tcmDiagLs" :key="ind" class="zd-con">
                {{ diag.diagName }}
                <span v-if="diag.diagTypeId === 2" class="zd-main">（主病）</span>
                <span v-else-if="diag.diagTypeId === 3" class="zd-main">（主症）</span>
              </span>
            </div>
          </div>
        </div>
      </Col>
      <Col :span="24" m-t-10px>
        <span>处理意见：</span>
        <span>{{ visit?.treatAbstract }}</span>
      </Col>
      <Col :span="24" v-if="visit?.healthEducation" m-t-10px>
        <span>健康宣教：</span>
        <span>{{ visit?.healthEducation }}</span>
      </Col>
      <Col :span="24" v-if="visit?.progressNotes" m-t-10px>
        <span>病程记录：</span>
        <span>{{ visit?.progressNotes }}</span>
      </Col>
    </Row>
    <div class="bline-solid"></div>
    <Row class="custom-ref-content">
      <Col :span="12">
        <span>接诊医生：</span>
        <Avatar v-if="visit?.signatureUrl" :src="signUrl(visit?.signatureUrl)" shape="square" :style="{ width: '60px' }"></Avatar>
        <span v-else>{{ visit.clinicianName }}</span>
      </Col>
      <Col :span="12" v-if="isRef" text-right>
        <Button type="primary" @click="handleVisitRef">引用</Button>
      </Col>
    </Row>
  </Card>
</template>

<style scoped lang="less">
@import url(../style.css);
</style>
