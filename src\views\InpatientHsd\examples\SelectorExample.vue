<script setup lang="ts">
import { SectionArtSelect, ArtSelect, WmDeptArtSelect, WmOrgArtSelect, SelectWithUnitTotal } from '@mh-inpatient-hsd/selector'
import { Card, Typography, Divider, Button, message, Form, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import {
  sectionArtSelectUsage,
  artSelectUsage,
  wmDeptArtSelectUsage,
  wmOrgArtSelectUsage,
  selectWithUnitTotalUsage,
  importCode,
  packageJsonCode
} from './code/SelectorCode'

const { Title, Paragraph } = Typography

// 表单引用
const formRef = ref()
const formState = ref({
  sectionId: undefined,
  artId: undefined,
  sectionArtId: undefined,
  wmDeptArtId: undefined,
  wmOrgArtId: undefined
})

// 选择回调
const handleChange = (value, option) => {
  console.log('选择值:', value)
  console.log('选择项:', option)
  message.success(`已选择: ${option?.label || value}`)
}

// 选择物品回调
const handleSelected = (selectedArt) => {
  console.log('选择物品:', selectedArt)
  message.success(`已选择物品: ${selectedArt?.artName || '未知物品'}`)
}

// 添加物品回调
const handleAddArt = (selectedArt) => {
  console.log('添加物品:', selectedArt)
  message.success(`已添加物品: ${selectedArt?.artName || '未知物品'}`)
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
}

// 模拟数据
const sectionId = 40
const wmDeptCode = '000013'
const artSelectWithUnitTotalRef = ref()

// 打开条目选择带单位数量弹窗
const handleVisibleArtSelectWithUnitTotal = () => {
  artSelectWithUnitTotalRef.value.open()
}
</script>

<template>
  <Card title="选择器组件 - 科室物品选择器" class="mb-16px">
    <div mb-16px>
      <Title :level="4">科室物品选择器</Title>
      <Paragraph>用于选择科室物品。</Paragraph>

      <Form ref="formRef" :model="formState" layout="vertical">
        <Row :gutter="16">
          <Col :span="12">
            <Form.Item label="科室物品" name="sectionArtId">
              <SectionArtSelect
                v-model:value="formState.sectionArtId"
                :section-id="sectionId"
                placeholder="请选择科室物品"
                @change="handleChange"
                @selected="handleSelected"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col :span="24">
            <Button type="primary" @click="resetForm" style="margin-right: 8px">重置</Button>
          </Col>
        </Row>
      </Form>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="sectionArtSelectUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>



  <Card title="选择器组件 - 物品选择器" class="mb-16px">
    <div mb-16px>
      <Title :level="4">物品选择器</Title>
      <Paragraph>用于选择物品。</Paragraph>

      <Form ref="formRef" :model="formState" layout="vertical">
        <Row :gutter="16">
          <Col :span="12">
            <Form.Item label="物品" name="artId">
              <ArtSelect
                v-model:value="formState.artId"
                placeholder="请选择物品"
                @change="handleChange"
                @selected="handleSelected"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col :span="24">
            <Button type="primary" @click="resetForm" style="margin-right: 8px">重置</Button>
          </Col>
        </Row>
      </Form>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="artSelectUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <Card title="选择器组件 - 药库科室物品选择器" class="mb-16px">
    <div mb-16px>
      <Title :level="4">药库科室物品选择器</Title>
      <Paragraph>用于选择药库科室物品。</Paragraph>

      <Form ref="formRef" :model="formState" layout="vertical">
        <Row :gutter="16">
          <Col :span="12">
            <Form.Item label="药库科室物品" name="wmDeptArtId">
              <WmDeptArtSelect
                v-model:value="formState.wmDeptArtId"
                :deptCode="wmDeptCode"
                :searchType="6"
                placeholder="请选择药库科室物品"
                @selected="handleSelected"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col :span="24">
            <Button type="primary" @click="resetForm" style="margin-right: 8px">重置</Button>
          </Col>
        </Row>
      </Form>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="wmDeptArtSelectUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <Card title="选择器组件 - 药库机构物品选择器" class="mb-16px">
    <div mb-16px>
      <Title :level="4">药库机构物品选择器</Title>
      <Paragraph>用于选择药库机构物品。</Paragraph>

      <Form ref="formRef" :model="formState" layout="vertical">
        <Row :gutter="16">
          <Col :span="12">
            <Form.Item label="药库机构物品" name="wmOrgArtId">
              <WmOrgArtSelect
                v-model:value="formState.wmOrgArtId"
                placeholder="请选择药库机构物品"
                @selected="handleSelected"
              />
            </Form.Item>
          </Col>
        </Row>
        <Row>
          <Col :span="24">
            <Button type="primary" @click="resetForm" style="margin-right: 8px">重置</Button>
          </Col>
        </Row>
      </Form>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="wmOrgArtSelectUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <Card title="选择器组件 - 条目选择带单位数量" class="mb-16px">
    <div mb-16px>
      <Title :level="4">条目选择带单位数量</Title>
      <Paragraph>用于选择物品并指定数量。</Paragraph>

      <Button type="primary" @click="handleVisibleArtSelectWithUnitTotal">打开条目选择带单位数量弹窗</Button>
      <SelectWithUnitTotal
        ref="artSelectWithUnitTotalRef"
        :artSearchType="1"
        @add-art="handleAddArt"
      />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="selectWithUnitTotalUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
