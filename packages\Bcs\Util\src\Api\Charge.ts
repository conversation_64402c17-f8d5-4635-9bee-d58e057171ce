import type { Data } from '@idmy/core'
import { Message, useLoading } from '@idmy/core'
import { lodapPrint, LodopOptions } from '@mh-base/core'
import { CashType, createElectricInvoice, getCashInfo, hasPrintTemplate, PaymentType } from '@mh-bcs/util'
import dayjs from 'dayjs'
import { isNil } from 'lodash-es'
import type { InjectionKey } from 'vue'


export interface ChargeContext {
  prepaid: number
  accountId?: number
  allowPayZero?: boolean
  autoFinish?: boolean
  blueCashId?: number
  authCode?: string
  card?: Data
  visit?: Data
  cashId: number
  insuranceTypeId: number
  medTypeId: number
  diseaseCode: number
  diseaseName: number
  cashType: CashType
  finished: boolean
  isFullAmtMiPay?: any
  isZero: boolean
  miFundCashPayment?: any
  miOnSettle?: any
  miTrans?: any
  midway?: boolean
  onLoad: (cashId?: number) => Promise<void>
  payAmount: number
  paying: boolean
  paymentType: PaymentType
  totalAmount: number
  unpaidAmount: number
  uploadFailedList?: any
  visitId: number
  onRefundPayment?: any
  isRefund?: any
  finishing?: any
  onFinish?: any
  preSettleLoading?: any
}

export const chargeInjectKey: InjectionKey<ChargeContext> = Symbol('chargeInjectKey')


export async function createInvoice(cashId: number) {
  const msg = Message.loading({ content: '正在开具电子发票……', duration: 0 })
  try {
    await createElectricInvoice(cashId)
  } catch {
  }
  msg()
}

export const READ_CARD_SUCCESS_EVENT = Symbol('读卡成功')

async function openInvoice(cashId: number, cashType: CashType, options?: LodopOptions) {
}

const [printCash] = useLoading(async (cashId: number, cashType: CashType, options?: LodopOptions) => {
  if (cfg.setting?.cashPrintTicket === 1 || cfg.setting?.cashPrintTicket === 3 || isNil(cfg.setting?.cashPrintTicket)) {
    const code = `${cashType}:charge`
    if (await hasPrintTemplate(code)) {
      const msg = Message.loading({ content: '正在打印小票……', duration: 0 })
      try {
        const tmp = await getCashInfo(cashId);
        await lodapPrint(code, tmp, options)
      } catch {
      } finally {
        msg()
      }
    }
  } else if (cfg.setting?.cashPrintTicket === 2) {
    await openInvoice(cashId, cashType, options)
  }
})

export { printCash }

export function sumLeEndDateUnpaidAmt(visitId: number, endDate: any) {
  endDate = dayjs(endDate).format('YYYYMMDD')
  return http.post('/api/Bcs/Bill/sumLeEndDateUnpaidAmt', { visitId, endDate }, { appKey: 'bcs' })
}

export async function sumGeEndDateUnpaidAmt(visitId: number, endDate: any) {
  endDate = dayjs(endDate).add(1, 'day').format('YYYYMMDD')
  return http.post('/api/bcs/bill/sumGeEndDateUnpaidAmt', { visitId, endDate }, { appKey: 'bcs' })
}
