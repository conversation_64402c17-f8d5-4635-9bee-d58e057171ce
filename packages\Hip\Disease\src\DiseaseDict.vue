<script setup lang="ts">
import { Dict } from '@idmy/antd'
import { addDict, DictProps } from '@idmy/core'
import { getPatientDiseases } from '@mh-hip/util'

const emit = defineEmits(['update:modelValue', 'select'])

const props = defineProps({
  // 患者ID
  patientId: {
    type: Number,
    default: undefined
  },
  // 当前选择的病种
  modelValue: {
    type: [Number, String],
    default: undefined
  }
})

// 已选择的病种
const selectedDiseaseCode = ref(props.modelValue)

// 增加病种字典
addDict('Disease', async (): Promise<DictProps[]> => {
  // 默认使用分类1的病种
  const data: any = await getPatientDiseases({ patientId: props.patientId })
  return data.map(
    (row: any): DictProps => ({
      data: row,
      formatName: row.diseaseName,
      id: row.diseaseCode,
      name: row.diseaseName,
      value: row.diseaseCode,
    })
  )
})

// 选择病种
const handleSelect = (data: any) => {
  const record = data.data || {}
  emit('update:modelValue', record.diseaseCode)
  emit('select', record)
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  val => {
    selectedDiseaseCode.value = val
  }
)

</script>

<template>
  <Dict v-model="selectedDiseaseCode" clazz="Disease" @change="handleSelect"/>
</template>
