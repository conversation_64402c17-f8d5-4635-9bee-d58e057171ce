<script setup lang="ts">
import { ref } from 'vue'
import { Card, Typography, Button, message, Space } from 'ant-design-vue'
import { WmDeptArtSelect } from '@mh-inpatient-hsd/selector'

const { Title, Paragraph } = Typography

// 部门编码
const deptCode = ref('000013')

// 组件引用
const wmDeptArtSelectRef = ref()

// 测试数据
const testData = {
  artId: 1001,
  artName: '阿莫西林胶囊',
  artSpec: '0.25g*24粒',
  producer: '哈药集团制药总厂',
  packUnit: '盒',
  cellUnit: '粒',
  packCells: 24,
  splittable: 1
}

// 测试setValue方法
const testSetValue = () => {
  console.log('开始测试 setValue 方法')
  console.log('wmDeptArtSelectRef.value:', wmDeptArtSelectRef.value)
  
  if (wmDeptArtSelectRef.value) {
    console.log('可用的方法:', Object.keys(wmDeptArtSelectRef.value))
    
    if (typeof wmDeptArtSelectRef.value.setValue === 'function') {
      console.log('调用 setValue 方法，传入数据:', testData)
      wmDeptArtSelectRef.value.setValue(testData)
      message.success('setValue 方法调用成功')
    } else {
      console.error('setValue 方法不存在')
      message.error('setValue 方法不存在')
    }
  } else {
    console.error('组件引用不存在')
    message.error('组件引用不存在')
  }
}

// 清空组件
const clearComponent = () => {
  if (wmDeptArtSelectRef.value && typeof wmDeptArtSelectRef.value.init === 'function') {
    wmDeptArtSelectRef.value.init()
    message.success('组件已清空')
  } else {
    message.error('清空方法不存在')
  }
}

// 聚焦组件
const focusComponent = () => {
  if (wmDeptArtSelectRef.value && typeof wmDeptArtSelectRef.value.focus === 'function') {
    wmDeptArtSelectRef.value.focus()
    message.success('组件已聚焦')
  } else {
    message.error('聚焦方法不存在')
  }
}

// 选择回调
const handleSelected = (item: any) => {
  console.log('选择了品种:', item)
  message.success(`选择了品种: ${item.artName}`)
}
</script>

<template>
  <div style="padding: 20px;">
    <Card title="WmDeptArtSelect setValue 方法测试" style="margin-bottom: 20px;">
      <div style="margin-bottom: 16px;">
        <Title :level="4">WmDeptArtSelect 组件 setValue 方法测试</Title>
        <Paragraph>
          测试 WmDeptArtSelect 组件的 setValue 方法是否能正确设置显示值。
        </Paragraph>
      </div>

      <!-- 组件演示 -->
      <div style="margin-bottom: 20px; padding: 16px; background: #fafafa; border-radius: 8px;">
        <Title :level="5">组件演示</Title>
        <div style="margin-bottom: 16px;">
          <WmDeptArtSelect
            ref="wmDeptArtSelectRef"
            :deptCode="deptCode"
            :searchType="6"
            @selected="handleSelected"
            style="width: 400px"
          />
        </div>
      </div>

      <!-- 操作按钮 -->
      <div style="margin-bottom: 20px;">
        <Space wrap>
          <Button @click="testSetValue" type="primary">测试 setValue 方法</Button>
          <Button @click="clearComponent">清空组件</Button>
          <Button @click="focusComponent">聚焦组件</Button>
        </Space>
      </div>

      <!-- 测试数据显示 -->
      <div>
        <Title :level="5">测试数据</Title>
        <pre style="background: #f6f8fa; padding: 12px; border-radius: 4px; font-size: 12px;">{{
          JSON.stringify(testData, null, 2)
        }}</pre>
      </div>
    </Card>

    <!-- 调试信息 -->
    <Card title="调试信息">
      <div>
        <Title :level="5">当前状态</Title>
        <pre style="background: #f6f8fa; padding: 12px; border-radius: 4px; font-size: 12px;">{{
          JSON.stringify({
            deptCode: deptCode,
            searchType: 6,
            componentRef: !!wmDeptArtSelectRef.value
          }, null, 2)
        }}</pre>
      </div>

      <div style="margin-top: 16px;">
        <Title :level="5">组件方法</Title>
        <pre style="background: #f6f8fa; padding: 12px; border-radius: 4px; font-size: 12px;">{{
          wmDeptArtSelectRef.value 
            ? Object.keys(wmDeptArtSelectRef.value).join(', ')
            : '组件未加载'
        }}</pre>
      </div>
    </Card>
  </div>
</template>

<style scoped>
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
