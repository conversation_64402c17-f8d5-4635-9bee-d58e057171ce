<script setup lang="ts">
import filters from './utils/filters.ts'
import type { TableColumnType } from 'ant-design-vue'
import { Table, Button, Col, Form, FormItem, Modal, Row, Radio, RadioGroup, InputNumber, message } from 'ant-design-vue'
import { ArtSelect } from '@mh-inpatient-hsd/selector'
import { userPendingManualLsWithStartLineApi, oeExecFeeInfoApi, oeFeeAddArtApi, artPriceApi } from '@mh-inpatient-hsd/util'

const props = defineProps({
  sectionId: {
    type: Number,
    default: null
  }
})

const columns: TableColumnType[] = [
  {
    title: '序号',
    align: 'center',
    dataIndex: 'series',
    width: 50
  },
  {
    title: '项目编码',
    dataIndex: 'artCode',
    align: 'left',
    width: 120,
    ellipsis: true
  },
  {
    title: '医保项目编码',
    dataIndex: 'miCode',
    align: 'left',
    width: 130,
    ellipsis: true
  },
  {
    title: '项目内容',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    align: 'center',
    width: 60
  },
  {
    title: '数量',
    dataIndex: 'total',
    align: 'right',
    width: 90,
    fixed: 'right'
  },
  {
    title: '单价',
    dataIndex: 'price',
    align: 'right',
    width: 90,
    fixed: 'right'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    align: 'right',
    width: 100,
    fixed: 'right'
  }
]

const emit = defineEmits(['page'])
const visible = ref(false);
const formRef = ref()
const formModel = ref<any>({})
const stConfirmLoading = ref<boolean>(false)
const dataSource = ref<any>([])
const execSeqid = shallowRef<string>()
const lineNo = shallowRef<number>()
const oeInfo = ref({})

const artSelectRef = ref<InstanceType<typeof ArtSelect>>()
const open = (opExecSeqid: string, opLineNo: number) => {
  dataSource.value = []
  clearFormModel()
  nextTick(() => {
    artSelectRef.value?.init()
  })
  execSeqid.value = opExecSeqid
  lineNo.value = opLineNo
  getOeInfo()
  visible.value = true
}

const getOeInfo = async () => {
  oeInfo.value = {}
  oeExecFeeInfoApi({execSeqid: execSeqid.value, lineNo: lineNo.value}).then((data: any) => {
    if (data) {
      oeInfo.value = data
    }
  })
}

const getDataSource = async () => {
  dataSource.value = []
  const startLineNo = oeInfo.value && oeInfo.value.execFeeLineCount ? oeInfo.value.execFeeLineCount : 0
  userPendingManualLsWithStartLineApi({execSeqid: execSeqid.value, startLineNo: startLineNo}).then((list: any) => {
    if (list) {
      dataSource.value = list
    }
  })
}

const clearFormModel = () => {
  formModel.value.artId = null
  formModel.value.artSpec = null
  formModel.value.artName = null
  formModel.value.producer = null
  formModel.value.total = null
  formModel.value.unitType = null
  formModel.value.packUnit = null
  formModel.value.cellUnit = null
  formModel.value.artCode = null
  formModel.value.packPrice = null
  formModel.value.cellPrice = null
  formModel.value.packCells = null
}

function handleCancel() {
  visible.value = false
  emit('page')
}

// 药品选择
function handleArtSelect (art: any) {
  clearFormModel()
  if (art) {
    let displayName = null
    const params = {
      execSeqid: execSeqid.value,
      lineNo: lineNo.value,
      artId: art.artId
    }
    artPriceApi(params).then((data: any) => {
      if (data) {
        formModel.value.packPrice = data.packPrice
        formModel.value.cellPrice = data.cellPrice
      }
      formModel.value.artId = art.artId
      formModel.value.artSpec = art.artSpec
      formModel.value.artName = art.artName
      formModel.value.producer = art.producer
      formModel.value.packUnit = art.packUnit
      formModel.value.cellUnit = art.cellUnit
      formModel.value.artCode = art.artCode
      formModel.value.packCells = art.packCells
      displayName = art.artName + (art.artSpec ? '|' + art.artSpec :'') + (art.producer ? '|' + art.producer :'')
      formModel.value.total = 1
      if (!formModel.value.packUnit && !formModel.value.cellUnit) {
        formModel.value.packUnit = '次'
      }
      // && art.artTypeId !== 14
      if (art.stockReq === 1) {
        if (formModel.value.cellUnit) {
          formModel.value.unitType = 1
          formModel.value.unit = formModel.value.cellUnit
        } else if (formModel.value.packUnit) {
          formModel.value.unitType = 2
          formModel.value.unit = formModel.value.packUnit
        }
      } else {
        if (formModel.value.packUnit) {
          formModel.value.unitType = 2
          formModel.value.unit = formModel.value.packUnit
        } else if (formModel.value.cellUnit) {
          formModel.value.unitType = 1
          formModel.value.unit = formModel.value.cellUnit
        }
      }
    })
  }
}

const handleAdd = () => {
  stConfirmLoading.value = true
  const params = {
    execSeqid: execSeqid.value,
    lineNo: lineNo.value,
    artId: formModel.value.artId,
    total: formModel.value.total,
    unitType: formModel.value.unitType,
    unit: formModel.value.unitType === 2 ? formModel.value.packUnit : formModel.value.cellUnit
  }

  oeFeeAddArtApi(params).then(() => {
      message.success('新增补录完成')
      getDataSource()
      clearFormModel()
      artSelectRef.value?.init()
  }).finally(() => {
    stConfirmLoading.value = false
  })
}

const articlePrice = computed(() => {
  if (formModel.value.artId && formModel.value.unitType) {
    return formModel.value.unitType === 2 ? formModel.value.packPrice : formModel.value.cellPrice
  }
  return null
})

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" title="新增调整" width="1000px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Button type="dashed" @click="handleCancel">
        关闭
      </Button>
    </template>
    <div class="content-req">
      <Form ref="formRef" :model="formModel" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <Row>
          <Col :span="24">
            <span m-l-1 m-r-2>患者：{{ oeInfo.bedNo }}床</span>
            <span m-r-2>{{ oeInfo.patientName }}</span>
            <span m-r-2>{{ oeInfo.genderName }}</span>
            <span m-r-4>{{ filters.formatAge(oeInfo.ageOfYears, oeInfo.ageOfDays) }}</span>
            <span>医嘱：<span class="art-name">{{ oeInfo.oeText }}</span></span>
          </Col>
        </Row>
        <Row>
          <Col :span="11">
            <Form-item label="条目" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
              <art-select ref="artSelectRef" :searchType="4" @selected="handleArtSelect" :section-id="props.sectionId"/>
            </Form-item>
          </Col>
          <Col :span="4">
            <Form-item label="数量" name="total">
              <Input-number v-model:value="formModel.total" placeholder="请设置数量" :precision="4" :min="0" :max="99999999" style="width: 100%;"/>
            </Form-item>
          </Col>
          <Col :span="7">
            <Form-item label="单位" name="unitType">
              <Radio-group v-model:value="formModel.unitType">
                <Radio v-if="formModel.packUnit" :value="2">
                  {{ formModel.packUnit }}(包装)
                </Radio>
                <Radio v-if="formModel.cellUnit" :value="1">
                  {{ formModel.cellUnit }}(拆零)
                </Radio>
              </Radio-group>
            </Form-item>
          </Col>
          <Col :span="2" style="text-align: right">
            <Button @click="handleAdd" :loading="stConfirmLoading" type="primary">
              添加
            </Button>
          </Col>
        </Row>
        <Row>
          <Col :span="24" v-show="articlePrice">
            <span m-r-4 class="art-name">{{ formModel.artCode }}</span>
            <span m-r-4 class="art-name">{{ formModel.artName }}</span>
            <span m-r-4 class="art-name" v-show="formModel.artSpec">{{ formModel.artSpec }}</span>
            <span m-r-4 class="art-name" v-show="formModel.producer">{{ formModel.producer }}</span>
            <span m-r-4 class="art-name" v-show="articlePrice">{{ filters.formatAmount(articlePrice) }}元/{{ formModel.unitType === 2 ? formModel.packUnit : formModel.cellUnit }}</span>
          </Col>
        </Row>
        <Table :rowKey="(record: any) => record.keyStr" :columns="columns" :data-source="dataSource" :scroll="{ y: 'calc(100vh - 270px)'}">
          <template #bodyCell="{ text, column, record, index }">
            <template v-if="column.dataIndex === 'artDesc'">
              <div>
                <span class="art-name">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
              </div>
              <div v-if="record.producer">{{ record.producer }}</div>
            </template>
            <template v-else-if="column.dataIndex === 'series'">
              {{ index + 1 }}
            </template>
            <template v-else-if="column.dataIndex === 'unitPrice'">
              {{ record.unitPrice ? record.unitPrice : '暂未设置'}}
            </template>
            <template v-else-if="['total', 'amount'].includes(column.dataIndex)">
              <span v-if="text > 0">{{ text }}</span>
              <span v-else class="total-le0">{{ text }}</span>
            </template>
          </template>
        </Table>
      </Form>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.content-req {
  height: calc(100vh - 160px);
  overflow: auto;
}
.art-name {
  font-weight: bold;
}
.total-gt0 {
  color: #018338;
}
.total-le0 {
  color: #ff0000;
}
</style>
