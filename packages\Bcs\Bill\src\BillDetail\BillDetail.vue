<script lang="ts" setup>
import { add, Data, Format, Message, useLoading } from '@idmy/core'
import { listFullBillDetailsByBillIds, updateSelfRatioByVisitIdAndArtId } from '@mh-bcs/util'
import { InputNumber, Popover, Table, Tag, Tooltip } from 'ant-design-vue'
import { PropType } from 'vue'
import BillDetailPopover from './BillDetailPopover.vue'
import TraceCodeInput from './TraceCodeInput.vue'

defineOptions({ name: 'BillDetail' })

const { visitId, showCycleCount, billIds, showNo, showTrackCodeInput } = defineProps({
  billIds: { type: Array as PropType<number[]>, required: true },
  showCycleCount: { type: Boolean as PropType<boolean>, default: false },
  showNo: { type: Boolean as PropType<boolean>, default: true },
  showHeader: { type: Boolean as PropType<boolean>, default: true },
  bordered: { type: Boolean, default: true },
  showTrackCodeInput: { type: Boolean, default: false },
  visitId: { type: Number },
})

const columns: Data[] = []

if (showNo) {
  columns.push({
    align: 'center',
    customRender: ({ index }: Data) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '#',
    width: 45,
  })
}

columns.push({ align: 'center', dataIndex: 'artId', width: 80, title: '项目代号' })
columns.push({ align: 'left', dataIndex: 'artName', minWidth: 130, title: '项目' })

if (showCycleCount) {
  columns.push({ align: 'center', dataIndex: 'cycleCount', title: '周期数', width: 80 })
}

if (visitId && cfg.setting?.withSelfpaidPct === 1) {
  columns.push({ align: 'right', dataIndex: 'selfPaidPct', title: '自付比例', width: 90 })
}

columns.push({ align: 'right', dataIndex: 'price', title: '单价', width: 100 })
columns.push({ align: 'right', dataIndex: 'total', title: '数量', width: 75 })
columns.push({ align: 'right', dataIndex: 'amount', title: '小计', width: 100 })
if (showTrackCodeInput) {
  columns.push({ align: 'center', dataIndex: 'trackCode', title: '溯源码', width: 150 })
}

const emits = defineEmits(['loading', 'load'])

const rows = ref<Data[]>([])
const [onLoad, loading] = useLoading(async () => {
  rows.value = await listFullBillDetailsByBillIds(billIds)
  emits('load', rows)
})

watch(
  () => loading.value,
  val => emits('loading', val)
)
watch(
  () => billIds,
  () => onLoad(),
  { deep: true, immediate: true }
)

const recalculateTotal = (row: Data) => (row.cellTotal ? row.total : row.packTotal)
const recalculatePrice = (row: Data) => (row.cellTotal ? row.price : row.packPrice)
const recalculateUnit = (row: Data) => (row.cellTotal ? row.unit : row.packUnit)

const [onSelfPaidPct] = useLoading(async ({ artId, selfPaidPct }: Data) => {
  await updateSelfRatioByVisitIdAndArtId([{ artId, selfPaidPct, visitId }])
  Message.success('修改成功')
  onLoad()
})

defineExpose({ onLoad })
</script>

<template>
  <Table :rowKey="(row: Data) => `${row.billId}${row.lineNo}`" :bordered="bordered" :columns="columns" :dataSource="rows" :loading="loading" :pagination="false" :showHeader="showHeader" size="small">
    <template v-if="showCycleCount" #headerCell="{ column }">
      <Tooltip v-if="column.dataIndex === 'cycleCount'">
        <template #title> 周期数是指患者完成整个治疗计划（包括药物治疗、物理治疗等）所需的重复时间段数量，比如每3周为一个周期，总共需要4个周期来完成全部治疗。</template>
        周期数<a>?</a>
      </Tooltip>
    </template>
    <template #bodyCell="{ column: col, record: row }">
      <template v-if="col.dataIndex === 'artName'">
        <BillDetailPopover :data="row">
          {{ row.artName }}
          <Tag v-if="row.artSpec">{{ row.artSpec }}</Tag>
          <Tag v-if="recalculateUnit(row)" color="purple">{{ recalculateUnit(row) }}</Tag>
        </BillDetailPopover>
      </template>
      <Format v-if="col.dataIndex === 'discount'" :value="add(row.discounted ?? 0, row.derated ?? 0)" type="Currency" />
      <Format v-if="col.dataIndex === 'total'" :value="recalculateTotal(row)" type="Currency" />
      <Format v-if="col.dataIndex === 'amount'" :value="row.amount" type="Currency" />
      <Format v-if="col.dataIndex === 'price'" :value="recalculatePrice(row)" params="4" type="Currency" />
      <template v-if="col.dataIndex === 'selfPaidPct'">
        <Popover destroyTooltipOnHide placement="right">
          <template #content>
            <Format :value="row.selfPaidPct" type="Percent" />
          </template>
          <InputNumber v-model:value="row.selfPaidPct" :max="1" :min="0" :precision="2" :step="0.1" class="w-100%" @change="onSelfPaidPct(row)" />
        </Popover>
      </template>
      <template v-if="showTrackCodeInput && col.dataIndex === 'trackCode' && row.amount >= 0 && !row.cellTotal">
        <TraceCodeInput :billDetail="row" />
      </template>
    </template>
  </Table>
</template>
