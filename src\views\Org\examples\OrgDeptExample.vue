<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './OrgDept/Basic.vue'
import CurrentUserExample from './OrgDept/CurrentUser.vue'
import MultipleExample from './OrgDept/Multiple.vue'
import ClinicianDeptExample from './OrgDept/ClinicianDept.vue'
import OrgAndClinicianExample from './OrgDept/OrgAndClinician.vue'
import OwnExample from './OrgDept/Own.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>部门选择组件，支持选择指定组织机构下的所有部门。</Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="currentUser" tab="当前用户">
        <CurrentUserExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="multiple" tab="多选模式">
        <MultipleExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="clinicianDept" tab="医生对应部门">
        <ClinicianDeptExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="orgAndClinician" tab="组织机构和医生">
        <OrgAndClinicianExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="own" tab="当前用户部门">
        <OwnExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
