<script setup lang="ts">
import { Select, Spin } from 'ant-design-vue'
import { Data, http } from '@idmy/core'
import { ref, watch, onMounted } from 'vue'

const props = defineProps({
  orgId: {
    type: Number,
    default: undefined,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  showCode: {
    type: Boolean,
    default: true,
  },
})

const modelValue = defineModel<any>()

// 医生列表
const doctorList = ref<any[]>([])
const rawDoctorData = ref<any[]>([]) // 存储原始医生数据
const loading = ref(false)

// 根据原始数据和showCode属性生成显示列表
const updateDoctorList = () => {
  doctorList.value = rawDoctorData.value.map(row => {
    // 根据showCode属性决定是否显示医师编码
    const label = props.showCode ? `${row.doctorName || row.clinicianName} [${row.doctorNo || row.clinicianNo}]` : `${row.doctorName || row.clinicianName}`

    return {
      code: row.doctorNo || row.clinicianNo, // 医师编码
      id: row.clinicianId, // 使用clinicianId作为医生ID
      value: row.clinicianId, // 使用clinicianId作为value
      label: label, // 根据showCode属性决定显示内容
      data: row,
    }
  })
}

// 获取组织机构下的医生列表
const fetchOrgDoctors = async () => {
  loading.value = true
  try {
    // 如果有orgId则传入，否则API会使用当前用户token中的orgId
    const params = props.orgId ? { orgId: props.orgId } : {}
    const arr = await http.post<Data[]>('/hip-base/clinician/findDoctorListByOrgId', params, { appKey: 'hip' })
    // 存储原始数据
    rawDoctorData.value = arr.filter(row => row.disabled !== 1)
    // 更新显示列表
    updateDoctorList()
  } catch (error) {
    console.error('获取医生列表失败:', error)
    rawDoctorData.value = []
    doctorList.value = []
  } finally {
    loading.value = false
  }
}

// 初始加载
onMounted(() => {
  fetchOrgDoctors()
})

// 监听orgId属性变化，重新加载列表
watch(
  () => props.orgId,
  () => {
    fetchOrgDoctors()
  }
)

// 监听showCode属性变化，只更新显示内容，不重新请求API
watch(
  () => props.showCode,
  () => {
    updateDoctorList()
  }
)
</script>

<template>
  <Spin :spinning="loading">
    <Select
      v-model:value="modelValue"
      :mode="props.multiple ? 'multiple' : undefined"
      :options="doctorList"
      placeholder="请选择医生"
      :fieldNames="{ label: 'label', value: 'value' }"
      :showSearch="true"
      :filterOption="(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())"
      :allowClear="true"
      style="width: 100%"
    />
  </Spin>
</template>
