<script lang="ts" setup>
import { Data } from '@idmy/core'
import { refundInjectKey } from '@mh-bcs/util'
import BillDetail from './BillDetail/index.vue'
import Charge from './charge/index.vue'

const { redCash } = defineProps({
  redCash: { type: Object as PropType<Data>, required: true },
})

provide(refundInjectKey, {
  onFinish: () => {},
  payMap: ref(new Map()),
  paymentTotal: ref({ currentMi: 0, currentCash: 0 }),
})
</script>

<template>
  <div>
    <BillDetail v-if="redCash.cashId" :blueCashId="redCash.blueCashId" :redCashId="redCash.cashId" />
    <div class="h-8px" />
    <Charge :blueCashId="redCash.blueCashId" :cashId="redCash.cashId" :cashType="'OUTPATIENT'" :showMiFund="true" :visitId="redCash.visitId" />
  </div>
</template>

<style lang="less" scoped>
:deep(.ant-collapse-content-box) {
  padding: 8px !important;
}

:deep(.ant-collapse-header) {
  padding: 4px 8px !important;
}
</style>
