# Visit 诊疗查询组件

一个用于展示患者诊疗记录的组件，支持多种展示模式。

## 功能特点

- 详细的诊疗记录展示
- 支持诊疗引入
- 支持三种展示模式：内嵌、弹窗、抽屉式

## 安装

```bash
# 使用 npm
npm install @mh-hsd/visit

# 使用 yarn
yarn add @mh-hsd/visit

# 使用 pnpm
pnpm add @mh-hsd/visit
```

## 基本用法

### 引入组件

```js
// 局部引入
import { Visit } from '@mh-hsd/visit'

// 或全局引入
import Visit from '@mh-hsd/visit'
```

### 在模板中使用

#### 1. 内嵌模式（默认）

```vue
<template>
  <Visit :visitId="visitId" :orgId="orgId" :canRef="canRef" @confirm="handleConfirm" />
</template>

<script setup>
import { ref } from 'vue'
import { Visit } from '@mh-hsd/visit'

const visitId = ref(1)
const orgId = ref(1)
const canRef = ref(false)

function handleConfirm(refType, value) {
  console.log('参考类型:', refType)
  console.log('参考数据:', value)
}
</script>
```

#### 2. 弹窗模式

```vue
<template>
  <div>
    <a-button type="primary" @click="showModal = true">打开诊疗查询</a-button>
    <Visit 
      :visitId="visitId" 
      :orgId="orgId" 
      type="modal"
      :visible="showModal"
      title="诊疗记录查看"
      :canRef="canRef" 
      @confirm="handleConfirm"
      @close="showModal = false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Visit } from '@mh-hsd/visit'

const visitId = ref(1)
const orgId = ref(1)
const canRef = ref(false)
const showModal = ref(false)

function handleConfirm(refType, value) {
  console.log('参考类型:', refType)
  console.log('参考数据:', value)
}
</script>
```

#### 3. 抽屉式模式

```vue
<template>
  <div>
    <a-button type="primary" @click="showDrawer = true">打开诊疗查询</a-button>
    <Visit 
      :visitId="visitId" 
      :orgId="orgId" 
      type="drawer"
      :visible="showDrawer"
      title="诊疗记录查看"
      :canRef="canRef" 
      @confirm="handleConfirm"
      @close="showDrawer = false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Visit } from '@mh-hsd/visit'

const visitId = ref(1)
const orgId = ref(1)
const canRef = ref(false)
const showDrawer = ref(false)

function handleConfirm(refType, value) {
  console.log('参考类型:', refType)
  console.log('参考数据:', value)
}
</script>
```

## 组件 API

### Props

| 属性名     | 类型 | 必填 | 默认值 | 说明    |
|---------|----|-----|-------|-------|
| visitId | Number | 是   | -     | 诊疗流水号 |
| orgId   | Number | 是   | -     | 机构ID |
| canRef  | Boolean | 否   | false  | 是否允许引用 |
| userFiles | String | 否   | ''    | 用户文件 |
| type    | String | 否   | 'inline' | 展示模式：'inline'(内嵌) / 'modal'(弹窗) / 'drawer'(抽屉式) |
| visible | Boolean | 否   | true  | 弹窗/抽屉的显示状态（仅在 modal/drawer 模式下有效） |
| title   | String | 否   | '诊疗查看' | 弹窗/抽屉的标题（仅在 modal/drawer 模式下有效） |

### 事件

| 事件名   | 参数                        | 说明                         |
|---------|----------------------------|----------------------------|
| confirm | refType: string, value: any | 当确认引用时触发的事件         |
| close   | -                          | 当关闭弹窗/抽屉时触发的事件（仅在 modal/drawer 模式下有效） |

## 完整示例

```vue
<template>
  <div>
    <a-space>
      <a-button type="primary" @click="openInline">内嵌模式</a-button>
      <a-button type="primary" @click="openModal">弹窗模式</a-button>
      <a-button type="primary" @click="openDrawer">抽屉模式</a-button>
    </a-space>

    <!-- 内嵌模式 -->
    <div v-if="currentMode === 'inline'" style="margin-top: 16px;">
      <Visit 
        :visitId="visitId" 
        :orgId="orgId" 
        type="inline"
        :canRef="canRef" 
        @confirm="handleConfirm" 
      />
    </div>

    <!-- 弹窗模式 -->
    <Visit 
      :visitId="visitId" 
      :orgId="orgId" 
      type="modal"
      :visible="showModal"
      title="诊疗记录查看"
      :canRef="canRef" 
      @confirm="handleConfirm"
      @close="showModal = false"
    />

    <!-- 抽屉模式 -->
    <Visit 
      :visitId="visitId" 
      :orgId="orgId" 
      type="drawer"
      :visible="showDrawer"
      title="诊疗记录查看"
      :canRef="canRef" 
      @confirm="handleConfirm"
      @close="showDrawer = false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Visit } from '@mh-hsd/visit'

const visitId = ref(1)
const orgId = ref(1)
const canRef = ref(false)
const currentMode = ref('')
const showModal = ref(false)
const showDrawer = ref(false)

function openInline() {
  currentMode.value = 'inline'
  showModal.value = false
  showDrawer.value = false
}

function openModal() {
  currentMode.value = ''
  showModal.value = true
  showDrawer.value = false
}

function openDrawer() {
  currentMode.value = ''
  showModal.value = false
  showDrawer.value = true
}

function handleConfirm(refType, value) {
  console.log('引用类型:', refType)
  console.log('引用数据:', value)
}
</script>
```

## 组件结构

```
Visit/
├── src/
│   ├── Visit.vue            # 主组件
│   └── components/          # 子组件
│       ├── detail.vue           # 诊疗详情组件
│       ├── visit/               # 就诊相关组件
│       │   └── view.vue         # 就诊记录查看组件
│       ├── recipe/              # 处方相关组件
│       │   └── list.vue         # 处方列表组件
│       ├── order/               # 医嘱相关组件
│       │   └── list.vue         # 医嘱列表组件
│       ├── treatment/           # 治疗相关组件
│       │   └── list.vue         # 治疗列表组件
├── index.ts               # 入口文件
├── package.json           # 包配置文件
└── README.md              # 说明文档
```

## 注意事项

1. 组件使用了 ant-design-vue 作为 UI 组件库
2. 在使用弹窗或抽屉模式时，需要通过 `visible` 属性控制显示状态
3. 弹窗和抽屉模式支持自定义标题，默认为"诊疗查看"
4. 所有模式都支持诊疗引用功能，通过 `canRef` 属性控制
