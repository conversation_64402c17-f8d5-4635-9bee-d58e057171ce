<script lang="ts" setup>
import { MoneyCollectOutlined, ReloadOutlined } from '@ant-design/icons-vue'
import { View } from '@idmy/antd'
import { add, Api, Data, Dialog, Format, Message, useLoading } from '@idmy/core'
import { BillDetail, BillPopover } from '@mh-bcs/bill'
import { listFullBillsByIds, listUnpaid, payOfZero, refundOfZero } from '@mh-bcs/util'
import { CardReaderButton } from '@mh-mi/card-reader'
import { Button, Checkbox, Col, Row, Table, Tag, Tooltip, TypographyText } from 'ant-design-vue'
import { first } from 'lodash-es'
import { reactive } from 'vue'
import Charge from './Charge.vue'
import Name from './Name.vue'
import { isRedBill, isSelfFee, useReadCard, useSettle, useUserCode } from './services'
import Tip from './Tip.vue'

const selected = reactive<{ keys: number[]; rows: Data[] }>({
  keys: [],
  rows: [],
})
const clearSelected = () => {
  selected.keys = []
  selected.rows = []
}
const firstSelected = computed(() => selected.rows[0] || {})

const amount = computed(() => selected.rows.reduce((prev: any, current: any) => add(prev, current.amount), 0))
const discount = computed(() => selected.rows.reduce((prev: any, current: any) => add(prev, add(current.discounted ?? 0, current.derated ?? 0)), 0))
const isZero = computed(() => amount.value === 0)

const patientId = ref()
const currentName = ref('')
const detailRef = ref()

const { columns, tableHeight, showTip, refreshShowTip, onRemoveBill } = useSettle(selected)

const buildNameKey = (row: Data) => `${row.name0 ?? ''}${row.patientId ?? ''}`
const buildGroupKey = (row: Data) => `${row.visitId ?? ''}${row.miDiseaseCode ?? ''}${row.insuranceTypeId ?? ''}`

const nameMap = reactive(new Map())
const groupMap = reactive(new Map())

const currentPatient = computed(() => groupMap.get(currentName.value))

const [onLoad, loading] = useLoading(async (clear = true) => {
  refreshShowTip()
  nameMap.clear()
  groupMap.clear()
  try {
    const rows = await listUnpaid(patientId.value)
    for (const row of rows) {
      const nameKey = buildNameKey(row)
      if (nameMap.has(nameKey)) {
        const old = nameMap.get(nameKey)
        ++old.billCount
        old.amount = add(old.amount, row.amount)
      } else {
        nameMap.set(nameKey, { ...row, key: nameKey, billCount: 1 })
      }
      if (!groupMap.has(nameKey)) {
        groupMap.set(nameKey, new Map())
      }
      const billMap = groupMap.get(nameKey)
      const patientKey = buildGroupKey(row)
      const bills = billMap.get(patientKey)
      if (!bills) {
        billMap.set(patientKey, [])
      }
      billMap.get(patientKey).push(row)
    }
  } finally {
    if (clear) {
      clearSelected()
    } else {
      await detailRef.value.onLoad()
    }
  }
  await onUserCode()
}, true)

const onSelectChange = (keys: number[], rows: Data[]) => {
  const firstRow = rows[0]
  if (firstRow) {
    const firstKey = buildGroupKey(firstSelected.value)
    const secondKey = buildGroupKey(firstRow)
    if (!firstKey || firstKey === secondKey) {
      selected.keys = keys
      selected.rows = rows
    } else {
      clearSelected()
      onSelectChange(keys, rows)
    }
  } else {
    selected.keys = keys
    selected.rows = rows
  }
}
watch(
  () => currentName.value,
  key => {
    const billMap = groupMap.get(key)
    if (billMap) {
      const arr = first(Array.from(billMap.values())) as Data[]
      selected.keys = arr.map(row => row.billId)
      selected.rows = arr
    } else {
      clearSelected()
    }
  }
)

const { card, readCardRef, clearCard, onReadCard, params } = useReadCard(async (id: number) => {
  patientId.value = id
  await onLoad()
  if (currentPatient.value?.size) {
    if (currentPatient.value?.size === 1) {
      onStart()
    }
  } else {
    Dialog.confirm({
      title: '未找到该患者待收费的划价单',
      content: '是否继续？',
      onOk: () => {
        patientId.value = undefined
        onLoad()
      },
    })
  }
})

const { onUserCode } = useUserCode({
  cashType: 'OUTPATIENT',
  clearCard,
  onLoad,
})

const onReload = () => {
  clearCard()
  patientId.value = null
  currentName.value = ''
  onLoad()
}

//region 开始
const hasZero = async () => {
  if (amount.value === 0 && selected.keys.length > 0) {
    const msg = Message.loading({ content: '正在处理金额为0的单据', duration: 0 })
    try {
      if (isRedBill(selected.rows[0])) {
        await refundOfZero(selected.keys)
      } else {
        await payOfZero(selected.keys)
      }
      await onLoad()
    } finally {
      msg()
    }
    return true
  }
}

const [onStart] = useLoading(async () => {
  if (await hasZero()) {
    return
  }
  const billIds = selected.keys
  const firstBill = selected.rows[0]
  const visitId = firstBill.visitId
  const showMiFund = !isSelfFee(firstBill.insuranceTypeId) && Boolean(visitId)
  const props = { cashType: 'OUTPATIENT', billIds, visitId, card, showMiFund }
  Modal.open({
    component: Charge,
    width: 4,
    title: `收费「${firstBill.name} ${firstBill.patientId ? '岁' : ''} ${firstBill.patientId ? '' : '未建档'}」`,
    props,
    maskClosable: false,
    onClose: async () => {
      clearCard()
      await onLoad()
    },
  })
})
//endregion
const detailLoading = ref(false)
</script>

<template>
  <Tip :show="showTip" :refresh="refreshShowTip" />
  <Row h-46px>
    <Col :span="10" :md="11">
      <div flex justify-between>
        <CardReaderButton businessTypeId="104" :medicalType="11" @success="onReadCard" />
        <Button class="w-50px!" size="middle" type="primary" @click="onReload">
          <template #icon>
            <ReloadOutlined />
          </template>
        </Button>
      </div>
    </Col>
    <Col :span="14" :md="13">
      <div flex justify-end>
        <div flex-center v-if="selected.keys.length">
          <Format text-18px mr-12px type="String" :value="firstSelected.name + (firstSelected.patientId ? '岁' : '')" />
          <TypographyText text-18px mr-12px v-if="firstSelected.idcertNo" :copyable="{ text: firstSelected.idcertNo }">
            <Format type="IdNo" :value="firstSelected.idcertNo" />
          </TypographyText>
          <template v-if="Number(amount) < 0">
            <Format :value="amount" text-18px mr-12px prefix="应付总额：" type="Currency" value-class="error font-bold text-22px f1" />
          </template>
          <template v-else>
            <Format :value="discount" text-18px mr-12px prefix="优惠：" type="Currency" value-class="f1" v-if="discount" />
            <Format :value="amount" flex items-center text-18px mr-12px prefix="应收总额：" type="Currency" value-class="error font-bold text-22px f1" />
          </template>
          <Button :disabled="isZero && selected.keys.length === 0" class="w-60px!" size="large" type="primary" @click="onStart()">
            <template #icon>
              <MoneyCollectOutlined />
            </template>
          </Button>
        </div>
      </div>
    </Col>
  </Row>
  <Row>
    <Col :span="3" :md="3">
      <Name :height="tableHeight" :loading="loading" v-model="currentName" :data="Array.from(nameMap.values())" />
    </Col>
    <Col :span="7" :md="8">
      <div :style="{ maxHeight: tableHeight + 'px', height: tableHeight + 'px' }" overflow-y-auto class="bd">
        <div v-for="bills in groupMap.get(currentName)?.values()">
          <div flex justify-between p-8px>
            <div>
              <Tag color="processing" title="诊疗流水" v-if="bills[0].visitId">{{ bills[0].visitId }}</Tag>
              <Tag color="processing" title="开单医生" v-if="bills[0].clinicianName">{{ bills[0].clinicianName }}</Tag>
              <Tag color="processing" title="开单科室" v-if="bills[0].deptName">{{ bills[0].deptName }}</Tag>
            </div>
            <div>
              <Tag color="processing">
                <template v-if="isSelfFee(bills[0].insuranceTypeId)">自费</template>
                <Format v-else :value="bills[0].insuranceTypeId" params="InsuranceType" type="Dict" />
              </Tag>
              <Tag color="processing" v-if="bills[0].miDiseaseName">{{ bills[0].miDiseaseName }}</Tag>
            </div>
          </div>
          <Table :rowSelection="{ selectedRowKeys: selected.keys, onChange: onSelectChange }" rowKey="billId" :columns="columns" :bordered="false" :dataSource="bills" :pagination="false">
            <template #headerCell="{ column }">
              <Tooltip v-if="column.dataIndex === 'times'">
                <template #title> 中药付数。仅在为中药处方时有效。</template>
                金额<a>?</a>
              </Tooltip>
            </template>
            <template #bodyCell="{ column: { dataIndex }, record: row }">
              <Format v-if="dataIndex === 'billType'" :value="row.billType" type="Enum" params="BillType" />
              <Format v-if="dataIndex === 'amount'" :value="row.amount" type="Currency" />
              <template v-if="dataIndex === 'billId'">
                <BillPopover :data="row">
                  <a class="color-primary">{{ row.notes || row.billId }}</a>
                </BillPopover>
              </template>
              <template v-if="dataIndex === 'clinicianName'">
                {{ row.clinicianName ?? '无' }}
              </template>
            </template>
          </Table>
        </div>
      </div>
    </Col>
    <Col :span="14" :md="13">
      <div style="border-left: none" :style="{ maxHeight: tableHeight + 'px', height: tableHeight + 'px' }" class="bd" pr-8px overflow-y-auto>
        <Api v-if="selected.keys.length" :input="selected.keys" :inputWatch="{ deep: true }" :load="() => listFullBillsByIds(selected.keys)" v-slot="{ output: bills }" type="Array">
          <View :colour="false" rightClass="f1" v-for="(bill, idx) in bills" :key="bill.billId">
            <template #title>
              <div pl-8px>
                <Checkbox :checked="true" @change="onRemoveBill(bill.billId)" />
                <span ml-8px v-if="bill.recipeNo">处方号#{{ bill.recipeNo }}</span>
              </div>
            </template>
            <template #left>
              <Format text-16px type="Currency" :value="bill.amount" />
            </template>
            <template #right> 划价单号#{{ bill.billId }} </template>
            <BillDetail :showNo="false" :bordered="false" :showHeader="idx === 0" ref="detailRef" :billIds="[bill.billId]" showCycleCount @loading="detailLoading = $event" />
          </View>
        </Api>
      </div>
    </Col>
  </Row>
</template>
<style lang="less" scoped>
.bd {
  border: solid 1px #ddd;
}
</style>
