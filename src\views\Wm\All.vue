<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个组件示例
import BatchAdjustExample from './examples/BatchAdjustExample.vue'
import ScmCustSelectExample from './examples/ScmCustSelectExample.vue'
import CountExample from './examples/CountExample.vue'
import PharmacySelectExample from './examples/PharmacySelectExample.vue'
import TrackCodeExample from './examples/TrackCodeExample.vue'
import RecipeTrackCodeExample from './examples/RecipeTrackCodeExample.vue'
import BillsTrackCodeExample from './examples/BillsTrackCodeExample.vue'
import CodeHighlight from './examples/CodeHighlight.vue'
import ReqComponentExample from './examples/ReqComponentExample.vue'
import MakerExample from './examples/MakerExample.vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

const { Title, Paragraph, Text } = Typography

// 代码高亮组件引用
const codeHighlight = ref()

// 处理MakerDeptArt组件的添加品种事件
const handleAddArt = (artData: any) => {
  console.log('添加品种成功，表单数据：', artData)
  Message.success(`成功添加品种: ${artData.artName}`)
}

// 当前激活的tab
const activeKey = ref('batchadjust')

// 标签页切换时更新高亮
const onTabChange = () => {
  codeHighlight.value?.updateHighlight()
}
</script>

<template>
  <Card title="仓库相关组件" class="mb-16px">
    <Paragraph>仓库相关组件，包括BatchAdjust（批号调整）、ScmCustSelect（供应商选择）、Count（盘点）、PharmacySelect（药房选择）、TrackCode（溯源码录入）、RecipeTrackCode（处方扫码）、BillsTrackCode（多单据追溯码扫描）、ReqComponent（制单录入）、Maker（Maker制单）和MakerDeptArt（品种选择）组件。</Paragraph>

    <!-- 代码高亮组件 -->
    <CodeHighlight ref="codeHighlight" />

    <Tabs v-model:activeKey="activeKey" tabPosition="left" @change="onTabChange">
      <Tabs.TabPane key="batchadjust" tab="批号调整">
        <BatchAdjustExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="scmcustselect" tab="供应商选择">
        <ScmCustSelectExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="count" tab="盘点">
        <CountExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="pharmacyselect" tab="药房选择">
        <PharmacySelectExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="trackcode" tab="溯源码录入">
        <TrackCodeExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="recipetrackcode" tab="处方扫码">
        <RecipeTrackCodeExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="billstrackcode" tab="多单据追溯码扫描">
        <BillsTrackCodeExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="reqcomponentcode" tab="制单录入">
        <ReqComponentExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="maker" tab="采购制单">
        <MakerExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="makerdeptart" tab="品种选择">
        <Card title="MakerDeptArt 品种选择组件" class="mb-16px">
          <Paragraph>
            MakerDeptArt 是一个专门用于品种选择和数量输入的组件，支持整包数量和拆零数量输入，具有完善的表单验证和键盘导航功能。
          </Paragraph>

          <div style="margin: 16px 0; padding: 16px; border: 1px solid #f0f0f0; border-radius: 6px; background-color: #fafafa;">
            <MakerDeptArt
              :deptCode="'000013'"
              :searchType="6"
              @addArt="handleAddArt"
            />
          </div>

          <div style="margin-top: 16px;">
            <Text strong>使用说明：</Text>
            <ul style="margin-top: 8px;">
              <li>在品种查询框中输入关键词搜索并选择品种</li>
              <li>根据品种的包装信息输入整包数量或拆零数量</li>
              <li>点击"添加"按钮或按Enter键完成添加</li>
              <li>表单会自动清空，可以继续添加下一个品种</li>
            </ul>
          </div>
        </Card>
      </Tabs.TabPane>
    </Tabs>
  </Card>
</template>

<style scoped>
/* 全局样式可以放在这里 */
:deep(.ant-tabs-left > .ant-tabs-content-holder) {
  border-left: 1px solid #f0f0f0;
  padding-left: 16px;
}

:deep(.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab) {
  padding: 8px 16px;
}

:deep(.ant-card-body) {
  padding: 24px;
}
</style>
