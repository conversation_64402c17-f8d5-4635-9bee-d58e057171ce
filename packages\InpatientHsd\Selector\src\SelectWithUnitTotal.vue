<script setup lang="ts">
import { Message } from '@idmy/core'
import type { Rule } from 'ant-design-vue/es/form'
import ArtSelect from './ArtSelect.vue'
import { Button, Col, Form, FormItem, Modal, Row, InputNumber, Radio, RadioGroup, Checkbox } from 'ant-design-vue'

const props = defineProps({
  visibleMultiplyByTimes: {
    type: Boolean,
    default: true
  },
  artSearchType: {
    type: Number,
    default: 2
  }
})

const emit = defineEmits(['addArt'])
const visible = ref(false);
const formRef = ref()
const formModel = ref<any>({})

const artSelectRef = ref<InstanceType<typeof ArtSelect>>()
const open = () => {
  formModel.value.routeId = null
  clearFormModel()
  nextTick(() => {
    artSelectRef.value?.init()
  })
  visible.value = true
}

const clearFormModel = () => {
  formModel.value.artId = null
  formModel.value.total = null
  formModel.value.artName = null
  formModel.value.artSpec = null
  formModel.value.producer = null
  formModel.value.unitType = null
  formModel.value.packUnit = null
  formModel.value.cellUnit = null
  formModel.value.multiplyByTimesBool = null
}

const rules: Record<string, Rule[]> = {
  total: [
    { required: true, message: '请设置数量', trigger: 'change' }
  ],
  unitType: [
    { required: true, message: '请选择单位类型', trigger: 'change' }
  ]
}

function handleCancel() {
  visible.value = false
}

// 耗材选择
function handleArtSelect (art: any) {
  clearFormModel()
  if (art) {
    formModel.value.artId = art.artId
    formModel.value.artName = art.artName
    formModel.value.artSpec = art.artSpec
    formModel.value.producer = art.producer
    formModel.value.packUnit = art.packUnit
    formModel.value.cellUnit = art.cellUnit
    formModel.value.multiplyByTimesBool = true
    formModel.value.total = 1
    // && art.artTypeId !== 14
    if (art.stockReq === 1) {
      if (formModel.value.cellUnit) {
        formModel.value.unitType = 1
        formModel.value.unit = formModel.value.cellUnit
      } else if (formModel.value.packUnit) {
        formModel.value.unitType = 2
        formModel.value.unit = formModel.value.packUnit
      }
    } else {
      if (formModel.value.packUnit) {
        formModel.value.unitType = 2
        formModel.value.unit = formModel.value.packUnit
      } else if (formModel.value.cellUnit) {
        formModel.value.unitType = 1
        formModel.value.unit = formModel.value.cellUnit
      }
    }
  }
}

const handleAdd = async () => {
  if (!formModel.value.artId) {
    Message.error('请选择条目')
    return
  }
  await formRef.value?.validate()
  var art = deepClone(formModel.value)
  art.multiplyByTimes = art.multiplyByTimesBool ? 1 : 0
  emit('addArt', art)
  clearFormModel()
  visible.value = false
}

const deepClone = (obj: any) => {
  return JSON.parse(JSON.stringify(obj))
}

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" title="条目选择" width="700px" @cancel="handleCancel" :mask-closable="false">
    <template #footer>
      <Button type="dashed" @click="handleCancel">
        关闭
      </Button>
      <Button @click="handleAdd" type="primary">
        添加
      </Button>
    </template>
    <div class="content-req">
      <Form ref="formRef" :model="formModel" :rules="rules" :label-col="{ span: 9 }" :wrapper-col="{ span: 16 }">
        <Row>
          <Col :span="24">
            <Form-item label="条目" :label-col="{ span: 3 }">
              <art-select ref="artSelectRef" :searchType="props.artSearchType" @selected="handleArtSelect"/>
            </Form-item>
          </Col>
          <Col :span="8">
            <Form-item label="数量" name="total">
              <Input-number v-model:value="formModel.total" placeholder="请设置数量" :controls="false" :precision="4" :min="0" :max="99999999" style="width: 100%;"/>
            </Form-item>
          </Col>
          <Col :span="12">
            <Form-item label="单位" name="unitType">
              <Radio-group v-model:value="formModel.unitType">
                <Radio v-if="formModel.packUnit" :value="2">
                  {{ formModel.packUnit }}(包装)
                </Radio>
                <Radio v-if="formModel.cellUnit" :value="1">
                  {{ formModel.cellUnit }}(拆零)
                </Radio>
              </Radio-group>
            </Form-item>
          </Col>
          <Col :span="4" v-show="visibleMultiplyByTimes">
            <Form-item label="倍乘频次" name="multiplyByTimesBool" :label-col="{ span: 15 }">
              <Checkbox v-model:checked="formModel.multiplyByTimesBool"/>
            </Form-item>
          </Col>
        </Row>
      </Form>
    </div>
  </Modal>
</template>
