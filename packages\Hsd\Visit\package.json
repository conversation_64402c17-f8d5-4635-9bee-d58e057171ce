{"name": "@mh-hsd/visit", "version": "1.0.5", "type": "module", "main": "umd/index.js", "module": "es/index.js", "types": "index.d.ts", "style": "index.css", "files": ["es", "umd", "src/*.d.ts", "src/**/*.d.ts", "index.d.ts", "index.css"], "scripts": {"publish:component": "cd ../../ && pnpm run publish:component Hsd/Visit"}, "peerDependencies": {"@vueuse/core": "^13.0.0", "@ant-design/icons-vue": "^7.0.1", "@surely-vue/table": "^5.0.1", "@idmy/antd": "^0.0.120", "@idmy/core": "^1.0.142", "ant-design-vue": "^4.2.6", "axios": "^1.8.3", "dayjs": "^1.11.8", "lodash-es": "^4.17.21", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "dependencies": {"@mh-base/core": "workspace:*", "@mh-hsd/util": "workspace:*", "@mh-hip/util": "workspace:*", "@mh-hsd/base": "workspace:*", "@mh-hsd/report": "workspace:*"}, "publishConfig": {"access": "public"}}