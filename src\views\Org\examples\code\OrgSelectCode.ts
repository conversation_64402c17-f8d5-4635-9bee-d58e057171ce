import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 显示所有组织机构 -->
  <OrgSelect
    v-model="orgId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgSelect } from '@mh-hip/org'
import { ref } from 'vue'

// 单选值
const orgId = ref()
</script>`)

// 用户权限
export const ownUsage = wrapCodeExample(`<template>
  <!-- 仅显示当前用户有权限的组织机构 -->
  <OrgSelect
    v-model="userOrgId"
    :own="true"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgSelect } from '@mh-hip/org'
import { ref } from 'vue'

// 单选值
const userOrgId = ref()
</script>`)

// 多选模式
export const multipleUsage = wrapCodeExample(`<template>
  <!-- 多选模式 -->
  <OrgSelect
    v-model="orgIds"
    multiple
    style="width: 100%"
  />
</template>

<script setup>
import { OrgSelect } from '@mh-hip/org'
import { ref } from 'vue'

// 多选值
const orgIds = ref([])
</script>`)

// 引入组件
export const importCode = `import { OrgSelect } from '@mh-hip/org'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-hip/org": "^1.0.8"
  }
}`
