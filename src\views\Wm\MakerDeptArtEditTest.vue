<script setup lang="ts">
import { ref } from 'vue'
import { Card, Typography, Button, message, Table, Space } from 'ant-design-vue'
import { MakerDeptArt } from '@mh-wm/maker'

const { Title, Paragraph } = Typography

// 部门编码
const deptCode = ref('000013')

// 组件引用
const makerDeptArtRef = ref()

// 添加的品种列表
const addedItems = ref<any[]>([])

// 编辑状态
const editingId = ref<string | null>(null)

// 表格列定义
const columns = [
  {
    title: '品名',
    dataIndex: 'artName',
    key: 'artName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 120,
  },
  {
    title: '生产厂家',
    dataIndex: 'producer',
    key: 'producer',
    width: 120,
  },
  {
    title: '整包数量',
    dataIndex: 'packTotal',
    key: 'packTotal',
    width: 100,
  },
  {
    title: '拆零数量',
    dataIndex: 'cellTotal',
    key: 'cellTotal',
    width: 100,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  }
]

// 添加品种回调
const handleAddArt = (artData: any) => {
  console.log('MakerDeptArt 添加品种成功，表单数据：', artData)

  if (editingId.value) {
    // 编辑模式：更新现有记录
    const index = addedItems.value.findIndex(item => item.id === editingId.value)
    if (index !== -1) {
      const updatedItem = {
        ...addedItems.value[index],
        ...artData,
        updateTime: new Date().toLocaleTimeString()
      }
      addedItems.value[index] = updatedItem
      message.success(`品种 "${artData.artName}" 已更新`)

      // 重置编辑状态
      editingId.value = null
    }
  } else {
    // 添加模式：新增记录
    const newItem = {
      id: Date.now().toString(),
      ...artData,
      addTime: new Date().toLocaleTimeString()
    }

    addedItems.value.push(newItem)
    message.success(`成功添加品种: ${artData.artName}`)
  }
}

// 编辑品种
const editItem = (record: any) => {
  console.log('开始编辑品种，record:', record)
  console.log('makerDeptArtRef.value:', makerDeptArtRef.value)

  if (makerDeptArtRef.value) {
    // 设置编辑状态
    editingId.value = record.id

    // 构建编辑数据格式
    const editData = {
      artId: record.artId,
      artName: record.artName,
      artSpec: record.artSpec,
      producer: record.producer,
      packUnit: record.packUnit,
      cellUnit: record.cellUnit,
      packCells: record.packCells,
      splittable: record.splittable,
      packTotal: record.packTotal,
      cellTotal: record.cellTotal,
    }

    console.log('准备编辑的数据:', editData)
    console.log('调用 setFormData 方法')

    // 检查 setFormData 方法是否存在
    if (typeof makerDeptArtRef.value.setFormData === 'function') {
      makerDeptArtRef.value.setFormData(editData)
      message.info('品种数据已回填到表单，修改后点击添加按钮更新')
    } else {
      console.error('setFormData 方法不存在')
      console.log('可用的方法:', Object.keys(makerDeptArtRef.value))
      message.error('setFormData 方法不存在')
    }
  } else {
    message.error('组件引用不存在')
  }
}

// 取消编辑
const cancelEdit = () => {
  editingId.value = null
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.clearForm()
  }
  message.info('已取消编辑')
}

// 删除品种
const deleteItem = (record: any) => {
  const index = addedItems.value.findIndex(item => item.id === record.id)
  if (index !== -1) {
    addedItems.value.splice(index, 1)
    message.success('删除成功')
  }
}

// 清空表单
const clearForm = () => {
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.clearForm()
    editingId.value = null
    message.success('表单已清空')
  } else {
    message.error('组件引用不存在')
  }
}

// 设置测试数据
const setTestData = () => {
  if (makerDeptArtRef.value) {
    const testData = {
      artId: 1001,
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '哈药集团制药总厂',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 24,
      splittable: 1,
      packTotal: 10,
      cellTotal: 5
    }

    makerDeptArtRef.value.setFormData(testData)
    message.success('测试数据已设置到表单')
  } else {
    message.error('组件引用不存在')
  }
}

// 清空所有数据
const clearAll = () => {
  addedItems.value = []
  clearForm()
  message.success('已清空所有数据')
}
</script>

<template>
  <div style="padding: 20px;">
    <Card title="MakerDeptArt 编辑功能测试" style="margin-bottom: 20px;">
      <div style="margin-bottom: 16px;">
        <Title :level="4">品种选择录入组件编辑功能测试</Title>
        <Paragraph>
          测试 MakerDeptArt 组件的编辑功能，包括数据回填、修改保存等。
        </Paragraph>
      </div>

      <!-- 组件使用 -->
      <div style="margin-bottom: 20px; padding: 16px; background: #fafafa; border-radius: 8px;">
        <Title :level="5">组件演示</Title>
        <MakerDeptArt
          :deptCode="deptCode"
          :searchType="6"
          @addArt="handleAddArt"
          ref="makerDeptArtRef"
        />

        <!-- 编辑状态提示 -->
        <div v-if="editingId" style="margin-top: 8px; padding: 8px; background-color: #fff7e6; border: 1px solid #ffd591; border-radius: 4px;">
          <span style="color: #fa8c16; font-weight: 500;">
            <i class="anticon anticon-edit" style="margin-right: 4px;"></i>
            当前正在编辑品种，修改完成后点击"添加"按钮保存更改。
          </span>
          <Button type="link" size="small" @click="cancelEdit" style="margin-left: 8px;">
            取消编辑
          </Button>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div style="margin-bottom: 20px;">
        <Space wrap>
          <Button @click="clearForm">清空表单</Button>
          <Button @click="setTestData" type="primary">设置测试数据</Button>
          <Button v-if="addedItems.length > 0" @click="clearAll" danger>清空所有</Button>
        </Space>
      </div>

      <!-- 已添加品种列表 -->
      <div>
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <Title :level="5" style="margin: 0;">已添加品种列表 ({{ addedItems.length }})</Title>
        </div>

        <Table
          v-if="addedItems.length > 0"
          :dataSource="addedItems"
          :columns="columns"
          rowKey="id"
          :pagination="{ pageSize: 10 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <Space>
                <Button type="link" size="small" @click="editItem(record)">编辑</Button>
                <Button type="link" size="small" danger @click="deleteItem(record)">删除</Button>
              </Space>
            </template>
          </template>
        </Table>

        <div v-else style="text-align: center; padding: 40px; color: #999; background: #fafafa; border: 1px dashed #d9d9d9; border-radius: 6px;">
          暂无数据，请使用上方组件添加品种
        </div>
      </div>
    </Card>

    <!-- 调试信息 -->
    <Card title="调试信息">
      <div>
        <Title :level="5">当前状态</Title>
        <pre style="background: #f6f8fa; padding: 12px; border-radius: 4px; font-size: 12px;">{{
          JSON.stringify({
            deptCode: deptCode,
            searchType: 6,
            addedCount: addedItems.length,
            editingId: editingId
          }, null, 2)
        }}</pre>
      </div>

      <div style="margin-top: 16px;">
        <Title :level="5">最后操作的品种数据</Title>
        <pre style="background: #f6f8fa; padding: 12px; border-radius: 4px; font-size: 12px;">{{
          addedItems.length > 0
            ? JSON.stringify(addedItems[addedItems.length - 1], null, 2)
            : '暂无数据'
        }}</pre>
      </div>
    </Card>
  </div>
</template>

<style scoped>
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
