# SectionTrackCode 病区绑定追溯码组件

病区绑定追溯码组件，用于处理病区药品追溯码的绑定和分配。该组件支持两种追溯码来源：病区自行扫码绑定和药房预绑定追溯码分配。

## 🚀 核心特性

- **Modal弹窗设计**：1500px宽度，上下20px间距，最大化利用页面空间
- **双重追溯码来源**：支持病区自行扫码绑定和药房预绑定追溯码分配
- **智能一键分配**：药房预绑定的拆零追溯码支持一键智能分配给患者
- **紧凑界面设计**：左侧药品卡片（30%），右侧患者网格（70%），适应大量数据
- **智能患者选择**：整包类单选，拆零类多选，自动计算需求量
- **日期切换保护**：切换日期前检查未完成绑定，防止数据丢失
- **床号排序**：患者按床号升序排列，优先满足前面床号的患者
- **追溯码验证**：支持19-27位追溯码验证，自动识别无效码
- **解绑保护**：已使用的追溯码不允许解绑，保护数据完整性
- **拆零上报管理**：支持设置/取消拆零上报，无追溯码管理
- 基于ant-design-vue的组件实现

## 📋 业务场景

### 病区自行扫码绑定
- **整包类药品**：一个追溯码分配给一个患者
- **拆零类药品**：根据实际情况将一个追溯码分给一个或多个患者，标记拆零做拆零上报

### 药房预绑定追溯码分配
- **业务背景**：药房绑定好追溯码后将药品发往病区
- **典型场景**：一瓶药100粒，一瓶只有一个码，给病区只发了10片药
- **分配需求**：药房提前发一个标记拆零且totalCells=10的追溯码让病区来分配
- **一键分配**：支持一键分配功能，自动响应医嘱的需求量

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-inpatient-hsd/util`: 工具库，提供API调用
- `@mh-hip/util`: 通用工具库
- `dayjs`: 日期处理库
- `lodash-es`: 工具函数库

## 打包与安装

### 打包组件

在开发完成后，需要打包组件以便发布和使用：

```bash
# 在项目根目录下执行打包命令
pnpm publish:component InpatientHsd/SectionTrackCode
```

这个命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

### 安装组件

在其他项目中安装该组件：

```bash
# 使用npm安装
npm install @mh-inpatient-hsd/section-track-code

# 或使用pnpm安装
pnpm add @mh-inpatient-hsd/section-track-code
```

## 使用方法

### 引入组件

```javascript
import { SectionTrackCode } from '@mh-inpatient-hsd/section-track-code'
import '@mh-inpatient-hsd/section-track-code/index.css'
```

### 基本用法

```vue
<template>
  <div>
    <Button type="primary" @click="openSectionTrackCode">打开病区绑定追溯码</Button>
    <SectionTrackCode
      ref="sectionTrackCodeRef"
      :sectionId="38"
      :modalWidth="1500"
      @success="handleSuccess"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { SectionTrackCode } from '@mh-inpatient-hsd/section-track-code'
import '@mh-inpatient-hsd/section-track-code/index.css'
import { Button, message } from 'ant-design-vue'

const sectionTrackCodeRef = ref()

const openSectionTrackCode = () => {
  sectionTrackCodeRef.value.open()
}

const handleSuccess = (data) => {
  message.success('追溯码绑定成功')
  console.log('绑定结果:', data)
}

const handleCancel = () => {
  message.info('取消操作')
}
</script>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| sectionId | 病区ID | number | 38 |
| modalWidth | Modal弹窗宽度 | number \| string | 1500 |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 追溯码绑定成功时触发 | (data: any) => void |
| cancel | 取消操作时触发 | () => void |

## 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开Modal弹窗 | - |

### 方法调用示例

```javascript
// 通过ref调用组件方法
const sectionTrackCodeRef = ref()

// 打开Modal
sectionTrackCodeRef.value.open()
```

## API 调用说明

### 主要 API 接口

1. **sectionFeeTrackCodeSummaryApi** - 获取病区费用追溯码汇总
   - 入参：`{ sectionId: number, bsnDate: string }`
   - 返回：包含药品信息汇总和药房追溯码汇总的数据

### API 数据结构

```typescript
// API返回数据结构
interface SectionFeeTrackCodeSummaryResponse {
  artSummaryList: ArtSummary[]           // 药品信息汇总列表
  pharmacyTrackCodeSums: PharmacyTrackCodeSum[] // 药房追溯码汇总列表
}

// 药品信息汇总
interface ArtSummary {
  artId: number                    // 药品编号
  keyStr: string                   // artId拼接unitType
  artName: string                  // 药品名称
  artSpec: string                  // 药品规格
  producer: string                 // 药品厂家
  packCells: number                // 药品拆零系数
  packUnit: string                 // 药品包装（整包）单位
  cellUnit: string                 // 药品制剂（拆零）单位
  unitType: number                 // 包装类型：1拆零2整包
  isDisassembled: number           // 拆零标志位
  totalAmount: number              // 上报数量
  unitName: string                 // 根据unitType计算出来的单位
  visitSummaryList: VisitSummary[] // 诊疗信息汇总
}

// 患者信息汇总
interface VisitSummary {
  visitId: string           // 诊疗编号
  patientName: string       // 患者姓名
  bedNo: string            // 床号
  feeDetailList: FeeDetail[] // 费用明细列表
}
```

## 核心功能说明

### 智能选择逻辑

#### 药品选择自动处理
- **整包类药品**（unitType=2 且 isDisassembled=0）：
  - 自动选择第一个未绑码的患者
  - 只能单选患者进行绑码
  - 扫一个码给选中的单个患者
- **拆零类药品**（unitType=1 或 isDisassembled=1）：
  - 自动选择所有未绑码的患者
  - 支持多选患者进行绑码
  - 自动勾选拆零标记
  - 自动计算拆零数量（所有选中患者需求量总和）

#### 患者选择处理
- **床号排序**：患者按床号升序排列
- **优先满足**：按床号顺序优先满足前面的患者
- **需求计算**：自动计算患者剩余需求量
- **多选支持**：拆零类药品支持多选患者

#### 追溯码绑定逻辑
- **整包类**：扫一个码给选中的单个患者加一个码
- **拆零类**：扫一个码根据数量按床号排序分给选中的多个患者
- **不足处理**：当患者需求量大于追溯码拆零数量时，按床号升序优先满足前面的患者

### 一键分配算法

组件提供智能的一键分配功能，特别适用于药房预绑定的拆零追溯码：

1. **需求分析**：分析每个患者的剩余需求量
2. **优先级排序**：按床号升序排序患者
3. **智能分配**：根据追溯码的可用数量智能分配给患者
4. **状态更新**：自动更新患者和药品的完成状态

### 追溯码验证

- **长度验证**：支持19-27位追溯码，自动识别无效码
- **格式提醒**：对13位条形码等无效格式给出明确提示
- **前缀识别**：追溯码前7位用于药品标识匹配

### 状态管理

- **完成状态**：实时计算患者和药品的完成状态
- **动画效果**：药品完成时支持滑出下沉动画
- **排序优化**：未完成的项目优先显示

## 界面布局

### Modal弹窗设计
- **宽度**：1500px，适应大量数据显示
- **高度**：calc(100vh - 40px)，上下各留20px间距
- **响应式**：小屏幕下左右布局变为上下布局

### 上部：紧凑日期选择器
- 选择指定日期的费用数据，日期格式化为yyyyMMdd
- 紧凑设计，减少空间占用
- 日期切换保护，防止未保存数据丢失

### 中部：核心绑码区域
- **左侧（30%）**：紧凑药品卡片列表
  - 第一行：药品名称 + 规格
  - 第二行：厂家信息
  - 右侧信息：类型、需求量、已绑定量、状态
  - 鼠标悬停显示完整信息（artId、artName、artSpec、producer）
- **右侧（70%）**：患者网格布局
  - 一行3-4个患者卡片，适应大量患者
  - 患者信息：床号 + 姓名格式
  - 按床号升序排序
  - 支持单选（整包类）和多选（拆零类）

### 下部：追溯码录入区域
- 追溯码输入框，支持扫码枪和手动输入
- 拆零数量设置和拆零标记选项
- 拆零上报管理按钮
- 无追溯码管理按钮
- 全部清除功能

## 注意事项

1. **数据完整性**：已使用的追溯码不允许解绑，保护数据完整性
2. **智能分配**：拆零类药品支持智能分配算法，避免手动分配错误
3. **状态同步**：患者和药品状态实时同步，确保界面准确性
4. **输入验证**：严格的追溯码格式验证，防止无效数据录入
5. **用户体验**：自动聚焦、动画效果等提升用户操作体验

## 开发与使用流程

### 开发流程

1. **组件开发**：在`packages/InpatientHsd/SectionTrackCode/src`目录下开发组件
2. **本地测试**：在`src/views/inpatient-hsd/examples`目录下创建示例页面进行测试
3. **打包组件**：开发完成后，执行打包命令

### 使用流程

1. **安装组件**：在项目中安装组件
2. **引入组件**：在Vue文件中引入组件
3. **配置参数**：设置sectionId等必要参数
4. **处理事件**：处理success和cancel事件

## 技术架构

### 文件结构
```
packages/InpatientHsd/SectionTrackCode/
├── src/
│   ├── index.vue          # 主组件文件
│   ├── types.ts           # TypeScript类型定义
│   ├── composables.ts     # 组合式函数
│   ├── styles.less        # 样式文件
│   └── mock.ts            # 模拟数据
├── package.json           # 组件包配置
├── index.ts              # 组件入口文件
└── README.md             # 组件文档
```

### 技术特点
- **TypeScript支持**：完整的类型定义和类型安全
- **组合式API**：使用Vue 3组合式API，代码更清晰
- **模块化设计**：功能拆分到不同文件，便于维护
- **响应式数据**：实时更新界面状态
- **样式分离**：独立的LESS样式文件
- **模拟数据**：开发阶段使用模拟数据，便于调试

### 核心算法

#### 智能分配算法
```typescript
// 智能分配追溯码到具体的费用明细
const smartAllocateTrackCode = (
  drugCard: DrugCard,
  trackCode: string,
  isDisassembled: boolean,
  totalCells: number
) => {
  // 1. 获取所有未完成的费用明细
  // 2. 按需求量排序（需求量大的优先）
  // 3. 智能分配算法执行
  // 4. 返回分配结果
}
```

#### 一键分配算法
```typescript
// 自动分配药房预绑定的追溯码
const autoAllocatePharmacyTrackCodes = (drugCard: DrugCard) => {
  // 1. 获取未完成绑定的患者
  // 2. 按需求量排序患者
  // 3. 遍历药房追溯码进行分配
  // 4. 更新患者和药品完成状态
}
```

## 开发指南

### 本地开发
1. 克隆项目到本地
2. 安装依赖：`pnpm install`
3. 启动开发服务器：`pnpm dev`
4. 访问示例页面：`http://localhost:3000/InpatientHsd/SectionTrackCode`

### 调试技巧
- 使用浏览器开发者工具查看组件状态
- 在组合式函数中添加console.log进行调试
- 修改mock.ts中的模拟数据测试不同场景
- 使用Vue DevTools查看响应式数据变化

### 自定义开发
- 修改types.ts添加新的数据类型
- 在composables.ts中添加新的组合式函数
- 更新styles.less自定义样式
- 扩展mock.ts添加更多测试数据

## 版本历史

### v1.0.0 (初始版本)
- 基础功能实现：病区追溯码绑定和分配
- 支持两种追溯码来源：自行扫码和药房预绑定
- 智能一键分配功能
- 可视化界面设计
- 追溯码验证和状态管理
- 基于TypeScript和组合式API开发
- 响应式设计，支持移动端适配
- 模块化架构，代码分离到不同文件
- 完整的类型定义和开发文档
- 模拟数据支持，便于开发和测试
