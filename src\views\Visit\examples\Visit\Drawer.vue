<script setup lang="ts">
import { Visit } from '@mh-hsd/visit'
import { Card, Typography, Divider, Button, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { packageJsonCode, drawerUsage, importCode } from '../code/VisitCode'

const { Title, Paragraph } = Typography
const visitId = ref(102)
const orgId = ref(1)
const canRef = ref(false)
const showDrawer = ref(false)

function handleConfirm(refType: string, value: any) {
  console.log('引用类型:', refType)
  console.log('引用数据:', value)
}
</script>

<template>
  <Card class="mb-16px">
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="24">
        <div>
          <Button type="primary" @click="showDrawer = true">打开诊疗查询</Button>
          <Visit
            :visitId="visitId"
            :orgId="orgId"
            type="drawer"
            :visible="showDrawer"
            title="诊疗记录查看"
            :canRef="canRef"
            @confirm="handleConfirm"
            @close="showDrawer = false"
          />
        </div>
      </Col>
    </Row>
    <Divider />
    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="drawerUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
