<script setup lang="ts">
import { Dict } from '@idmy/antd'
import { addDict, DictProps } from '@idmy/core'
import {allArtSubTypes} from '@mh-hip/util'

addDict('ArtSubType', async (): Promise<DictProps[]> => {
  const data = await allArtSubTypes(true)
  return data.map(
    (row: any): DictProps => ({
      data: row,
      formatName: row.label,
      id: row.id,
      name: row.label,
      value: row.value,
    })
  )
})

const modelValue = defineModel({ type: Number })
</script>

<template>
  <Dict v-model="modelValue" clazz="ArtSubType" />
</template>
