<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'
import BasicExample from './DiseaseSelectorModal/Basic.vue'
const { Paragraph } = Typography
const activeKey = ref('basic')
</script>
<template>
  <div>
    <Paragraph>病种选择弹窗组件，支持弹窗方式选择病种。</Paragraph>
    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template> 