{"name": "@mh-bcs/pay", "version": "1.0.0", "type": "module", "main": "umd/index.js", "module": "es/index.js", "types": "index.d.ts", "style": "index.css", "files": ["es", "umd", "src/*.d.ts", "src/**/*.d.ts", "index.d.ts", "index.css"], "scripts": {"publish:build": "cd ../../ && pnpm run publish:component Bcs/Pay --no-publish", "publish:component": "cd ../../ && pnpm run publish:component Bcs/Pay"}, "dependencies": {"@mh-hip/util": "workspace:*", "@mh-bcs/util": "workspace:*", "@mh-base/core": "workspace:*"}, "peerDependencies": {"@ant-design/icons-vue": "~7.0.1", "ant-design-vue": "~4.2.6", "axios": "~1.8.3", "crypto-js": "~4.2.0", "dayjs": "~1.11.8", "lodash-es": "~4.17.21", "vue": "~3.5.13", "vue-router": "~4.5.0", "@idmy/antd": "~0.0.120", "@idmy/core": "~1.0.143", "@mh-base/core": "~1.0.4"}, "publishConfig": {"access": "public"}}