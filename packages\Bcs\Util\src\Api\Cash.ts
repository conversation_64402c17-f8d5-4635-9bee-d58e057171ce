import { http, PageOut } from '@idmy/core'
import { ActionType, CashStatus, CashType, PaidSource } from '../Base'

export interface Cash {
  // 结算流水号
  cashId: number

  // 原结算流水号
  blueCashId?: number

  // 诊疗流水号
  visitId: number

  // 总费用
  totalAmount: number

  // 应收总金额
  amount: number

  // 交易类型
  actionType: ActionType

  // 结算日期
  cashDate: number

  // 创建时间
  createdAt: string

  // 审核时间
  updatedAt?: string

  // 结算状态
  status: CashStatus

  // 使用预交金标志位
  prepaidCredited: boolean

  // 折让金额
  discounted?: number

  // 减免金额
  derated?: number

  // 发票张数
  invoiceCount?: number

  // 收费用户ID
  userId?: number

  // 收款人
  payee?: string

  // 付款人
  payer?: string

  // 机构ID
  orgId: number

  // 机构名称
  orgName?: string

  // 说明备注
  notes?: string

  // 起始日期
  startDate?: number

  // 截止日期
  endDate?: number

  // 是否中途结算
  midway?: boolean

  // 收费类型代号
  cashType: CashType

  // 医疗类别代号
  medTypeId?: number

  // 交账流水号
  checkId?: number

  // 已冲红金额
  refundedAmount?: number

  // 来源应用ID
  appId?: string

  // 互联网医院服务编号
  serviceNo?: string

  // 支付来源
  paidSource?: PaidSource

  // 患者类型代号
  patientTypeId?: number

  // 险种类型代号
  insuranceTypeId?: number

  // 医保统筹金额
  miFundAmt?: number

  // 医保个账金额
  miAcctAmt?: number

  // 共济金额
  familyAcctAmt?: number

  // 疾病编码
  diseaseCode?: string

  // 疾病名称
  diseaseName?: string

  payerId?: number
  billCount?: number
  paymentNames?: string
  insuranceName?: string
  clinicianName?: string
}

export function sumUnpaidAmount(cashId: number): Promise<number> {
  return http.post('/api/bcs/Cash/calcUnpaidAmount/' + cashId, {}, { appKey: 'bcs' })
}

export async function updateMedTypeIdByVisitId(visitId: number, medTypeId: number) {
}

export function updateMedType(cashId: number, medTypeId: number) {
  return http.post('/api/bcs/Cash/updateMedType', { cashId, medTypeId }, { appKey: 'bcs' })
}

export function updateMiDisease(cashId: number, diseaseCode: string, diseaseName: string) {
  return http.post('/api/bcs/cash/updateMiDisease', { cashId, diseaseCode, diseaseName }, { appKey: 'bcs' })
}

export function listAllCashes(cashId: number): Promise<Cash[]> {
  return http.post('/api/bcs/Cash/listAllCashes/' + cashId, null, { appKey: 'bcs' })
}

export function getCash(cashId: number): Promise<Cash> {
  return http.post('/api/bcs/Cash/get/' + cashId, null, { appKey: 'bcs' })
}

export function getPrevCashId(cashId: number): Promise<number | null> {
  return http.post(`/api/bcs/Cash/getPrevId/${cashId}`, null, { appKey: 'bcs' })
}

export function countCash(params: any) {
  return http.post('/api/bcs/Cash/count', params, { appKey: 'bcs' })
}

export function pageCash(params: any): Promise<PageOut<Cash>> {
  return http.post('/api/bcs/Cash/page', params, { appKey: 'bcs' })
}
