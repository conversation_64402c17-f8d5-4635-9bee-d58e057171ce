import { wrapCodeExample } from '@/utils/codeUtils'

// 录入盘点结果示例代码
export const editUsage = wrapCodeExample(`<template>
  <!-- 输入盘点单ID -->
  <Form layout="inline">
    <Form.Item label="盘点单ID">
      <Input v-model:value="countId" placeholder="请输入盘点单ID" />
    </Form.Item>
    <Form.Item>
      <Button type="primary" @click="openEdit">打开录入</Button>
    </Form.Item>
  </Form>

  <!-- 录入盘点结果组件 -->
  <WmCountEdit
    buttonText="录入盘点结果"
    buttonType="primary"
    @success="handleEditSuccess"
    @cancel="handleEditCancel"
    @recheck="handleRecheckSuccess"
    @finish="handleFinishSuccess"
    ref="countEditRef"
  />
</template>

<script setup>
import { WmCountEdit } from '@mh-wm/count'
import { Button, Input, Form, message } from 'ant-design-vue'
import { ref } from 'vue'

// 盘点单ID
const countId = ref('123')
// 组件引用
const countEditRef = ref(null)

// 打开录入盘点结果对话框
const openEdit = () => {
  if (!countId.value) {
    message.warning('请输入盘点单ID')
    return
  }

  // 将输入的ID转换为数字
  const id = Number(countId.value)
  if (isNaN(id) || id <= 0) {
    message.error('请输入有效的盘点单ID')
    return
  }

  // 打开对应ID的盘点单
  countEditRef.value.open(id)
}

// 保存盘点结果成功回调
const handleEditSuccess = (id) => {
  message.success(\`保存盘点结果成功，盘点单ID: \${id}\`)
}

// 取消录入盘点结果回调
const handleEditCancel = () => {
  console.log('取消录入盘点结果')
}

// 复盘成功回调
const handleRecheckSuccess = (id) => {
  message.success(\`复盘操作成功，盘点单ID: \${id}\`)
}

// 复核成功回调
const handleFinishSuccess = (id) => {
  message.success(\`复核操作成功，盘点单ID: \${id}\`)
}
</script>`)

// 导入代码
export const importCode = `import { WmCountEdit } from '@mh-wm/count'
import { Button, Input, Form, message } from 'ant-design-vue'
import '@mh-wm/count/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/count": "^1.0.0"
  }
}`
