import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <div>
    <!-- 拆零盒整按钮 -->
    <a-button type="primary" @click="handleOpenSplitPack">
      拆零盒整
    </a-button>
    
    <!-- 拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @split-pack-success="handleSplitPackSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// 库存记录数据
const stockRecord = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  producer: '华北制药',
  packCells: 10,        // 包装规格：10粒/盒
  cellUnit: '粒',       // 拆零单位
  packUnit: '盒',       // 包装单位
  deptTotalPacks: 5,    // 仓库总库存整包数
  deptTotalCells: 3,    // 仓库总库存拆零数
  totalPacks: 2,        // 批次库存整包数
  totalCells: 8,        // 批次库存拆零数
}

// 打开拆零盒整模态框
const handleOpenSplitPack = () => {
  wholeBoxRef.value?.handleSplitPack(stockRecord)
}

// 拆零盒整成功回调
const handleSplitPackSuccess = (data) => {
  console.log('拆零盒整操作成功:', data)
  // 处理成功后的逻辑，如刷新数据、显示提示等
}
</script>`)

// 安装组件
export const installationCode = wrapCodeExample(`# 使用pnpm安装（推荐）
pnpm add @mh-wm/whole-box

# 使用npm安装
npm install @mh-wm/whole-box

# 使用yarn安装
yarn add @mh-wm/whole-box

# 检查安装是否成功
pnpm list @mh-wm/whole-box`)

// 组件导入
export const importCode = wrapCodeExample(`// 方式1: 默认导入
import WholeBox from '@mh-wm/whole-box'

// 方式2: 命名导入
import { WholeBox } from '@mh-wm/whole-box'

// 方式3: 导入类型
import type { WholeBoxInstance } from '@mh-wm/whole-box'

// 方式4: 导入样式（如果需要）
import '@mh-wm/whole-box/index.css'

// 在组件中使用
export default {
  components: {
    WholeBox
  }
}

// 或在setup中使用
<script setup>
import { WholeBox } from '@mh-wm/whole-box'
</script>`)

// 完整引入示例
export const fullImportExample = wrapCodeExample(`<template>
  <div class="stock-management">
    <!-- 库存数据表格 -->
    <a-table
      :columns="columns"
      :data-source="stockData"
      row-key="artId"
    >
      <template #action="{ record }">
        <a-button
          type="primary"
          size="small"
          @click="handleSplitPack(record)"
        >
          拆零盒整
        </a-button>
      </template>
    </a-table>

    <!-- 拆零盒整组件 -->
    <WholeBox
      ref="wholeBoxRef"
      @split-pack-success="handleSplitPackSuccess"
      @split-pack-error="handleSplitPackError"
    />
  </div>
</template>

<script setup lang="ts">
// 1. 导入Vue相关
import { ref, onMounted } from 'vue'

// 2. 导入UI组件
import { message } from 'ant-design-vue'

// 3. 导入WholeBox组件
import { WholeBox } from '@mh-wm/whole-box'
import type { WholeBoxInstance } from '@mh-wm/whole-box'

// 4. 导入样式（可选，如果全局没有导入）
// import '@mh-wm/whole-box/index.css'

// 组件引用
const wholeBoxRef = ref<WholeBoxInstance>()

// 库存数据
const stockData = ref([
  {
    artId: 1001,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    producer: '华北制药集团有限责任公司',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8,
    batchNo: '*********',
    expDate: '20251201'
  }
])

// 表格列配置
const columns = [
  { title: '药品名称', dataIndex: 'artName', key: 'artName' },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec' },
  { title: '仓库库存', key: 'deptStock' },
  { title: '批次库存', key: 'batchStock' },
  { title: '操作', key: 'action', slots: { customRender: 'action' } }
]

// 拆零盒整操作
const handleSplitPack = (record: any) => {
  if (!wholeBoxRef.value) {
    message.error('组件未初始化')
    return
  }

  // 调用组件方法打开拆零盒整模态框
  wholeBoxRef.value.handleSplitPack(record)
}

// 拆零盒整成功回调
const handleSplitPackSuccess = (data: any) => {
  message.success('拆零盒整操作成功！')
  console.log('操作成功数据:', data)

  // 刷新表格数据
  refreshStockData()
}

// 拆零盒整失败回调
const handleSplitPackError = (error: any) => {
  message.error('拆零盒整操作失败！')
  console.error('操作失败:', error)
}

// 刷新库存数据
const refreshStockData = () => {
  // 这里可以调用API重新获取数据
  console.log('刷新库存数据')
}

// 组件挂载后的初始化
onMounted(() => {
  console.log('WholeBox组件已挂载')
})
</script>

<style scoped>
.stock-management {
  padding: 24px;
}
</style>`)

// package.json配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/whole-box": "^1.0.1",
    "@mh-wm/util": "^1.0.6",
    "ant-design-vue": "^4.2.6",
    "vue": "^3.5.13"
  }
}`

// 组件方法调用
export const methodsUsage = wrapCodeExample(`<template>
  <div>
    <!-- 拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
    
    <!-- 操作按钮 -->
    <div class="actions">
      <a-button @click="openSplitPack">打开拆零盒整</a-button>
      <a-button @click="getComponentData">获取组件数据</a-button>
      <a-button @click="validateForm">验证表单</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// 打开拆零盒整模态框
const openSplitPack = () => {
  const record = {
    artName: '测试药品',
    artSpec: '100mg',
    packCells: 10,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8
  }
  
  wholeBoxRef.value.handleSplitPack(record)
}

// 获取组件数据
const getComponentData = () => {
  const data = wholeBoxRef.value.getCurrentData()
  console.log('当前组件数据:', data)
}

// 验证表单
const validateForm = () => {
  const isValid = wholeBoxRef.value.validateForm()
  console.log('表单验证结果:', isValid)
}
</script>`)

// 表格集成示例
export const tableIntegration = wrapCodeExample(`<template>
  <div>
    <!-- 库存数据表格 -->
    <a-table
      :columns="columns"
      :data-source="stockData"
      row-key="artId"
    >
      <!-- 操作列 -->
      <template #action="{ record }">
        <a-button 
          type="primary" 
          size="small"
          @click="handleSplitPack(record)"
        >
          拆零盒整
        </a-button>
      </template>
    </a-table>
    
    <!-- 拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" @split-pack-success="handleSuccess" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()
const stockData = ref([
  {
    artId: 1001,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8
  }
])

const columns = [
  { title: '药品名称', dataIndex: 'artName' },
  { title: '规格', dataIndex: 'artSpec' },
  { title: '仓库库存', key: 'deptStock' },
  { title: '批次库存', key: 'batchStock' },
  { title: '操作', key: 'action', slots: { customRender: 'action' } }
]

// 拆零盒整操作
const handleSplitPack = (record) => {
  wholeBoxRef.value.handleSplitPack(record)
}

// 操作成功回调
const handleSuccess = (data) => {
  // 刷新表格数据
  refreshTableData()
}
</script>`)

// API集成示例
export const apiIntegration = wrapCodeExample(`<template>
  <div>
    <!-- API状态监控 -->
    <div class="api-status">
      <a-tag :color="apiStatus.connected ? 'green' : 'red'">
        {{ apiStatus.connected ? 'API已连接' : 'API未连接' }}
      </a-tag>
      <span>成功: {{ apiStatus.successCount }}</span>
      <span>失败: {{ apiStatus.errorCount }}</span>
    </div>
    
    <!-- 拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @api-call="handleApiCall"
      @split-pack-success="handleSuccess"
      @split-pack-error="handleError"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// API状态
const apiStatus = reactive({
  connected: true,
  successCount: 0,
  errorCount: 0,
  lastCall: null
})

// 处理API调用
const handleApiCall = (event) => {
  apiStatus.lastCall = new Date()
  
  if (event.type === 'success') {
    apiStatus.successCount++
  } else if (event.type === 'error') {
    apiStatus.errorCount++
  }
}

// 操作成功
const handleSuccess = (data) => {
  console.log('拆零盒整成功:', data)
}

// 操作失败
const handleError = (error) => {
  console.error('拆零盒整失败:', error)
}
</script>`)

// 打包发布指令
export const publishCommands = `# 在项目根目录下执行以下命令打包并发布组件

# 正式版本
pnpm publish:component Wm/WholeBox

# 测试版本
pnpm publish:test-component Wm/WholeBox

# 开发版本
pnpm publish:dev-component Wm/WholeBox

# Beta版本
pnpm publish:beta-component Wm/WholeBox

# 仅构建不发布
pnpm publish:component Wm/WholeBox --no-publish

# 安装组件
pnpm add @mh-wm/whole-box`

// 打包流程说明
export const buildProcess = `打包命令会执行以下操作：
1. 编译组件源码（TypeScript + Vue）
2. 生成类型声明文件（.d.ts）
3. 打包CSS样式文件
4. 生成ES模块和UMD格式
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

构建产物结构：
dist/Wm/WholeBox/
├── es/index.js          # ES模块格式
├── umd/index.js         # UMD格式
├── src/index.d.ts       # 类型声明
├── index.d.ts           # 主入口类型
├── index.css            # 样式文件
└── package.json         # 包配置`

// 全局注册组件
export const globalRegistration = wrapCodeExample(`// main.ts 或 main.js
import { createApp } from 'vue'
import App from './App.vue'

// 导入WholeBox组件
import { WholeBox } from '@mh-wm/whole-box'
import '@mh-wm/whole-box/index.css'

const app = createApp(App)

// 全局注册组件
app.component('WholeBox', WholeBox)

app.mount('#app')

// 使用时无需再导入
// <template>
//   <WholeBox ref="wholeBoxRef" />
// </template>`)

// 按需导入示例
export const onDemandImport = wrapCodeExample(`// 方式1: 直接按需导入
<script setup>
import { WholeBox } from '@mh-wm/whole-box'

// 使用组件
const wholeBoxRef = ref()
</script>

// 方式2: 动态导入（懒加载）
<script setup>
import { defineAsyncComponent } from 'vue'

const WholeBox = defineAsyncComponent(() =>
  import('@mh-wm/whole-box').then(module => module.WholeBox)
)
</script>

// 方式3: 条件导入
<script setup>
import { ref, computed } from 'vue'

const showWholeBox = ref(false)
const WholeBox = computed(() => {
  if (showWholeBox.value) {
    return import('@mh-wm/whole-box').then(module => module.WholeBox)
  }
  return null
})
</script>`)

// TypeScript使用示例
export const typescriptUsage = wrapCodeExample(`<template>
  <div>
    <WholeBox
      ref="wholeBoxRef"
      @split-pack-success="handleSuccess"
      @split-pack-error="handleError"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'

// 导入组件和类型
import { WholeBox } from '@mh-wm/whole-box'
import type {
  WholeBoxInstance,
  StockRecord,
  SplitPackResult,
  SplitPackError
} from '@mh-wm/whole-box'

// 组件引用（带类型）
const wholeBoxRef = ref<WholeBoxInstance>()

// 定义库存记录类型
interface StockData extends StockRecord {
  artId: number
  status: 'normal' | 'low' | 'empty'
}

// 库存数据
const stockData = ref<StockData[]>([
  {
    artId: 1001,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g',
    producer: '华北制药集团有限责任公司',
    packCells: 10,
    cellUnit: '粒',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8,
    batchNo: '*********',
    expDate: '20251201',
    status: 'normal'
  }
])

// 拆零盒整操作（带类型检查）
const handleSplitPack = (record: StockData): void => {
  if (!wholeBoxRef.value) {
    message.error('组件未初始化')
    return
  }

  // 类型安全的方法调用
  wholeBoxRef.value.handleSplitPack(record)
}

// 成功回调（带类型）
const handleSuccess = (data: SplitPackResult): void => {
  message.success('拆零盒整操作成功！')
  console.log('操作结果:', data)

  // 更新库存数据
  updateStockData(data)
}

// 错误回调（带类型）
const handleError = (error: SplitPackError): void => {
  message.error(\`拆零盒整操作失败: \${error.message}\`)
  console.error('错误详情:', error)
}

// 更新库存数据
const updateStockData = (result: SplitPackResult): void => {
  const index = stockData.value.findIndex(item => item.artId === result.artId)
  if (index !== -1) {
    stockData.value[index] = { ...stockData.value[index], ...result }
  }
}
</script>`)

// 高级配置
export const advancedConfig = wrapCodeExample(`<template>
  <div>
    <!-- 高级配置示例 -->
    <WholeBox
      ref="wholeBoxRef"
      @split-pack-success="handleSuccess"
      @split-pack-error="handleError"
    />
  </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// 通过provide/inject传递配置
provide('wholeBoxConfig', {
  apiConfig: {
    baseURL: '/api/wm',
    timeout: 10000,
    retryCount: 3
  },
  validationRules: {
    required: true,
    minPackCount: 0,
    maxPackCount: 9999,
    allowNegative: false
  },
  uiConfig: {
    modalWidth: '800px',
    showBatchInfo: true,
    theme: 'default'
  }
})

// 事件处理
const handleSuccess = (data) => {
  console.log('操作成功:', data)
}

const handleError = (error) => {
  console.error('操作失败:', error)
}
</script>`)

// 常见问题解决方案
export const troubleshooting = wrapCodeExample(`// 问题1: 组件无法导入
// 解决方案: 检查依赖安装和导入路径
import { WholeBox } from '@mh-wm/whole-box'

// 如果导入失败，尝试以下方法：
// 1. 检查包是否正确安装
console.log(require('@mh-wm/whole-box/package.json').version)

// 2. 清除缓存重新安装
// pnpm store prune && pnpm install

// 3. 使用完整路径导入
import WholeBox from '@mh-wm/whole-box/dist/es/index.js'

// 问题2: 样式显示异常
// 解决方案: 确保导入了CSS文件
import '@mh-wm/whole-box/index.css'

// 或在main.ts中全局导入
// import '@mh-wm/whole-box/index.css'

// 问题3: TypeScript类型错误
// 解决方案: 正确导入类型定义
import type { WholeBoxInstance } from '@mh-wm/whole-box'

// 如果类型仍然无法识别，检查tsconfig.json
// {
//   "compilerOptions": {
//     "moduleResolution": "node",
//     "esModuleInterop": true
//   }
// }

// 问题4: 组件方法调用失败
// 解决方案: 确保组件已挂载
const wholeBoxRef = ref<WholeBoxInstance>()

const handleSplitPack = (record: any) => {
  // 检查组件是否已挂载
  if (!wholeBoxRef.value) {
    console.error('WholeBox组件未挂载')
    return
  }

  // 检查方法是否存在
  if (typeof wholeBoxRef.value.handleSplitPack !== 'function') {
    console.error('handleSplitPack方法不存在')
    return
  }

  // 安全调用
  try {
    wholeBoxRef.value.handleSplitPack(record)
  } catch (error) {
    console.error('调用失败:', error)
  }
}

// 问题5: API调用失败
// 解决方案: 检查网络和API配置
const handleApiError = (error: any) => {
  if (error.code === 'NETWORK_ERROR') {
    message.error('网络连接失败，请检查网络设置')
  } else if (error.code === 'TIMEOUT') {
    message.error('请求超时，请稍后重试')
  } else if (error.code === 'UNAUTHORIZED') {
    message.error('权限不足，请联系管理员')
  } else {
    message.error(\`操作失败: \${error.message}\`)
  }
}`)

// 最佳实践
export const bestPractices = wrapCodeExample(`<template>
  <div class="stock-management">
    <!-- 1. 使用v-if确保组件在需要时才渲染 -->
    <WholeBox
      v-if="showWholeBox"
      ref="wholeBoxRef"
      @split-pack-success="handleSuccess"
      @split-pack-error="handleError"
    />

    <!-- 2. 提供加载状态 -->
    <a-spin :spinning="loading">
      <a-button @click="handleSplitPack">拆零盒整</a-button>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { WholeBox } from '@mh-wm/whole-box'
import type { WholeBoxInstance } from '@mh-wm/whole-box'

// 最佳实践1: 使用响应式状态管理
const wholeBoxRef = ref<WholeBoxInstance>()
const loading = ref(false)
const showWholeBox = ref(false)

// 最佳实践2: 错误边界处理
const handleError = (error: any) => {
  loading.value = false

  // 记录错误日志
  console.error('WholeBox Error:', {
    timestamp: new Date().toISOString(),
    error: error,
    userAgent: navigator.userAgent
  })

  // 用户友好的错误提示
  if (error.code === 'VALIDATION_ERROR') {
    message.warning('数据验证失败，请检查输入')
  } else if (error.code === 'NETWORK_ERROR') {
    message.error('网络连接失败，请稍后重试')
  } else {
    message.error('操作失败，请联系技术支持')
  }
}

// 最佳实践3: 防抖处理
let debounceTimer: NodeJS.Timeout | null = null

const handleSplitPack = (record: any) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }

  debounceTimer = setTimeout(() => {
    performSplitPack(record)
  }, 300)
}

const performSplitPack = async (record: any) => {
  if (!wholeBoxRef.value) {
    message.warning('组件未准备就绪，请稍后重试')
    return
  }

  loading.value = true

  try {
    // 确保DOM更新完成
    await nextTick()

    // 执行拆零盒整
    wholeBoxRef.value.handleSplitPack(record)
  } catch (error) {
    handleError(error)
  }
}

// 最佳实践4: 生命周期管理
onMounted(() => {
  // 延迟显示组件，确保依赖加载完成
  setTimeout(() => {
    showWholeBox.value = true
  }, 100)
})

onUnmounted(() => {
  // 清理定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer)
  }
})

// 最佳实践5: 数据验证
const validateStockRecord = (record: any): boolean => {
  const requiredFields = [
    'artName', 'artSpec', 'packCells',
    'cellUnit', 'packUnit', 'deptTotalPacks',
    'deptTotalCells', 'totalPacks', 'totalCells'
  ]

  for (const field of requiredFields) {
    if (record[field] === undefined || record[field] === null) {
      message.error(\`缺少必要字段: \${field}\`)
      return false
    }
  }

  if (record.packCells <= 0) {
    message.error('包装规格必须大于0')
    return false
  }

  return true
}

// 最佳实践6: 成功处理
const handleSuccess = (data: any) => {
  loading.value = false
  message.success('拆零盒整操作成功')

  // 记录成功日志
  console.log('WholeBox Success:', {
    timestamp: new Date().toISOString(),
    data: data
  })

  // 触发数据刷新
  emit('refresh-data', data)
}
</script>

<style scoped>
.stock-management {
  padding: 24px;
}

/* 确保组件在不同屏幕尺寸下正常显示 */
@media (max-width: 768px) {
  .stock-management {
    padding: 12px;
  }
}
</style>`)

// 性能优化
export const performanceOptimization = wrapCodeExample(`<script setup lang="ts">
import { ref, shallowRef, markRaw, defineAsyncComponent } from 'vue'

// 性能优化1: 使用shallowRef减少响应式开销
const stockData = shallowRef([])

// 性能优化2: 使用markRaw标记不需要响应式的对象
const staticConfig = markRaw({
  apiEndpoint: '/api/wm/split-pack',
  timeout: 10000
})

// 性能优化3: 懒加载组件
const WholeBox = defineAsyncComponent({
  loader: () => import('@mh-wm/whole-box'),
  loadingComponent: () => h('div', '加载中...'),
  errorComponent: () => h('div', '加载失败'),
  delay: 200,
  timeout: 3000
})

// 性能优化4: 使用Web Worker处理大量数据
const processLargeDataset = (data: any[]) => {
  if (data.length > 1000) {
    // 使用Web Worker处理大数据集
    const worker = new Worker('/workers/stock-processor.js')
    worker.postMessage(data)
    worker.onmessage = (event) => {
      stockData.value = event.data
    }
  } else {
    stockData.value = data
  }
}

// 性能优化5: 虚拟滚动（大列表）
const virtualScrollConfig = {
  itemHeight: 60,
  visibleCount: 20,
  bufferCount: 5
}
</script>`)
