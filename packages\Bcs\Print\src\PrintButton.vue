<script lang="ts" setup>
import { Data, useLoading } from '@idmy/core'
import { getPrintTemplate } from '@mh-base/core'
import { Button } from 'ant-design-vue'

const { code } = defineProps({
  code: { type: String, required: true },
  text: { type: String },
})

const state = ref({})
const [_, loading] = useLoading(async () => {
  state.value = await getPrintTemplate(code)
}, true)

const emits = defineEmits({
  click: (_: Data) => true,
})

const onClick = () => {
  emits('click', state.value)
}
</script>

<template>
  <template v-if="!loading">
    <Button v-if="state.title" type="primary" v-bind="$attrs" @click="onClick()">
      <slot>
        {{ text }}
      </slot>
    </Button>
  </template>
</template>
