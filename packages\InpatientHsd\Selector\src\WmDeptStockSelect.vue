<script setup lang="ts">
import { BaseAutoComplete } from "@mh-base/core"
import { deptStockPageApi } from "@mh-wm/util"
import { ref, watch, reactive } from 'vue'

const props = defineProps({
  deptCode: {
    type: String,
    default: null
    }
})
const emit = defineEmits(['selected'])

const artDataModel = reactive({
  columns: [
   { title: "条目ID", dataIndex: "artId", width: 100, align: "right" },
    { title: "品名", dataIndex: "artName", width: 150 },
    { title: "规格", dataIndex: "artSpec", width: 100 },
    { title: "生产厂家", dataIndex: "producer", width: 150 },
    { title: "包装单位", dataIndex: "packUnit", width: 75 },
    { title: "包装制剂数", dataIndex: "packCells", width: 80 },
    { title: "批号", dataIndex: "batchNo", width: 90 },
    { title: "整包库存数", dataIndex: "stockPacksTotal", width: 90 },
    { title: "拆零库存数", dataIndex: "stockCellsTotal", width: 90 },
    { title: "整包单价", dataIndex: "packPrice", width: 90 },
    { title: "医保编码", dataIndex: "miCode", width: 150 },
    { title: "批准文号", dataIndex: "approvalNo", width: 150 },
    { title: "允许拆零", dataIndex: "splittable", width: 90 }
  ],
  searchFormModel: {
     order: "asc",
    sidx: "t_article.art_id",
    keyword: "",
    artName: undefined, // 添加artName属性用于显示
  },
  options: [],
  pagination: {
    pageSize: 10, // 每页条数
    pageNum: 1, // 当前页码
    pages: 0, // 总页数
    total: 0, // 总条数
    showTotal: total => `共：${total} 条`,
    onChange: async (current, pageSize) => {
      artDataModel.pagination.pageSize = pageSize
      artDataModel.pagination.pageNum = current
      await artDataModel.loadOptions()
    }
  },  loadOptions: async () => {
    console.log('加载选项，参数:', {
      deptCode: props.deptCode,
      searchForm: artDataModel.searchFormModel,
      pagination: artDataModel.pagination
    });

    if (!props.deptCode) {
      console.warn('警告: deptCode为空，无法加载数据');
      return;
    }

    try {
      const data = await deptStockPageApi({
        deptCode: props.deptCode,
        ...artDataModel.searchFormModel,
        ...artDataModel.pagination
      });

      console.log('API返回数据:', data);

      if (data && data.list && data.list.length > 0) {
        data.list.forEach((item) => {
          item.pkStr = item.artId + "-" + item.stockNo;
          item.stockPacksTotal = item.totalPacks;
          item.stockCellsTotal = item.totalCells;
        });
        artDataModel.options = data.list;
        artDataModel.pagination.pageNum = Number(data.pageNum);
        artDataModel.pagination.pages = Number(data.pages);
        artDataModel.pagination.total = Number(data.total);
      } else {
        console.warn('API返回数据为空或格式不正确');
        artDataModel.options = [];
      }
    } catch (error) {
      console.error('加载选项出错:', error);
      artDataModel.options = [];
    }
  },
  onSearch: async (value: string) => {
     artDataModel.pagination.pageNum = 1;
    artDataModel.searchFormModel.keyword = value;
    await artDataModel.loadOptions()
  },
  onSelect: (item: any) => {
    if (item) {
      artDataModel.searchFormModel.artName = item.artName +  (item.artSpec ? ' ' + item.artSpec : '') + (item.producer? ' ' + item.producer: '')
      emit('selected', item)
    }
  },
})

const init = () => {
  artDataModel.searchFormModel.artName = undefined
  artDataModel.options = []
}

watch(() => props.deptCode, (_) => {
  // 当deptCode变化时重置组件
  init()
  // 如果deptCode有值，尝试加载数据
  if (props.deptCode) {
    artDataModel.loadOptions();
  }
}, {
  immediate: true,
  deep: true,
})
const keywordRef = ref()

const focus = () => {
  keywordRef?.value.$refs().focus()
}

defineExpose({
  init,
  focus
})
</script>

<template>
  <div>
    <base-auto-complete
      style="width: 100%"
      ref="keywordRef"
      v-model:value="artDataModel.searchFormModel.artName"
      keyColumn="artId"
      :columns="artDataModel.columns"
      :options="artDataModel.options"
      :pagination="artDataModel.pagination"
      @onSearch="artDataModel.onSearch"
      @onSelect="artDataModel.onSelect"
    />
  </div>
</template>
