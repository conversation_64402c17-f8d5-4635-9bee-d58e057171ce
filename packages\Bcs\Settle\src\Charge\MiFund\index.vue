<script lang="ts" setup>
import { ChargeContext, chargeInjectKey } from '@mh-bcs/util'

const Inpatient = defineAsyncComponent(() => import('./Inpatient.vue'))
const Outpatient = defineAsyncComponent(() => import('./Outpatient.vue'))

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})
</script>
<template>
  <Outpatient v-if="ctx.cashType === 'OUTPATIENT' && ctx.visitId" />
  <Inpatient v-if="ctx.cashType === 'INPATIENT' && ctx.visitId" />
</template>
