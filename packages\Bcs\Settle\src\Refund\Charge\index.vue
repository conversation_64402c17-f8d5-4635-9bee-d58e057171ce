<script lang="ts" setup>
import { add, Format, subtract, useLoading } from '@idmy/core'
import { SelfFeePaymentType } from '@mh-bcs/pay'
import { CashType, chargeInjectKey, getCash, MiTrans, MiTransStatusEnum, sumUnpaidAmount } from '@mh-bcs/util'
import { Space } from 'ant-design-vue'
import { isNil } from 'lodash-es'
import type { PropType } from 'vue'
import Discount from '../../charge/Discount.vue'
import MiFund from '../../charge/miFund/index.vue'

import CashPayment from './CashPayment.vue'
import Finish from './Finish.vue'
import Refund from './Refund.vue'

const { blueCashId, card, cashId, cashType, midway, showMiFund, visitId } = defineProps({
  card: { type: Object as PropType<Record<string, any>> },
  midway: { type: Boolean as PropType<boolean>, default: false },
  cashId: { type: Number as PropType<number>, required: true },
  blueCashId: { type: Number as PropType<number>, required: true },
  visitId: { type: Number as PropType<number> },
  cashType: { type: String as PropType<CashType>, required: true },
  showMiFund: { type: Boolean as PropType<boolean>, default: true },
})

const cpRef = ref()
const onLoadCashPayment = () => cpRef.value?.onLoad()

const miTrans = reactive<MiTrans>({
  transStatus: undefined,
  acctPayAmt: undefined,
  fundPayAmt: undefined,
  multiAidAmt: undefined,
  miTransId: undefined,
})

const finished = ref(false)
const totalAmount = ref()
const cashUnpaidAmount = ref()
const loaded = ref(false)

const unpaidAmount = computed(() => {
  if (miTrans.transStatus === MiTransStatusEnum.preSettle) {
    return subtract(cashUnpaidAmount.value, add(miTrans.fundPayAmt, miTrans.acctPayAmt, miTrans.multiAidAmt))
  } else {
    return cashUnpaidAmount.value
  }
})

const [onLoad] = useLoading(async () => {
  const tmp = await getCash(cashId)
  finished.value = tmp.status === 1
  totalAmount.value = subtract(tmp.amount, tmp.refundedAmount)
  cashUnpaidAmount.value = await sumUnpaidAmount(cashId)
  await nextTick()
  await onLoadCashPayment()
  await nextTick()
  loaded.value = true
}, true)

const isZero = computed(() => unpaidAmount.value === 0)

const payAmount = ref(0)
const paying = ref(false)
watch(
  () => unpaidAmount.value,
  val => {
    if (!paying.value) {
      payAmount.value = val ?? 0
    }
  }
)

watch(
  () => payAmount.value,
  val => {
    if (isNil(val)) {
      payAmount.value = 0
    }
  }
)

const autoFinish = ref(false)
const paymentType = ref(cashType === 'INPATIENT' ? 'PREPAID' : 'CASH')
const miFundCashPayment = ref()
const ctx = reactive({
  allowPayZero: showMiFund,
  autoFinish,
  blueCashId,
  card,
  cashId,
  cashType,
  finished,
  isRefund: true,
  isZero,
  miFundCashPayment,
  miTrans,
  midway,
  onLoad,
  payAmount,
  paying,
  paymentType,
  totalAmount,
  unpaidAmount,
  finishing: false,
  visitId,
})
provide(chargeInjectKey, ctx)
</script>

<template>
  <div class="flex justify-between mb-8px!">
    <Space :size="24" items-center text-16px>
      <Discount :cashId="cashId" />
      <Format :value="totalAmount" prefix="应退金额：" type="Currency" />
      <Format :value="unpaidAmount" flex items-center prefix="待退金额：" type="Currency" value-class="error font-bold text-22px" />
      <Refund v-if="loaded" />
    </Space>
    <Space text-16px size="large">
      <Finish />
    </Space>
  </div>
  <MiFund v-if="loaded && showMiFund" />
  <div flex>
    <div class="f1">
      <CashPayment ref="cpRef" />
    </div>
    <div class="w-530px">
      <SelfFeePaymentType />
    </div>
  </div>
  <slot />
</template>
