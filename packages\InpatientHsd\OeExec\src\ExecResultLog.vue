<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import { BackTop as ABackTop, List, ListItem } from 'ant-design-vue'

const results = ref<{ title: string; message: string; success: boolean; isNew?: boolean }[]>([]);

const successResults = computed(() => results.value.filter(item => item.success));
const errorResults = computed(() => results.value.filter(item => !item.success));
const loading = ref(false);
const logContainer = ref<HTMLElement | null>(null);

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight;
  }
};

const scrollToTop = () => {
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTo({
        top: 0,
        behavior: 'smooth',
        left: 0
      });
    }
  });
};

const addResult = (result: { title: string; message: string; success: boolean }) => {
  const newResult = { ...result, isNew: true };
  if (!result.success) {
    results.value.unshift(newResult);
  } else {
    results.value.push(newResult);
  }

  nextTick(() => {
    scrollToBottom();
    setTimeout(() => {
      newResult.isNew = false;
    }, 500);
  });
};

const clearResults = () => {
  results.value = [];
};

const getResults = () => {
  return results.value
}

defineExpose({
  addResult,
  clearResults,
  getResults,
  scrollToTop
});
</script>

<template>
  <div class="exec-result-log" ref="logContainer">
    <a-back-top :target="() => logContainer" :visibilityHeight="100" class="custom-back-top" />
    <div class="error-items" v-show="errorResults && errorResults.length > 0">
      <List :data-source="errorResults" size="small" :loading="loading">
        <template #renderItem="{ item }">
          <List-item :class="{ 'float-to-top': item.isNew }">
            <div class="log-item">
              <span class="log-title">
                {{ item.title }}
                <close-circle-outlined style="color: red;" />
              </span>
              <div class="log-message">{{ item.message }}</div>
            </div>
          </List-item>
        </template>
      </List>
    </div>
    <div class="success-items" v-show="successResults && successResults.length > 0">
      <List :data-source="successResults" size="small" :loading="loading" :grid="{ column: 4, gutter: 16 }">
        <template #renderItem="{ item }">
          <List-item>
            <div class="log-item">
              <span class="log-title">
                {{ item.title }}
                <check-circle-outlined style="color: green;" />
              </span>
            </div>
          </List-item>
        </template>
      </List>
    </div>
  </div>
</template>

<style lang="less" scoped>
.exec-result-log {
  max-height: calc(100vh - 160px);
  overflow-y: auto;
  overflow-x: hidden;
}

.success-items {
  margin-bottom: 16px;
}

.error-items {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.log-item {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.log-title {
  font-weight: bold;
}

.log-icon {
  margin-left: auto;
}

.log-message {
  color: red;
  font-size: 12px;
  margin-top: 4px;
}

.ant-list-item {
  padding: 0;
}

.float-to-top {
  animation: floatToTop 0.5s ease-out;
}

@keyframes floatToTop {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.custom-back-top {
  position: absolute !important;
  right: 24px;
  top: 24px;
}

.custom-back-top :deep(.ant-back-top-content) {
  background-color: #1890ff;
  opacity: 0.8;
  transition: all 0.3s;
}

.custom-back-top :deep(.ant-back-top-content:hover) {
  opacity: 1;
}
</style>
