<script setup lang="ts">
import { ref, computed, nextTick } from 'vue';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import { BackTop as ABackTop, List, ListItem } from 'ant-design-vue'

const results = ref<{ title: string; message: string; success: boolean; isNew?: boolean }[]>([]);

const successResults = computed(() => results.value.filter(item => item.success));
const errorResults = computed(() => results.value.filter(item => !item.success));
const loading = ref(false);
const logContainer = ref<HTMLElement | null>(null);

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight;
  }
};

const scrollToTop = () => {
  nextTick(() => {
    if (logContainer.value) {
      logContainer.value.scrollTo({
        top: 0,
        behavior: 'smooth',
        left: 0
      });
    }
  });
};

const addResult = (result: { title: string; message: string; success: boolean }) => {
  const newResult = { ...result, isNew: true };
  if (!result.success) {
    results.value.unshift(newResult);
  } else {
    results.value.push(newResult);
  }

  nextTick(() => {
    scrollToBottom();
    setTimeout(() => {
      newResult.isNew = false;
    }, 500);
  });
};

const clearResults = () => {
  results.value = [];
};

const getResults = () => {
  return results.value
}

defineExpose({
  addResult,
  clearResults,
  getResults,
  scrollToTop
});
</script>

<template>
  <div class="exec-result-log" ref="logContainer">
    <a-back-top :target="() => logContainer" :visibilityHeight="100" class="custom-back-top" />
    <div v-if="errorResults && errorResults.length > 0" class="error-items-enhanced">
      <div class="error-header">
        <close-circle-outlined style="color: #ff4d4f; font-size: 22px; margin-right: 8px;" />
        <span class="error-title">有失败项，请及时处理！</span>
        <span class="error-count">（共 {{ results.length }} 条，失败 {{ errorResults.length }} 条）</span>
      </div>
      <List :data-source="errorResults" size="small" :loading="loading">
        <template #renderItem="{ item, index }">
          <List-item :class="{ 'float-to-top': item.isNew }">
            <div class="log-item error-bg">
              <span class="log-title">
                <span class="error-index">{{ index + 1 }}.</span>
                {{ item.title }}
                <close-circle-outlined style="color: red; margin-left: 4px;" />
              </span>
              <div class="log-message copyable">{{ item.message }}</div>
            </div>
          </List-item>
        </template>
      </List>
    </div>
    <div v-if="successResults && successResults.length > 0" class="success-items-enhanced">
      <div v-if="successResults.length === results.length" class="success-header-all">
        <check-circle-outlined style="color: #52c41a; font-size: 28px; margin-right: 8px;" />
        <span class="success-title">全部成功</span>
        <span class="success-count">（共 {{ results.length }} 条）</span>
      </div>
      <div v-else class="success-header">
        <check-circle-outlined style="color: #52c41a; font-size: 20px; margin-right: 6px;" />
        <span class="success-title">成功</span>
        <span class="success-count">（共 {{ results.length }} 条，成功 {{ successResults.length }} 条）</span>
      </div>
      <List :data-source="successResults" size="small" :loading="loading" :grid="{ column: 4, gutter: 16 }">
        <template #renderItem="{ item }">
          <List-item>
            <div class="log-item success-bg">
              <span class="log-title">
                {{ item.title }}
                <check-circle-outlined style="color: #52c41a; margin-left: 4px;" />
              </span>
            </div>
          </List-item>
        </template>
      </List>
    </div>
  </div>
</template>

<style lang="less" scoped>
.exec-result-log {
  max-height: calc(100vh - 160px);
  overflow-y: auto;
  overflow-x: hidden;
}
.success-items-enhanced {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  margin-bottom: 16px;
  padding: 12px 12px 4px 12px;
}
.success-header-all {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: #389e0d;
  margin-bottom: 8px;
}
.success-header {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: bold;
  color: #52c41a;
  margin-bottom: 8px;
}
.success-title {
  margin-right: 8px;
}
.success-count {
  color: #888;
  font-size: 13px;
}
.success-bg {
  background: #f6ffed;
  border-radius: 4px;
  padding: 6px 8px;
  margin-bottom: 4px;
}
.error-items-enhanced {
  background: #fff1f0;
  border: 1px solid #ffa39e;
  border-radius: 6px;
  margin-bottom: 16px;
  padding: 12px 12px 4px 12px;
}
.error-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #cf1322;
  margin-bottom: 8px;
}
.error-title {
  margin-right: 8px;
}
.error-count {
  color: #888;
  font-size: 13px;
}
.error-bg {
  background: #fff1f0;
  border-radius: 4px;
  padding: 6px 8px;
  margin-bottom: 4px;
}
.error-index {
  color: #cf1322;
  font-weight: bold;
  margin-right: 4px;
}
.log-item {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}
.log-title {
  font-weight: bold;
  word-break: break-all;
}
.log-message {
  color: #cf1322;
  font-size: 13px;
  margin-top: 4px;
  white-space: pre-line;
  word-break: break-all;
}
.copyable {
  user-select: text;
}
.ant-list-item {
  padding: 0;
}
.float-to-top {
  animation: floatToTop 0.5s ease-out;
}
@keyframes floatToTop {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.custom-back-top {
  position: absolute !important;
  right: 24px;
  top: 24px;
}
.custom-back-top :deep(.ant-back-top-content) {
  background-color: #1890ff;
  opacity: 0.8;
  transition: all 0.3s;
}
.custom-back-top :deep(.ant-back-top-content:hover) {
  opacity: 1;
}
</style>
