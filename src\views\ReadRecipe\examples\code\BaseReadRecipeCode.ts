import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <div>
    <BaseReadRecipe
      ref="recipeRef"
      type="inline"
      :recipe="recipeData"
      :filePath="filePath"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { BaseReadRecipe } from '@mh-hsd/base'

const recipeRef = ref()
const filePath = ref('/UserFiles')

// 处方数据
const recipeData = ref({
  recipeId: 1,
  rxNo: 'RX202412190001',
  patientName: '张三',
  patientGenderName: '男',
  ageOfYears: 35,
  applyDeptname: '内科',
  clinicianName: '李医生',
  diseaseDiagName: '感冒',
  recipeTypeId: 1,
  timeCreated: new Date(),
  recipeGroupLs: [
    {
      groupNo: 1,
      routeName: '口服',
      freqCode: 'BID',
      notice: '饭后服用',
      recipeDetailLs: [
        {
          artId: 1,
          artName: '阿莫西林胶囊',
          artSpec: '0.25g',
          mealCells: 2,
          cellUnit: '粒',
          total: 24,
          unit: '粒',
          amount: 15.60
        }
      ]
    }
  ]
})

</script>`)

// 弹窗模式
export const modalUsage = wrapCodeExample(`<template>
  <div>
    <Button type="primary" @click="showModal">查看处方弹窗</Button>

    <BaseReadRecipe
      ref="modalRef"
      type="modal"
      :recipe="recipeData"
      :modalWidth="900"
      :filePath="filePath"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { BaseReadRecipe } from '@mh-hsd/base'

const modalRef = ref()
const recipeData = ref({ /* 处方数据 */ })
const filePath = ref('/UserFiles')

const showModal = () => {
  modalRef.value?.openModal()
}
</script>`)

// 导入代码
export const importCode = `import { BaseReadRecipe } from '@mh-hsd/base'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-hsd/base": "^1.0.0"
  }
}`
