<script setup lang="ts">
import { OrgDoctor, OrgDept } from '@mh-hip/org'
import { Card, Typography, Divider, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { clinicianDeptUsage, importCode, packageJsonCode } from '../code/OrgDeptCode'

const { Title } = Typography

// 选中的医生ID
const clinicianId = ref<number>()

// 医生对应的部门选择
const clinicianDeptId = ref<number>()
</script>

<template>
  <Card title="根据医生ID查询对应的部门" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="12">
        <div>
          <div mb-8px>医生：</div>
          <OrgDoctor v-model="clinicianId" style="width: 100%" />
          <div mt-8px>选中的医生ID: {{ clinicianId }}</div>
        </div>
      </Col>
      <Col :span="12">
        <div>
          <div mb-8px>医生对应的部门：</div>
          <OrgDept v-model="clinicianDeptId" :clinicianId="clinicianId" style="width: 100%" />
          <div mt-8px>选中的部门ID: {{ clinicianDeptId }}</div>
        </div>
      </Col>
    </Row>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="clinicianDeptUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
