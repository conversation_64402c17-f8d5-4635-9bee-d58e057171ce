import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { ExecForm, PackDeliverForm } from '@mh-inpatient-hsd/oe-exec'
import '@mh-inpatient-hsd/oe-exec/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/oe-exec": "^1.0.5",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 医嘱执行 - ExecForm
export const execFormUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleExecForm">打开医嘱执行</Button>
  
  <ExecForm 
    ref="execFormRef" 
    :oe-type-id="oeTypeId"
    :section-id="sectionId" 
    :medicines-accounting-mode="medicinesAccountingMode"
    @executed="handleExecuted" 
  />
</template>

<script setup>
import { ExecForm } from '@mh-inpatient-hsd/oe-exec'
import '@mh-inpatient-hsd/oe-exec/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const execFormRef = ref()

// 模拟数据
const sectionId = 40
const oeTypeId = 2
const medicinesAccountingMode = 1

// 打开医嘱执行弹窗
const handleVisibleExecForm = () => {
  execFormRef.value.open()
}

// 执行完成回调
const handleExecuted = (data) => {
  message.success('执行完成')
  console.log('执行数据:', data)
}
</script>`)

// 医嘱执行 - PackDeliverForm
export const packDeliverFormUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisiblePackDeliver">打开申请用药</Button>
  
  <pack-deliver-form
    ref="packDeliverFormRef"
    :section-id="sectionId"
    :oe-type-id="oeTypeId"
    :medicines-accounting-mode="medicinesAccountingMode"
    :pack-deliver-acc="packDeliverAcc"
    @submit="handleExecuted"
  />
</template>

<script setup>
import { PackDeliverForm } from '@mh-inpatient-hsd/oe-exec'
import '@mh-inpatient-hsd/oe-exec/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const packDeliverFormRef = ref()

// 模拟数据
const sectionId = 40
const oeTypeId = 2
const medicinesAccountingMode = 1
const packDeliverAcc = true

// 打开申请用药弹窗
const handleVisiblePackDeliver = () => {
  const oeIdLs = [{"visitId": 83798, "oeNo": 1}]
  const selectedRows = [{"keyStr":"83798-1","visitId":83798,"oeNo":1,"bedNo":"10","bedDisplayOrder":363,"patientName":"孔令才","oeTypeId":2,"freqCode":"qd","lastExecDate":********,"nextExecDate":********,"groupMark":0,"displayOrder":1,"oeText":"内科护理常规","cycleExecTimes":0,"cycleTotalTimes":1,"oneDayMaxTimes":1,"oeTimeStarted":"2025-03-12 14:22:00","dischargeOe":false,"artTypeId":20,"mealCells":1,"unit":"次","oeCat":9,"artId":8000319,"lastExecTime":"2025-03-17 08:02:52","lastExecUser":"张琳琳","orderEntryStatus":2,"firstAid":false,"stockReq":0,"requestToday":false,"requestTomorrow":false,"requestAfterTomorrow":false,"requestAfterMore":false,"artName":"内科护理常规","packUnit":"次","todayInt":********,"visitIdCount":19,"groupNoCount":1}]
  packDeliverFormRef.value.open(selectedRows)
}

// 执行完成回调
const handleExecuted = (data) => {
  message.success('执行完成')
  console.log('执行数据:', data)
}
</script>`)
