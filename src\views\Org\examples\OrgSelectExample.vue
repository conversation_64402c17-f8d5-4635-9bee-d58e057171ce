<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './OrgSelect/Basic.vue'
import OwnExample from './OrgSelect/Own.vue'
import MultipleExample from './OrgSelect/Multiple.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>组织机构选择组件，支持选择所有组织机构或仅当前用户有权限的组织机构。</Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="own" tab="用户权限">
        <OwnExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="multiple" tab="多选模式">
        <MultipleExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
