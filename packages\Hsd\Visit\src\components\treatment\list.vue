<script setup lang="ts">
import { Card, Row, Col } from 'ant-design-vue'
// 治疗单列表
const props = defineProps(['visitId'])

import { findOrderLsApi, ageFormatYearDays, dateFormatYMD, priceFormat, RecipeTypeEnum } from '@mh-hsd/util'

const orderLs = ref<any>([])

async function findOrderLs() {
  orderLs.value = []
  try {
    const params = {
      S_EQ_t_recipe__Recipe_Type_ID: RecipeTypeEnum.TREATMENT,
      S_EQ_t_visit__Visit_ID: props.visitId,
      S_NOTIN_t_recipe__Exec_Status: '4,5',
      S_ISNOTNULL_t_recipe_extra__Time_Signed: 1,
      sidx: 't_recipe.Recipe_TypeID ASC, t_recipe_extra.Time_Signed',
      order: 'desc'
    }
    const data: any = await findOrderLsApi(params)
    data.forEach((item: any) => {
      const detailLs = item.recipeDetailLs.filter((detail: any) => detail.catTypeId === 201)
      if (detailLs.length > 0) {
        orderLs.value.push(item)
      }
    })
  } catch (err) {
    console.log(err)
  }
}

const amount = computed(() => {
  return (recipeDetailLs: any) => {
    let amount = 0
    recipeDetailLs.forEach((item: any) => {
      if (item.amount) {
        amount = Number(amount) + Number(item.amount)
      }
    })
    if (amount) {
      amount = parseFloat((amount).toFixed(2))
    }
    return amount
  }
})

watch(() => props.visitId, (val) => {
  if (val) {
    findOrderLs()
  }
}, {
  immediate: true,
  deep: true
})
</script>

<template>
  <div v-for="item in orderLs" :key="item.orderId">
    <Card :bordered="false" m-b-5px :body-style="{ padding: '20px' }">
      <h2 font-size-16px font-bold text-center>{{ item.orgName }}</h2>
      <div font-size-16px font-bold text-center>治疗单</div>
      <Row>
        <Col :span="24">费别：{{ item.insuranceName }}</Col>
        <Col :span="12">单号：{{ item.accessionNo }}</Col>
        <Col :span="12" text-right><span>门诊号：{{ item.visitId }}</span></Col>
        <Col :span="24">
          <div class="custom-doubleline"></div>
        </Col>
        <Col :span="14">
          <span m-r-10px>姓名：{{ item.patientName }}</span>
          <span m-r-10px>性别：{{ item.patientGenderName }}</span>
          <span m-r-10px>年龄：{{ ageFormatYearDays(item.ageOfYears, item.ageOfDays) }}</span>
        </Col>
        <Col :span="10" text-right>科室：{{ item.applyDeptname }}</Col>
        <Col :span="12">执行科室：{{ item.exceDeptname }}</Col>
        <Col :span="12" text-right>
          <span>日期：{{ dateFormatYMD(item.timeCreated) }}</span>
        </Col>
        <Col :span="24" h-auto>
          <span>申请目的：</span>{{ item.purposeDesc }}
        </Col>
        <Col :span="24" h-auto>
          <span font-bold color="#00061b">治疗项目：</span>
        </Col>
        <Col :span="24" p-x-30px m-b-5px>
          <Row>
            <Col v-for="detail in item.recipeDetailLs" :key="detail.lineNo" :span="24">
              <span>{{ detail.artName }}</span>
              <span p-l-20px>{{ detail.total }}*{{ detail.unit }}</span>
            </Col>
          </Row>
        </Col>
        <Col :span="12">
          <span>申请医师：{{ item.clinicianName }}</span>
        </Col>
        <Col :span="12" text-right>
          <span>金额：{{ priceFormat(amount(item.recipeDetailLs)) }}</span>
        </Col>
      </Row>
    </Card>
  </div>
</template>

<style scoped lang="less">
@import url(../style.css);
</style>
