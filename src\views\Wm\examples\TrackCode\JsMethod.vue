<script setup lang="ts">
import { TrackCode } from '@mh-wm/track-code'
import { Card, Typography, Divider, Input, Button, Space } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { jsMethodUsage, importCode, packageJsonCode } from '../code/TrackCodeCode'

const { Title, Paragraph } = Typography

// 组件引用
const trackCodeRef = ref()

// 溯源码ID
const wbSeqid = ref<string>('12345')

// 打开溯源码录入窗口
const openTrackCode = () => {
  trackCodeRef.value.open(wbSeqid.value)
}

// 成功回调
const handleSuccess = (data: any) => {
  console.log('溯源码录入成功', data)
}
</script>

<template>
  <Card title="通过JS方法调用" class="mb-16px">
    <div mb-16px>
      <Title :level="4">通过JS方法调用</Title>
      <Paragraph>溯源码录入组件可以通过JS方法调用，不显示默认按钮。</Paragraph>

      <Space direction="vertical" style="width: 100%">
        <div>
          <span style="margin-right: 8px">溯源码ID:</span>
          <Input v-model:value="wbSeqid" style="width: 200px" placeholder="请输入溯源码ID" />
        </div>

        <div>
          <!-- 不显示按钮，通过js方法调用 -->
          <TrackCode ref="trackCodeRef" :showButton="false" @success="handleSuccess" />

          <!-- 自定义按钮 -->
          <Button type="primary" @click="openTrackCode">打开溯源码录入</Button>
          <span style="margin-left: 8px; color: #666; font-size: 13px"> 点击按钮将通过JS方法调用打开溯源码录入弹窗（不响应F5键） </span>
        </div>
      </Space>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="jsMethodUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
