<script setup lang="ts">
import { Maker } from '@mh-wm/maker'

import { Card, Typography, Divider, Button, message, Table, Form, Input, Select, DatePicker } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, reactive } from 'vue'
import { formUsage, importCode, packageJsonCode, publishCommands, buildProcess } from '@/views/Wm/examples/code/MakerCode'
import dayjs from 'dayjs'

const { Title, Paragraph, Text } = Typography

// 组件引用
const makerRef = ref()

// 移除了科室列表，组件内部不再需要外部传入仓库编码

// 表单数据
const formState = reactive({
  reqNo: 'REQ' + Date.now(),
  reqUser: '张三',
  reqDate: dayjs(),
  items: [],
  remark: ''
})

// 表格列定义
const columns = [
  {
    title: '品名',
    dataIndex: 'artName',
    key: 'artName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 120,
  },
  {
    title: '生产厂家',
    dataIndex: 'producer',
    key: 'producer',
    width: 120,
  },
  {
    title: '原产地',
    dataIndex: 'originPlace',
    key: 'originPlace',
    width: 100,
  },
  {
    title: '生产批号',
    dataIndex: 'batchNo',
    key: 'batchNo',
    width: 120,
  },
  {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    key: 'dateManufactured',
    width: 100,
  },
  {
    title: '有效期至',
    dataIndex: 'expiry',
    key: 'expiry',
    width: 100,
  },
  {
    title: '整包单价',
    dataIndex: 'packPrice',
    key: 'packPrice',
    width: 100,
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ record }: any) => {
      return `${record.totalPacks || 0} ${record.packUnit || ''}`
    }
  },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    key: 'totalCells',
    width: 100,
    customRender: ({ record }: any) => {
      if (record.packCells && record.packCells > 1) {
        return `${record.totalCells || 0} ${record.cellUnit || ''}`
      }
      return '-'
    }
  }
]

// 添加品种
const handleAddArt = (formData: any) => {
  console.log('添加品种成功，表单数据：', formData)

  const newItem = {
    id: Date.now().toString(),
    ...formData,
    // 从artData中提取品种信息
    artName: formData.artData?.artName || '',
    artSpec: formData.artData?.artSpec || '',
    producer: formData.artData?.producer || '',
  }

  formState.items.push(newItem)
  message.success('品种添加成功')
}

// 移除了科室变更处理函数

// 提交表单
const handleSubmit = () => {
  if (formState.items.length === 0) {
    message.warning('请至少添加一个品种')
    return
  }

  console.log('提交申请:', formState)
  message.success('申请提交成功')
}

// 重置表单
const handleReset = () => {
  formState.items = []
  formState.reqNo = 'REQ' + Date.now()
  formState.reqDate = dayjs()
  formState.remark = ''
  if (makerRef.value) {
    makerRef.value.clearFormData()
  }
  message.success('表单已重置')
}

// 删除品种
const deleteItem = (index: number) => {
  formState.items.splice(index, 1)
  message.success('删除成功')
}
</script>

<template>
  <Card title="在表单中使用 - 采购制单组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">申请单表单</Title>
      <Paragraph>
        演示如何在完整的表单中使用采购组件，包括表单验证、数据收集和提交等功能。
      </Paragraph>

      <Form :model="formState" layout="vertical">
        <div class="form-row">
          <div class="form-col">
            <Form.Item label="申请单号" name="reqNo">
              <Input v-model:value="formState.reqNo" />
            </Form.Item>
          </div>

          <div class="form-col">
            <Form.Item label="申请人" name="reqUser">
              <Input v-model:value="formState.reqUser" />
            </Form.Item>
          </div>
          <div class="form-col">
            <Form.Item label="申请日期" name="reqDate">
              <DatePicker v-model:value="formState.reqDate" style="width: 100%" />
            </Form.Item>
          </div>
        </div>

        <!-- 制单录入组件 -->
        <Form.Item label="品种录入">
          <Maker
            ref="makerRef"
            @addArt="handleAddArt"
          />
        </Form.Item>

        <!-- 已添加品种列表 -->
        <Form.Item label="申请品种列表">
          <div v-if="formState.items.length > 0">
            <Table
              :dataSource="formState.items"
              :columns="columns"
              rowKey="id"
              :pagination="false"
              :scroll="{ x: 1200 }"
            />
            <div style="margin-top: 8px; color: #666;">
              共 {{ formState.items.length }} 个品种
            </div>
          </div>
          <div v-else style="text-align: center; padding: 40px; color: #999; border: 1px dashed #d9d9d9;">
            暂无品种，请使用上方表单添加品种
          </div>
        </Form.Item>

        <Form.Item label="备注" name="remark">
          <Input.TextArea v-model:value="formState.remark" :rows="3" placeholder="请输入备注信息" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" @click="handleSubmit">提交申请</Button>
          <Button style="margin-left: 8px" @click="handleReset">重置</Button>
        </Form.Item>
      </Form>

      <div class="mt-8px tip-text">
        <i class="tip-icon">i</i>
        在表单中使用采购制单组件，可以实现完整的制单录入流程，包括表单验证、数据收集和提交等功能。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="formUsage"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>

  <!-- 打包发布指令 -->
  <Card title="打包与发布" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">组件打包指令</Title>
      <Paragraph>
        在开发完成后，需要打包组件以便发布和使用。以下是采购制单组件的打包和发布指令：
      </Paragraph>

      <div class="code-section">
        <Title :level="5">打包发布指令</Title>
        <pre class="code-block">{{ publishCommands }}</pre>
      </div>

      <div class="code-section">
        <Title :level="5">打包流程说明</Title>
        <pre class="code-block">{{ buildProcess }}</pre>
      </div>

      <div class="tips-section">
        <Title :level="5">使用说明</Title>
        <ul class="tips-list">
          <li><strong>正式版本：</strong>用于生产环境，版本号会自动递增</li>
          <li><strong>测试版本：</strong>用于测试环境，版本号带有beta标识</li>
          <li><strong>开发版本：</strong>用于开发环境，版本号带有alpha标识</li>
          <li><strong>安装组件：</strong>在其他项目中使用pnpm add命令安装组件</li>
        </ul>
      </div>

      <div class="warning-section">
        <Paragraph type="warning">
          <strong>注意：</strong>打包前请确保组件代码已经完成开发和测试，并且所有依赖项都已正确配置。
        </Paragraph>
      </div>
    </div>
  </Card>
</template>

<style scoped>
.mb-16px {
  margin-bottom: 16px;
}

.mt-8px {
  margin-top: 8px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-col {
  flex: 1;
}

.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  font-style: normal;
  margin-right: 8px;
  flex-shrink: 0;
}

/* 打包指令相关样式 */
.code-section {
  margin-bottom: 24px;
}

.code-block {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.45;
  color: #24292e;
  overflow-x: auto;
  white-space: pre;
  margin: 0;
}

.tips-section {
  margin-bottom: 24px;
}

.tips-list {
  margin: 8px 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.tips-list strong {
  color: #1890ff;
}

.warning-section {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 12px 16px;
  margin-top: 16px;
}
</style>
