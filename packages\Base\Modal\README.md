# @mh-base/modal

基础Modal弹窗组件包

## 概述

本包提供了基于Ant Design Vue Modal组件的封装，增加了拖拽、全屏显示等增强功能，统一了弹窗的样式和交互行为规范。

## 功能特性

- 🚀 **拖拽功能**：支持拖拽移动弹窗位置
- 📱 **全屏显示**：支持全屏/窗口模式切换
- 🎨 **统一样式**：标准化的弹窗外观和交互
- 🔧 **增强按钮**：自带图标的确定/取消按钮
- ⚡ **Vue 3**：基于Vue 3 + TypeScript + Composition API

## 组件列表

### BaseModal 基础弹窗组件

增强版的Modal组件，支持拖拽、全屏、自定义头部/底部等功能。

## 安装

### 组件包发布
```bash
# 发布组件
pnpm run publish:component Base/Modal
```

### 项目依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-base/modal": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```typescript
import { BaseModal } from '@mh-base/modal'
```

### 基础用法

```vue
<template>
  <div>
    <a-button type="primary" @click="showModal">打开弹窗</a-button>

    <BaseModal
      v-model:open="visible"
      title="基础弹窗"
      :width="600"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <p>这是弹窗内容</p>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BaseModal } from '@mh-base/modal'

const visible = ref(false)

const showModal = () => {
  visible.value = true
}

const handleOk = () => {
  console.log('确认操作')
  visible.value = false
}

const handleCancel = () => {
  console.log('取消操作')
  visible.value = false
}
</script>
```

### 可拖拽弹窗

```vue
<template>
  <BaseModal
    v-model:open="visible"
    title="可拖拽弹窗"
    :width="600"
    :hasHeader="true"
  >
    <p>拖拽标题栏可以移动弹窗位置</p>
  </BaseModal>
</template>
```

### 全屏模式

```vue
<template>
  <BaseModal
    v-model:open="visible"
    title="全屏弹窗"
    :isFull="true"
    :hasHeader="true"
  >
    <p>点击标题栏的全屏图标可以切换全屏/窗口模式</p>
  </BaseModal>
</template>
```

### 自定义底部

```vue
<template>
  <BaseModal
    v-model:open="visible"
    title="自定义底部"
    :hasFooter="true"
  >
    <p>弹窗内容</p>
    
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSave">保存</a-button>
        <a-button type="primary" @click="handleSubmit">提交</a-button>
      </a-space>
    </template>
  </BaseModal>
</template>
```

### 无头部弹窗

```vue
<template>
  <BaseModal
    v-model:open="visible"
    :hasHeader="false"
    :width="400"
  >
    <div style="text-align: center; padding: 20px;">
      <p>这是一个无头部的弹窗</p>
    </div>
  </BaseModal>
</template>
```

## API 文档

### BaseModal Props

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| open (v-model) | 弹窗显示状态 | boolean | false |
| title | 弹窗标题 | string | '系统提示' |
| width | 弹窗宽度 | string \| number | '520px' |
| hasHeader | 是否显示头部 | boolean | true |
| isFull | 是否支持全屏功能 | boolean | false |

### BaseModal Events

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| ok | 点击确定按钮时触发 | () => void |
| cancel | 点击取消按钮时触发 | () => void |

### BaseModal Slots

| 插槽名 | 说明 | 参数 |
| --- | --- | --- |
| default | 弹窗内容 | - |
| footer | 自定义底部内容 | - |

## 样式定制

### CSS类名

- `.base-modal`：基础弹窗容器
- `.full-base-modal`：全屏模式弹窗容器
- `.full-icon`：全屏图标样式

### 样式覆盖示例

```css
/* 自定义弹窗样式 */
.base-modal .ant-modal-content {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 全屏模式样式 */
.full-base-modal .ant-modal {
  max-width: 100vw;
  padding: 0;
}
```
