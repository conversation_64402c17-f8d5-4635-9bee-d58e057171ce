# KeyMap 快捷键管理组件

KeyMap组件用于管理用户在不同页面的快捷键配置，支持自定义快捷键并保存到localStorage中。

## 特性

- 支持为不同页面配置不同的快捷键
- 支持为不同用户保存不同的快捷键配置
- 支持通过双击Shift键在右侧显示抽屉式界面修改快捷键
- 支持控制是否允许用户修改快捷键配置
- 支持恢复默认快捷键配置
- 基于localStorage存储用户配置
- 配置更改后自动重新加载页面，确保配置立即生效

## 使用方法

### 引入组件

```javascript
// 从npm包导入
import { KeyMap } from '@mh-base/key-map'
import type { KeyMapItem } from '@mh-base/key-map'
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-base/key-map": "^1.0.0"
  }
}
```

### 基本用法

```vue
<template>
  <div>
    <Button @click="openKeyMapSettings">打开快捷键设置</Button>
    <p>提示：双击Shift键可以打开快捷键设置抽屉</p>
    <KeyMap
      ref="keyMapRef"
      pageKey="track-code-page"
      :functionKeys="functionKeys"
      :editable="true"
      @update="handleKeyMapUpdate"
      @reset="handleKeyMapReset"
    />
  </div>
</template>

<script setup>
import { KeyMap } from '@mh-base/key-map'
import type { KeyMapItem } from '@mh-base/key-map'
import { ref, onMounted } from 'vue'
import { Button } from 'ant-design-vue'

// 组件引用
const keyMapRef = ref()

// 预定义的功能键配置
const functionKeys = [
  { btnKey: "OPEN_TRACK_CODE", btnDesc: "打开溯源码窗口", bindKey: "F5" },
  { btnKey: "SAVE_TRACK_CODE", btnDesc: "保存溯源码数据", bindKey: "Ctrl + S" }
]

// 当前快捷键配置
const currentKeyMap = ref([])

// 打开快捷键设置抽屉
const openKeyMapSettings = () => {
  keyMapRef.value.openDrawer()
}

// 快捷键配置更新回调
const handleKeyMapUpdate = (keyMap) => {
  currentKeyMap.value = keyMap
  console.log('快捷键配置已更新:', keyMap)
}

// 快捷键配置重置回调
const handleKeyMapReset = () => {
  currentKeyMap.value = [...functionKeys]
  console.log('快捷键配置已重置为默认')
}

// 组件挂载时获取当前快捷键配置
onMounted(() => {
  currentKeyMap.value = keyMapRef.value.getKeyMap()
})
</script>
```

### 不可编辑模式

```vue
<template>
  <div>
    <p>提示：双击Shift键可以打开快捷键设置抽屉（只读模式）</p>
    <KeyMap
      ref="keyMapRef"
      pageKey="read-only-page"
      :functionKeys="functionKeys"
      :editable="false"
      @update="handleKeyMapUpdate"
      @reset="handleKeyMapReset"
    />
  </div>
</template>

<script setup>
import { KeyMap } from '@mh-base/key-map'
import type { KeyMapItem } from '@mh-base/key-map'
import { ref } from 'vue'

// 组件引用
const keyMapRef = ref()

// 预定义的功能键配置
const functionKeys = [
  { btnKey: "VIEW_RECORD", btnDesc: "查看记录", bindKey: "F3" },
  { btnKey: "PRINT_REPORT", btnDesc: "打印报表", bindKey: "Ctrl + P" }
]

// 快捷键配置更新回调（在只读模式下不会触发）
const handleKeyMapUpdate = (keyMap) => {
  console.log('快捷键配置已更新:', keyMap)
}

// 快捷键配置重置回调（在只读模式下不会触发）
const handleKeyMapReset = () => {
  console.log('快捷键配置已重置为默认')
}
</script>
```

### 监听快捷键

```vue
<script setup>
import { KeyMap } from '@mh-base/key-map'
import type { KeyMapItem } from '@mh-base/key-map'
import { ref, onMounted, onUnmounted } from 'vue'

// 组件引用
const keyMapRef = ref()

// 预定义的功能键配置
const functionKeys = [
  { btnKey: "OPEN_TRACK_CODE", btnDesc: "打开溯源码窗口", bindKey: "F5" },
  { btnKey: "SAVE_TRACK_CODE", btnDesc: "保存溯源码数据", bindKey: "Ctrl + S" }
]

// 当前快捷键配置
const currentKeyMap = ref([])

// 处理键盘事件
const handleKeyDown = (event) => {
  // 如果是在输入框中，不处理快捷键
  if (
    event.target instanceof HTMLInputElement ||
    event.target instanceof HTMLTextAreaElement ||
    event.target instanceof HTMLSelectElement ||
    (event.target as HTMLElement)?.isContentEditable
  ) {
    return
  }

  // 构建按键组合字符串
  let keyCombo = ''
  if (event.ctrlKey) keyCombo += 'Ctrl + '
  if (event.altKey) keyCombo += 'Alt + '
  if (event.shiftKey) keyCombo += 'Shift + '
  if (event.metaKey) keyCombo += 'Meta + '

  if (event.key === ' ') {
    keyCombo += 'Space'
  } else if (event.key.length === 1) {
    keyCombo += event.key.toUpperCase()
  } else {
    keyCombo += event.key
  }

  // 查找匹配的快捷键
  const matchedItem = currentKeyMap.value.find(item => item.bindKey === keyCombo)

  if (matchedItem) {
    // 阻止默认行为
    event.preventDefault()

    // 根据功能键执行相应操作
    switch (matchedItem.btnKey) {
      case 'OPEN_TRACK_CODE':
        openTrackCode()
        break
      case 'SAVE_TRACK_CODE':
        saveTrackCode()
        break
    }
  }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
  // 获取当前快捷键配置
  currentKeyMap.value = keyMapRef.value.getKeyMap()

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
})

// 打开溯源码窗口
const openTrackCode = () => {
  console.log('打开溯源码窗口')
}

// 保存溯源码数据
const saveTrackCode = () => {
  console.log('保存溯源码数据')
}
</script>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| pageKey | 页面标识，用于区分不同页面的快捷键配置 | string | - |
| functionKeys | 预定义的功能键配置 | KeyMapItem[] | [] |
| editable | 是否允许编辑快捷键配置 | boolean | true |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| update | 快捷键配置更新时触发 | (keyMap: KeyMapItem[]) => void |
| reset | 快捷键配置重置为默认时触发 | () => void |

## 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| openDrawer | 打开快捷键设置抽屉 | () => void |
| closeDrawer | 关闭快捷键设置抽屉 | () => void |
| getKeyMap | 获取当前快捷键配置 | () => KeyMapItem[] |
| resetToDefault | 重置为默认快捷键配置 | () => void |

## 类型定义

```typescript
interface KeyMapItem {
  btnKey: string;    // 功能键名（唯一标识）
  btnDesc: string;   // 功能描述
  bindKey: string;   // 绑定的快捷键
}
```
