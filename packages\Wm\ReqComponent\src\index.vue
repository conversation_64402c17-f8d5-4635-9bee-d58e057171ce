
<script setup lang="ts">
  import { ref, reactive,  nextTick, computed } from 'vue'
  import { message} from 'ant-design-vue'
  import { WmDeptStockSelect } from '@mh-inpatient-hsd/selector';


  // 定义组件事件
  const emit = defineEmits(['addArt']);

// 表单数据
const formState = reactive({
  deptCode: '',
  artIds: [] as number[],
  hasStock: false
});

// 缓存选中的品种数据
const selectedArtData = ref(null);

// 搜索表单数据
const searchFormModel = reactive({
  batchNo: '',
  expiry: '',
  stockPacksTotal: '',
  totalPacks: undefined,
  stockCellsTotal:undefined,
  totalCells: undefined,
  packUnit: '',
  cellUnit: '',
  splittable: undefined
});

// 表格容器引用
const tableContainerRef = ref();
// 品种选择组件引用
const artSelectRef = ref();
// 当前激活的页签
const activeTabKey = ref('1');
// 品种列表（按页码分组）
const artListByPage = reactive(new Map<number, any[]>());



// 添加品种
const onAddArt = () => {
  // 验证表单是否为空


  // 验证数量是否为空
  if (searchFormModel.totalPacks <= 0 &&
      (searchFormModel.splittable !== 1 || searchFormModel.totalCells <= 0)) {
    message.error('请输入有效的数量');
    return;
  }

  // 验证是否选择了品种
  if (!selectedArtData.value) {
    message.error('请先选择一个品种');
    return;
  }

  // 克隆当前表单数据
  const formData = {
    ...searchFormModel,
    // 添加选中的品种数据
    artData: selectedArtData.value
  };

  // 发射事件，返回表单数据
  emit('addArt', formData);

  // 清空表单
  clearFormData();

  // 聚焦回品种选择框，方便继续添加
  nextTick(() => {
    if (artSelectRef.value && typeof artSelectRef.value.focus === 'function') {
      artSelectRef.value.focus();
    }
  });
  console.log('添加品种成功，表单数据：', formData);
  // 返回表单数据，方便外部使用
  return formData;
};
// 处理品种选择
const handleArtSelect = (selectedArt: any) => {
  if (!selectedArt) return;
  // 将选中的品种数据缓存到新变量中
  selectedArtData.value = selectedArt;
  // 更新表单中的相关字段
  if (selectedArt.packUnit) {
    searchFormModel.packUnit = selectedArt.packUnit;
  }
  if (selectedArt.cellUnit) {
    searchFormModel.cellUnit = selectedArt.cellUnit;
  }
  if (selectedArt.splittable !== undefined) {
    searchFormModel.splittable = selectedArt.splittable;
  }

  // 对选中的input标签进行赋值
  if (selectedArt.batchNo !== undefined) {
    searchFormModel.batchNo = selectedArt.batchNo;
  }
  if (selectedArt.expiry !== undefined) {
    searchFormModel.expiry = selectedArt.expiry;
  }
  if (selectedArt.stockPacksTotal !== undefined) {
    searchFormModel.stockPacksTotal = selectedArt.stockPacksTotal;
  }
  if (selectedArt.stockCellsTotal !== undefined) {
    searchFormModel.stockCellsTotal = selectedArt.stockCellsTotal;
  }

  // 聚焦到整包数量输入框，方便用户直接输入数量
  nextTick(() => {
    if (totalPacksRef.value) {
      totalPacksRef.value.focus();
    }
  });
};

// 清空表单数据
const clearFormData = () => {
  // 重置表单数据
  searchFormModel.batchNo = '';
  searchFormModel.expiry = '';
  searchFormModel.stockPacksTotal = '';
  searchFormModel.stockCellsTotal = undefined;
  searchFormModel.totalPacks = undefined;
  searchFormModel.totalCells = undefined;
  searchFormModel.packUnit = '';
  searchFormModel.cellUnit = '';
  searchFormModel.splittable = undefined;

  // 清空品种选择组件
  if (artSelectRef.value) {
    // 使用init方法清空选择组件
    if (typeof artSelectRef.value.init === 'function') {
      artSelectRef.value.init();
    }
  }
  // 清空缓存的品种数据
  selectedArtData.value = null;
};
// 接收外部传入的仓库编码
const props = defineProps({
  deptCode: {
    type: String,
    default: ''
  }
});

// 使用示例中的仓库编码
const deptCode = computed(() => {
  // 优先使用外部传入的仓库编码，如果没有则使用表单中的
  return props.deptCode || formState.deptCode || '';
});
// 获取选中的品种数据
const getSelectedArtData = () => {
  return selectedArtData.value;
};
// 定义所有输入框的引用
const originPlaceRef = ref();
const batchNoRef = ref();
const expiryRef = ref();
const packPriceRef = ref();
const totalPacksRef = ref();
const totalCellsRef = ref();
// 聚焦到下一个输入框
const focusNext = (refName: string) => {
  nextTick(() => {
    if (refName === 'totalCellsRef' && searchFormModel.splittable !== 1) {
      // 如果下一个是拆零数量但不可拆零，则直接提交
      onAddArt();
      return;
    }
    // 根据引用名称获取对应的ref对象
    const refMap: Record<string, any> = {
      'originPlaceRef': originPlaceRef,
      'batchNoRef': batchNoRef,
      'expiryRef': expiryRef,
      'packPriceRef': packPriceRef,
      'totalPacksRef': totalPacksRef,
      'totalCellsRef': totalCellsRef
    };

    const nextRef = refMap[refName]?.value;
    if (nextRef) {
      // 处理不同类型的组件
      if (typeof nextRef.focus === 'function') {
        nextRef.focus();
      } else if (nextRef.$el && typeof nextRef.$el.focus === 'function') {
        nextRef.$el.focus();
      } else if (nextRef.input && typeof nextRef.input.focus === 'function') {
        nextRef.input.focus();
      }
    }
  });
};

// 暴露方法给外部使用
defineExpose({
  getSelectedArtData,
  clearFormData
});
</script>
<template>
     <a-form layout="inline" :model="searchFormModel">
        <a-form-item label="品种查询" name="keyword">
            <div class="art-select-box">
                <WmDeptStockSelect :deptCode="deptCode" ref="artSelectRef" @selected="handleArtSelect" :hasStock="formState.hasStock" style="width: 300px" @keydown.enter="focusNext('totalPacksRef')"/>
              </div>
        </a-form-item>
        <a-form-item label="生产批号" name="batchNo">
          <a-input v-model:value="searchFormModel.batchNo" style="width: 100px" :disabled="true" />
        </a-form-item>
        <a-form-item label="有效期至" name="expiry">
          <a-input v-model:value="searchFormModel.expiry" style="width: 100px" :disabled="true" />
        </a-form-item>
        <a-form-item label="整包库存数" name="stockPacksTotal">
          <a-input v-model:value="searchFormModel.stockPacksTotal" style="width: 100px" :disabled="true"
                   :addon-after="searchFormModel.packUnit" />
        </a-form-item>
        <a-form-item label="折零库存数" name="stockCellsTotal">
          <a-input v-model:value="searchFormModel.stockCellsTotal" style="width: 100px" :disabled="true"
                   :addon-after="searchFormModel.cellUnit" />
        </a-form-item>
        <a-form-item label="整包数量" name="totalPacks">
          <a-input-number v-model:value="searchFormModel.totalPacks" :min="0" :addon-after="searchFormModel.packUnit" ref="totalPacksRef" @keydown.enter="searchFormModel.splittable === 1 ? focusNext('totalCellsRef') : onAddArt()"/>
        </a-form-item>
        <a-form-item label="拆零数量" name="totalCells" v-if="searchFormModel.splittable === 1">
          <a-input-number v-model:value="searchFormModel.totalCells" :min="0" :addon-after="searchFormModel.cellUnit" ref="totalCellsRef" @keydown.enter="onAddArt"/>
        </a-form-item>
        <a-form-item>
          <a-button @click="onAddArt">添加</a-button>
        </a-form-item>

      </a-form>
</template>
