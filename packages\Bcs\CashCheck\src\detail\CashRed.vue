<script lang="ts" setup>
import { add, Api, Format, subtract } from '@idmy/core'
import { CashTypeEnum, pageCash } from '@mh-bcs/util'

const { cashTypeIds, checkId } = defineProps({
  cashTypeIds: { type: Array as PropType<CashTypeEnum[]>, required: true },
  checkId: { type: Number as PropType<number> },
})

const userStore = useUserStore()

const inputParams = computed(() => ({
  params: {
    S_IN_t_cash__cash_type_id: cashTypeIds?.join(','),
    S_EQ_t_cash__action_type: 2,
    S_EQ_t_cash__check_id: checkId,
    S_EQ_t_cash__user_id: checkId ? null : Currents.id,
    statuss: [1],
    check: <PERSON><PERSON>an(checkId),
    S_NE_t_cash__amount: 0,
  },
  pageSize: 20,
}))
</script>

<template>
  <Api v-slot="{ output }" :input="inputParams" :input-watch="{ deep: true }" :load="pageCash" first>
    <template v-if="output.list.length">
      <table class="print-table" style="margin-top: -1px">
        <tr>
          <th class="tac" colspan="8">退费明细</th>
        </tr>
        <tr>
          <th style="width: 80px">退费流水</th>
          <th>患者姓名</th>
          <th>收费类型</th>
          <th>支付方式</th>
          <th>退费总额</th>
          <th>自费</th>
          <th>医保</th>
          <th>险种</th>
        </tr>
        <tr v-for="row in output.list">
          <Format :value="row.cashId" component="td" type="String" />
          <Format :value="row.patientName" component="td" type="String" />
          <Format :value="row.cashTypeId" component="td" params="CashType" type="Enum" />
          <Format :value="row.paymentNames" component="td" type="String" />
          <Format :colour="false" :value="row.amount" component="td" type="Currency" />
          <Format :colour="false" :value="subtract(row.amount, row.miFundAmt ?? 0)" component="td" type="Currency" />
          <Format :colour="false" :value="row.miFundAmt" component="td" type="Currency" />
          <Format :value="row.insuranceTypeId" component="td" params="InsuranceType" type="Dict" />
        </tr>
        <tr>
          <td colspan="8">
            <div class="ml-8px mr-8px" flex justify-between>
              <strong>退费记录合计</strong>
              <Format :value="output.list.reduce((a, b) => add(a, b.amount), 0)" type="Currency" value-class="b" />
            </div>
          </td>
        </tr>
      </table>
    </template>
  </Api>
</template>
