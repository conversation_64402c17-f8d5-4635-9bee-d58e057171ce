<script lang="ts" setup>
import { cfg, Data } from '@idmy/core'
import CreateRedBill from './index.vue'

const { data } = defineProps({
  data: { type: Object as PropType<Data>, required: true },
})

const emit = defineEmits(['ok'])

const [onOpen] = useLoading(async () => {
  Modal.open({
    component: CreateRedBill,
    onClose: () => {
      emit('ok')
    },
    escClosable: true,
    maskClosable: true,
    props: { cashId: data.blueCashId ?? data.cashId, cashType: 'OUTPATIENT' },
    title: `红冲退费#${data.blueCashId ?? data.cashId}【${data.payer}】`,
    width: cfg.tenant.enablePartialRefund ? 4 : 5,
  })
})
</script>

<template>
  <template v-if="cfg.tenant.enableRefundCreate">
    <a v-if="data.amount !== data.refundedAmount && data.amount > 0" class="color-primary" @click="onOpen">开红冲单</a>
  </template>
</template>
