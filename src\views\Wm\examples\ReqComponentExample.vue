<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './ReqComponent/Basic.vue'
import FormExample from './ReqComponent/Form.vue'
import MethodsExample from './ReqComponent/Methods.vue'
import ValidationExample from './ReqComponent/Validation.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>
      品种申请表单组件，用于库房管理系统中快速录入品种申请信息。该组件提供了一个完整的表单界面，支持品种选择、信息填写和数据验证。支持回车键顺序导航表单字段，自动填充品种相关信息（包装单位、拆零单位等），支持日期格式验证（YYYYMMDD）。
    </Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="form" tab="在表单中使用">
        <FormExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="methods" tab="组件方法">
        <MethodsExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="validation" tab="表单验证">
        <ValidationExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
