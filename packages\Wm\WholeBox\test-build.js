#!/usr/bin/env node

/**
 * WholeBox组件打包测试脚本
 * 用于验证组件的打包服务是否正常工作
 */

import { execSync } from 'child_process'
import { existsSync, readdirSync, statSync, readFileSync } from 'fs'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🚀 开始测试 WholeBox 组件打包服务...\n')

// 测试1: 检查源文件是否存在
console.log('📁 检查源文件...')
const sourceFiles = [
  'src/index.vue',
  'src/index.ts', 
  'index.ts',
  'package.json',
  'README.md',
  'examples/Basic.vue'
]

sourceFiles.forEach(file => {
  if (existsSync(file)) {
    console.log(`✅ ${file} - 存在`)
  } else {
    console.log(`❌ ${file} - 不存在`)
  }
})

// 测试2: 执行构建
console.log('\n🔨 执行构建测试...')
try {
  console.log('执行命令: pnpm run publish:build')
  execSync('pnpm run publish:build', { 
    stdio: 'inherit',
    cwd: process.cwd()
  })
  console.log('✅ 构建成功')
} catch (error) {
  console.log('❌ 构建失败:', error.message)
  process.exit(1)
}

// 测试3: 检查构建产物
console.log('\n📦 检查构建产物...')
const distPath = resolve(__dirname, '../../../dist/Wm/WholeBox')

if (existsSync(distPath)) {
  console.log('✅ 构建目录存在:', distPath)
  
  const distFiles = readdirSync(distPath)
  console.log('📋 构建产物列表:')
  distFiles.forEach(file => {
    const filePath = resolve(distPath, file)
    const stats = statSync(filePath)
    if (stats.isDirectory()) {
      console.log(`  📁 ${file}/`)
      const subFiles = readdirSync(filePath)
      subFiles.forEach(subFile => {
        console.log(`    📄 ${subFile}`)
      })
    } else {
      console.log(`  📄 ${file}`)
    }
  })
  
  // 检查关键文件
  const requiredFiles = [
    'es/index.js',
    'index.d.ts',
    'index.css',
    'package.json'
  ]
  
  console.log('\n🔍 检查关键文件:')
  requiredFiles.forEach(file => {
    const filePath = resolve(distPath, file)
    if (existsSync(filePath)) {
      const stats = statSync(filePath)
      console.log(`✅ ${file} - 存在 (${stats.size} bytes)`)
    } else {
      console.log(`❌ ${file} - 不存在`)
    }
  })
  
} else {
  console.log('❌ 构建目录不存在:', distPath)
}

// 测试4: 验证package.json脚本
console.log('\n📋 验证package.json脚本...')
try {
  const packageJson = JSON.parse(readFileSync('./package.json', 'utf-8'))
  
  if (packageJson.scripts && packageJson.scripts['publish:build']) {
    console.log('✅ publish:build 脚本存在')
  } else {
    console.log('❌ publish:build 脚本不存在')
  }
  
  if (packageJson.scripts && packageJson.scripts['publish:component']) {
    console.log('✅ publish:component 脚本存在')
  } else {
    console.log('❌ publish:component 脚本不存在')
  }
  
  // 检查依赖
  console.log('\n📦 检查依赖配置:')
  console.log('peerDependencies:', Object.keys(packageJson.peerDependencies || {}).length, '个')
  console.log('dependencies:', Object.keys(packageJson.dependencies || {}).length, '个')
  
} catch (error) {
  console.log('❌ 读取package.json失败:', error.message)
}

console.log('\n🎉 WholeBox 组件打包服务测试完成!')
console.log('\n📖 使用说明:')
console.log('  构建组件: pnpm run publish:build')
console.log('  发布组件: pnpm run publish:component')
console.log('  或在项目根目录: pnpm publish:component Wm/WholeBox')
