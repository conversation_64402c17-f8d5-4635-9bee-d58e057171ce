<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue'
import * as antDesignVue from 'ant-design-vue'
import { computed } from 'vue'
const { Tag: ATag, Row: ARow, Col: ACol, Table: ATable, Flex: AFlex, Card: ACard, Divider: ADivider } = antDesignVue

const props = defineProps({
  recipe: {
    type: Object,
    default: () => {}
  },
  filePath: {
    type: String,
    default: ''
  }
})

const columns: TableColumnType[] = [
  {
    title: '序号',
    dataIndex: 'groupNo',
    width: 40,
    align: 'center',
    customCell: (record) => {
      if (record.recipeTypeId === 4) {
        return { rowSpan: 1 }
      } else if (record.groupMark === 1) {
        return { rowSpan: groupCount.value(record.groupNo) }
      } else if (record.groupMark >= 2) {
        return { rowSpan: 0 }
      } else {
        return { rowSpan: 1 }
      }
    }
  },
  {
    title: '药品名称',
    dataIndex: 'artName',
    width: 300
  },
  {
    title: '单次',
    dataIndex: 'mealCells',
    width: 80,
    align: 'right'
  },
  {
    title: '总量',
    dataIndex: 'total',
    width: 80,
    align: 'right'
  },
  {
    title: '用法',
    dataIndex: 'routeId',
    width: 150,
    align: 'left',
    customCell: (record) => {
      if (record.recipeTypeId === 4) {
        return { rowSpan: 1 }
      } else if (record.groupMark === 1) {
        return { rowSpan: groupCount.value(record.groupNo) }
      } else if (record.groupMark >= 2) {
        return { rowSpan: 0 }
      } else {
        return { rowSpan: 1 }
      }
    }
  }
]

const mealDoses = computed(() => {
  return (record: any) => {
    if (record.cellDoses && record.cellDoses > 1 && record.doseUnit != record.cellUnit) {
      const mealCells = record.mealCells
      const cellDoses = record.cellDoses
      return parseFloat(((Number(mealCells) * Number(cellDoses)) * 100 / 100).toFixed(4)) + '' + record.doseUnit
    } else {
      return null
    }
  }
})

const dataSource = computed(() => {
  const recipe = props.recipe
  const dataSource: any = []

  let amount = 0
  if (recipe.recipeGroupLs && recipe.recipeGroupLs.length > 0) {
    recipe.recipeGroupLs.forEach((item: any) => {
      item.recipeDetailLs.forEach((detail: any) => {
        const data: any = detail
        data.groupNo = item.groupNo
        data.routeId = item.routeId
        data.routeName = item.routeName
        data.freqCode = item.freqCode
        data.freqName = item.freqName
        data.notice = item.notice
        data.recipeTypeId = recipe.recipeTypeId
        data.dpm = item.dpm
        if (data.amount) {
          amount = Number(amount) + Number(data.amount)
        }
        if (recipe.recipeTypeId !== 4) {
          dataSource.push(data)
        } else if (recipe.recipeTypeId === 4 && data.stockReq === 1) {
          dataSource.push(data)
        }
      })
    })
  }

  if (recipe.recipeTypeId === 2) {
    recipe.notice = recipe.recipeGroupLs.length > 0 ? recipe.recipeGroupLs[0].notice : ''
    recipe.routeId = recipe.recipeGroupLs.length > 0 ? recipe.recipeGroupLs[0].routeId : ''
    recipe.routeName = recipe.recipeGroupLs.length > 0 ? recipe.recipeGroupLs[0].routeName : ''
  }
  if (!recipe.amount) {
    recipe.totalAmount = amount
  } else if (recipe.amount) {
    recipe.totalAmount = recipe.amount
  }
  // if (recipe.totalAmount) {
  //   // console.log(recipe.totalAmount, 'recipe.totalAmount', recipe.times)
  //   console.log((recipe.totalAmount / (recipe.times ? recipe.times : 1)) * 100 / 100, 'p')
  //   // recipe.amount = (recipe.totalAmount / (recipe.times ? recipe.times : 1)) * 100 / 100
  // }
  // console.log(recipe.amount, 'recipe.amount')
  return dataSource
})

const groupCount = computed(() => {
  return (groupNo: number) => {
    return dataSource.value.filter((item: any) => item.groupNo === groupNo).length
  }
})

// 工具方法
const dateFormatY = (dataStr: any): string => {
  if (!dataStr) {
    return ''
  }
  const date = new Date(dataStr)
  var year = date.getFullYear()
  var month = date.getMonth() + 1; // getMonth返回的是0-11，需要加1
  var day = date.getDate();
  var formattedDate = year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);
  return formattedDate
}

const amountFun = (recipe) => {
  if (recipe.totalAmount) {
    return priceFormat2((recipe.totalAmount / (recipe.times ? recipe.times : 1)) * 100 / 100)
  }
  return priceFormat2(recipe.amount)
}

const priceFormat2 = (value: number | undefined): string => {
  if (!value) {
    return ' '
  }
  if (Number.isNaN(value) || value === 0) {
    return '0.00'
  }
  // 将价格四舍五入到最接近的整数，并将价格转为字符串进行处理
  const roundedValue = Number(Math.round(value * 100) / 100).toString()

  // 获取整数部分
  const integerPart = roundedValue.split('.')[0]

  // 对整数部分进行千分位格式化
  const integerPartFormatted = integerPart.replace(/(\d)(?=(\d{3})+$)/g, '$1,')

  let floatPart = '.00' // 定义小数部分
  const decimalPart = roundedValue.split('.')[1]

  // 如果有小数部分，处理小数部分的长度
  if (decimalPart) {
    floatPart = '.' + decimalPart.padEnd(2, '0')
  }

  // 返回格式化后的价格字符串
  return `${integerPartFormatted}${floatPart}`
}

const ageFormatYearDays = (ageOfYears: number | null, ageOfDays: number | null): string => {
  let year = ageOfYears ? ageOfYears : 0
  let month = ageOfDays ? Math.floor(ageOfDays / 30) : 0
  let day = ageOfDays ? ageOfDays : 0
  if (year > 6) {
    return year + '岁'
  } else if (year > 0) {
    return year + '岁' + month + '月'
  } else if (month > 0 && day > 0) {
    return month + '月' + day + '天'
  } else if (month > 0) {
    return month + '月'
  } else if (day > 0) {
    return day + '天'
  } else {
    return ''
  }
}

const setSignatureUrl = (url) => {
  if (!url) return url
  const row: any = ['http://', 'https://'].some(item => url.indexOf(item) > -1)
  if (row) {
    return url
  } else {
    return `${props.filePath}${url}`
  }
}
</script>
<template>
  <a-card v-if="recipe && recipe.recipeId" class="bgfff m-b-5px" :body-style="{ padding: 0 }">
    <a-row :gutter="8" :style="{ backgroundColor: recipe.catColorCode || '#FFFFFF', 'padding': '20px' }">
      <a-col class="text-center text-size-18px font-bold" :span="22">
        <span v-if="recipe.recipeTypeId === 1">西(成)药处方笺</span>
        <span v-else-if="recipe.recipeTypeId === 2">中药处方笺</span>
        <span v-else-if="recipe.recipeTypeId === 4">治疗处方笺</span>
      </a-col>
      <a-col class="text-center" :span="2">
        <span class="custom-typename">{{ recipe.catMark || '普通' }}</span>
      </a-col>
      <a-col class="text-right" :span="24">处方号：{{ recipe.rxNo }}</a-col>
      <a-col :span="4">
        患者：{{ recipe.patientName }}
      </a-col>
      <a-col :span="4">
        性别：{{ recipe.patientGenderName }}
      </a-col>
      <a-col :span="4">
        年龄：{{ ageFormatYearDays(recipe.ageOfYears, recipe.ageOfDays) }}
      </a-col>
      <a-col :span="6">
        科别：{{ recipe.applyDeptname }}
      </a-col>
      <a-col class="text-right" :span="6">
        处方医生：{{ recipe.clinicianName }}
      </a-col>
      <a-col v-if="recipe.recipeTypeId === 1" :span="18">
        <span>诊断：{{ recipe.diseaseDiagName }}</span>
        <span v-if="recipe.diagStatus == 0" style="padding-left: 5px;">?</span>
      </a-col>
      <a-col v-if="recipe.recipeTypeId === 2" :span="8">
        主病：{{ recipe.diseaseDiagName }}
      </a-col>
      <a-col v-if="recipe.recipeTypeId === 2" :span="10">
        主症：{{ recipe.symptomDiagName }}
      </a-col>
      <a-col class="text-right" :span="6">
        日期：{{ dateFormatY(recipe.timeCreated) }}
      </a-col>
      <a-col :span="24">
        <a-flex gap="large">
          <div>既往史：{{ recipe.pastHistory ? recipe.pastHistory : '无' }}</div>
          <div>过敏史：{{ recipe.allergicHistory ? recipe.allergicHistory : '无' }}</div>
        </a-flex>
      </a-col>
      <a-col :span="24"><a-divider style="margin: 10px 0;" /></a-col>
      <a-col class="m-b-10px" :span="24">
        <b>Rp:</b>
      </a-col>
      <a-col :span="24" v-if="[1, 4].includes(recipe.recipeTypeId)">
        <a-table :row-key="(record: any) => record.artId" ref="table" :columns="columns"
                 :data-source="dataSource" :showHeader="false" :bordered="false" :pagination="false">
          <template #bodyCell="{ index, column, record }">
            <template v-if="column.dataIndex === 'serial'">
              <div>{{ index + 1 }}。</div>
            </template>
            <template v-else-if="column.dataIndex === 'artName'">
              <a-flex gap="small">
                <div>{{ record.artName || '' }}</div>
                <div>{{ record.artSpec || '' }}</div>
                <div v-if="record.stResultName">
                  <a-tag v-if="record.stResult === 1" color="green">{{ record.stResultName }}</a-tag>
                  <a-tag v-else-if="record.stResult === 2" color="red">{{ record.stResultName }}</a-tag>
                </div>
              </a-flex>
              <a-flex gap="small" class="custom-art-content">
                <div style="width: 83%;">{{ record.producer }}</div>
              </a-flex>
            </template>
            <template v-else-if="column.dataIndex === 'mealCells'">
              <div v-if="recipe.recipeTypeId === 1">{{ record.mealCells }}{{ record.cellUnit }}/次</div>
            </template>
            <template v-else-if="column.dataIndex === 'total'">
              <template v-if="recipe.recipeTypeId === 1">
                <span v-if="record.groupMark === 1" class="b-r-line-s"></span>
                <span v-else-if="record.groupMark === 2" class="b-r-line-c"></span>
                <span v-else-if="record.groupMark === 3" class="b-r-line-e"></span>
              </template>
              <span :class="{ 'b-r-line-txt': [1, 2, 3].includes(record.groupMark) }">共{{ record.total }}{{ record.unit
                }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'routeId'">
              <span v-if="recipe.recipeTypeId !== 4">Sig：{{ record.routeName }} {{ record.freqCode }} {{ record.periodCycles ? ('x' + record.periodCycles + '天') : '' }} {{ record.notice }}</span>
            </template>
          </template>
        </a-table>
      </a-col>
      <a-col v-else-if="recipe.recipeTypeId === 2" :span="24">
        <a-row>
          <a-col :span="8" v-for="item in dataSource" :key="item.artId">
            <a-flex gap="small" class="custom-art-content">
              <div style="width: 66%">
                <a-flex gap="small">
                  <span>{{ item.artName }}</span>
                  <span v-if="mealDoses(item)">{{ mealDoses(item) }}</span>
                  <span v-if="item.unitType === 1">{{ item.mealCells }}{{ item.cellUnit }}</span>
                  <span v-else-if="item.unitType === 2">{{ item.total }}{{ item.unit }}</span>
                  <span v-else-if="item.unitType === 3">{{ item.mealDoses }}{{ item.doseUnit }}</span>
                  <span v-if="item.processMethod">({{ item.processMethod }})</span>
                </a-flex>
              </div>
            </a-flex>
          </a-col>
        </a-row>
      </a-col>
      <template v-if="dataSource.length > 0">
        <a-col class="m-t-10px" :span="6">
          <div>共 {{ dataSource.length }} 种药品</div>
        </a-col>
        <a-col class="m-t-10px" :span="6" v-if="recipe.deliverType === 1 && recipe.amount && recipe.bseqid">
          <div font-bold>合价：{{ amountFun(recipe) }}元</div>
        </a-col>
        <a-col class="m-t-10px" :span="6" v-if="recipe.recipeTypeId === 2">
          剂数：{{ recipe.times }}
        </a-col>
        <a-col class="m-t-10px" :span="6" v-if="recipe.recipeTypeId === 2 && recipe.deliverType === 1 && recipe.totalAmount && recipe.bseqid">
          <div font-bold>总价：{{ priceFormat2(recipe.totalAmount) }}元</div>
        </a-col>
        <a-col :span="6" v-if="recipe.recipeTypeId === 2">
          <div>{{ recipe.routeId ? recipe.routeName + ' ' : '' }}</div>
        </a-col>
        <a-col :span="18" v-if="recipe.recipeTypeId === 2">
          <div>{{ recipe.notice }}</div>
        </a-col>

      </template>

      <a-col class="text-right" :span="24" v-if="recipe.timeSigned && recipe.signatureUrl">
        <div class="flex items-center" style="justify-content: end;">
          <span>医师：</span>
          <img :src="setSignatureUrl(recipe.signatureUrl)" :style="{ margin: '0 10px 0 0', width: '100px', height: '20px' }" alt=""/>
        </div>
      </a-col>
      <a-col class="text-right" :span="24" v-else-if="recipe.timeSigned">
        <div>医师：{{ recipe.clinicianName }}</div>
      </a-col>
    </a-row>
  </a-card>
</template>


<style scoped lang="less">
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.m-t-10px {
  margin-top: 10px;
}
.m-b-10px {
  margin-bottom: 10px;
}
.bgfff {
  background-color: #fff;
}
.m-b-5px {
  margin-bottom: 5px;
}
.text-size-18px {
  font-size: 18px;
}
.font-bold {
  font-weight: bold;
}
:deep(.ant-table-cell) {
  padding: 5px !important;
}

:deep(.ant-table) {
  background-color: unset;
}

.custom-typename {
  position: absolute;
  text-align: center;
  font-size: 12px;
  color: #000000;
  padding: 2px 5px;
  border-radius: 2px;
  right: 5px;
  top: 5px;
  border: 1px solid #000
}
</style>
