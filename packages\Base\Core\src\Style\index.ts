import { generate } from '@arco-design/color'
import { Data } from '@idmy/core'
import { theme as antdTheme } from 'ant-design-vue'
import './var.less'


export const themeConfig = reactive<Data>({
  algorithm: antdTheme.defaultAlgorithm,
  token: {
    colorBgContainer: 'white',
    borderRadius: 3,
  },
  components: {
    Table: {
      colorBorderSecondary: '#ddd',
    }
  },
})

const setVar = (key, val) => {
  const root = document.documentElement.style
  root.setProperty(key, val)
  if (key === '--primary-color') {
    themeConfig.token.colorPrimary = val
    // localStorage.setItem('app-primary-color', val)
  }
}

const baseColor = {
  primary: localStorage.getItem('app-primary-color') ?? '#009B9B',
  success: '#52c41a',
  warning: '#faad14',
  error: '#ff4d4f',
  info: '#1890ff',
}

setVar('--success-color', baseColor.success)
setVar('--warning-color', baseColor.warning)
setVar('--error-color', baseColor.error)
setVar('--info-color', baseColor.info)

export const changePrimaryColor = color => {
  setVar('--primary-color', color)
  setVar('--surely-table-primary-color', color)
  setVar('--surely-table-cell-focus-border-color', color)
  for (let index = 8; index >= 2; index--) {
    const idx = index - 1
    setVar(`--primary-color-${idx}`, generate(color, { index: idx }))
  }
  for (let j = 10; j >= 1; j--) {
    setVar(`--surely-table-primary-color-${j}`, generate(color, { index: j }))
  }
}

changePrimaryColor(baseColor.primary)

for (let index = 8; index >= 2; index--) {
  const idx = index - 1
  setVar(`--success-color-${idx}`, generate(baseColor.success, { index: idx }))
  setVar(`--warning-color-${idx}`, generate(baseColor.warning, { index: idx }))
  setVar(`--error-color-${idx}`, generate(baseColor.error, { index: idx }))
  setVar(`--info-color-${idx}`, generate(baseColor.info, { index: idx }))
}
