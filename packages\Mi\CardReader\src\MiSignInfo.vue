<script setup lang="ts">
import * as antDesignVue from 'ant-design-vue'
const { Table, Spin, Divider, Empty } = antDesignVue
import { ref, onMounted, watch, computed } from 'vue'
import type { PropType } from 'vue'
import { getPersonSign, BUSINESS_TYPE } from '@mh-mi/util'
import BusinessTypeSelect from './BusinessTypeSelect.vue'

defineOptions({
  name: 'MiSignInfo'
})

const props = defineProps({
  // 人员编号
  psnNo: {
    type: String,
    default: ''
  },
  // 患者ID
  patientId: {
    type: Number,
    default: undefined
  },
  // 就诊ID
  visitId: {
    type: Number,
    default: undefined
  },
  // 证件类型
  psnCertType: {
    type: String,
    default: '',
  },
  // 证件号码
  psnCertNo: {
    type: String,
    default: '',
  }
})

// 签约信息列表
const signInfoList = ref<any[]>([])
// 加载状态
const loading = ref(false)
// 是否有数据
const hasData = ref(false)
// 选中的业务类型
const selectedBusinessType = ref<string>('')

// 表格列定义
const columns = [
  {
    title: '定点医药机构名称',
    dataIndex: 'fixmedins_name',
    key: 'fixmedins_name',
    width: 200,
  },
  {
    title: '定点医药机构编号',
    dataIndex: 'fixmedins_code',
    key: 'fixmedins_code',
    width: 180,
  },
  {
    title: '险种类型',
    dataIndex: 'insutype',
    key: 'insutype',
    width: 120,
  },
  {
    title: '开始日期',
    dataIndex: 'begndate',
    key: 'begndate',
    width: 120,
  },
  {
    title: '结束日期',
    dataIndex: 'enddate',
    key: 'enddate',
    width: 120,
  },
  {
    title: '定点排序号',
    dataIndex: 'fix_srt_no',
    key: 'fix_srt_no',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'memo',
    key: 'memo',
    width: 150,
  }
]

// 将业务类型转换为选项格式
const businessTypeOptions = computed(() => {
  return Object.entries(BUSINESS_TYPE).map(([value, label]) => ({
    value,
    label
  }))
})

// 获取第一个业务类型
const getFirstBusinessType = () => {
  const options = businessTypeOptions.value
  if (options && options.length > 0) {
    return options[0].value
  }
  return ''
}

// 业务类型变更处理
const handleBusinessTypeChange = (value: string | number) => {
  console.log('选择业务类型:', value)
  selectedBusinessType.value = value as string
  if (props.psnNo) {
    getSignInfo()
  }
}

// 获取签约信息
const getSignInfo = async () => {
  if (!props.psnNo) return
  if (!selectedBusinessType.value) {
    signInfoList.value = []
    hasData.value = false
    return
  }
  
  try {
    loading.value = true
    // 将业务类型作为参数传递给接口
    const res = await getPersonSign({
      psn_no: props.psnNo,
      business_type_id: selectedBusinessType.value,
      psn_cert_type: props.psnCertType,
      psn_cert_no: props.psnCertNo
    })
    if (res?.psnfixmedin && Array.isArray(res.psnfixmedin)) {
      signInfoList.value = res.psnfixmedin
      hasData.value = res.psnfixmedin.length > 0
    } else {
      signInfoList.value = []
      hasData.value = false
    }
  } catch (error) {
    console.error('获取签约信息失败:', error)
    signInfoList.value = []
    hasData.value = false
  } finally {
    loading.value = false
  }
}

// 监听 psnNo 变化
watch(
  () => props.psnNo,
  (val) => {
    if (val) {
      // 如果没有选中业务类型，则自动选择第一个
      if (!selectedBusinessType.value) {
        selectedBusinessType.value = getFirstBusinessType()
      }
      getSignInfo()
    } else {
      signInfoList.value = []
      hasData.value = false
    }
  }
)

// 组件挂载时获取签约信息
onMounted(() => {
  // 自动选择第一个业务类型
  selectedBusinessType.value = getFirstBusinessType()
  
  if (props.psnNo && selectedBusinessType.value) {
    getSignInfo()
  }
})

// 暴露方法给父组件
defineExpose({
  getSignInfo
})
</script>

<template>
  <div class="mi-sign-info">
    <!-- 业务类型选择 -->
    <div class="business-type-select-container">
      <business-type-select
        v-model="selectedBusinessType"
        placeholder="请选择业务类型"
        @change="handleBusinessTypeChange"
      />
    </div>
    
    <Divider style="margin: 12px 0" />
    
    <!-- 加载中 -->
    <div v-if="loading" class="loading-container">
      <Spin />
    </div>
    
    <!-- 选择提示 -->
    <div v-else-if="!selectedBusinessType" class="mi-info-empty">
      请选择业务类型查看签约信息
    </div>
    
    <!-- 表格显示数据 -->
    <div v-else-if="hasData" class="sign-info-table">
      <Table 
        :dataSource="signInfoList" 
        :columns="columns" 
        :pagination="false" 
        size="small" 
        :scroll="{ x: 1000, y: 240 }" 
      />
    </div>
    
    <!-- 无数据 -->
    <div v-else class="mi-info-empty">
      暂无签约信息
    </div>
  </div>
</template>

<style lang="less" scoped>
.mi-sign-info {
  min-height: 100px;
  position: relative;
}

.business-type-select-container {
  margin-bottom: 12px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

.mi-info-empty {
  text-align: center;
  color: #999;
  padding: 30px 0;
}

.sign-info-table {
  margin: 10px 0;
  
  :deep(.ant-table-body) {
    overflow-x: auto !important;
  }
}
</style> 