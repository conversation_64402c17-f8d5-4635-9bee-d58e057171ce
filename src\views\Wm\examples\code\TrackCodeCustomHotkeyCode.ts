import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { TrackCode } from '@mh-wm/track-code'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/track-code": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}`

// 自定义热键示例
export const customHotkeyUsage = wrapCodeExample(`<template>
  <!-- 自定义热键示例 -->
  <TrackCode
    :wbSeqid="wbSeqid"
    :hotkey="customHotkey"
    @success="handleSuccess"
  />
  
  <!-- 热键录入组件 -->
  <div style="margin-top: 16px;">
    <Input
      v-model:value="customHotkey"
      placeholder="请点击录入按键"
      readonly
      style="width: 200px; margin-right: 8px;"
    />
    <Button @click="startRecording" :disabled="isRecording">
      {{ isRecording ? '正在录入...' : '点击录入按键' }}
    </Button>
  </div>
</template>

<script setup>
import { TrackCode } from '@mh-wm/track-code'
import { Input, Button } from 'ant-design-vue'
import { ref } from 'vue'

// 溯源码ID
const wbSeqid = ref('12345')

// 自定义热键
const customHotkey = ref('F5')

// 是否处于录入状态
const isRecording = ref(false)

// 开始录入按键
const startRecording = () => {
  isRecording.value = true
  window.addEventListener('keydown', recordKey)
}

// 录入按键
const recordKey = (event) => {
  event.preventDefault()
  
  // 获取按键名称
  let keyName = event.key
  
  // 对于功能键，使用特殊处理
  if (event.key === 'F1' || event.key === 'F2' || event.key === 'F3' || 
      event.key === 'F4' || event.key === 'F5' || event.key === 'F6' || 
      event.key === 'F7' || event.key === 'F8' || event.key === 'F9' || 
      event.key === 'F10' || event.key === 'F11' || event.key === 'F12') {
    keyName = event.key
  } else if (event.key === 'Escape') {
    keyName = 'Esc'
  } else if (event.key === ' ') {
    keyName = 'Space'
  } else if (event.key.length === 1) {
    // 对于字母和数字，转为大写
    keyName = event.key.toUpperCase()
  }
  
  // 设置自定义热键
  customHotkey.value = keyName
  
  // 停止录入
  isRecording.value = false
  window.removeEventListener('keydown', recordKey)
}

// 成功回调
const handleSuccess = (data) => {
  console.log('溯源码录入成功', data)
}
</script>`)
