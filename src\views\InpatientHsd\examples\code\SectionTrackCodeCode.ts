import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import SectionTrackCode from '@mh-inpatient-hsd/section-track-code'
import '@mh-inpatient-hsd/section-track-code/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/section-track-code": "^1.0.0",
    "@mh-inpatient-hsd/util": "^1.0.0",
    "@mh-hip/util": "^1.0.0"
  }
}`

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleOpenSectionTrackCode">打开病区绑定追溯码</Button>
  <SectionTrackCode 
    ref="sectionTrackCodeRef" 
    :sectionId="sectionId" 
    :modalWidth="1500"
    :showButton="false"
    @success="handleSuccess"
    @cancel="handleCancel"
  />
</template>

<script setup>
import SectionTrackCode from '@mh-inpatient-hsd/section-track-code'
import '@mh-inpatient-hsd/section-track-code/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const sectionTrackCodeRef = ref()

// 模拟数据
const sectionId = 38

// 打开病区绑定追溯码弹窗
const handleOpenSectionTrackCode = () => {
  sectionTrackCodeRef.value.open()
}

// 成功回调
const handleSuccess = (data) => {
  message.success('追溯码绑定成功')
  console.log('绑定结果:', data)
}

// 取消回调
const handleCancel = () => {
  message.info('取消操作')
}
</script>`)
