import { ref, shallowRef } from 'vue'
import { message } from 'ant-design-vue'
import type { FormState, Article } from '../types'
import { wmOrgArtPageApi, wmDeptArtPageApi, articlePageApi, RecipeTypeEnum, ArtTypeEnum } from '@mh-hsd/util'

export interface ArticleHookProps {
  formState: FormState
  storeId?: string,
  recipeTypeId?: number,
  onArtConfirm: (art: Article) => void
}

export function useArticle({ formState, storeId, recipeTypeId, onArtConfirm }: ArticleHookProps) {
  const fetching = shallowRef<boolean>(false)
  const finished = shallowRef<boolean>(false)
  const keyword = ref<string>('')
  const searchType = ref<number>(1)
  const pageNum = shallowRef<number>(1)
  const pages = ref<number>(0)
  const articleLs = ref<Article[]>([])
  const artOpen = ref<boolean>(false)
  const artRef = ref()

  function resetArt() {
    articleLs.value = []
  }

  function handleArtFocus() {
    keyword.value = ''
    handleSearchArt('')
  }

  function handleSearchArt(value: string) {
    if (value) {
      keyword.value = value
    } else {
      keyword.value = ''
    }
    pageNum.value = 1
    finished.value = false
    if (keyword.value) {
      artOpen.value = true
      fetchArts()
    }
  }

  function searchTypeChange() {
    if (keyword.value) {
      pageNum.value = 1
      finished.value = false
      artOpen.value = true
      fetchArts()
    }
  }

  function handlePreArt() {
    finished.value = false
    pageNum.value = pageNum.value - 1
    fetchArts()
  }

  function handleNextArt() {
    finished.value = false
    pageNum.value = pageNum.value + 1
    fetchArts()
  }

  async function fetchArts() {
    const params: any = {
      pageNum: pageNum.value,
      pageSize: 20
    }

    if (searchType.value === 1) {
      params.artName = keyword.value
    } else if (searchType.value === 2) {
      params.S_LIKE_t_article__QS_Code1 = keyword.value ? keyword.value.toUpperCase() : ''
    } else if (searchType.value === 3) {
      params.S_LIKE_t_article__QS_Code2 = keyword.value ? keyword.value.toUpperCase() : ''
    }
    if (recipeTypeId === RecipeTypeEnum.WEST_DRUG) {
      params.S_IN_t_article__Art_Type_ID = '' + ArtTypeEnum.West_Drug + ',' + ArtTypeEnum.Chinese_Patent_Drug + ''
    } else if (recipeTypeId === RecipeTypeEnum.CHINESE_DRUG) {
      params.S_EQ_t_article__Art_Type_ID = ArtTypeEnum.Chinese_Drug
    }
    fetching.value = true
    try {
      let data: any
      if (formState.deliverType === 1) {
        if (storeId) {
          params.deptCode = storeId
          params.applyDeptCode = formState.applyDeptcode
          params.sidx = 't_org_art.Sale_Disabled ASC, t_dept_art.Total_Packs DESC, t_dept_art.Art_ID'
          params.order = 'ASC'
          data = await wmDeptArtPageApi(params)
        } else {
          params.deptCode = formState.applyDeptcode
          params.sidx = 't_org_art.Sale_Disabled ASC, t_org_art.Total_Packs DESC, t_org_art.Art_ID'
          params.order = 'ASC'
          data = await wmOrgArtPageApi(params)
        }
      } else {
        params.S_EQ_t_article__Cat_Type_ID = formState.catTypeId
        params.deptCode = formState.applyDeptcode
        data = await articlePageApi(params)
      }
      pageNum.value = Number(data.pageNum)
      pages.value = Number(data.pages)
      if (data.pages !== 0 && data.pageNum === data.pages) {
        finished.value = true
      }
      if (data.list && data.list.length > 0) {
        data.list.forEach((item: any) => {
          if (item.reservedCells && item.reservedCells > 0) {
            let reservedCells: number = Number(item.reservedCells)
            if (item.totalCells && item.totalCells >= reservedCells) {
              item.totalCells = Number(item.totalCells) - Number(reservedCells)
              reservedCells = 0
            }
            if (reservedCells > 0) {
              let reservedPacks = parseFloat((reservedCells / (item.packCells || 1)).toFixed(4))
              if (item.totalPacks && item.totalPacks >= reservedPacks) {
                item.totalPacks = item.totalPacks - reservedPacks
                reservedPacks = 0
              } else {
                reservedPacks = Number(reservedPacks) - Number(item.totalPacks)
                item.totalPacks = 0
              }
              if (reservedPacks > 0) {
                reservedCells = Number(reservedPacks) * Number(item.packCells || 1)
              } else {
                reservedCells = 0
              }
            }
            if (reservedCells > 0 && item.totalCells && item.totalCells !== reservedCells) {
              item.totalCells = Number(item.totalCells) - Number(reservedCells)
            }
            if (item.totalPacks && item.totalPacks < 1) {
              item.totalCells = Number(item.totalCells) + (Number(item.totalPacks) * Number(item.packCells || 1))
              item.totalPacks = 0
            }
          }
        })
      }
      articleLs.value = data.list
      fetching.value = false
    } catch (err) {
      fetching.value = false
      console.error(err)
    }
  }

  function handleArtSelect(v: any) {
    const art = articleLs.value.find((item) => item.artId === v)
    if (!art) return

    if (art.saleDisabled === 1) {
      message.warning('该项目已被禁售，不能添加')
      formState.displayName = ''
      onArtConfirm({} as Article)
      return
    }

    if (storeId && !art.totalPacks && !art.totalCells) {
      message.warning('该项目库存不足，不能添加')
      formState.displayName = ''
      onArtConfirm({} as Article)
      return
    }

    onArtConfirm(art)
    formState.displayName = ''
    keyword.value = ''
    artOpen.value = false
    artRef.value?.blur()
  }

  const setInputFocus = () => {
    artRef.value?.focus()
  }

  return {
    fetching,
    finished,
    keyword,
    searchType,
    pageNum,
    pages,
    articleLs,
    artOpen,
    artRef,
    resetArt,
    handleArtFocus,
    handleSearchArt,
    searchTypeChange,
    handlePreArt,
    handleNextArt,
    handleArtSelect,
    setInputFocus
  }
}
