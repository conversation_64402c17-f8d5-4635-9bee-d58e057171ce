# 目录介绍
- OeApply: 医嘱核对
-

# 自动配置 tsconfig.json paths
运行 `scripts/generate-tsconfig-paths.js` tsconfig.json paths 会自动添加对应的配置

# 发布该目录下全部组件
pnpm publish:component InpatientHsd

# 组件说明
### 工具类
pnpm publish:component InpatientHsd/Util

### 医嘱核对
# QuestionForm          质疑弹窗
# "@mh-inpatient-hsd/oe-apply": "^1.0.2",
import { QuestionForm } from "@mh-inpatient-hsd/oe-apply"
pnpm publish:component InpatientHsd/OeApply        # 医嘱核对

### 医嘱执行
# ExecForm             医嘱执行
# PackDeliverForm      申请用药
# "@mh-inpatient-hsd/oe-exec": "^1.0.5",
import { ExecForm, PackDeliverForm } from '@mh-inpatient-hsd/oe-exec'
import '@mh-inpatient-hsd/oe-exec/index.css'
pnpm publish:component InpatientHsd/OeExec

### 计费医嘱
# BillingEntryModal     计费医嘱弹窗
# BillingEntry          计费医嘱列表页
# "@mh-inpatient-hsd/billing-entry": "^1.0.1",
import { BillingEntryModal, BillingEntry, BillingEntryItem } from '@mh-inpatient-hsd/billing-entry'
import '@mh-inpatient-hsd/billing-entry/index.css'
pnpm publish:component InpatientHsd/BillingEntry

### 医嘱费用调整
# AddArt                新增费用
# ChangeTotal           复制补录
# RefundOe              费用冲红
# "@mh-inpatient-hsd/oe-fee-change": "^1.0.1",
import { AddArt, ChangeTotal, RefundOe } from '@mh-inpatient-hsd/oe-fee-change'
import '@mh-inpatient-hsd/oe-fee-change/index.css'
pnpm publish:component InpatientHsd/OeFeeChange

### 医技费用调整
# AddArt                新增费用
# RefundOrderExecFee    费用冲红
# "@mh-inpatient-hsd/order-fee-change": "^1.0.3",
import { AddArt, RefundOrderExecFee } from '@mh-inpatient-hsd/order-fee-change'
import '@mh-inpatient-hsd/order-fee-change/index.css'
pnpm publish:component InpatientHsd/OrderFeeChange

### 诊疗信息展示
# VisitInfo             诊疗信息展示，原PgUserInfo
# "@mh-inpatient-hsd/visit-info": "^1.0.6",
import { index as PgUserInfo } from '@mh-inpatient-hsd/visit-info'
import '@mh-inpatient-hsd/visit-info/index.css'
pnpm publish:component InpatientHsd/VisitInfo      # 诊疗信息展示（原PgUserInfo）

### 诊疗信息调整
# "@mh-inpatient-hsd/visit-form": "^1.0.1",
import { index as VisitForm } from '@mh-inpatient-hsd/visit-info'
pnpm publish:component InpatientHsd/VisitForm      # 诊疗信息调整@1.0.0

### 选择器
# ArtSelect             机构处方集/orgItemPrice
# SectionArtSelect      病区条目
# SelectWithUnitTotal   条目选择+单位+数量
# WmDeptArtSelect       药房科室条目
# WmOrgArtSelect        药房条目
# "@mh-inpatient-hsd/selector": "^1.0.7",
import { ArtSelect, SectionArtSelect, SelectWithUnitTotal, WmDeptArtSelect, WmOrgArtSelect } from '@mh-inpatient-hsd/selector'
pnpm publish:component InpatientHsd/Selector
pnpm publish:component InpatientHsd/Util

### 病区给药途径绑定
# index                 病区给药途径绑定
# "@mh-inpatient-hsd/section-route-consumable": "^1.0.0",
import { index as SectionRouteConsumable } from '@mh-inpatient-hsd/section-route-consumable'
pnpm publish:component InpatientHsd/SectionRouteConsumable

### 病区库存台账
# index                 病区库存台账
# "@mh-inpatient-hsd/wm-bill-detail": "^1.0.3",
import { index as WmBillDetail } from '@mh-inpatient-hsd/wm-bill-detail'
pnpm publish:component InpatientHsd/WmBillDetail
