<script setup lang="ts">
import { Tabs, Typography } from 'ant-design-vue'
import { ref } from 'vue'
import OrgSelectExample from './examples/OrgSelectExample.vue'
import OrgDeptExample from './examples/OrgDeptExample.vue'
import OrgDoctorExample from './examples/OrgDoctorExample.vue'
import CodeHighlight from './examples/CodeHighlight.vue'

const { Title, Paragraph } = Typography

// 代码高亮组件引用
const codeHighlight = ref()

// 标签页切换时更新高亮
const onTabChange = () => {
  codeHighlight.value?.updateHighlight()
}
</script>

<template>
  <div p-16px>
    <Title :level="2">组织机构相关组件</Title>
    <Paragraph>组织机构相关组件，包括OrgSelect（组织机构选择）、OrgDept（部门选择）和OrgDoctor（医生选择）组件。</Paragraph>

    <!-- 代码高亮组件 -->
    <CodeHighlight ref="codeHighlight" />

    <Tabs @change="onTabChange">
      <Tabs.TabPane key="orgselect" tab="OrgSelect 组件">
        <OrgSelectExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="orgdept" tab="OrgDept 组件">
        <OrgDeptExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="orgdoctor" tab="OrgDoctor 组件">
        <OrgDoctorExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
