export function sectionListApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/sectionList', params, { appKey: 'inpatientHsd' })
}

export function deptClinicianJobApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/deptClinicianJob', params, { appKey: 'inpatientHsd' })
}

export function findOrgDeptInPatientLsApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/findOrgDeptInPatientLs', params, { appKey: 'inpatientHsd' })
}

export function sectionNurseJobApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/sectionNurseJob', params, { appKey: 'inpatientHsd' })
}

export function cancelEnterSectionApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/cancelEnterSection', params, { appKey: 'inpatientHsd' })
}

export function recoveryBedApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/recoveryBed', params, { appKey: 'inpatientHsd' })
}

export function orgSectionInPatientLsApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/orgSectionInPatientLs', params, { appKey: 'inpatientHsd' })
}

export function enterSectionApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/enterSection', params, { appKey: 'inpatientHsd' })
}

export function leaveSectionApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/leaveSection', params, { appKey: 'inpatientHsd' })
}

export function changeBedApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/changeBed', params, { appKey: 'inpatientHsd' })
}

export function findSectionByDeptApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/findSectionByDept', params, { appKey: 'inpatientHsd' })
}

export function sectionVisitStateDeptLsApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/sectionVisitStateDeptLs', params, { appKey: 'inpatientHsd' })
}

export function visitDischargeOeArtTypeApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/visitDischargeOeArtType', params, { appKey: 'inpatientHsd' })
}

export function admissionInfoApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/admissionInfo', params, { appKey: 'inpatientHsd' })
}

export function visitPatientInfoApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/visitPatientInfo', params, { appKey: 'inpatientHsd' })
}

export function updateVisitPatientInfoApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/updateVisitPatientInfo', params, { appKey: 'inpatientHsd' })
}

export function pageApi(params: any): Promise<any> {
  return http.post('/hsd/inpatientVisit/page', params, { appKey: 'inpatientHsd' })
}

export function infoApi(params: any) {
  return http.post('/hsd/inpatientVisit/info', params, { appKey: 'inpatientHsd' })
}

export function expSectionInPatientLsApi(params: any) {
  return http.post('/hsd/inpatientVisit/expSectionInPatientLs', params, { appKey: 'inpatientHsd' })
}

export function expDischargeApi(params: any) {
  return http.post('/hsd/inpatientVisit/expDischarge', params, { appKey: 'inpatientHsd' })
}
