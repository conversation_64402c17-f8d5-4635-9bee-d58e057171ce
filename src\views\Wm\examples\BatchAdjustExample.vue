<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './BatchAdjust/Basic.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>批号调整组件，用于调整药品的批号信息。</Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
