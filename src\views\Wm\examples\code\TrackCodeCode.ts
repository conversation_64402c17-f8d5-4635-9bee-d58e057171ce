import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { TrackCode } from '@mh-wm/track-code'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/track-code": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}`

// 基础用法 - 按钮触发
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 显示按钮 -->
  <TrackCode
    :wbSeqid="wbSeqid"
    @success="handleSuccess"
  />
</template>

<script setup>
import { TrackCode } from '@mh-wm/track-code'
import { ref } from 'vue'

// 溯源码ID
const wbSeqid = ref('12345')

// 成功回调
const handleSuccess = (data) => {
  console.log('溯源码录入成功', data)
}
</script>

<!--
默认情况下，组件会显示"溯源码(F5)"按钮，
点击按钮或按F5键都可以打开弹窗。
-->`)

// 通过js方法调用
export const jsMethodUsage = wrapCodeExample(`<template>
  <!-- 不显示按钮，通过js方法调用 -->
  <TrackCode
    ref="trackCodeRef"
    :showButton="false"
    @success="handleSuccess"
  />

  <!-- 自定义按钮 -->
  <Button @click="openTrackCode">打开溯源码录入</Button>
</template>

<script setup>
import { TrackCode } from '@mh-wm/track-code'
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

// 组件引用
const trackCodeRef = ref()

// 溯源码ID
const wbSeqid = ref('12345')

// 打开溯源码录入窗口
const openTrackCode = () => {
  trackCodeRef.value.open(wbSeqid.value)
}

// 成功回调
const handleSuccess = (data) => {
  console.log('溯源码录入成功', data)
}
</script>

<!--
当showButton设置为false时，组件不会显示按钮，
也不会响应键盘快捷键，只能通过JS方法调用打开弹窗。
-->`)
