<!--病区绑定追溯码组件-->
<template>
  <!-- Modal弹窗 -->
  <Modal
    v-model:open="modalVisible"
    :title="modalTitle"
    :width="modalWidth"
    :maskClosable="false"
    :keyboard="false"
    :style="{ top: '20px', maxHeight: 'calc(100vh - 40px)' }"
    @cancel="handleCancel"
  >
    <template #footer>
      <Space>
        <!-- 队列状态显示 -->
        <div v-if="queueStatus.totalPending > 0" class="queue-status">
          <Tag color="processing">
            正在处理 {{ queueStatus.totalPending }} 个请求
          </Tag>
        </div>
        <Button @click="handleCancel">关闭</Button>
      </Space>
    </template>

    <div class="section-track-code-container">
      <!-- 上部：日期选择器 -->
      <div class="section-track-code-header">
        <Form class="compact-form">
          <Row>
            <Col flex="200px">
              <FormItem label="日期" style="margin-right: 12px;">
                <DatePicker
                  v-model:value="selectedDate"
                  format="YYYY-MM-DD"
                  placeholder="选择日期"
                  size="small"
                  @change="handleDateChange"
                  style="width: 140px;"
                  :allow-clear="false"
                />
              </FormItem>
            </Col>
            <Col flex="100px">
              <FormItem>
                <Button type="primary" size="small" :loading="loading" @click="loadData">
                  查询
                </Button>
              </FormItem>
            </Col>
            <Col flex="auto"></Col>
            <Col flex="150px">
              <!-- 全局一键分配按钮 -->
              <FormItem v-if="props.autoAllocateAll">
                <Button
                  size="small"
                  danger
                  @click="handleGlobalAutoAllocate"
                  :loading="globalAllocating"
                >
                  一键分配药房已扫码
                </Button>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>

      <!-- 中部：核心绑码区域 -->
      <div class="main-content">
        <Row :gutter="16">
          <!-- 左侧：药品卡片列表 -->
          <Col :span="8">
            <div class="table-section">
              <div class="table-header">
                <h4>药品列表</h4>
                <!-- 搜索框 -->
                <div class="search-inline">
                  <Input
                    v-model:value="searchKeyword"
                    placeholder="搜索药品名称、编码..."
                    size="small"
                    allowClear
                    style="width: 200px;"
                  >
                    <template #prefix>
                      <span style="color: #bfbfbf;">🔍</span>
                    </template>
                  </Input>
                </div>
                <span class="table-count">共 {{ filteredDrugCards.length }}/{{ drugCards.length }} 项</span>
              </div>
              <div class="drug-list-container">
                <div v-if="drugCards.length === 0 && !loading" class="empty-state">
                  <Empty description="暂无数据" />
                </div>
                <div v-else class="drug-cards-wrapper">
                  <div
                    v-for="drug in sortedDrugCards"
                    :key="drug.keyStr"
                    :class="[
                      'drug-card-compact',
                      { 'drug-card-compact--selected': selectedDrugKey === drug.keyStr },
                      { 'drug-card-compact--completed': drug.isCompleted }
                    ]"
                    @click="handleSelectDrug(drug)"
                    :title="`artId: ${drug.artId}, 药品: ${drug.artName}, 规格: ${drug.artSpec}, 厂家: ${drug.producer}`"
                  >
                    <!-- 药房预绑定追溯码提示 - 右上角 -->
                    <div v-if="drug.pharmacyTrackCodes.length > 0" class="drug-card-compact__pharmacy-corner">
                      <Tag color="blue" size="small">
                        {{ getPharmacyCodeDisplay(drug) }}
                      </Tag>
                    </div>

                    <div class="drug-card-compact__main">
                      <div class="drug-card-compact__name">{{ drug.artName }} {{ drug.artSpec }}</div>
                      <div class="drug-card-compact__producer">{{ drug.producer }}</div>
                    </div>
                    <div class="drug-card-compact__info">
                      <div class="drug-card-compact__type">
                        <Tag :color="drug.unitType === 1 ? 'orange' : 'blue'" size="small">
                          {{ drug.unitType === 1 ? '拆零' : '整包' }}
                        </Tag>
                      </div>
                      <div class="drug-card-compact__amount">{{ drug.totalAmount }}{{ drug.unitName }}</div>
                      <div class="drug-card-compact__bound">{{ getBoundAmount(drug) }}/{{ drug.totalAmount }}</div>
                      <div class="drug-card-compact__status">
                        <Tag :color="drug.isCompleted ? 'green' : 'orange'" size="small">
                          {{ drug.isCompleted ? '完成' : '进行中' }}
                        </Tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Col>

          <!-- 右侧：患者列表和操作区域 -->
          <Col :span="16">
            <!-- 药房追溯码列表 -->
            <div v-if="selectedDrug && selectedDrug.pharmacyTrackCodes.length > 0" class="pharmacy-codes-section">
              <div class="table-header">
                <h4>药房追溯码</h4>
                <Button type="primary" size="small" @click="handleAutoAllocate">一键分配</Button>
              </div>
              <div class="pharmacy-codes-list">
                <div
                  v-for="(code, index) in selectedDrug.pharmacyTrackCodes"
                  :key="index"
                  class="pharmacy-code-item"
                >
                  <span class="code-text">
                    <span :class="getPharmacyCodePrefixClass(code.trackCode)">{{ code.trackCode.slice(0, 7) }}</span><span class="code-suffix">{{ code.trackCode.slice(7) }}</span>
                  </span>
                  <span class="code-amount">{{ getPharmacyCodeAmountDisplay(code) }}</span>
                  <Button size="small" @click="handleManualAllocate(code)">分配</Button>
                </div>
              </div>
            </div>

            <div class="table-section">
              <div class="table-header">
                <h4 v-if="selectedDrug">{{ selectedDrug.artName }} - 患者列表</h4>
                <h4 v-else>患者列表</h4>
                <div v-if="selectedDrug" class="table-header-right">
                  <span class="table-count">共 {{ selectedDrug.patients.length }} 位患者</span>
                </div>
              </div>
              <div class="patient-list-container">
                <div v-if="!selectedDrug" class="empty-state">
                  <Empty description="请选择药品" />
                </div>
                <div v-else class="patients-grid">
                  <!-- 患者卡片网格布局 -->
                  <div
                    v-for="patient in sortedPatients"
                    :key="patient.visitId"
                    :class="[
                      'patient-card-compact',
                      { 'patient-card-compact--selected': selectedPatientIds.includes(patient.visitId) },
                      { 'patient-card-compact--completed': patient.isCompleted }
                    ]"
                    @click="handleSelectPatient(patient)"
                  >
                    <div class="patient-card-compact__header">
                      <span class="patient-card-compact__name">{{ patient.bedNo }} {{ patient.patientName }}</span>
                      <span class="patient-card-compact__amount">{{ patient.needAmount }}{{ selectedDrug.unitName }}</span>
                    </div>

                    <!-- 已绑定的追溯码 -->
                    <div v-if="patient.boundTrackCodes.length > 0" class="patient-card-compact__codes">
                      <div
                        v-for="(code, index) in patient.boundTrackCodes"
                        :key="index"
                        class="patient-card-compact__code-item"
                      >
                        <span class="code-text">
                          <span :class="getTrackCodePrefixClass(patient, code.trackCode)">{{ code.trackCode.slice(0, 7) }}</span><span class="code-suffix">{{ code.trackCode.slice(7) }}</span>
                        </span>
                        <span class="code-amount">{{ code.cellsDispensed }}</span>
                        <span
                          class="code-unbind"
                          @click.stop="handleUnbindTrackCode(patient, index)"
                          :title="(code as any).isUsed ? '解绑已使用追溯码' : '解绑'"
                        >
                          ✕
                        </span>
<!--                        <span v-if="(code as any).isUsed" class="code-used" title="已使用">✓</span>-->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      <!-- 下部：追溯码录入区域 -->
      <div class="section-track-code-input-area">
        <div class="current-art-info">
<!--          <div v-if="selectedDrug" style="font-size: large;">-->
<!--            {{ selectedDrug.artName }} {{ selectedDrug.artSpec }} {{ selectedDrug.producer }}-->
<!--          </div>-->
<!--          <div v-else style="font-size: large; color: #999;">-->
<!--            请选择药品和患者-->
<!--          </div>-->
          <div class="track-code-form-container">
            <Form
              layout="inline"
              class="track-code-form"
              autocomplete="off"
            >
              <FormItem label="拆零" v-if="selectedDrug && (selectedDrug.unitType === 1 || isDisassembledDrug)">
                <Checkbox v-model:checked="isDisassembled" />
              </FormItem>
              <FormItem label="拆零数量" v-if="selectedDrug && isDisassembled">
                <InputNumber
                  v-model:value="inputCells"
                  :min="1"
                  :max="999"
                  placeholder="拆零数量"
                  style="width: 120px;"
                  @press-enter="focusInput"
                />
              </FormItem>
              <FormItem label="追溯码">
                <Input
                  ref="trackCodeInputRef"
                  v-model:value="trackCodeInput"
                  placeholder="请扫描或输入追溯码"
                  style="width: 400px"
                  autocomplete="off"
                  @press-enter="handleAddTrackCode"
                />
              </FormItem>
            </Form>
            <div class="action-buttons-container">
              <!-- 拆零上报按钮组 -->
              <div class="button-group" v-if="selectedDrug && selectedDrug.unitType === 2">
                <Button
                  v-if="!isDisassembledDrug"
                  danger
                  @click="handleSetDisassembled"
                  :disabled="!selectedDrug"
                >拆零上报</Button>
                <Button
                  v-if="isDisassembledDrug"
                  @click="handleClearDisassembled"
                  :disabled="!selectedDrug"
                >取消拆零上报</Button>
              </div>
            </div>
          </div>

          <!-- 拆零类药品的患者选择控制按钮 -->
          <div v-if="selectedDrug && (selectedDrug.unitType === 1 || isDisassembledDrug)" class="patient-control-buttons">
            <Space>
              <!-- 选择控制按钮 -->
              <Button
                v-if="selectedPatientIds.length > 0"
                size="small"
                @click="handleUnselectAllPatients"
              >
                取消选中
              </Button>
              <Button
                v-else-if="shouldShowSelectAll"
                size="small"
                type="primary"
                @click="handleSelectAllPatients"
              >
                全部选中
              </Button>

              <!-- 一键解绑新绑码按钮 -->
              <Button
                size="small"
                danger
                @click="handleUnbindNewTrackCodes"
                :disabled="!hasNewTrackCodes"
              >
                一键解绑新绑码
              </Button>
            </Space>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { Button, Checkbox, Col, DatePicker, Empty, Form, FormItem, Input, InputNumber, Modal, Row, Space, Tag, message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  useDatePicker,
  useDataLoader,
  useDrugCards,
  useSelection,
  useTrackCodeInput,
  useAutoAllocation,
  useTrackCodeBinding,
  useSmartAllocation,
  useApiCalls,
  useQueueMonitor
} from './composables'
import type { DrugCard, PatientCard, TrackCodeItem } from './types'
import './styles.less'

// 组件属性
interface Props {
  sectionId: number // 病区ID
  modalWidth?: number | string // Modal宽度
  autoAllocateAll?: boolean // 是否显示全局一键分配按钮
  checkTrackCode20Num?: boolean // 是否校验追溯码为20位纯数字
}

const props = withDefaults(defineProps<Props>(), {
  sectionId: 0,
  modalWidth: 1500,
  autoAllocateAll: false,
  checkTrackCode20Num: true
})

// 组件事件
const emit = defineEmits<{
  success: [data: any]
  cancel: []
}>()

// 使用组合式函数
const { selectedDate, formatDate } = useDatePicker()
const { loading, rawData, loadData: loadRawData } = useDataLoader(props.sectionId)
const { drugCards, processDrugCards } = useDrugCards()
const { selectedDrugKey, selectedPatientId, selectDrug, selectPatient } = useSelection()
const { trackCodeInput, trackCodeInputRef, focusInput, validateTrackCode, clearInput } = useTrackCodeInput()
const { autoAllocatePharmacyTrackCodes } = useAutoAllocation()
const { smartAllocateTrackCode } = useSmartAllocation()
const { callSectionFeeTrackCodeSummaryApi, setDisassembled, clearDisassembled, bindTrackCodeToFeeDetail, unbindTrackCodeFromFeeDetail } = useApiCalls()
const { queueStatus, startMonitoring, clearQueue } = useQueueMonitor()

// 本地状态
const modalVisible = ref(false)
const modalTitle = ref('病区绑定追溯码')
const animatingDrugs = ref(new Set<string>())
const currentTrackCodes = ref<TrackCodeItem[]>([])
const inputCells = ref(1)
const isDisassembled = ref(false)
const previousDate = ref<string>('')
const selectedPatientIds = ref<string[]>([]) // 多选患者ID列表
const searchKeyword = ref('') // 搜索关键词
const globalAllocating = ref(false) // 全局一键分配加载状态

// 计算属性
const selectedDrug = computed(() => {
  return drugCards.value.find(drug => drug.keyStr === selectedDrugKey.value)
})

// 过滤后的药品列表
const filteredDrugCards = computed(() => {
  if (!searchKeyword.value.trim()) {
    return drugCards.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return drugCards.value.filter(drug => {
    // 搜索药品名称、编码等字段
    const qsCode1 = (drug as any).qsCode1 || ''
    const qsCode2 = (drug as any).qsCode2 || ''

    return (
      drug.artName.toLowerCase().includes(keyword) ||
      qsCode1.toLowerCase().includes(keyword) ||
      qsCode2.toLowerCase().includes(keyword) ||
      drug.artId.toString().includes(keyword) ||
      drug.artSpec.toLowerCase().includes(keyword) ||
      drug.producer.toLowerCase().includes(keyword)
    )
  })
})

const sortedDrugCards = computed(() => {
  // 对过滤后的药品进行排序
  return [...filteredDrugCards.value].sort((a, b) => {
    // 1. 已绑定的放到队列尾部
    if (a.isCompleted && !b.isCompleted) return 1
    if (!a.isCompleted && b.isCompleted) return -1

    // 2. 需求量大的优先（未完成的药品中）
    if (!a.isCompleted && !b.isCompleted) {
      return b.totalAmount - a.totalAmount
    }

    return 0
  })
})

const sortedPatients = computed(() => {
  if (!selectedDrug.value) return []
  // 只按床号排序，不区分完成状态
  return [...selectedDrug.value.patients].sort((a, b) => {
    const bedNoA = parseInt(a.bedNo?.replace(/[^\d]/g, '') || '999')
    const bedNoB = parseInt(b.bedNo?.replace(/[^\d]/g, '') || '999')
    return bedNoA - bedNoB
  })
})

// 计算已绑定数量
const getBoundAmount = (drug: any) => {
  return drug.patients.reduce((total: number, patient: any) => {
    return total + patient.boundTrackCodes.reduce((sum: number, code: any) => sum + code.cellsDispensed, 0)
  }, 0)
}

// 获取追溯码前7位的样式类
const getTrackCodePrefixClass = (patient: any, trackCode: string) => {
  if (!selectedDrug.value) return 'code-prefix-default'

  // 获取当前药品所有患者的所有追溯码前7位
  const allPrefixes = selectedDrug.value.patients.flatMap((p: any) =>
    p.boundTrackCodes.map((code: any) => code.trackCode.slice(0, 7))
  )

  // 去重
  const uniquePrefixes = [...new Set(allPrefixes)]

  // 如果只有一种前7位，使用绿色；如果有多种，使用橙色
  if (uniquePrefixes.length === 1) {
    return 'code-prefix-consistent' // 绿色
  } else {
    return 'code-prefix-inconsistent' // 橙色
  }
}

// 获取药房码显示文本
const getPharmacyCodeDisplay = (drug: any) => {
  const codeCount = drug.pharmacyTrackCodes.length

  if (drug.unitType === 2 && !(drug as any).isDisassembled) {
    // 整包类：显示"药房X码"
    return `药房${codeCount}码`
  } else {
    // 拆零类：显示"药房X码Y单位"，使用药品的实际单位
    const totalCells = drug.pharmacyTrackCodes.reduce((sum: number, code: any) =>
      sum + (code.originalTotalCells || 0), 0
    )
    return `药房${codeCount}码${totalCells}${drug.unitName || '支'}`
  }
}

// 获取药房追溯码前7位的样式类
const getPharmacyCodePrefixClass = (trackCode: string) => {
  // 药房追溯码默认使用绿色
  return 'code-prefix-consistent'
}

// 获取药房追溯码数量显示（已分配/总数量）
const getPharmacyCodeAmountDisplay = (code: any) => {
  if (!selectedDrug.value) return ''

  if (selectedDrug.value.unitType === 2) {
    // 整包类：显示已分配包数/总包数
    const totalPacks = (code.originalTotalPacks || code.curTotalPacks || 1)
    const allocatedPacks = totalPacks - (code.curTotalPacks || 0)
    return `${allocatedPacks}/${totalPacks}包`
  } else {
    // 拆零类：显示已分配支数/总支数
    const totalCells = (code.originalTotalCells || code.curTotalCells || 0)
    const allocatedCells = totalCells - (code.curTotalCells || 0)
    return `${allocatedCells}/${totalCells}支`
  }
}

// 手动分配药房追溯码（带API调用）
const handleManualAllocate = async (code: any) => {
  if (!selectedDrug.value || selectedPatientIds.value.length === 0) {
    message.warning('请先选择患者')
    return
  }

  // 检查追溯码是否还有剩余数量
  const availableAmount = selectedDrug.value.unitType === 2 ?
    (code.curTotalPacks || 0) : (code.curTotalCells || 0)

  if (availableAmount <= 0) {
    message.warning('该追溯码已无剩余数量')
    return
  }

  // 获取选中的患者并按床号排序
  const selectedPatients = selectedDrug.value.patients
    .filter((p: any) => selectedPatientIds.value.includes(p.visitId))
    .sort((a: any, b: any) => {
      const bedNoA = parseInt(a.bedNo?.replace(/[^\d]/g, '') || '0')
      const bedNoB = parseInt(b.bedNo?.replace(/[^\d]/g, '') || '0')
      return bedNoA - bedNoB
    })

  // 计算总需求量
  let totalNeed = 0
  for (const patient of selectedPatients) {
    const currentBound = patient.boundTrackCodes.reduce((sum: number, boundCode: any) =>
      sum + boundCode.cellsDispensed, 0)
    const remainingNeed = patient.needAmount - currentBound
    if (remainingNeed > 0) {
      totalNeed += remainingNeed
    }
  }

  if (totalNeed <= 0) {
    message.info('选中患者的需求已全部满足')
    return
  }

  // 分配数量不能超过可用数量
  const allocateAmount = Math.min(totalNeed, availableAmount)

  try {
    let apiCalls: Promise<any>[] = []
    let allocatedPatients: string[] = []

    // 按床号顺序分配给患者 - 更新为费用明细级别绑定
    let remainingToAllocate = allocateAmount
    for (const patient of selectedPatients) {
      if (remainingToAllocate <= 0) break

      const currentBound = patient.boundTrackCodes.reduce((sum: number, boundCode: any) =>
        sum + boundCode.cellsDispensed, 0)
      const remainingNeed = patient.needAmount - currentBound

      if (remainingNeed > 0) {
        const allocateToThisPatient = Math.min(remainingNeed, remainingToAllocate)

        // 绑定到费用明细级别
        const uncompletedFeeDetails = patient.feeDetails.filter((fee: any) =>
          fee.artId === selectedDrug.value.artId &&
          fee.boundTrackCodes.reduce((sum: number, tc: any) =>
            sum + (selectedDrug.value.unitType === 2 ? (tc.totalPacks || 0) : (tc.totalCells || 0)), 0) < fee.total
        )

        let patientRemainingToAllocate = allocateToThisPatient
        for (const feeDetail of uncompletedFeeDetails) {
          if (patientRemainingToAllocate <= 0) break

          const currentFeeDetailBound = feeDetail.boundTrackCodes.reduce((sum: number, tc: any) =>
            sum + (selectedDrug.value.unitType === 2 ? (tc.totalPacks || 0) : (tc.totalCells || 0)), 0)
          const feeDetailNeed = feeDetail.total - currentFeeDetailBound

          if (feeDetailNeed > 0) {
            const feeDetailAllocate = Math.min(feeDetailNeed, patientRemainingToAllocate)

            // 添加API调用到队列
            apiCalls.push(
              bindTrackCodeToFeeDetail(
                props.sectionId,
                feeDetail,
                code.trackCode,
                feeDetailAllocate,
                selectedDrug.value.unitType === 1
              ).then(() => {
                // API调用成功后更新本地数据
                feeDetail.boundTrackCodes.push({
                  trackCode: code.trackCode,
                  totalPacks: selectedDrug.value.unitType === 2 ? feeDetailAllocate : undefined,
                  totalCells: selectedDrug.value.unitType === 1 ? feeDetailAllocate : undefined,
                  isDisassembled: selectedDrug.value.unitType === 1
                })
              })
            )

            patientRemainingToAllocate -= feeDetailAllocate
          }
        }

        // 计算实际分配的数量
        const actualAllocated = allocateToThisPatient - patientRemainingToAllocate

        if (actualAllocated > 0) {
          // 同步更新患者显示的追溯码
          patient.boundTrackCodes.push({
            trackCode: code.trackCode,
            cellsDispensed: actualAllocated,
            cellsRemain: 0,
            isDisassembled: selectedDrug.value.unitType === 1,
            isUsed: false
          })

          allocatedPatients.push(`${patient.bedNo} ${patient.patientName}`)
        }

        remainingToAllocate -= allocateToThisPatient

        // 检查患者是否完成
        const newBoundTotal = patient.boundTrackCodes.reduce((sum: number, boundCode: any) =>
          sum + boundCode.cellsDispensed, 0)
        patient.isCompleted = newBoundTotal >= patient.needAmount
      }
    }

    // 等待所有API调用完成
    await Promise.all(apiCalls)

    // 更新追溯码剩余数量
    if (selectedDrug.value.unitType === 2) {
      code.curTotalPacks = (code.curTotalPacks || 0) - allocateAmount
    } else {
      code.curTotalCells = (code.curTotalCells || 0) - allocateAmount
    }

    // 检查药品完成状态
    checkDrugCompletion(selectedDrug.value)

    if (allocatedPatients.length > 0) {
      message.success(`已分配给: ${allocatedPatients.join(', ')}`)
    }
  } catch (error) {
    console.error('手动分配失败:', error)
    message.error('手动分配失败，请重试')
  }
}

// 计算属性：当前药品是否为拆零上报
const isDisassembledDrug = computed(() => {
  if (!selectedDrug.value) return false
  // 这里需要根据实际的数据结构来判断
  return selectedDrug.value.unitType === 1 || (selectedDrug.value as any).isDisassembled === 1
})

// 计算属性：是否有新绑定的追溯码（isUsed=false）
const hasNewTrackCodes = computed(() => {
  if (!selectedDrug.value) return false

  return selectedDrug.value.patients.some(patient =>
    patient.boundTrackCodes.some(code => !(code as any).isUsed)
  )
})

// 计算属性：是否应该显示"全部选中"按钮
const shouldShowSelectAll = computed(() => {
  if (!selectedDrug.value) return false
  if (selectedPatientIds.value.length > 0) return false // 已有选中患者时不显示

  // 检查是否有未完成的患者（未绑满追溯码的患者）
  const hasUncompletedPatients = selectedDrug.value.patients.some(patient => !patient.isCompleted)

  return hasUncompletedPatients
})



// 方法
// Modal相关方法
const open = () => {
  modalVisible.value = true
  // 设置当前日期
  selectedDate.value = dayjs()
  previousDate.value = dayjs().format('YYYY-MM-DD')

  // 打开时自动加载当日数据
  setTimeout(() => {
    loadTodayData()
    focusInput()
  }, 100)
}

// 加载当日数据
const loadTodayData = async () => {
  try {
    const bsnDate = dayjs().format('YYYYMMDD')
    const response = await callSectionFeeTrackCodeSummaryApi({
      sectionId: props.sectionId,
      bsnDate
    })
    processDrugCards(response.artSummaryList, response.pharmacyTrackCodeSums)
    // message.success('数据加载成功')
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
    // 如果加载失败，使用模拟数据
    loadData()
  }
}



const handleCancel = () => {
  emit('cancel')
  modalVisible.value = false
}

const loadData = async () => {
  try {
    const data = await loadRawData(formatDate.value)
    if (data) {
      processDrugCards(data.artSummaryList, data.pharmacyTrackCodeSums)
      // message.success('数据加载成功')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

const handleDateChange = async (date: any, dateString: string) => {
  console.log('日期切换:', previousDate.value, '->', dateString)

  // 如果是初始化，直接设置
  if (!previousDate.value) {
    previousDate.value = dateString
    return
  }

  // 如果日期没有变化，直接返回
  if (previousDate.value === dateString) {
    return
  }

  // 检查是否有新录入的追溯码（未保存的绑定）
  const hasNewTrackCodes = currentTrackCodes.value.length > 0 ||
    drugCards.value.some(drug =>
      drug.patients.some(patient =>
        patient.boundTrackCodes.some(code =>
          !(code as any).isUsed && !(code as any).isFromServer
        )
      )
    )

  console.log('检查新追溯码:', hasNewTrackCodes)

  if (hasNewTrackCodes) {
    // 弹出确认对话框
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '确认切换日期',
        content: '当前日期录入的追溯码不会保存，是否切换日期？',
        okText: '确认切换',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (!confirmed) {
      // 恢复到之前的日期
      selectedDate.value = dayjs(previousDate.value)
      return
    }
  }

  // 记录当前日期
  previousDate.value = dateString

  // 清空当前状态
  selectedDrugKey.value = ''
  selectedPatientIds.value = []
  currentTrackCodes.value = []
  trackCodeInput.value = ''

  // 调用真实API加载新日期的数据
  if (dateString) {
    try {
      const bsnDate = dayjs(dateString).format('YYYYMMDD')
      console.log('调用API加载数据:', bsnDate)
      const response = await callSectionFeeTrackCodeSummaryApi({
        sectionId: props.sectionId,
        bsnDate
      })
      processDrugCards(response.artSummaryList, response.pharmacyTrackCodeSums)
      // message.success('数据加载成功')
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
      // 如果加载失败，使用模拟数据
      loadData()
    }
  }
}

// 选择药品处理
const handleSelectDrug = (drug: any) => {
  selectedDrugKey.value = drug.keyStr

  // 根据药品类型自动选择患者
  if (drug.unitType === 2 && !(drug as any).isDisassembled) {
    // 整包类：只能选择一个患者，自动选择第一个未绑码的患者
    const uncompletedPatient = drug.patients.find((p: any) => !p.isCompleted)
    selectedPatientIds.value = uncompletedPatient ? [uncompletedPatient.visitId] : []

    // 整包类不自动勾选拆零标记
    isDisassembled.value = false
    inputCells.value = 1
  } else {
    // 拆零类：多选所有未绑码的患者
    const uncompletedPatients = drug.patients.filter((p: any) => !p.isCompleted)
    selectedPatientIds.value = uncompletedPatients.map((p: any) => p.visitId)

    // 自动勾选拆零标记
    isDisassembled.value = true

    // 计算拆零数量（所有选中患者的需求量总和）
    const totalNeed = uncompletedPatients.reduce((sum: number, p: any) => {
      const currentBound = p.boundTrackCodes.reduce((codeSum: number, code: any) => codeSum + code.cellsDispensed, 0)
      return sum + (p.needAmount - currentBound)
    }, 0)
    inputCells.value = totalNeed
  }

  // 聚焦输入框
  setTimeout(() => {
    focusInput()
  }, 100)
}

// 选择患者处理
const handleSelectPatient = (patient: any) => {
  if (!selectedDrug.value) return

  if (selectedDrug.value.unitType === 2 && !(selectedDrug.value as any).isDisassembled) {
    // 整包类：单选
    selectedPatientIds.value = [patient.visitId]
  } else {
    // 拆零类：多选
    const index = selectedPatientIds.value.indexOf(patient.visitId)
    if (index > -1) {
      selectedPatientIds.value.splice(index, 1)
    } else {
      selectedPatientIds.value.push(patient.visitId)
    }

    // 重新计算拆零数量
    updateInputCells()
  }
}

const handleAutoAllocate = async () => {
  if (!selectedDrug.value) return

  // 检查是否还有剩余的药房追溯码可以分配
  const availableCodes = selectedDrug.value.pharmacyTrackCodes.filter((code: any) => {
    if (selectedDrug.value.unitType === 2) {
      // 整包类：检查是否还有剩余包装数
      return (code.curTotalPacks || 0) > 0
    } else {
      // 拆零类：检查是否还有剩余制剂数
      return (code.curTotalCells || 0) > 0
    }
  })

  if (availableCodes.length === 0) {
    message.warning('药房追溯码已全部分配完毕')
    return
  }

  // 检查是否还有未满足的患者需求
  const uncompletedPatients = selectedDrug.value.patients.filter((p: any) => !p.isCompleted)
  if (uncompletedPatients.length === 0) {
    message.info('所有患者需求已满足')
    return
  }

  await autoAllocatePharmacyTrackCodes(selectedDrug.value, props.sectionId, bindTrackCodeToFeeDetail)
  checkDrugCompletion(selectedDrug.value)
}

// 全局一键分配药房已扫码
const handleGlobalAutoAllocate = async () => {
  globalAllocating.value = true

  try {
    // 获取所有有药房追溯码的药品
    const drugsWithPharmacyCodes = drugCards.value.filter(drug =>
      drug.pharmacyTrackCodes && drug.pharmacyTrackCodes.length > 0
    )

    if (drugsWithPharmacyCodes.length === 0) {
      message.info('没有药房预绑定的追溯码')
      return
    }

    let allocatedCount = 0
    let totalCount = drugsWithPharmacyCodes.length

    // 遍历每个有药房追溯码的药品
    for (const drug of drugsWithPharmacyCodes) {
      // 检查是否还有剩余的药房追溯码可以分配
      const availableCodes = drug.pharmacyTrackCodes.filter((code: any) => {
        if (drug.unitType === 2) {
          return (code.curTotalPacks || 0) > 0
        } else {
          return (code.curTotalCells || 0) > 0
        }
      })

      // 检查是否还有未满足的患者需求
      const uncompletedPatients = drug.patients.filter((p: any) => !p.isCompleted)

      if (availableCodes.length > 0 && uncompletedPatients.length > 0) {
        // 执行一键分配
        await autoAllocatePharmacyTrackCodes(drug, props.sectionId, bindTrackCodeToFeeDetail)
        checkDrugCompletion(drug)
        allocatedCount++
      }
    }

    if (allocatedCount > 0) {
      message.success(`成功为 ${allocatedCount}/${totalCount} 个药品分配了药房追溯码`)
    } else {
      message.info('所有药品的药房追溯码已分配完毕或患者需求已满足')
    }
  } catch (error) {
    console.error('全局一键分配失败:', error)
    message.error('全局一键分配失败')
  } finally {
    globalAllocating.value = false
  }
}

const handleAddTrackCode = async () => {
  const code = trackCodeInput.value.trim()
  if (!code) return

  if (!validateTrackCode(code, props.checkTrackCode20Num)) {
    clearInput()
    return
  }

  // 检查是否选择了药品和患者
  if (!selectedDrug.value) {
    message.warning('请先选择药品')
    clearInput()
    return
  }

  if (!selectedPatientIds.value || selectedPatientIds.value.length === 0) {
    message.warning('请先选择患者')
    clearInput()
    return
  }

  // 拆零数量提醒和packCells处理
  if (selectedDrug.value.unitType === 1 || isDisassembled.value) {
    const packCells = (selectedDrug.value as any).packCells || 1

    if (packCells > 1) {
      // 如果当前拆零数量超过packCells，给出提醒
      if (inputCells.value > packCells) {
        const confirmed = await new Promise((resolve) => {
          Modal.confirm({
            title: '拆零数量提醒',
            content: `当前药品包装规格为${packCells}支/盒，建议单次绑定不超过${packCells}支。当前设置为${inputCells.value}支，是否继续？`,
            okText: '继续绑定',
            cancelText: '调整数量',
            onOk: () => resolve(true),
            onCancel: () => resolve(false)
          })
        })

        if (!confirmed) {
          // 用户选择调整数量，自动设置为packCells
          inputCells.value = packCells
          clearInput()
          return
        }
      }

      // 如果拆零数量为1，自动设置为packCells
      if (inputCells.value === 1) {
        inputCells.value = packCells
        message.info(`已自动调整拆零数量为${packCells}支（一盒装量）`)
      }
    }
  }

  // 获取选中的患者并按床号排序
  const selectedPatients = selectedDrug.value.patients
    .filter(p => selectedPatientIds.value.includes(p.visitId))
    .sort((a, b) => {
      const bedNoA = parseInt(a.bedNo?.replace(/[^\d]/g, '') || '999')
      const bedNoB = parseInt(b.bedNo?.replace(/[^\d]/g, '') || '999')
      return bedNoA - bedNoB
    })

  if (selectedPatients.length === 0) {
    message.error('未找到选中的患者')
    clearInput()
    return
  }

  // 检查追溯码是否已经绑定过
  const isCodeAlreadyBound = selectedPatients.some(patient =>
    patient.boundTrackCodes.some((boundCode: any) => boundCode.trackCode === code)
  )

  if (isCodeAlreadyBound) {
    message.warning('该追溯码已经绑定过')
    clearInput()
    return
  }

  // 检查是否有患者还需要绑定追溯码
  const patientsNeedBinding = selectedPatients.filter(patient => {
    const currentBound = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
    return currentBound < patient.needAmount
  })

  if (patientsNeedBinding.length === 0) {
    message.info('选中的患者已全部完成绑定')
    clearInput()
    return
  }

  // 绑定追溯码到费用明细级别
  if (selectedDrug.value.unitType === 2 && !isDisassembled.value) {
    // 整包类：只能绑定给一个患者的一个费用明细
    const firstPatient = patientsNeedBinding[0]

    // 找到第一个未完成的费用明细
    const uncompletedFeeDetail = firstPatient.feeDetails.find(fee =>
      fee.artId === selectedDrug.value.artId &&
      fee.boundTrackCodes.reduce((sum, tc) => sum + (tc.totalPacks || 0), 0) < fee.total
    )

    if (uncompletedFeeDetail) {
      const currentBound = uncompletedFeeDetail.boundTrackCodes.reduce((sum, tc) => sum + (tc.totalPacks || 0), 0)
      const stillNeed = uncompletedFeeDetail.total - currentBound
      const bindAmount = Math.min(1, stillNeed)

      try {
        // 调用API绑定追溯码
        await bindTrackCodeToFeeDetail(
          props.sectionId,
          uncompletedFeeDetail,
          code,
          bindAmount,
          false
        )

        // API调用成功后更新本地数据
        // 绑定到费用明细
        uncompletedFeeDetail.boundTrackCodes.push({
          trackCode: code,
          totalPacks: bindAmount,
          isDisassembled: false
        })

        // 同步更新患者显示的追溯码
        firstPatient.boundTrackCodes.push({
          trackCode: code,
          cellsDispensed: bindAmount,
          cellsRemain: 0,
          isDisassembled: false
        })

        // 更新患者完成状态
        const newBoundTotal = firstPatient.boundTrackCodes.reduce((sum, tc) => sum + tc.cellsDispensed, 0)
        firstPatient.isCompleted = newBoundTotal >= firstPatient.needAmount

        message.success(`追溯码已绑定给 ${firstPatient.bedNo} ${firstPatient.patientName}`)
      } catch (error) {
        message.error('绑定追溯码失败')
        clearInput()
        return
      }
    }
  } else {
    // 拆零类：按床号顺序分配给多个患者
    let remainingCells = inputCells.value || 1
    let allocatedPatients: string[] = []
    let totalAllocated = 0
    let apiCalls: Promise<any>[] = []

    for (const patient of patientsNeedBinding) {
      if (remainingCells <= 0) break

      const currentBound = patient.boundTrackCodes.reduce((sum, tc) => sum + tc.cellsDispensed, 0)
      const stillNeed = patient.needAmount - currentBound

      if (stillNeed > 0) {
        const allocateAmount = Math.min(stillNeed, remainingCells)

        // 找到该患者的费用明细进行绑定
        const uncompletedFeeDetails = patient.feeDetails.filter(fee =>
          fee.artId === selectedDrug.value.artId &&
          fee.boundTrackCodes.reduce((sum, tc) => sum + (tc.totalCells || 0), 0) < fee.total
        )

        let patientRemainingToAllocate = allocateAmount
        for (const feeDetail of uncompletedFeeDetails) {
          if (patientRemainingToAllocate <= 0) break

          const currentFeeDetailBound = feeDetail.boundTrackCodes.reduce((sum, tc) => sum + (tc.totalCells || 0), 0)
          const feeDetailNeed = feeDetail.total - currentFeeDetailBound

          if (feeDetailNeed > 0) {
            const feeDetailAllocate = Math.min(feeDetailNeed, patientRemainingToAllocate)

            // 添加API调用到队列
            apiCalls.push(
              bindTrackCodeToFeeDetail(
                props.sectionId,
                feeDetail,
                code,
                feeDetailAllocate,
                true
              ).then(() => {
                // API调用成功后更新本地数据
                feeDetail.boundTrackCodes.push({
                  trackCode: code,
                  totalCells: feeDetailAllocate,
                  isDisassembled: true
                })
              })
            )

            patientRemainingToAllocate -= feeDetailAllocate
          }
        }

        // 同步更新患者显示的追溯码
        patient.boundTrackCodes.push({
          trackCode: code,
          cellsDispensed: allocateAmount,
          cellsRemain: remainingCells - allocateAmount,
          isDisassembled: true
        })

        remainingCells -= allocateAmount
        totalAllocated += allocateAmount
        allocatedPatients.push(`${patient.bedNo} ${patient.patientName}`)

        // 更新患者完成状态
        const newBoundTotal = patient.boundTrackCodes.reduce((sum, tc) => sum + tc.cellsDispensed, 0)
        patient.isCompleted = newBoundTotal >= patient.needAmount
      }
    }

    try {
      // 等待所有API调用完成
      await Promise.all(apiCalls)

      if (allocatedPatients.length > 0) {
        message.success(`追溯码已分配给: ${allocatedPatients.join(', ')}`)
      } else {
        message.warning('无法分配追溯码')
      }
    } catch (error) {
      message.error('绑定追溯码失败，请重试')
      // 这里可以考虑回退本地数据，但由于逻辑复杂，暂时保持现状
      clearInput()
      return
    }
  }

  // 更新药品完成状态
  checkDrugCompletion(selectedDrug.value)

  // 重新计算拆零数量
  updateInputCells()

  clearInput()
  focusInput()
}

const removeTrackCode = (index: number) => {
  currentTrackCodes.value.splice(index, 1)
}

// 患者选择控制方法
const handleSelectAllPatients = () => {
  if (!selectedDrug.value) return

  // 选中所有未完成的患者
  const uncompletedPatients = selectedDrug.value.patients.filter(p => !p.isCompleted)
  selectedPatientIds.value = uncompletedPatients.map(p => p.visitId)

  // 重新计算拆零数量
  updateInputCells()
}

const handleUnselectAllPatients = () => {
  selectedPatientIds.value = []
  inputCells.value = 1
}

// 一键解绑新绑码方法
const handleUnbindNewTrackCodes = async () => {
  if (!selectedDrug.value) return

  const confirmed = await new Promise((resolve) => {
    Modal.confirm({
      title: '确认解绑新绑码',
      content: '将解绑所有新绑定的追溯码（不包括已使用的历史追溯码），是否继续？',
      okText: '确认解绑',
      cancelText: '取消',
      onOk: () => resolve(true),
      onCancel: () => resolve(false)
    })
  })

  if (!confirmed) return

  try {
    let apiCalls: Promise<any>[] = []
    let unboundCount = 0

    // 遍历所有患者，找到新绑定的追溯码
    for (const patient of selectedDrug.value.patients) {
      // 从后往前遍历，避免删除时索引变化
      for (let i = patient.boundTrackCodes.length - 1; i >= 0; i--) {
        const trackCode = patient.boundTrackCodes[i]

        // 只处理新绑定的追溯码（isUsed=false）
        if (!(trackCode as any).isUsed) {
          // 从费用明细中解绑
          patient.feeDetails.forEach(feeDetail => {
            if (feeDetail.artId === selectedDrug.value.artId) {
              const trackCodeIndex = feeDetail.boundTrackCodes.findIndex(tc => tc.trackCode === trackCode.trackCode)
              if (trackCodeIndex > -1) {
                const boundTrackCode = feeDetail.boundTrackCodes[trackCodeIndex]
                const amount = boundTrackCode.totalPacks || boundTrackCode.totalCells

                if (amount) {
                  apiCalls.push(
                    unbindTrackCodeFromFeeDetail(
                      props.sectionId,
                      feeDetail,
                      trackCode.trackCode,
                      amount,
                      boundTrackCode.isDisassembled
                    ).then(() => {
                      // API调用成功后移除本地数据
                      feeDetail.boundTrackCodes.splice(trackCodeIndex, 1)
                    })
                  )
                }
              }
            }
          })

          // 从患者显示列表中移除
          patient.boundTrackCodes.splice(i, 1)
          unboundCount++
        }
      }

      // 更新患者完成状态
      const boundTotal = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
      patient.isCompleted = boundTotal >= patient.needAmount
    }

    // 等待所有API调用完成
    await Promise.all(apiCalls)

    // 更新药品完成状态
    checkDrugCompletion(selectedDrug.value)

    if (unboundCount > 0) {
      message.success(`已解绑 ${unboundCount} 个新绑定的追溯码`)
    } else {
      message.info('没有找到新绑定的追溯码')
    }
  } catch (error) {
    console.error('一键解绑失败:', error)
    message.error('一键解绑失败，请重试')
  }
}

// 设置拆零上报
const handleSetDisassembled = async () => {
  if (!selectedDrug.value) return
  const success = await setDisassembled(selectedDrug.value.artId)
  if (success) {
    message.success('已设置为拆零上报')
    // 更新本地状态
    ;(selectedDrug.value as any).isDisassembled = 1
  }
}

// 取消拆零上报
const handleClearDisassembled = async () => {
  if (!selectedDrug.value) return
  const success = await clearDisassembled(selectedDrug.value.artId)
  if (success) {
    message.success('已取消拆零上报')
    // 更新本地状态
    ;(selectedDrug.value as any).isDisassembled = 0
  }
}



const handleUnbindTrackCode = async (patient: PatientCard, index: number) => {
  const trackCode = patient.boundTrackCodes[index]

  // 检查是否已使用，需要确认
  if ((trackCode as any).isUsed) {
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '确认解绑已使用的追溯码',
        content: '该追溯码已被使用，确定要解绑吗？',
        okText: '确认解绑',
        okType: 'danger',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (!confirmed) {
      return
    }
  }

  try {
    let apiCalls: Promise<any>[] = []

    // 解绑追溯码的API调用
    if (selectedDrug.value && trackCode) {
      if ((trackCode as any).isUsed) {
        // 已使用的追溯码：需要解绑所有相关的费用明细
        const relatedFeeDetails = (trackCode as any).feeDetails || []

        if (relatedFeeDetails.length > 0) {
          // 为每个相关的费用明细调用解绑API
          relatedFeeDetails.forEach((feeDetail: any) => {
            // 查找该费用明细中对应追溯码的使用量
            const usedTrackCodeDetail = feeDetail.usedTrackCodeDetails?.find(
              (used: any) => used.trackCode === trackCode.trackCode
            )

            if (usedTrackCodeDetail) {
              apiCalls.push(
                unbindTrackCodeFromFeeDetail(
                  props.sectionId,
                  feeDetail,
                  trackCode.trackCode,
                  usedTrackCodeDetail.usedAmount,
                  trackCode.isDisassembled
                )
              )
            }
          })
        } else {
          console.warn('未找到对应的费用明细，无法解绑已使用的追溯码:', trackCode.trackCode)
        }
      } else {
        // 未使用的追溯码：从费用明细的boundTrackCodes中查找并解绑
        patient.feeDetails.forEach(feeDetail => {
          if (feeDetail.artId === selectedDrug.value.artId) {
            const trackCodeIndex = feeDetail.boundTrackCodes.findIndex(tc => tc.trackCode === trackCode.trackCode)
            if (trackCodeIndex > -1) {
              const boundTrackCode = feeDetail.boundTrackCodes[trackCodeIndex]

              // 获取正确的数量值，不使用默认值
              const amount = boundTrackCode.totalPacks || boundTrackCode.totalCells
              if (amount) {
                apiCalls.push(
                  unbindTrackCodeFromFeeDetail(
                    props.sectionId,
                    feeDetail,
                    trackCode.trackCode,
                    amount,
                    boundTrackCode.isDisassembled
                  ).then(() => {
                    // API调用成功后移除本地数据
                    feeDetail.boundTrackCodes.splice(trackCodeIndex, 1)
                  })
                )
              }
            }
          }
        })
      }

      // 如果是药房预绑定的追溯码，需要恢复数量
      const pharmacyCode = selectedDrug.value.pharmacyTrackCodes.find((pc: any) => pc.trackCode === trackCode.trackCode)
      if (pharmacyCode) {
        // 恢复药房追溯码的剩余数量
        if (selectedDrug.value.unitType === 2) {
          pharmacyCode.curTotalPacks = (pharmacyCode.curTotalPacks || 0) + trackCode.cellsDispensed
        } else {
          pharmacyCode.curTotalCells = (pharmacyCode.curTotalCells || 0) + trackCode.cellsDispensed
        }
      }
    }

    // 等待所有API调用完成
    await Promise.all(apiCalls)

    // 从患者显示列表中移除
    patient.boundTrackCodes.splice(index, 1)

    // 更新患者完成状态
    const boundTotal = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
    patient.isCompleted = boundTotal >= patient.needAmount

    // 更新药品完成状态
    if (selectedDrug.value) {
      checkDrugCompletion(selectedDrug.value)
    }

    // 重新计算拆零数量
    updateInputCells()

    message.success('追溯码解绑成功')
  } catch (error) {
    console.error('解绑失败:', error)
    message.error('解绑失败')
  }
}

const checkDrugCompletion = (drug: DrugCard) => {
  const allCompleted = drug.patients.every(p => p.isCompleted)
  const wasCompleted = drug.isCompleted
  drug.isCompleted = allCompleted

  // 如果从未完成变为完成，触发动画
  if (!wasCompleted && allCompleted) {
    animatingDrugs.value.add(drug.keyStr)
    setTimeout(() => {
      animatingDrugs.value.delete(drug.keyStr)
    }, 800)
  }
}



// 重新计算拆零数量
const updateInputCells = () => {
  if (!selectedDrug.value || selectedPatientIds.value.length === 0) {
    inputCells.value = 1
    return
  }

  const selectedPatients = selectedDrug.value.patients.filter((p: any) =>
    selectedPatientIds.value.includes(p.visitId)
  )

  // 计算选中患者的总剩余需求量
  const totalRemainingNeed = selectedPatients.reduce((sum: number, p: any) => {
    const currentBound = p.boundTrackCodes.reduce((codeSum: number, code: any) => codeSum + code.cellsDispensed, 0)
    const remainingNeed = Math.max(0, p.needAmount - currentBound)
    return sum + remainingNeed
  }, 0)

  // 对于拆零类药品，如果packCells>1，默认最大数值是packCells而不是需求量
  let maxCells = Math.max(1, totalRemainingNeed)

  if (selectedDrug.value.unitType === 1 || isDisassembled.value) {
    const packCells = (selectedDrug.value as any).packCells || 1
    if (packCells > 1) {
      maxCells = Math.min(maxCells, packCells)
    }
  }

  inputCells.value = maxCells

  console.log('更新拆零数量:', {
    selectedPatients: selectedPatients.map(p => ({
      bedNo: p.bedNo,
      needAmount: p.needAmount,
      boundAmount: p.boundTrackCodes.reduce((sum: number, code: any) => sum + code.cellsDispensed, 0),
      remainingNeed: Math.max(0, p.needAmount - p.boundTrackCodes.reduce((sum: number, code: any) => sum + code.cellsDispensed, 0))
    })),
    totalRemainingNeed,
    inputCells: inputCells.value
  })
}

// 生命周期
onMounted(() => {
  // Modal模式下不自动加载数据，在open方法中加载
})

// 监听选中药品变化，自动聚焦输入框
watch(selectedDrugKey, () => {
  focusInput()
})

// 监听选中患者变化，自动更新拆零数量
watch(selectedPatientIds, () => {
  updateInputCells()
}, { deep: true })

// 组件生命周期
onMounted(() => {
  // 启动队列监控
  const stopMonitoring = startMonitoring()

  // 组件卸载时停止监控
  onUnmounted(() => {
    stopMonitoring()
    clearQueue()
  })
})

// 暴露方法给外部调用
defineExpose({
  open
})
</script>
