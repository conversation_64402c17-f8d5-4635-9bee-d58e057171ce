<!--病区绑定追溯码组件-->
<template>
  <!-- Modal弹窗 -->
  <Modal
    v-model:open="modalVisible"
    :title="modalTitle"
    :width="modalWidth"
    :maskClosable="false"
    :keyboard="false"
    :style="{ top: '20px', maxHeight: 'calc(100vh - 40px)' }"
    @cancel="handleCancel"
  >
    <template #footer>
      <Space>
        <Button @click="handleCancel">取消</Button>
        <Button type="primary" @click="handleOk">确定</Button>
      </Space>
    </template>

    <div class="section-track-code-container">
      <!-- 上部：日期选择器 -->
      <div class="section-track-code-header">
        <Form layout="inline" class="compact-form">
          <FormItem label="日期" style="margin-right: 12px;">
            <DatePicker
              v-model:value="selectedDate"
              format="YYYY-MM-DD"
              placeholder="选择日期"
              size="small"
              @change="handleDateChange"
              style="width: 140px;"
            />
          </FormItem>
          <FormItem>
            <Button type="primary" size="small" :loading="loading" @click="loadData">
              查询
            </Button>
          </FormItem>
        </Form>
      </div>

      <!-- 中部：核心绑码区域 -->
      <div class="main-content">
        <Row :gutter="16">
          <!-- 左侧：药品卡片列表 -->
          <Col :span="8">
            <div class="table-section">
              <div class="table-header">
                <h4>药品列表</h4>
                <!-- 搜索框 -->
                <div class="search-inline">
                  <Input
                    v-model:value="searchKeyword"
                    placeholder="搜索药品名称、编码..."
                    size="small"
                    allowClear
                    style="width: 200px;"
                  >
                    <template #prefix>
                      <span style="color: #bfbfbf;">🔍</span>
                    </template>
                  </Input>
                </div>
                <span class="table-count">共 {{ filteredDrugCards.length }}/{{ drugCards.length }} 项</span>
              </div>
              <div class="drug-list-container">
                <div v-if="drugCards.length === 0 && !loading" class="empty-state">
                  <Empty description="暂无数据" />
                </div>
                <div v-else class="drug-cards-wrapper">
                  <div
                    v-for="drug in sortedDrugCards"
                    :key="drug.keyStr"
                    :class="[
                      'drug-card-compact',
                      { 'drug-card-compact--selected': selectedDrugKey === drug.keyStr },
                      { 'drug-card-compact--completed': drug.isCompleted }
                    ]"
                    @click="handleSelectDrug(drug)"
                    :title="`artId: ${drug.artId}, 药品: ${drug.artName}, 规格: ${drug.artSpec}, 厂家: ${drug.producer}`"
                  >
                    <!-- 药房预绑定追溯码提示 - 右上角 -->
                    <div v-if="drug.pharmacyTrackCodes.length > 0" class="drug-card-compact__pharmacy-corner">
                      <Tag color="blue" size="small">
                        {{ getPharmacyCodeDisplay(drug) }}
                      </Tag>
                    </div>

                    <div class="drug-card-compact__main">
                      <div class="drug-card-compact__name">{{ drug.artName }} {{ drug.artSpec }}</div>
                      <div class="drug-card-compact__producer">{{ drug.producer }}</div>
                    </div>
                    <div class="drug-card-compact__info">
                      <div class="drug-card-compact__type">
                        <Tag :color="drug.unitType === 1 ? 'orange' : 'blue'" size="small">
                          {{ drug.unitType === 1 ? '拆零' : '整包' }}
                        </Tag>
                      </div>
                      <div class="drug-card-compact__amount">{{ drug.totalAmount }}{{ drug.unitName }}</div>
                      <div class="drug-card-compact__bound">{{ getBoundAmount(drug) }}/{{ drug.totalAmount }}</div>
                      <div class="drug-card-compact__status">
                        <Tag :color="drug.isCompleted ? 'green' : 'orange'" size="small">
                          {{ drug.isCompleted ? '完成' : '进行中' }}
                        </Tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Col>

          <!-- 右侧：患者列表和操作区域 -->
          <Col :span="16">
            <!-- 药房追溯码列表 -->
            <div v-if="selectedDrug && selectedDrug.pharmacyTrackCodes.length > 0" class="pharmacy-codes-section">
              <div class="table-header">
                <h4>药房追溯码</h4>
                <Button type="primary" size="small" @click="handleAutoAllocate">一键分配</Button>
              </div>
              <div class="pharmacy-codes-list">
                <div
                  v-for="(code, index) in selectedDrug.pharmacyTrackCodes"
                  :key="index"
                  class="pharmacy-code-item"
                >
                  <span class="code-text">
                    <span :class="getPharmacyCodePrefixClass(code.trackCode)">{{ code.trackCode.slice(0, 7) }}</span><span class="code-suffix">{{ code.trackCode.slice(7) }}</span>
                  </span>
                  <span class="code-amount">{{ selectedDrug.unitType === 2 ? '1包' : `${code.curTotalCells || 0}支` }}</span>
                  <Button size="small" @click="handleManualAllocate(code)">分配</Button>
                </div>
              </div>
            </div>

            <div class="table-section">
              <div class="table-header">
                <h4 v-if="selectedDrug">{{ selectedDrug.artName }} - 患者列表</h4>
                <h4 v-else>患者列表</h4>
                <div v-if="selectedDrug" class="table-header-right">
                  <span class="table-count">共 {{ selectedDrug.patients.length }} 位患者</span>
                </div>
              </div>
              <div class="patient-list-container">
                <div v-if="!selectedDrug" class="empty-state">
                  <Empty description="请选择药品" />
                </div>
                <div v-else class="patients-grid">
                  <!-- 患者卡片网格布局 -->
                  <div
                    v-for="patient in sortedPatients"
                    :key="patient.visitId"
                    :class="[
                      'patient-card-compact',
                      { 'patient-card-compact--selected': selectedPatientIds.includes(patient.visitId) },
                      { 'patient-card-compact--completed': patient.isCompleted }
                    ]"
                    @click="handleSelectPatient(patient)"
                  >
                    <div class="patient-card-compact__header">
                      <span class="patient-card-compact__name">{{ patient.bedNo }} {{ patient.patientName }}</span>
                      <span class="patient-card-compact__amount">{{ patient.needAmount }}{{ selectedDrug.unitName }}</span>
                    </div>

                    <!-- 已绑定的追溯码 -->
                    <div v-if="patient.boundTrackCodes.length > 0" class="patient-card-compact__codes">
                      <div
                        v-for="(code, index) in patient.boundTrackCodes"
                        :key="index"
                        class="patient-card-compact__code-item"
                      >
                        <span class="code-text">
                          <span :class="getTrackCodePrefixClass(patient, code.trackCode)">{{ code.trackCode.slice(0, 7) }}</span><span class="code-suffix">{{ code.trackCode.slice(7) }}</span>
                        </span>
                        <span class="code-amount">{{ code.cellsDispensed }}</span>
                        <span
                          v-if="!(code as any).isUsed"
                          class="code-unbind"
                          @click.stop="handleUnbindTrackCode(patient, index)"
                          title="解绑"
                        >
                          ✕
                        </span>
                        <span v-else class="code-used" title="已使用">✓</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      <!-- 下部：追溯码录入区域 -->
      <div class="section-track-code-input-area">
        <div class="current-art-info">
<!--          <div v-if="selectedDrug" style="font-size: large;">-->
<!--            {{ selectedDrug.artName }} {{ selectedDrug.artSpec }} {{ selectedDrug.producer }}-->
<!--          </div>-->
<!--          <div v-else style="font-size: large; color: #999;">-->
<!--            请选择药品和患者-->
<!--          </div>-->
          <div class="track-code-form-container">
            <Form
              layout="inline"
              class="track-code-form"
              autocomplete="off"
            >
              <FormItem label="拆零" v-if="selectedDrug && (selectedDrug.unitType === 1 || isDisassembledDrug)">
                <Checkbox v-model:checked="isDisassembled" />
              </FormItem>
              <FormItem label="拆零数量" v-if="selectedDrug && isDisassembled">
                <InputNumber
                  v-model:value="inputCells"
                  :min="1"
                  :max="999"
                  placeholder="拆零数量"
                  style="width: 120px;"
                  @press-enter="focusInput"
                />
              </FormItem>
              <FormItem label="追溯码">
                <Input
                  ref="trackCodeInputRef"
                  v-model:value="trackCodeInput"
                  placeholder="请扫描或输入追溯码"
                  style="width: 400px"
                  autocomplete="off"
                  @press-enter="handleAddTrackCode"
                />
              </FormItem>
            </Form>
            <div class="action-buttons-container">
              <!-- 拆零上报按钮组 -->
              <div class="button-group" v-if="selectedDrug && selectedDrug.unitType === 2">
                <Button
                  v-if="!isDisassembledDrug"
                  danger
                  @click="handleSetDisassembled"
                  :disabled="!selectedDrug"
                >拆零上报</Button>
                <Button
                  v-if="isDisassembledDrug"
                  @click="handleClearDisassembled"
                  :disabled="!selectedDrug"
                >取消拆零上报</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { Button, Checkbox, Col, DatePicker, Empty, Form, FormItem, Input, InputNumber, Modal, Row, Space, Tag, message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  useDatePicker,
  useDataLoader,
  useDrugCards,
  useSelection,
  useTrackCodeInput,
  useAutoAllocation,
  useTrackCodeBinding,
  useSmartAllocation,
  useApiCalls
} from './composables'
import type { DrugCard, PatientCard, TrackCodeItem } from './types'
import './styles.less'

// 组件属性
interface Props {
  sectionId: number // 病区ID
  modalWidth?: number | string // Modal宽度
}

const props = withDefaults(defineProps<Props>(), {
  sectionId: 38,
  modalWidth: 1500
})

// 组件事件
const emit = defineEmits<{
  success: [data: any]
  cancel: []
}>()

// 使用组合式函数
const { selectedDate, formatDate } = useDatePicker()
const { loading, rawData, loadData: loadRawData } = useDataLoader(props.sectionId)
const { drugCards, processDrugCards } = useDrugCards()
const { selectedDrugKey, selectedPatientId, selectDrug, selectPatient } = useSelection()
const { trackCodeInput, trackCodeInputRef, focusInput, validateTrackCode, clearInput } = useTrackCodeInput()
const { autoAllocatePharmacyTrackCodes } = useAutoAllocation()
const { bindTrackCodeToFeeDetail, unbindTrackCode } = useTrackCodeBinding()
const { smartAllocateTrackCode } = useSmartAllocation()
const { callSectionFeeTrackCodeSummaryApi, setDisassembled, clearDisassembled } = useApiCalls()

// 本地状态
const modalVisible = ref(false)
const modalTitle = ref('病区绑定追溯码')
const animatingDrugs = ref(new Set<string>())
const currentTrackCodes = ref<TrackCodeItem[]>([])
const inputCells = ref(1)
const isDisassembled = ref(false)
const previousDate = ref<string>('')
const selectedPatientIds = ref<string[]>([]) // 多选患者ID列表
const searchKeyword = ref('') // 搜索关键词

// 计算属性
const selectedDrug = computed(() => {
  return drugCards.value.find(drug => drug.keyStr === selectedDrugKey.value)
})

// 过滤后的药品列表
const filteredDrugCards = computed(() => {
  if (!searchKeyword.value.trim()) {
    return drugCards.value
  }

  const keyword = searchKeyword.value.toLowerCase()
  return drugCards.value.filter(drug => {
    // 搜索药品名称、编码等字段
    const qsCode1 = (drug as any).qsCode1 || ''
    const qsCode2 = (drug as any).qsCode2 || ''

    return (
      drug.artName.toLowerCase().includes(keyword) ||
      qsCode1.toLowerCase().includes(keyword) ||
      qsCode2.toLowerCase().includes(keyword) ||
      drug.artId.toString().includes(keyword) ||
      drug.artSpec.toLowerCase().includes(keyword) ||
      drug.producer.toLowerCase().includes(keyword)
    )
  })
})

const sortedDrugCards = computed(() => {
  // 对过滤后的药品进行排序
  return [...filteredDrugCards.value].sort((a, b) => {
    // 1. 已绑定的放到队列尾部
    if (a.isCompleted && !b.isCompleted) return 1
    if (!a.isCompleted && b.isCompleted) return -1

    // 2. 需求量大的优先（未完成的药品中）
    if (!a.isCompleted && !b.isCompleted) {
      return b.totalAmount - a.totalAmount
    }

    return 0
  })
})

const sortedPatients = computed(() => {
  if (!selectedDrug.value) return []
  // 按床号排序，未完成的患者排在前面
  return [...selectedDrug.value.patients].sort((a, b) => {
    // 先按完成状态排序
    if (a.isCompleted && !b.isCompleted) return 1
    if (!a.isCompleted && b.isCompleted) return -1

    // 再按床号排序
    const bedNoA = parseInt(a.bedNo.replace(/[^\d]/g, '')) || 0
    const bedNoB = parseInt(b.bedNo.replace(/[^\d]/g, '')) || 0
    return bedNoA - bedNoB
  })
})

// 计算已绑定数量
const getBoundAmount = (drug: any) => {
  return drug.patients.reduce((total: number, patient: any) => {
    return total + patient.boundTrackCodes.reduce((sum: number, code: any) => sum + code.cellsDispensed, 0)
  }, 0)
}

// 获取追溯码前7位的样式类
const getTrackCodePrefixClass = (patient: any, trackCode: string) => {
  if (!selectedDrug.value) return 'code-prefix-default'

  // 获取当前药品所有患者的所有追溯码前7位
  const allPrefixes = selectedDrug.value.patients.flatMap((p: any) =>
    p.boundTrackCodes.map((code: any) => code.trackCode.slice(0, 7))
  )

  // 去重
  const uniquePrefixes = [...new Set(allPrefixes)]

  // 如果只有一种前7位，使用绿色；如果有多种，使用橙色
  if (uniquePrefixes.length === 1) {
    return 'code-prefix-consistent' // 绿色
  } else {
    return 'code-prefix-inconsistent' // 橙色
  }
}

// 获取药房码显示文本
const getPharmacyCodeDisplay = (drug: any) => {
  const codeCount = drug.pharmacyTrackCodes.length

  if (drug.unitType === 2 && !(drug as any).isDisassembled) {
    // 整包类：显示"药房X码"
    return `药房${codeCount}码`
  } else {
    // 拆零类：显示"X码Y支"
    const totalCells = drug.pharmacyTrackCodes.reduce((sum: number, code: any) =>
      sum + (code.curTotalCells || 0), 0
    )
    return `${codeCount}码${totalCells}支`
  }
}

// 获取药房追溯码前7位的样式类
const getPharmacyCodePrefixClass = (trackCode: string) => {
  // 药房追溯码默认使用绿色
  return 'code-prefix-consistent'
}

// 手动分配药房追溯码
const handleManualAllocate = (code: any) => {
  if (!selectedDrug.value || selectedPatientIds.value.length === 0) {
    message.warning('请先选择患者')
    return
  }

  // 这里实现手动分配逻辑
  message.info('手动分配功能待实现')
}

// 计算属性：当前药品是否为拆零上报
const isDisassembledDrug = computed(() => {
  if (!selectedDrug.value) return false
  // 这里需要根据实际的数据结构来判断
  return selectedDrug.value.unitType === 1 || (selectedDrug.value as any).isDisassembled === 1
})



// 方法
// Modal相关方法
const open = () => {
  modalVisible.value = true
  // 设置当前日期
  selectedDate.value = dayjs()
  previousDate.value = dayjs().format('YYYY-MM-DD')

  // 打开时自动加载当日数据
  setTimeout(() => {
    loadTodayData()
    focusInput()
  }, 100)
}

// 加载当日数据
const loadTodayData = async () => {
  try {
    const bsnDate = dayjs().format('YYYYMMDD')
    const response = await callSectionFeeTrackCodeSummaryApi({
      sectionId: props.sectionId,
      bsnDate
    })
    processDrugCards(response.artSummaryList, response.pharmacyTrackCodeSums)
    message.success('数据加载成功')
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
    // 如果加载失败，使用模拟数据
    loadData()
  }
}

const handleOk = () => {
  // 确定操作，可以在这里进行数据保存等操作
  emit('success', {
    drugCards: drugCards.value,
    selectedDrug: selectedDrug.value
  })
  modalVisible.value = false
}

const handleCancel = () => {
  emit('cancel')
  modalVisible.value = false
}

const loadData = async () => {
  try {
    const data = await loadRawData(formatDate.value)
    if (data) {
      processDrugCards(data.artSummaryList, data.pharmacyTrackCodeSums)
      message.success('数据加载成功')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

const handleDateChange = async (date: any, dateString: string) => {
  console.log('日期切换:', previousDate.value, '->', dateString)

  // 如果是初始化，直接设置
  if (!previousDate.value) {
    previousDate.value = dateString
    return
  }

  // 如果日期没有变化，直接返回
  if (previousDate.value === dateString) {
    return
  }

  // 检查是否有新录入的追溯码（未保存的绑定）
  const hasNewTrackCodes = currentTrackCodes.value.length > 0 ||
    drugCards.value.some(drug =>
      drug.patients.some(patient =>
        patient.boundTrackCodes.some(code =>
          !(code as any).isUsed && !(code as any).isFromServer
        )
      )
    )

  console.log('检查新追溯码:', hasNewTrackCodes)

  if (hasNewTrackCodes) {
    // 弹出确认对话框
    const confirmed = await new Promise((resolve) => {
      Modal.confirm({
        title: '确认切换日期',
        content: '当前日期录入的追溯码不会保存，是否切换日期？',
        okText: '确认切换',
        cancelText: '取消',
        onOk: () => resolve(true),
        onCancel: () => resolve(false)
      })
    })

    if (!confirmed) {
      // 恢复到之前的日期
      selectedDate.value = dayjs(previousDate.value)
      return
    }
  }

  // 记录当前日期
  previousDate.value = dateString

  // 清空当前状态
  selectedDrugKey.value = ''
  selectedPatientIds.value = []
  currentTrackCodes.value = []
  trackCodeInput.value = ''

  // 调用真实API加载新日期的数据
  if (dateString) {
    try {
      const bsnDate = dayjs(dateString).format('YYYYMMDD')
      console.log('调用API加载数据:', bsnDate)
      const response = await callSectionFeeTrackCodeSummaryApi({
        sectionId: props.sectionId,
        bsnDate
      })
      processDrugCards(response.artSummaryList, response.pharmacyTrackCodeSums)
      message.success('数据加载成功')
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
      // 如果加载失败，使用模拟数据
      loadData()
    }
  }
}

// 选择药品处理
const handleSelectDrug = (drug: any) => {
  selectedDrugKey.value = drug.keyStr

  // 根据药品类型自动选择患者
  if (drug.unitType === 2 && !(drug as any).isDisassembled) {
    // 整包类：只能选择一个患者，自动选择第一个未绑码的患者
    const uncompletedPatient = drug.patients.find((p: any) => !p.isCompleted)
    selectedPatientIds.value = uncompletedPatient ? [uncompletedPatient.visitId] : []

    // 整包类不自动勾选拆零标记
    isDisassembled.value = false
    inputCells.value = 1
  } else {
    // 拆零类：多选所有未绑码的患者
    const uncompletedPatients = drug.patients.filter((p: any) => !p.isCompleted)
    selectedPatientIds.value = uncompletedPatients.map((p: any) => p.visitId)

    // 自动勾选拆零标记
    isDisassembled.value = true

    // 计算拆零数量（所有选中患者的需求量总和）
    const totalNeed = uncompletedPatients.reduce((sum: number, p: any) => {
      const currentBound = p.boundTrackCodes.reduce((codeSum: number, code: any) => codeSum + code.cellsDispensed, 0)
      return sum + (p.needAmount - currentBound)
    }, 0)
    inputCells.value = totalNeed
  }

  // 聚焦输入框
  setTimeout(() => {
    focusInput()
  }, 100)
}

// 选择患者处理
const handleSelectPatient = (patient: any) => {
  if (!selectedDrug.value) return

  if (selectedDrug.value.unitType === 2 && !(selectedDrug.value as any).isDisassembled) {
    // 整包类：单选
    selectedPatientIds.value = [patient.visitId]
  } else {
    // 拆零类：多选
    const index = selectedPatientIds.value.indexOf(patient.visitId)
    if (index > -1) {
      selectedPatientIds.value.splice(index, 1)
    } else {
      selectedPatientIds.value.push(patient.visitId)
    }

    // 重新计算拆零数量
    const selectedPatients = selectedDrug.value.patients.filter((p: any) =>
      selectedPatientIds.value.includes(p.visitId)
    )
    const totalNeed = selectedPatients.reduce((sum: number, p: any) => {
      const currentBound = p.boundTrackCodes.reduce((codeSum: number, code: any) => codeSum + code.cellsDispensed, 0)
      return sum + (p.needAmount - currentBound)
    }, 0)
    inputCells.value = totalNeed
  }
}

const handleAutoAllocate = () => {
  if (selectedDrug.value) {
    autoAllocatePharmacyTrackCodes(selectedDrug.value)
    checkDrugCompletion(selectedDrug.value)
  }
}

const handleAddTrackCode = async () => {
  const code = trackCodeInput.value.trim()
  if (!code) return

  if (!validateTrackCode(code)) {
    clearInput()
    return
  }

  // 检查是否选择了药品和患者
  if (!selectedDrug.value) {
    message.warning('请先选择药品')
    clearInput()
    return
  }

  if (!selectedPatientId.value) {
    message.warning('请先选择患者')
    clearInput()
    return
  }

  // 获取选中的患者
  const selectedPatient = selectedDrug.value.patients.find(p => p.visitId === selectedPatientId.value)
  if (!selectedPatient) {
    message.error('未找到选中的患者')
    clearInput()
    return
  }

  // 检查患者是否还需要绑定追溯码
  const currentBound = selectedPatient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
  const stillNeed = selectedPatient.needAmount - currentBound

  if (stillNeed <= 0) {
    message.info('该患者已完成绑定')
    clearInput()
    return
  }

  // 计算绑定数量
  const bindAmount = isDisassembled.value ?
    Math.min(inputCells.value, stillNeed) :
    Math.min(1, stillNeed)

  // 使用智能分配算法
  if (selectedDrug.value.unitType === 1 && isDisassembled.value) {
    // 拆零类药品使用智能分配
    const allocations = smartAllocateTrackCode(
      selectedDrug.value,
      code,
      isDisassembled.value,
      inputCells.value
    )

    // 执行分配
    for (const allocation of allocations) {
      allocation.patient.boundTrackCodes.push({
        trackCode: code,
        cellsDispensed: allocation.allocatedAmount,
        cellsRemain: 0,
        isDisassembled: isDisassembled.value
      })

      // 更新患者完成状态
      const newBoundTotal = allocation.patient.boundTrackCodes.reduce((sum, tc) => sum + tc.cellsDispensed, 0)
      allocation.patient.isCompleted = newBoundTotal >= allocation.patient.needAmount
    }
  } else {
    // 整包类药品直接绑定到选中患者
    selectedPatient.boundTrackCodes.push({
      trackCode: code,
      cellsDispensed: bindAmount,
      cellsRemain: 0,
      isDisassembled: isDisassembled.value
    })

    // 更新患者完成状态
    const newBoundTotal = selectedPatient.boundTrackCodes.reduce((sum, tc) => sum + tc.cellsDispensed, 0)
    selectedPatient.isCompleted = newBoundTotal >= selectedPatient.needAmount
  }

  // 更新药品完成状态
  checkDrugCompletion(selectedDrug.value)

  clearInput()
  focusInput()
  message.success('追溯码绑定成功')
}

const removeTrackCode = (index: number) => {
  currentTrackCodes.value.splice(index, 1)
}

// 设置拆零上报
const handleSetDisassembled = async () => {
  if (!selectedDrug.value) return
  const success = await setDisassembled(selectedDrug.value.artId)
  if (success) {
    message.success('已设置为拆零上报')
    // 更新本地状态
    ;(selectedDrug.value as any).isDisassembled = 1
  }
}

// 取消拆零上报
const handleClearDisassembled = async () => {
  if (!selectedDrug.value) return
  const success = await clearDisassembled(selectedDrug.value.artId)
  if (success) {
    message.success('已取消拆零上报')
    // 更新本地状态
    ;(selectedDrug.value as any).isDisassembled = 0
  }
}



const handleUnbindTrackCode = async (patient: PatientCard, index: number) => {
  const trackCode = patient.boundTrackCodes[index]
  if ((trackCode as any).isUsed) {
    message.warning('该追溯码已使用，无法解绑')
    return
  }

  try {
    // 调用解绑API（这里需要实际的费用明细信息）
    // await unbindTrackCode({
    //   execSeqid: 'xxx',
    //   lineNo: 1,
    //   trackCode: trackCode.trackCode
    // })

    patient.boundTrackCodes.splice(index, 1)

    // 更新患者完成状态
    const boundTotal = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
    patient.isCompleted = boundTotal >= patient.needAmount

    // 更新药品完成状态
    if (selectedDrug.value) {
      checkDrugCompletion(selectedDrug.value)
    }

    message.success('追溯码解绑成功')
  } catch (error) {
    console.error('解绑失败:', error)
    message.error('解绑失败')
  }
}

const checkDrugCompletion = (drug: DrugCard) => {
  const allCompleted = drug.patients.every(p => p.isCompleted)
  const wasCompleted = drug.isCompleted
  drug.isCompleted = allCompleted

  // 如果从未完成变为完成，触发动画
  if (!wasCompleted && allCompleted) {
    animatingDrugs.value.add(drug.keyStr)
    setTimeout(() => {
      animatingDrugs.value.delete(drug.keyStr)
    }, 800)
  }
}

// 生命周期
onMounted(() => {
  // Modal模式下不自动加载数据，在open方法中加载
})

// 监听选中药品变化，自动聚焦输入框
watch(selectedDrugKey, () => {
  focusInput()
})

// 暴露方法给外部调用
defineExpose({
  open
})
</script>
