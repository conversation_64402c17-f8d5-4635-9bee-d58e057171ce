<!--病区绑定追溯码组件-->
<template>
  <!-- 触发按钮 -->
  <a-button v-if="showButton" type="primary" @click="open">
    {{ buttonText || '病区绑定追溯码' }}
  </a-button>

  <!-- Modal弹窗 -->
  <a-modal
    v-model:open="modalVisible"
    :title="modalTitle"
    :width="modalWidth"
    :maskClosable="false"
    :keyboard="false"
    :style="{ top: '20px', maxHeight: 'calc(100vh - 40px)' }"
    @cancel="handleCancel"
  >
    <template #footer>
      <a-space>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">确定</a-button>
      </a-space>
    </template>

    <div class="section-track-code-container">
      <!-- 上部：日期选择器 -->
      <div class="section-track-code-header">
        <a-form layout="inline">
          <a-form-item label="选择日期">
            <a-date-picker
              v-model:value="selectedDate"
              format="YYYY-MM-DD"
              placeholder="请选择日期"
              @change="handleDateChange"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" :loading="loading" @click="loadData">
              查询数据
            </a-button>
          </a-form-item>
        </a-form>
      </div>

      <!-- 中部：核心绑码区域 -->
      <div class="main-content">
        <a-row :gutter="16">
          <!-- 左侧：药品卡片列表 -->
          <a-col :span="8">
            <div class="table-section">
              <div class="table-header">
                <h4>药品列表</h4>
                <span class="table-count">共 {{ drugCards.length }} 项</span>
              </div>
              <div class="drug-list-container">
                <div v-if="drugCards.length === 0 && !loading" class="empty-state">
                  <a-empty description="暂无数据" />
                </div>
                <div v-else class="drug-cards-wrapper">
                  <div
                    v-for="drug in sortedDrugCards"
                    :key="drug.keyStr"
                    :class="[
                      'drug-card',
                      { 'drug-card--selected': selectedDrugKey === drug.keyStr },
                      { 'drug-card--completed': drug.isCompleted },
                      { 'drug-card--animating': drug.isCompleted && animatingDrugs.has(drug.keyStr) }
                    ]"
                    @click="selectDrug(drug.keyStr)"
                  >
                    <div class="drug-card__header">
                      <div class="drug-card__title">{{ drug.artName }}</div>
                      <div class="drug-card__status">
                        <span v-if="drug.isCompleted" style="color: #52c41a;">✓ 已完成</span>
                        <span v-else style="color: #faad14;">进行中</span>
                      </div>
                    </div>
                    <div class="drug-card__info">
                      <div>规格：{{ drug.artSpec }}</div>
                      <div>厂家：{{ drug.producer }}</div>
                      <div>类型：{{ drug.unitType === 1 ? '拆零' : '整包' }}</div>
                    </div>
                    <div class="drug-card__amount">
                      需求量：{{ drug.totalAmount }} {{ drug.unitName }}
                    </div>
                    <!-- 药房预绑定追溯码提示 -->
                    <div v-if="drug.pharmacyTrackCodes.length > 0" style="margin-top: 8px;">
                      <a-tag color="blue" size="small">
                        药房预绑定 {{ drug.pharmacyTrackCodes.length }} 个追溯码
                      </a-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-col>

          <!-- 右侧：患者列表和操作区域 -->
          <a-col :span="16">
            <div class="table-section">
              <div class="table-header">
                <h4 v-if="selectedDrug">{{ selectedDrug.artName }} - 患者列表</h4>
                <h4 v-else>患者列表</h4>
                <div v-if="selectedDrug" class="table-header-right">
                  <!-- 一键分配按钮 -->
                  <a-button
                    v-if="selectedDrug.pharmacyTrackCodes.length > 0"
                    type="primary"
                    class="auto-allocate-btn"
                    @click="handleAutoAllocate"
                  >
                    一键分配
                  </a-button>
                  <span class="table-count">共 {{ selectedDrug.patients.length }} 位患者</span>
                </div>
              </div>
              <div class="patient-list-container">
                <div v-if="!selectedDrug" class="empty-state">
                  <a-empty description="请选择药品" />
                </div>
                <div v-else class="patients-wrapper">
                  <!-- 患者卡片列表 -->
                  <div
                    v-for="patient in sortedPatients"
                    :key="patient.visitId"
                    :class="[
                      'patient-card',
                      { 'patient-card--selected': selectedPatientId === patient.visitId },
                      { 'patient-card--completed': patient.isCompleted }
                    ]"
                    @click="selectPatient(patient.visitId)"
                  >
                    <div class="patient-card__header">
                      <div>
                        <span class="patient-card__name">{{ patient.patientName }}</span>
                        <span class="patient-card__bed">{{ patient.bedNo }}</span>
                      </div>
                      <div class="patient-card__amount">
                        需求：{{ patient.needAmount }} {{ selectedDrug.unitName }}
                      </div>
                    </div>

                    <!-- 已绑定的追溯码 -->
                    <div v-if="patient.boundTrackCodes.length > 0" class="patient-card__track-codes">
                      <div
                        v-for="(code, index) in patient.boundTrackCodes"
                        :key="index"
                        class="patient-card__track-code-item"
                      >
                        <span class="track-code">{{ code.trackCode }}</span>
                        <span class="amount">{{ code.cellsDispensed }}</span>
                        <span
                          v-if="!(code as any).isUsed"
                          class="unbind-btn"
                          @click.stop="handleUnbindTrackCode(patient, index)"
                        >
                          解绑
                        </span>
                        <span v-else style="color: #52c41a; font-size: 12px;">已使用</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 下部：追溯码录入区域 -->
      <div class="track-code-input-area">
        <div class="current-art-info">
          <div v-if="selectedDrug" style="font-size: large;">
            {{ selectedDrug.artName }} {{ selectedDrug.artSpec }} {{ selectedDrug.producer }}
          </div>
          <div v-else style="font-size: large; color: #999;">
            请选择药品和患者
          </div>
          <div class="track-code-form-container">
            <a-form
              layout="inline"
              class="track-code-form"
              autocomplete="off"
            >
              <a-form-item label="拆零" v-if="selectedDrug && selectedDrug.unitType === 1">
                <a-checkbox v-model:checked="isDisassembled" />
              </a-form-item>
              <a-form-item label="拆零数量" v-if="selectedDrug && isDisassembled">
                <a-input-number
                  v-model:value="inputCells"
                  :min="1"
                  :max="999"
                  placeholder="拆零数量"
                  style="width: 120px;"
                  @press-enter="focusInput"
                />
              </a-form-item>
              <a-form-item label="追溯码">
                <a-input
                  ref="trackCodeInputRef"
                  v-model:value="trackCodeInput"
                  placeholder="请扫描或输入追溯码"
                  style="width: 400px"
                  autocomplete="off"
                  @press-enter="handleAddTrackCode"
                />
              </a-form-item>
            </a-form>
            <div class="action-buttons-container">
              <div class="button-group">
                <a-button type="primary" @click="handleAddTrackCode">添加追溯码</a-button>
              </div>
              <div class="button-group">
                <a-button @click="clearAllTrackCodes" danger>全部清除</a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  useDatePicker,
  useDataLoader,
  useDrugCards,
  useSelection,
  useTrackCodeInput,
  useAutoAllocation,
  useTrackCodeBinding,
  useSmartAllocation
} from './composables'
import type { DrugCard, PatientCard, TrackCodeItem } from './types'
import './styles.less'

// 组件属性
interface Props {
  sectionId: number // 病区ID
  modalWidth?: number | string // Modal宽度
  showButton?: boolean // 是否显示触发按钮
  buttonText?: string // 按钮文本
}

const props = withDefaults(defineProps<Props>(), {
  sectionId: 38,
  modalWidth: 1400,
  showButton: true,
  buttonText: ''
})

// 组件事件
const emit = defineEmits<{
  success: [data: any]
  cancel: []
}>()

// 使用组合式函数
const { selectedDate, formatDate } = useDatePicker()
const { loading, rawData, loadData: loadRawData } = useDataLoader(props.sectionId)
const { drugCards, processDrugCards } = useDrugCards()
const { selectedDrugKey, selectedPatientId, selectDrug, selectPatient } = useSelection()
const { trackCodeInput, trackCodeInputRef, focusInput, validateTrackCode, clearInput } = useTrackCodeInput()
const { autoAllocatePharmacyTrackCodes } = useAutoAllocation()
const { bindTrackCodeToFeeDetail, unbindTrackCode } = useTrackCodeBinding()
const { smartAllocateTrackCode } = useSmartAllocation()

// 本地状态
const modalVisible = ref(false)
const modalTitle = ref('病区绑定追溯码')
const animatingDrugs = ref(new Set<string>())
const currentTrackCodes = ref<TrackCodeItem[]>([])
const inputCells = ref(1)
const isDisassembled = ref(false)

// 计算属性
const selectedDrug = computed(() => {
  return drugCards.value.find(drug => drug.keyStr === selectedDrugKey.value)
})

const sortedDrugCards = computed(() => {
  // 未完成的药品排在前面，已完成的排在后面
  return [...drugCards.value].sort((a, b) => {
    if (a.isCompleted && !b.isCompleted) return 1
    if (!a.isCompleted && b.isCompleted) return -1
    return 0
  })
})

const sortedPatients = computed(() => {
  if (!selectedDrug.value) return []
  // 未完成的患者排在前面
  return [...selectedDrug.value.patients].sort((a, b) => {
    if (a.isCompleted && !b.isCompleted) return 1
    if (!a.isCompleted && b.isCompleted) return -1
    return 0
  })
})

// 方法
// Modal相关方法
const open = () => {
  modalVisible.value = true
  // 打开时自动加载数据
  setTimeout(() => {
    loadData()
    focusInput()
  }, 100)
}

const handleOk = () => {
  // 确定操作，可以在这里进行数据保存等操作
  emit('success', {
    drugCards: drugCards.value,
    selectedDrug: selectedDrug.value
  })
  modalVisible.value = false
}

const handleCancel = () => {
  emit('cancel')
  modalVisible.value = false
}

const loadData = async () => {
  try {
    const data = await loadRawData(formatDate.value)
    if (data) {
      processDrugCards(data.artSummaryList, data.pharmacyTrackCodeSums)
      message.success('数据加载成功')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

const handleDateChange = () => {
  // 日期改变时可以自动加载数据
  // loadData()
}

const handleAutoAllocate = () => {
  if (selectedDrug.value) {
    autoAllocatePharmacyTrackCodes(selectedDrug.value)
    checkDrugCompletion(selectedDrug.value)
  }
}

const handleAddTrackCode = async () => {
  const code = trackCodeInput.value.trim()
  if (!code) return

  if (!validateTrackCode(code)) {
    clearInput()
    return
  }

  // 检查是否选择了药品和患者
  if (!selectedDrug.value) {
    message.warning('请先选择药品')
    clearInput()
    return
  }

  if (!selectedPatientId.value) {
    message.warning('请先选择患者')
    clearInput()
    return
  }

  // 获取选中的患者
  const selectedPatient = selectedDrug.value.patients.find(p => p.visitId === selectedPatientId.value)
  if (!selectedPatient) {
    message.error('未找到选中的患者')
    clearInput()
    return
  }

  // 检查患者是否还需要绑定追溯码
  const currentBound = selectedPatient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
  const stillNeed = selectedPatient.needAmount - currentBound

  if (stillNeed <= 0) {
    message.info('该患者已完成绑定')
    clearInput()
    return
  }

  // 计算绑定数量
  const bindAmount = isDisassembled.value ?
    Math.min(inputCells.value, stillNeed) :
    Math.min(1, stillNeed)

  // 使用智能分配算法
  if (selectedDrug.value.unitType === 1 && isDisassembled.value) {
    // 拆零类药品使用智能分配
    const allocations = smartAllocateTrackCode(
      selectedDrug.value,
      code,
      isDisassembled.value,
      inputCells.value
    )

    // 执行分配
    for (const allocation of allocations) {
      allocation.patient.boundTrackCodes.push({
        trackCode: code,
        cellsDispensed: allocation.allocatedAmount,
        cellsRemain: 0,
        isDisassembled: isDisassembled.value
      })

      // 更新患者完成状态
      const newBoundTotal = allocation.patient.boundTrackCodes.reduce((sum, tc) => sum + tc.cellsDispensed, 0)
      allocation.patient.isCompleted = newBoundTotal >= allocation.patient.needAmount
    }
  } else {
    // 整包类药品直接绑定到选中患者
    selectedPatient.boundTrackCodes.push({
      trackCode: code,
      cellsDispensed: bindAmount,
      cellsRemain: 0,
      isDisassembled: isDisassembled.value
    })

    // 更新患者完成状态
    const newBoundTotal = selectedPatient.boundTrackCodes.reduce((sum, tc) => sum + tc.cellsDispensed, 0)
    selectedPatient.isCompleted = newBoundTotal >= selectedPatient.needAmount
  }

  // 更新药品完成状态
  checkDrugCompletion(selectedDrug.value)

  clearInput()
  focusInput()
  message.success('追溯码绑定成功')
}

const removeTrackCode = (index: number) => {
  currentTrackCodes.value.splice(index, 1)
}

const clearAllTrackCodes = () => {
  currentTrackCodes.value = []
  message.info('已清除所有追溯码')
}

const handleUnbindTrackCode = async (patient: PatientCard, index: number) => {
  const trackCode = patient.boundTrackCodes[index]
  if ((trackCode as any).isUsed) {
    message.warning('该追溯码已使用，无法解绑')
    return
  }

  try {
    // 调用解绑API（这里需要实际的费用明细信息）
    // await unbindTrackCode({
    //   execSeqid: 'xxx',
    //   lineNo: 1,
    //   trackCode: trackCode.trackCode
    // })

    patient.boundTrackCodes.splice(index, 1)

    // 更新患者完成状态
    const boundTotal = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
    patient.isCompleted = boundTotal >= patient.needAmount

    // 更新药品完成状态
    if (selectedDrug.value) {
      checkDrugCompletion(selectedDrug.value)
    }

    message.success('追溯码解绑成功')
  } catch (error) {
    console.error('解绑失败:', error)
    message.error('解绑失败')
  }
}

const checkDrugCompletion = (drug: DrugCard) => {
  const allCompleted = drug.patients.every(p => p.isCompleted)
  const wasCompleted = drug.isCompleted
  drug.isCompleted = allCompleted

  // 如果从未完成变为完成，触发动画
  if (!wasCompleted && allCompleted) {
    animatingDrugs.value.add(drug.keyStr)
    setTimeout(() => {
      animatingDrugs.value.delete(drug.keyStr)
    }, 800)
  }
}

// 生命周期
onMounted(() => {
  // Modal模式下不自动加载数据，在open方法中加载
})

// 监听选中药品变化，自动聚焦输入框
watch(selectedDrugKey, () => {
  focusInput()
})

// 暴露方法给外部调用
defineExpose({
  open
})
</script>
