<template>
  <div class="examples-index">
    <div class="header">
      <h1>WholeBox 拆零盒整组件示例集</h1>
      <p class="description">
        WholeBox是一个专业的药品库存拆零盒整组件，支持仓库总库存和批次库存的整包数与拆零数互相转换。
        本示例集展示了组件在不同场景下的使用方法和最佳实践。
      </p>
    </div>

    <div class="examples-grid">
      <!-- 基本用法示例 -->
      <div class="example-card" @click="navigateToExample('Basic')">
        <div class="card-icon">📚</div>
        <h3>基本用法</h3>
        <p>展示WholeBox组件的基本使用方法，包括组件导入、基本配置和简单的拆零盒整操作。</p>
        <div class="card-tags">
          <Tag color="blue">入门</Tag>
          <Tag color="green">基础</Tag>
        </div>
        <div class="card-footer">
          <Button type="link">查看示例 →</Button>
        </div>
      </div>

      <!-- 多场景示例 -->
      <div class="example-card" @click="navigateToExample('MultipleScenarios')">
        <div class="card-icon">🏥</div>
        <h3>多场景应用</h3>
        <p>展示不同类型药品和医疗器械的拆零盒整场景，包括药品、器械、特殊包装和边界情况测试。</p>
        <div class="card-tags">
          <Tag color="purple">场景</Tag>
          <Tag color="orange">实用</Tag>
        </div>
        <div class="card-footer">
          <Button type="link">查看示例 →</Button>
        </div>
      </div>

      <!-- 表格集成示例 -->
      <div class="example-card" @click="navigateToExample('TableDemo')">
        <div class="card-icon">📊</div>
        <h3>表格集成</h3>
        <p>展示如何在数据表格中集成WholeBox组件，实现库存管理系统的完整功能。</p>
        <div class="card-tags">
          <Tag color="cyan">表格</Tag>
          <Tag color="geekblue">集成</Tag>
        </div>
        <div class="card-footer">
          <Button type="link">查看示例 →</Button>
        </div>
      </div>

      <!-- API集成示例 -->
      <div class="example-card" @click="navigateToExample('ApiIntegration')">
        <div class="card-icon">🔗</div>
        <h3>API集成</h3>
        <p>展示WholeBox组件与后端API的集成方式，包括API调用监控、错误处理和性能分析。</p>
        <div class="card-tags">
          <Tag color="red">API</Tag>
          <Tag color="volcano">高级</Tag>
        </div>
        <div class="card-footer">
          <Button type="link">查看示例 →</Button>
        </div>
      </div>
    </div>

    <!-- 快速开始 -->
    <div class="quick-start">
      <h2>🚀 快速开始</h2>
      <div class="quick-start-content">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>安装组件</h4>
            <div class="code-block">
              <code>pnpm add @mh-wm/whole-box</code>
            </div>
          </div>
        </div>

        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>导入组件</h4>
            <div class="code-block">
              <code>import WholeBox from '@mh-wm/whole-box'</code>
            </div>
          </div>
        </div>

        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>使用组件</h4>
            <div class="code-block">
              <code>&lt;WholeBox ref="wholeBoxRef" /&gt;</code>
            </div>
          </div>
        </div>

        <div class="step">
          <div class="step-number">4</div>
          <div class="step-content">
            <h4>调用方法</h4>
            <div class="code-block">
              <code>wholeBoxRef.value.handleSplitPack(record)</code>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 特性介绍 -->
    <div class="features">
      <h2>✨ 核心特性</h2>
      <div class="features-grid">
        <div class="feature-item">
          <div class="feature-icon">🔄</div>
          <h4>双向联动</h4>
          <p>整包数与拆零数自动联动计算，确保数据一致性</p>
        </div>

        <div class="feature-item">
          <div class="feature-icon">✅</div>
          <h4>实时校验</h4>
          <p>实时校验输入数据的准确性，防止数据错误</p>
        </div>

        <div class="feature-item">
          <div class="feature-icon">🔌</div>
          <h4>API集成</h4>
          <p>完整的后端API集成，支持数据持久化</p>
        </div>

        <div class="feature-item">
          <div class="feature-icon">🎨</div>
          <h4>用户友好</h4>
          <p>清晰的界面设计和操作提示，提升用户体验</p>
        </div>

        <div class="feature-item">
          <div class="feature-icon">📱</div>
          <h4>响应式设计</h4>
          <p>适配不同屏幕尺寸，支持移动端使用</p>
        </div>

        <div class="feature-item">
          <div class="feature-icon">🔧</div>
          <h4>高度可配置</h4>
          <p>支持多种配置选项，满足不同业务需求</p>
        </div>
      </div>
    </div>

    <!-- 技术栈 -->
    <div class="tech-stack">
      <h2>🛠️ 技术栈</h2>
      <div class="tech-items">
        <Tag color="blue" class="tech-tag">Vue 3</Tag>
        <Tag color="green" class="tech-tag">TypeScript</Tag>
        <Tag color="orange" class="tech-tag">Ant Design Vue</Tag>
        <Tag color="purple" class="tech-tag">Vite</Tag>
        <Tag color="cyan" class="tech-tag">Less</Tag>
        <Tag color="geekblue" class="tech-tag">ESLint</Tag>
      </div>
    </div>

    <!-- 文档链接 -->
    <div class="documentation">
      <h2>📖 相关文档</h2>
      <div class="doc-links">
        <Button type="primary" @click="openDoc('README.md')">
          📋 使用文档
        </Button>
        <Button @click="openDoc('BUILD.md')">
          🔨 构建文档
        </Button>
        <Button @click="openDoc('API.md')">
          🔗 API文档
        </Button>
        <Button @click="openGitHub">
          🐙 GitHub
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button, Tag, message } from 'ant-design-vue'

// 导航到示例
const navigateToExample = (exampleName: string) => {
  message.info(`正在跳转到 ${exampleName} 示例...`)
  // 这里可以实现路由跳转或者其他导航逻辑
  console.log(`Navigate to: ${exampleName}`)
}

// 打开文档
const openDoc = (docName: string) => {
  message.info(`正在打开 ${docName} 文档...`)
  // 这里可以实现文档打开逻辑
  console.log(`Open document: ${docName}`)
}

// 打开GitHub
const openGitHub = () => {
  message.info('正在跳转到GitHub仓库...')
  // 这里可以实现GitHub跳转逻辑
  console.log('Open GitHub repository')
}
</script>

<style lang="less" scoped>
.examples-index {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px;
}

.header {
  text-align: center;
  margin-bottom: 48px;

  h1 {
    font-size: 32px;
    color: #1890ff;
    margin-bottom: 16px;
  }

  .description {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
  }
}

.examples-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.example-card {
  padding: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #1890ff;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1);
    transform: translateY(-2px);
  }

  .card-icon {
    font-size: 32px;
    margin-bottom: 16px;
  }

  h3 {
    margin-bottom: 12px;
    color: #262626;
    font-size: 18px;
  }

  p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 16px;
  }

  .card-tags {
    margin-bottom: 16px;

    .ant-tag {
      margin-right: 8px;
    }
  }

  .card-footer {
    text-align: right;
  }
}

.quick-start {
  margin-bottom: 48px;
  padding: 32px;
  background-color: #f6f8fa;
  border-radius: 12px;

  h2 {
    text-align: center;
    margin-bottom: 32px;
    color: #1890ff;
  }

  .quick-start-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
  }

  .step {
    display: flex;
    align-items: flex-start;
    gap: 16px;

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: #1890ff;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      flex-shrink: 0;
    }

    .step-content {
      flex: 1;

      h4 {
        margin-bottom: 8px;
        color: #262626;
      }

      .code-block {
        background-color: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 6px;
        padding: 12px;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 14px;
        color: #d73a49;
      }
    }
  }
}

.features {
  margin-bottom: 48px;

  h2 {
    text-align: center;
    margin-bottom: 32px;
    color: #1890ff;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
  }

  .feature-item {
    text-align: center;
    padding: 24px 16px;

    .feature-icon {
      font-size: 32px;
      margin-bottom: 16px;
    }

    h4 {
      margin-bottom: 8px;
      color: #262626;
    }

    p {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

.tech-stack {
  margin-bottom: 48px;

  h2 {
    text-align: center;
    margin-bottom: 24px;
    color: #1890ff;
  }

  .tech-items {
    text-align: center;

    .tech-tag {
      margin: 4px 8px;
      font-size: 14px;
      padding: 4px 12px;
    }
  }
}

.documentation {
  text-align: center;

  h2 {
    margin-bottom: 24px;
    color: #1890ff;
  }

  .doc-links {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;

    .ant-btn {
      min-width: 120px;
    }
  }
}
</style>
