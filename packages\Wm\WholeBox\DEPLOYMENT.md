# WholeBox 组件部署指南

## 📦 组件概述

WholeBox（拆零盒整）是一个专业的药品库存管理组件，用于处理仓库总库存和批次库存的整包数与拆零数转换操作。

### 🎯 核心功能

- ✅ 双向联动计算：整包数与拆零数自动联动
- ✅ 实时数据校验：确保数据准确性和一致性
- ✅ API集成：完整的后端接口集成
- ✅ 用户友好界面：清晰的操作提示和状态反馈
- ✅ 多场景支持：药品、医疗器械等不同类型商品
- ✅ 响应式设计：适配不同屏幕尺寸

## 🚀 快速开始

### 1. 安装组件

```bash
# 使用pnpm安装（推荐）
pnpm add @mh-wm/whole-box

# 使用npm安装
npm install @mh-wm/whole-box

# 使用yarn安装
yarn add @mh-wm/whole-box
```

### 2. 导入组件

```javascript
// 方式1: 默认导入
import WholeBox from '@mh-wm/whole-box'

// 方式2: 命名导入
import { WholeBox } from '@mh-wm/whole-box'

// 方式3: 导入类型
import type { WholeBoxInstance } from '@mh-wm/whole-box'
```

### 3. 基本使用

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <a-button type="primary" @click="handleOpenSplitPack">
      拆零盒整
    </a-button>
    
    <!-- 拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @split-pack-success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// 库存记录数据
const stockRecord = {
  artName: '阿莫西林胶囊',
  artSpec: '0.25g',
  packCells: 10,        // 包装规格：10粒/盒
  cellUnit: '粒',       // 拆零单位
  packUnit: '盒',       // 包装单位
  deptTotalPacks: 5,    // 仓库总库存整包数
  deptTotalCells: 3,    // 仓库总库存拆零数
  totalPacks: 2,        // 批次库存整包数
  totalCells: 8,        // 批次库存拆零数
}

// 打开拆零盒整模态框
const handleOpenSplitPack = () => {
  wholeBoxRef.value?.handleSplitPack(stockRecord)
}

// 操作成功回调
const handleSuccess = (data) => {
  console.log('拆零盒整成功:', data)
}
</script>
```

## 📋 组件API

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| - | - | - | 当前版本无props配置 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| split-pack-success | `(data: any) => void` | 拆零盒整操作成功时触发 |
| split-pack-error | `(error: any) => void` | 拆零盒整操作失败时触发 |

### Methods

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| handleSplitPack | `record: StockRecord` | `void` | 打开拆零盒整模态框 |

### 数据结构

```typescript
interface StockRecord {
  artName: string          // 药品名称
  artSpec: string          // 规格
  producer?: string        // 生产厂家
  packCells: number        // 包装规格数量
  cellUnit: string         // 拆零单位
  packUnit: string         // 包装单位
  deptTotalPacks: number   // 仓库总库存整包数
  deptTotalCells: number   // 仓库总库存拆零数
  totalPacks: number       // 批次库存整包数
  totalCells: number       // 批次库存拆零数
  batchNo?: string         // 批次号
  expDate?: string         // 有效期
}
```

## 🛠️ 开发环境

### 依赖要求

```json
{
  "peerDependencies": {
    "vue": "^3.5.13",
    "ant-design-vue": "^4.2.6",
    "@mh-wm/util": "^1.0.6"
  }
}
```

### 构建命令

```bash
# 构建ES模块
pnpm run build:es

# 构建UMD模块
pnpm run build:umd

# 构建所有格式
pnpm run build:all

# 打包并发布
pnpm publish:component Wm/WholeBox

# 仅构建不发布
pnpm publish:component Wm/WholeBox --no-publish
```

## 📁 项目结构

```
packages/Wm/WholeBox/
├── src/
│   ├── index.vue          # 主组件文件
│   └── index.ts           # 类型定义
├── examples/              # 示例文件
│   ├── index.vue          # 示例索引
│   ├── Basic.vue          # 基础用法
│   ├── MultipleScenarios.vue  # 多场景应用
│   ├── TableDemo.vue      # 表格集成
│   └── ApiIntegration.vue # API集成
├── dist/                  # 构建产物
│   ├── es/index.js        # ES模块
│   ├── umd/index.js       # UMD模块
│   ├── index.d.ts         # 类型声明
│   └── index.css          # 样式文件
├── package.json           # 包配置
├── README.md              # 使用文档
├── BUILD.md               # 构建文档
└── DEPLOYMENT.md          # 部署指南
```

## 🌐 在线示例

### 组件仓库访问

1. **主展示页面**: `/Wm/WholeBox`
2. **组件集合页面**: `/Wm/All` (WholeBox标签页)
3. **测试页面**: `/test/whole-box`

### 示例场景

- **基础用法**: 展示组件的基本使用方法
- **多场景应用**: 药品、器械、特殊包装等场景
- **表格集成**: 在数据表格中集成组件
- **API集成**: API调用监控和错误处理
- **组件方法**: 各种组件方法的调用示例

## 🔧 故障排除

### 常见问题

1. **组件无法导入**
   ```bash
   # 检查依赖是否正确安装
   pnpm list @mh-wm/whole-box
   
   # 重新安装依赖
   pnpm install
   ```

2. **样式显示异常**
   ```javascript
   // 确保导入了样式文件
   import '@mh-wm/whole-box/index.css'
   ```

3. **API调用失败**
   ```javascript
   // 检查@mh-wm/util依赖
   import { splitPackApi } from '@mh-wm/util'
   ```

### 调试模式

```javascript
// 开启调试模式
console.log('WholeBox Debug Mode')

// 监听组件事件
wholeBoxRef.value.$on('split-pack-success', (data) => {
  console.log('Success:', data)
})
```

## 📈 版本历史

- **v1.0.1**: 初始版本，包含基础拆零盒整功能
- **v1.0.0**: 预发布版本

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用内部许可证，仅供公司内部使用。

---

**联系方式**: 如有问题请联系开发团队
**更新时间**: 2024年12月
