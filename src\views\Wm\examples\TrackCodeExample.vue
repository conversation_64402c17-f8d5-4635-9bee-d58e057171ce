<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './TrackCode/Basic.vue'
import JsMethodExample from './TrackCode/JsMethod.vue'
import CustomHotkeyExample from './TrackCode/CustomHotkey.vue'

const { Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <Paragraph>溯源码录入组件，用于录入和管理溯源码信息。</Paragraph>

  <Tabs v-model:activeKey="activeKey">
    <Tabs.TabPane key="basic" tab="基础用法">
      <BasicExample />
    </Tabs.TabPane>
    <Tabs.TabPane key="jsmethod" tab="JS方法调用">
      <JsMethodExample />
    </Tabs.TabPane>
    <Tabs.TabPane key="customhotkey" tab="自定义热键">
      <CustomHotkeyExample />
    </Tabs.TabPane>
  </Tabs>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
