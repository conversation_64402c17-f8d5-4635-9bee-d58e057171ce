<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Button, Modal, Row, Col, Spin } from 'ant-design-vue'
import type { PropType } from 'vue'

// 定义组件属性
const props = defineProps({
  // 按钮文本
  buttonText: {
    type: String,
    default: '',
  },
  // 按钮类型
  buttonType: {
    type: String as PropType<'primary' | 'ghost' | 'dashed' | 'link' | 'text' | 'default'>,
    default: 'default',
  },
  // 按钮大小
  buttonSize: {
    type: String as PropType<'large' | 'middle' | 'small'>,
    default: 'middle',
  },
  // 对话框宽度
  modalWidth: {
    type: [Number, String],
    default: 1200,
  },
  // 是否显示触发按钮
  showButton: {
    type: Boolean,
    default: true,
  },
  // 溯源码ID
  wbSeqid: {
    type: [Number, String],
    default: '',
  },
  // 绑定的按键
  hotkey: {
    type: String,
    default: 'F5',
  },
})

// 定义事件
const emit = defineEmits(['success', 'cancel'])

// 对话框可见性
const visible = ref(false)
// 加载状态
const loading = ref(false)
// 溯源码ID
const trackCodeId = ref<string | number>('')

// 计算属性：按钮文本
const displayButtonText = computed(() => {
  // 如果提供了自定义按钮文本，则使用它
  if (props.buttonText) {
    return props.buttonText
  }

  // 否则，使用默认文本加上热键
  return `溯源码(${props.hotkey})`
})

// 打开对话框
const open = async (id?: string | number) => {
  // 如果传入了ID，使用传入的ID，否则使用props中的wbSeqid
  trackCodeId.value = id || props.wbSeqid || ''

  if (!trackCodeId.value) {
    // 如果没有ID，可以显示一个输入框让用户输入
    visible.value = true
    return
  }

  // 显示对话框
  visible.value = true

  // 加载数据
  await loadData()
}

// 关闭对话框
const close = () => {
  visible.value = false
  emit('cancel')
}

// 加载数据
const loadData = async () => {
  if (!trackCodeId.value) return

  loading.value = true

  try {
    // 这里将来会调用API加载数据
    // 目前只是模拟加载过程
    await new Promise(resolve => setTimeout(resolve, 500))

    // 加载成功后的处理
    console.log('加载溯源码数据成功，ID:', trackCodeId.value)
  } catch (error) {
    console.error('加载溯源码数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理按钮点击
const handleButtonClick = () => {
  open(props.wbSeqid)
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  // 获取热键
  const hotkey = props.hotkey || 'F1'

  // 检查是否按下指定的热键
  if (event.key === hotkey) {
    // 阻止默认行为（例如F1的浏览器帮助）
    event.preventDefault()

    // 只有在showButton为true时才响应热键
    if (props.showButton) {
      open(props.wbSeqid)
    }
  }
}

// 添加和移除键盘事件监听
onMounted(() => {
  window.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
})

// 暴露方法
defineExpose({
  open,
  close,
})
</script>

<template>
  <div class="wm-track-code">
    <!-- 触发按钮，只在showButton为true时显示 -->
    <Button v-if="showButton" :type="buttonType" :size="buttonSize" @click="handleButtonClick">
      {{ displayButtonText }}
    </Button>

    <!-- 溯源码录入对话框 -->
    <Modal v-model:open="visible" :title="'溯源码录入'" :width="modalWidth" :maskClosable="false" :destroyOnClose="true" @cancel="close">
      <Spin :spinning="loading">
        <div class="track-code-content">
          <Row :gutter="16">
            <Col :span="24">
              <div class="track-code-info">
                <p>溯源码ID: {{ trackCodeId || '未指定' }}</p>
                <p>这里将显示溯源码相关信息和录入界面</p>
              </div>
            </Col>
          </Row>
        </div>
      </Spin>

      <template #footer>
        <Button @click="close">关闭</Button>
      </template>
    </Modal>
  </div>
</template>

<style>
.track-code-content {
  padding: 16px 0;
}

.track-code-info {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  min-height: 200px;
}
</style>
