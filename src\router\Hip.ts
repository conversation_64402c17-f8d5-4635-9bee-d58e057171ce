import { DefaultLayout } from '@mh-base/core'

export default {
  path: '/hip',
  meta: {
    title: '公共组件',
  },
  component: DefaultLayout,
  children: [
    {
      path: '/hip/FeeCat/all',
      component: () => import('@v/FeeCat/All.vue'),
      meta: {
        title: '发票费用类别',
      },
    },
    {
      path: '/hip/FeeType/all',
      component: () => import('@v/FeeType/All.vue'),
      meta: {
        title: '费用类别',
      },
    },
    {
      path: '/hip/MedicalType/all',
      component: () => import('@v/MedicalType/All.vue'),
      meta: {
        title: '医疗类别',
      },
    },
    {
      path: '/hip/Route/all',
      component: () => import('@v/Route/All.vue'),
      meta: {
        title: '给药途径',
      },
    },
    {
      path: '/hip/Org/all',
      component: () => import('@v/Org/All.vue'),
      meta: {
        title: '组织机构-旧',
      },
    },
    {
      path: '/hip/Org/components',
      component: () => import('@v/Org/Org.vue'),
      meta: {
        title: '组织机构组件',
      },
    },
    {
      path: '/hip/Disease/components',
      component: () => import('@v/Disease/Disease.vue'),
      meta: {
        title: '病种组件',
      },
    },
  ],
}
