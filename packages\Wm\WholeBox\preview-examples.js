#!/usr/bin/env node

/**
 * WholeBox组件示例预览脚本
 * 快速查看所有可用的示例文件
 */

import { readdirSync, statSync, readFileSync, existsSync } from 'fs'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🎨 WholeBox 组件示例预览\n')

const examplesDir = resolve(__dirname, 'examples')

// 检查examples目录是否存在
if (!existsSync(examplesDir)) {
  console.log('❌ examples目录不存在')
  process.exit(1)
}

// 获取所有示例文件
const exampleFiles = readdirSync(examplesDir)
  .filter(file => file.endsWith('.vue'))
  .sort()

if (exampleFiles.length === 0) {
  console.log('❌ 没有找到示例文件')
  process.exit(1)
}

console.log(`📁 找到 ${exampleFiles.length} 个示例文件:\n`)

// 示例信息映射
const exampleInfo = {
  'index.vue': {
    title: '示例索引',
    description: '所有示例的导航页面，包含快速开始指南和特性介绍',
    icon: '🏠',
    difficulty: '入门',
    tags: ['导航', '索引', '指南']
  },
  'Basic.vue': {
    title: '基本用法',
    description: '展示组件的基本使用方法和简单的拆零盒整操作',
    icon: '📚',
    difficulty: '入门',
    tags: ['基础', '入门', '教程']
  },
  'MultipleScenarios.vue': {
    title: '多场景应用',
    description: '展示不同类型药品和医疗器械的拆零盒整场景',
    icon: '🏥',
    difficulty: '中级',
    tags: ['场景', '药品', '器械', '测试']
  },
  'TableDemo.vue': {
    title: '表格集成',
    description: '在数据表格中集成WholeBox组件，实现完整的库存管理功能',
    icon: '📊',
    difficulty: '中级',
    tags: ['表格', '集成', '管理']
  },
  'ApiIntegration.vue': {
    title: 'API集成',
    description: 'API调用监控、错误处理和性能分析的完整演示',
    icon: '🔗',
    difficulty: '高级',
    tags: ['API', '监控', '性能', '错误处理']
  }
}

// 显示示例列表
exampleFiles.forEach((file, index) => {
  const info = exampleInfo[file] || {
    title: file.replace('.vue', ''),
    description: '自定义示例文件',
    icon: '📄',
    difficulty: '未知',
    tags: ['自定义']
  }

  const filePath = resolve(examplesDir, file)
  const stats = statSync(filePath)
  const fileSize = (stats.size / 1024).toFixed(1)

  console.log(`${info.icon} ${index + 1}. ${info.title}`)
  console.log(`   📝 ${info.description}`)
  console.log(`   📊 难度: ${info.difficulty} | 大小: ${fileSize}KB`)
  console.log(`   🏷️  标签: ${info.tags.join(', ')}`)
  console.log(`   📁 文件: examples/${file}`)
  console.log('')
})

// 分析示例内容
console.log('📈 示例统计:\n')

let totalLines = 0
let totalSize = 0
const componentUsage = new Map()
const featureUsage = new Set()

exampleFiles.forEach(file => {
  const filePath = resolve(examplesDir, file)
  const content = readFileSync(filePath, 'utf-8')
  const lines = content.split('\n').length
  const size = statSync(filePath).size

  totalLines += lines
  totalSize += size

  // 分析组件使用
  const componentMatches = content.match(/import\s+.*\s+from\s+['"]ant-design-vue['"]/g)
  if (componentMatches) {
    componentMatches.forEach(match => {
      const components = match.match(/\{([^}]+)\}/)?.[1]
      if (components) {
        components.split(',').forEach(comp => {
          const cleanComp = comp.trim()
          componentUsage.set(cleanComp, (componentUsage.get(cleanComp) || 0) + 1)
        })
      }
    })
  }

  // 分析功能使用
  if (content.includes('handleSplitPack')) featureUsage.add('拆零盒整')
  if (content.includes('Table')) featureUsage.add('表格展示')
  if (content.includes('Modal')) featureUsage.add('模态框')
  if (content.includes('Form')) featureUsage.add('表单')
  if (content.includes('Button')) featureUsage.add('按钮操作')
  if (content.includes('Tag')) featureUsage.add('标签显示')
  if (content.includes('message')) featureUsage.add('消息提示')
})

console.log(`📊 总行数: ${totalLines.toLocaleString()} 行`)
console.log(`📦 总大小: ${(totalSize / 1024).toFixed(1)} KB`)
console.log(`🎯 平均大小: ${(totalSize / 1024 / exampleFiles.length).toFixed(1)} KB/文件`)
console.log('')

console.log('🧩 常用组件统计:')
const sortedComponents = Array.from(componentUsage.entries())
  .sort((a, b) => b[1] - a[1])
  .slice(0, 10)

sortedComponents.forEach(([component, count]) => {
  console.log(`   ${component}: ${count} 次使用`)
})
console.log('')

console.log('⚡ 功能特性覆盖:')
Array.from(featureUsage).sort().forEach(feature => {
  console.log(`   ✅ ${feature}`)
})
console.log('')

// 使用建议
console.log('💡 使用建议:\n')

console.log('🔰 新手推荐路径:')
console.log('   1. index.vue - 了解组件概览')
console.log('   2. Basic.vue - 学习基本用法')
console.log('   3. MultipleScenarios.vue - 掌握不同场景')
console.log('')

console.log('🚀 进阶学习路径:')
console.log('   1. TableDemo.vue - 学习表格集成')
console.log('   2. ApiIntegration.vue - 掌握API集成')
console.log('')

console.log('📖 快速查看示例:')
console.log('   # 在Vue项目中导入示例文件')
console.log('   import ExampleComponent from "@mh-wm/whole-box/examples/Basic.vue"')
console.log('')

console.log('🔧 开发调试:')
console.log('   # 在组件目录下运行')
console.log('   cd packages/Wm/WholeBox')
console.log('   node preview-examples.js')
console.log('')

console.log('✨ 示例预览完成！选择合适的示例开始你的WholeBox之旅吧！')
