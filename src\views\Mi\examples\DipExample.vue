<script setup lang="ts">
import { Card, Typography, Divider, Tabs, Button, message, Form, InputNumber, Select, Input } from 'ant-design-vue'
import { ref, reactive } from 'vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { Dip } from '@mh-mi/dip'
import type { TriggerScene } from '@mh-mi/dip'
import { basicUsage, switchModeUsage, checkboxModeUsage, importCode, packageJsonCode } from '@/views/Mi/examples/code/DipCode'

const { Title, Paragraph } = Typography
const { TabPane } = Tabs
const { Item: FormItem } = Form

// 当前激活的tab
const activeKey = ref('basic')

// 组件引用
const basicDipRef = ref()
const switchDipRef = ref()
const checkboxDipRef = ref()

// 基础模式配置
const basicConfig = reactive({
  visitId: 125338,
  trigScen: 3 as TriggerScene,
  oeList: [2],
  cashId: 0, // 收费ID，可以为0或undefined
})

// 开关模式配置
const switchConfig = reactive({
  visitId: 125338,
  trigScen: 1 as TriggerScene,
  oeList: [2],
  cashId: 0, // 收费ID，可以为0或undefined
})

// 复选框模式配置
const checkboxConfig = reactive({
  visitId: 125338,
  trigScen: 2 as TriggerScene,
  oeList: [2],
  cashId: 0, // 收费ID，可以为0或undefined
})

// 场景选项
const sceneOptions = [
  { value: 1, label: '门诊处方签名' },
  { value: 2, label: '门诊预结算' },
  { value: 3, label: '住院医嘱签名' },
  { value: 4, label: '住院预结算' },
]

// 调用事前分析 - 基础模式
const handleBasicPreAnalyze = async () => {
  try {
    // 基础模式下没有启用/禁用控制，直接调用callPreDip
    const result = await basicDipRef.value.callPreDip(basicConfig.visitId, basicConfig.oeList, basicConfig.cashId)
    console.log('事前分析结果:', result)
  } catch (error: any) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事中分析 - 基础模式
const handleBasicInAnalyze = async () => {
  try {
    // 基础模式下没有启用/禁用控制，直接调用callInDip
    const result = await basicDipRef.value.callInDip(basicConfig.visitId, basicConfig.oeList, basicConfig.cashId)
    console.log('事中分析结果:', result)
  } catch (error: any) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事前分析 - 开关模式
const handleSwitchPreAnalyze = async () => {
  try {
    // 直接调用callPreDip方法，组件内部会处理禁用状态
    const result = await switchDipRef.value.callPreDip(switchConfig.visitId, switchConfig.oeList, switchConfig.cashId)
    console.log('事前分析结果:', result)
  } catch (error: any) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事中分析 - 开关模式
const handleSwitchInAnalyze = async () => {
  try {
    // 直接调用callInDip方法，组件内部会处理禁用状态
    const result = await switchDipRef.value.callInDip(switchConfig.visitId, switchConfig.oeList, switchConfig.cashId)
    console.log('事中分析结果:', result)
  } catch (error: any) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事前分析 - 复选框模式
const handleCheckboxPreAnalyze = async () => {
  try {
    const result = await checkboxDipRef.value.callPreDip(checkboxConfig.visitId, checkboxConfig.oeList, checkboxConfig.cashId)
    console.log('事前分析结果:', result)
  } catch (error: any) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事中分析 - 复选框模式
const handleCheckboxInAnalyze = async () => {
  try {
    const result = await checkboxDipRef.value.callInDip(checkboxConfig.visitId, checkboxConfig.oeList, checkboxConfig.cashId)
    console.log('事中分析结果:', result)
  } catch (error: any) {
    message.error('分析失败: ' + error.message)
  }
}

// 状态变化回调
const onChange = (enabled: boolean) => {
  message.info('事前事中分析状态: ' + (enabled ? '启用' : '禁用'))
}

// 成功回调
const onSuccess = (result: any) => {
  if (result.action === 'continue') {
    message.success('用户选择继续执行，原因: ' + result.reason)
  } else {
    message.info('分析成功: ' + result.message)
  }
}

// 错误回调
const onError = (result: any) => {
  if (result.action === 'modify') {
    message.warning('用户选择返回修改')
  } else {
    message.error('分析失败: ' + result.message)
  }
}

// 更新医嘱列表
const updateOeList = (config: any, value: string) => {
  try {
    // 如果输入为空，则设置为空数组
    if (!value || value.trim() === '') {
      config.oeList = []
      return
    }

    // 将输入的字符串转换为数字数组
    const oeList = value
      .split(',')
      .map(item => {
        const num = parseInt(item.trim())
        return isNaN(num) ? 0 : num
      })
      .filter(num => num > 0)

    config.oeList = oeList.length > 0 ? oeList : []
  } catch (error) {
    config.oeList = []
  }
}
</script>

<template>
  <div>
    <Paragraph>事前事中分析组件，用于医保业务场景中进行事前事中分析并提供反馈。该组件可以展示分析结果，并允许用户进行相应的操作，如继续执行或返回修改。</Paragraph>

    <Card title="触发场景说明" class="mb-16px">
      <div class="scene-table">
        <table>
          <thead>
            <tr>
              <th>场景代码</th>
              <th>场景名称</th>
              <th>说明</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1</td>
              <td>门诊处方签名</td>
              <td>门诊医生开具处方并签名时触发</td>
            </tr>
            <tr>
              <td>2</td>
              <td>门诊预结算</td>
              <td>门诊患者进行费用预结算时触发</td>
            </tr>
            <tr>
              <td>3</td>
              <td>住院医嘱签名</td>
              <td>住院医生开具医嘱并签名时触发</td>
            </tr>
            <tr>
              <td>4</td>
              <td>住院预结算</td>
              <td>住院患者进行费用预结算时触发</td>
            </tr>
          </tbody>
        </table>
      </div>
    </Card>

    <Tabs v-model:activeKey="activeKey">
      <TabPane key="basic" tab="基础用法">
        <Card title="基础用法 - 隐藏模式" class="mb-16px">
          <div mb-16px>
            <Title :level="4">组件说明</Title>
            <Paragraph> 事前事中分析组件默认为隐藏模式，不在页面上显示任何控制界面，只能通过调用 callInDip 方法来触发分析流程。 分析结果会在弹窗中显示，用户可以选择继续执行或返回修改。 </Paragraph>
          </div>

          <Divider />

          <Title :level="4">实际效果</Title>
          <div mb-16px class="demo-container">
            <div class="config-form">
              <Form layout="inline">
                <FormItem label="就诊ID">
                  <InputNumber v-model:value="basicConfig.visitId" :min="1" style="width: 120px" />
                </FormItem>
                <FormItem label="触发场景">
                  <Select v-model:value="basicConfig.trigScen" style="width: 150px">
                    <Select.Option v-for="option in sceneOptions" :key="option.value" :value="option.value">
                      {{ option.label }}
                    </Select.Option>
                  </Select>
                </FormItem>
                <FormItem label="医嘱列表">
                  <Input placeholder="输入医嘱ID，多个用逗号分隔，可以为空" style="width: 250px" :value="basicConfig.oeList.join(', ')" @change="e => updateOeList(basicConfig, e.target.value)" />
                  <div class="form-help-text">医嘱列表可以为空</div>
                </FormItem>
                <FormItem label="收费ID">
                  <InputNumber v-model:value="basicConfig.cashId" :min="0" style="width: 120px" placeholder="可选参数" />
                  <div class="form-help-text">收费ID，可以为空</div>
                </FormItem>
              </Form>
            </div>

            <div class="component-container">
              <div class="component-wrapper">
                <!-- 组件实例 -->
                <Dip ref="basicDipRef" :trig-scen="basicConfig.trigScen" @success="onSuccess" @error="onError" />

                <!-- 触发按钮 -->
                <Button type="primary" @click="handleBasicPreAnalyze" style="margin-right: 8px">执行事前分析</Button>
                <Button type="primary" @click="handleBasicInAnalyze">执行事中分析</Button>
              </div>

              <div class="description">
                <p>使用说明：</p>
                <ol>
                  <li>配置就诊ID、触发场景、医嘱列表（医嘱列表可以为空）和收费ID（可选）</li>
                  <li>点击"执行事前分析"按钮触发事前分析流程，或点击"执行事中分析"按钮触发事中分析流程</li>
                  <li>弹窗显示分析结果</li>
                  <li>如有违规项，可选择"继续执行"或"返回修改"</li>
                  <li>选择"继续执行"时，需要输入处理原因</li>
                </ol>
              </div>
            </div>
          </div>

          <Divider />

          <Title :level="4">代码示例</Title>
          <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </Card>
      </TabPane>

      <TabPane key="switch" tab="开关模式">
        <Card title="开关模式" class="mb-16px">
          <div mb-16px>
            <Title :level="4">组件说明</Title>
            <Paragraph> 开关模式下，组件会在页面上显示一个开关控件，用户可以通过切换开关来启用或禁用事前事中分析功能。 当禁用时，调用 callInDip 方法不会触发分析流程，而是直接返回成功。 </Paragraph>
          </div>

          <Divider />

          <Title :level="4">实际效果</Title>
          <div mb-16px class="demo-container">
            <div class="config-form">
              <Form layout="inline">
                <FormItem label="就诊ID">
                  <InputNumber v-model:value="switchConfig.visitId" :min="1" style="width: 120px" />
                </FormItem>
                <FormItem label="触发场景">
                  <Select v-model:value="switchConfig.trigScen" style="width: 150px">
                    <Select.Option v-for="option in sceneOptions" :key="option.value" :value="option.value">
                      {{ option.label }}
                    </Select.Option>
                  </Select>
                </FormItem>
                <FormItem label="医嘱列表">
                  <Input placeholder="输入医嘱ID，多个用逗号分隔，可以为空" style="width: 250px" :value="switchConfig.oeList.join(', ')" @change="e => updateOeList(switchConfig, e.target.value)" />
                  <div class="form-help-text">医嘱列表可以为空</div>
                </FormItem>
                <FormItem label="收费ID">
                  <InputNumber v-model:value="switchConfig.cashId" :min="0" style="width: 120px" placeholder="可选参数" />
                  <div class="form-help-text">收费ID，可以为空</div>
                </FormItem>
              </Form>
            </div>

            <div class="component-container">
              <div class="component-wrapper">
                <!-- 组件实例 -->
                <Dip ref="switchDipRef" :trig-scen="switchConfig.trigScen" label="启用事前事中分析" storage-key="dip_switch" @change="onChange" @success="onSuccess" @error="onError" />

                <!-- 触发按钮 -->
                <Button type="primary" @click="handleSwitchPreAnalyze" style="margin-right: 8px" ml-16px>执行事前分析</Button>
                <Button type="primary" @click="handleSwitchInAnalyze">执行事中分析</Button>
              </div>

              <div class="description">
                <p>使用说明：</p>
                <ol>
                  <li>配置就诊ID、触发场景、医嘱列表（医嘱列表可以为空）和收费ID（可选）</li>
                  <li>通过开关控制是否启用事前事中分析（状态会保存在localStorage中）</li>
                  <li>点击"执行事前分析"按钮触发事前分析流程，或点击"执行事中分析"按钮触发事中分析流程</li>
                  <li>禁用状态下，不会显示分析弹窗，直接返回成功</li>
                </ol>
              </div>
            </div>
          </div>

          <Divider />

          <Title :level="4">代码示例</Title>
          <CodeDemoVue :usage="switchModeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </Card>
      </TabPane>

      <TabPane key="checkbox" tab="复选框模式">
        <Card title="复选框模式" class="mb-16px">
          <div mb-16px>
            <Title :level="4">组件说明</Title>
            <Paragraph> 复选框模式下，组件会在页面上显示一个复选框控件，用户可以通过勾选复选框来启用或禁用事前事中分析功能。 功能与开关模式相同，只是UI展现形式不同。 </Paragraph>
          </div>

          <Divider />

          <Title :level="4">实际效果</Title>
          <div mb-16px class="demo-container">
            <div class="config-form">
              <Form layout="inline">
                <FormItem label="就诊ID">
                  <InputNumber v-model:value="checkboxConfig.visitId" :min="1" style="width: 120px" />
                </FormItem>
                <FormItem label="触发场景">
                  <Select v-model:value="checkboxConfig.trigScen" style="width: 150px">
                    <Select.Option v-for="option in sceneOptions" :key="option.value" :value="option.value">
                      {{ option.label }}
                    </Select.Option>
                  </Select>
                </FormItem>
                <FormItem label="医嘱列表">
                  <Input
                    placeholder="输入医嘱ID，多个用逗号分隔，可以为空"
                    style="width: 250px"
                    :value="checkboxConfig.oeList.join(', ')"
                    @change="e => updateOeList(checkboxConfig, e.target.value)"
                  />
                  <div class="form-help-text">医嘱列表可以为空</div>
                </FormItem>
                <FormItem label="收费ID">
                  <InputNumber v-model:value="checkboxConfig.cashId" :min="0" style="width: 120px" placeholder="可选参数" />
                  <div class="form-help-text">收费ID，可以为空</div>
                </FormItem>
              </Form>
            </div>

            <div class="component-container">
              <div class="component-wrapper">
                <!-- 组件实例 -->
                <Dip ref="checkboxDipRef" :trig-scen="checkboxConfig.trigScen" label="启用事前事中分析" storage-key="dip_checkbox" @change="onChange" @success="onSuccess" @error="onError" />

                <!-- 触发按钮 -->
                <Button type="primary" @click="handleCheckboxPreAnalyze" style="margin-right: 8px" ml-16px>执行事前分析</Button>
                <Button type="primary" @click="handleCheckboxInAnalyze">执行事中分析</Button>
              </div>

              <div class="description">
                <p>使用说明：</p>
                <ol>
                  <li>配置就诊ID、触发场景、医嘱列表（医嘱列表可以为空）和收费ID（可选）</li>
                  <li>通过复选框控制是否启用事前事中分析（状态会保存在localStorage中）</li>
                  <li>点击"执行事前分析"按钮触发事前分析流程，或点击"执行事中分析"按钮触发事中分析流程</li>
                  <li>禁用状态下，不会显示分析弹窗，直接返回成功</li>
                </ol>
              </div>
            </div>
          </div>

          <Divider />

          <Title :level="4">代码示例</Title>
          <CodeDemoVue :usage="checkboxModeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </Card>
      </TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
.demo-container {
  margin: 16px 0;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.component-container {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.component-wrapper {
  display: flex;
  align-items: center;
  min-width: 300px;
}

.description {
  flex: 1;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.description p {
  margin-bottom: 8px;
  font-weight: 500;
}

.description ol {
  padding-left: 20px;
  margin: 0;
}

.description li {
  margin-bottom: 4px;
}

.scene-table table {
  width: 100%;
  border-collapse: collapse;
}

.scene-table th,
.scene-table td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #f0f0f0;
}

.scene-table th {
  background-color: #fafafa;
  font-weight: 500;
}

.scene-table tr:hover {
  background-color: #f5f5f5;
}

.config-form {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.form-help-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.5;
}
</style>
