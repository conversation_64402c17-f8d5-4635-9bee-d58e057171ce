<script setup lang="ts">
import { DiseaseDict } from '@mh-hip/disease'
import { Card, Typography, Divider, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { packageJsonCode, dictUsage } from '../code/DiseaseCode'
import { ref } from 'vue'

const { Title } = Typography
const diseaseId = ref()

const importCode = `import { DiseaseDict } from '@mh-hip/disease'`
</script>
<template>
  <Card title="基础用法 - 病种字典" class="mb-16px">
    <Title :level="4">选择病种</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="24">
        <div>
          <div mb-8px>病种：</div>
          <DiseaseDict v-model="diseaseId" style="width: 100%" />
          <div mt-8px>选中值: {{ diseaseId }}</div>
        </div>
      </Col>
    </Row>
    <Divider />
    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="dictUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
