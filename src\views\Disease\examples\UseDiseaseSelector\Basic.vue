<script setup lang="ts">
import { ref } from 'vue'
import { Button, Radio } from 'ant-design-vue'
import { useDiseaseSelector, DiseaseSelectorModal } from '@mh-hip/disease'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { packageJsonCode, useUsage } from '../code/DiseaseCode'

const mode = ref('dict')
const diseaseId = ref()
const showModal = ref(false)
const patientId = ref(1)

// 计算属性，根据模式返回相应的配置
const diseaseConfig = computed(() => {
  return useDiseaseSelector({
    mode: mode.value,
    patientId: patientId.value,
    title: '选择病种'
  })
})

const changeMode = () => {
  console.log(`切换到${mode.value}模式`)
}

const handleDiseaseSelect = (disease: any) => {
  console.log('选择的病种:', disease)
}

const importCode = `import { useDiseaseSelector, DiseaseSelectorModal } from '@mh-hip/disease'`
</script>
<template>
  <div>
    <Radio.Group v-model:value="mode" button-style="solid" @change="changeMode">
      <Radio.Button value="dict">字典模式</Radio.Button>
      <Radio.Button value="selector">选择器模式</Radio.Button>
      <Radio.Button value="modal">弹窗模式</Radio.Button>
    </Radio.Group>
    <div style="margin-top: 10px;">

      <template v-if="diseaseConfig.component === DiseaseSelectorModal">
        <Button @click="showModal = true">选择病种</Button>
        <component
          :is="diseaseConfig.component"
          v-model:visible="showModal"
          v-model="diseaseId"
          v-bind="diseaseConfig.props"
          @select="handleDiseaseSelect"
        />
      </template>
      <template v-else>
        <component
          :is="diseaseConfig.component"
          v-model="diseaseId"
          v-bind="diseaseConfig.props"
          @select="handleDiseaseSelect"
        />
      </template>
      <div style="margin-top: 8px;">选中值: {{ diseaseId }}</div>
    </div>
    <CodeDemoVue :usage="useUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </div>
</template>
