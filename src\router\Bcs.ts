import { DefaultLayout } from '@mh-base/core'
import { CashPage } from '@mh-bcs/cash'
import { DepositIndex } from '@mh-bcs/deposit'
import { Count, SettleList } from '@mh-bcs/settle'


export default {
  path: '/bcs',
  meta: {
    title: '收费工作站',
  },
  component: DefaultLayout,
  children: [
    {
      path: '/bcs/Settle/count',
      component: Count,
      meta: {
        title: '我的收费',
      },
    },
    {
      path: '/bcs/Settle/list',
      component: SettleList,
      meta: {
        title: '门诊收费',
      },
    },
    {
      path: '/bcs/Settle/list2',
      component: SettleList,
      meta: {
        title: '住院收费',
      },
    },
    {
      path: '/bcs/Deposit/index',
      component: DepositIndex,
      meta: {
        title: '预交金',
      },
    },
    {
      path: '/bcs/Cash/page',
      component: CashPage,
      meta: {
        title: '收费记录',
      },
      props: {},
    },
    {
      path: '/bcs/Cash/page2',
      component: CashPage,
      meta: {
        title: '交账记录',
      },
      props: {},
    },
    {
      path: '/bcs/Cash/page3',
      component: CashPage,
      meta: {
        title: '发票记录',
      },
      props: {},
    },
  ],
}
