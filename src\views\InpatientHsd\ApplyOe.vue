<script setup lang="ts">
import { QuestionForm } from '@mh-inpatient-hsd/oe-apply'
import { index as PgUserInfo } from '@mh-inpatient-hsd/visit-info'
import { ArtSelect, SectionArtSelect, SelectWithUnitTotal, WmDeptArtSelect, WmOrgArtSelect } from '@mh-inpatient-hsd/selector'
import { ExecForm, PackDeliverForm } from '@mh-inpatient-hsd/oe-exec'
import { BillingEntryModal, BillingEntry, BillingEntryItem } from '@mh-inpatient-hsd/billing-entry'
import { AddArt as OeAddArt, ChangeTotal, RefundOe } from '@mh-inpatient-hsd/oe-fee-change'
import { AddArt, RefundOrderExecFee } from '@mh-inpatient-hsd/order-fee-change'
import { index as SectionRouteConsumable } from '@mh-inpatient-hsd/section-route-consumable'
import { CwBillType, getCwBillTypeName } from '@mh-wm/util'
import { BatchAdjust as BatchChangeForm } from '@mh-wm/batch-adjust'

const questionFormRef = ref<InstanceType<typeof QuestionForm>>()
const artSelectWithUnitTotalRef = ref<InstanceType<typeof SelectWithUnitTotal>>()
const execFormRef = ref<InstanceType<typeof ExecForm>>()
const packDeliverFormRef = ref<InstanceType<typeof PackDeliverForm>>()
const billingEntryModalRef = ref<InstanceType<typeof BillingEntryModal>>()
const batchChangeFormRef = ref<InstanceType<typeof BatchChangeForm>>()
const sectionRouteConsumableRef = ref<InstanceType<typeof SectionRouteConsumable>>()

const sectionId = 40
const sectionName = '内三科住院'
const wmDeptCode = '000013'
const oeTypeId = 2
const medicinesAccountingMode = 1
const packDeliverAcc = true
const showCommunityPressureSoresRisk = true
const visitInfo = {
  "keyStr": "84503-1",
  visitId: 84503,
  "caseNo": "**********",
  "deptNo": 1,
  "sectionId": 40,
  "bedNo": "1",
  "admissionDeptName": "内三科住院",
  "deptCode": "000015",
  "deptName": "内三科住院",
  "patientId": 270389,
  "patientName": "张振忠",
  "genderId": 1,
  "genderName": "男",
  "ageOfYears": 68,
  "ageOfDays": 38,
  "nursingLevel": "Ⅱ级护理",
  "diet": "低盐低脂饮食",
  "hospitalizedDays": 11,
  "deptDays": 11,
  "admDiags": "肺炎,三里发,一度",
  "treatmentPhrase": "住院",
  "treatmentDays": 11,
  "totalAmount": 2503.07,
  "unpaidAmount": 2503.07,
  "timeAdmission": "2025-03-14 00:48:59",
  "deptNames": [
    "内三科住院"
  ]
}

const handleVisibleQuestionForm = () => {
  questionFormRef.value.open(84503, [79])
}
const questioned = () => {
  console.log('questioned')
}

const handleVisibleArtSelectWithUnitTotal = () => {
  artSelectWithUnitTotalRef.value.open()
}

const handleVisibleSectionRouteConsumable = () => {
  sectionRouteConsumableRef.value.open()
}

const handleVisibleExecForm = () => {
  const oeIdLs = [{"visitId":83798,"oeNo":1}]
  const selectedRows = [{"keyStr":"83798-1","visitId":83798,"oeNo":1,"bedNo":"10","bedDisplayOrder":363,"patientName":"孔令才","oeTypeId":2,"freqCode":"qd","lastExecDate":20250317,"nextExecDate":20250318,"groupMark":0,"displayOrder":1,"oeText":"内科护理常规","cycleExecTimes":0,"cycleTotalTimes":1,"oneDayMaxTimes":1,"oeTimeStarted":"2025-03-12 14:22:00","dischargeOe":false,"artTypeId":20,"mealCells":1,"unit":"次","oeCat":9,"artId":8000319,"lastExecTime":"2025-03-17 08:02:52","lastExecUser":"张琳琳","orderEntryStatus":2,"firstAid":false,"stockReq":0,"requestToday":false,"requestTomorrow":false,"requestAfterTomorrow":false,"requestAfterMore":false,"artName":"内科护理常规","packUnit":"次","todayInt":20250326,"visitIdCount":19,"groupNoCount":1}]
  execFormRef.value?.open(oeIdLs, false, selectedRows)
}

const handleVisiblePackDeliver = () => {
  const selectedRows = [{"keyStr":"84503-15","visitId":84503,"oeNo":15,"bedNo":"1","bedDisplayOrder":355,"patientName":"张振忠","oeTypeId":2,"routeId":100,"routeName":"口服","freqCode":"tid","lastExecDate":20250325,"nextExecDate":20250326,"groupMark":0,"displayOrder":15,"oeText":"盐酸氨溴索口服溶液  100ml:300mg/瓶  每次10ml  (0.1瓶)","cycleExecTimes":0,"cycleTotalTimes":3,"oneDayMaxTimes":3,"oeTimeStarted":"2025-03-14 00:52:00","stFlag":0,"dischargeOe":false,"artTypeId":11,"isPatientProvided":0,"isPackDeliver":1,"totalPackPlanned":3,"isOePlan":0,"feedMethod":10,"mealCells":0.1,"mealDoses":10,"unit":"瓶","packDeliverTotal":1,"packUnitType":2,"oeCat":1,"artId":1044295,"lastExecTime":"2025-03-25 11:07:37","lastExecUser":"张慧","orderEntryStatus":2,"requestEnded":20250328,"firstAid":false,"stockReq":1,"requestToday":true,"requestTomorrow":true,"requestAfterTomorrow":true,"requestAfterMore":false,"artName":"盐酸氨溴索口服溶液","artSpec":" 100ml:300mg/瓶","producer":"江苏汉晨药业有限公司","packUnit":"瓶","cellUnit":"瓶","doseUnit":"ml","todayInt":20250326,"visitIdCount":2,"groupNoCount":1,"total":1,"wmTotalPacks":104},{"keyStr":"84503-55","visitId":84503,"oeNo":55,"bedNo":"1","bedDisplayOrder":355,"patientName":"张振忠","oeTypeId":2,"routeId":100,"routeName":"口服","freqCode":"tid","lastExecDate":20250325,"nextExecDate":20250326,"groupMark":0,"displayOrder":55,"oeText":"肺力咳合剂 150ml  每次20ml  (0.1333瓶)","cycleExecTimes":0,"cycleTotalTimes":3,"oneDayMaxTimes":3,"oeTimeStarted":"2025-03-17 08:45:00","stFlag":0,"dischargeOe":false,"artTypeId":12,"isPatientProvided":0,"isPackDeliver":1,"totalPackPlanned":2,"isOePlan":0,"feedMethod":10,"mealCells":0.1333,"mealDoses":20,"unit":"瓶","packDeliverTotal":1,"packUnitType":2,"oeCat":1,"artId":4567,"lastExecTime":"2025-03-25 11:07:37","lastExecUser":"张慧","orderEntryStatus":2,"requestEnded":20250327,"firstAid":false,"stockReq":1,"requestToday":true,"requestTomorrow":true,"requestAfterTomorrow":false,"requestAfterMore":false,"artName":"肺力咳合剂","artSpec":"150ml","producer":"贵州健兴药业有限公司","packUnit":"瓶","cellUnit":"瓶","doseUnit":"ml","todayInt":20250326,"visitIdCount":0,"groupNoCount":1,"total":1,"wmTotalPacks":40},{"keyStr":"83440-20","visitId":83440,"oeNo":20,"bedNo":"8","bedDisplayOrder":361,"patientName":"史允才","oeTypeId":2,"routeId":100,"routeName":"口服","freqCode":"bid","lastExecDate":20250325,"nextExecDate":20250326,"groupMark":0,"displayOrder":20,"oeText":"寒喘祖帕颗粒  12g*6袋  每次12g  (1袋)","cycleExecTimes":0,"cycleTotalTimes":2,"oneDayMaxTimes":2,"oeTimeStarted":"2025-03-12 09:07:00","stFlag":0,"dischargeOe":false,"artTypeId":12,"isPatientProvided":0,"isPackDeliver":1,"totalPackPlanned":3,"isOePlan":0,"feedMethod":10,"mealCells":1,"mealDoses":12,"unit":"袋","packDeliverTotal":1,"packUnitType":2,"oeCat":1,"artId":1045777,"lastExecTime":"2025-03-25 11:07:55","lastExecUser":"张慧","orderEntryStatus":2,"requestEnded":20250322,"firstAid":false,"stockReq":1,"requestToday":false,"requestTomorrow":false,"requestAfterTomorrow":false,"requestAfterMore":false,"artName":"寒喘祖帕颗粒","artSpec":" 12g*6袋","producer":"新疆维吾尔药业有限责任公司","packUnit":"盒","cellUnit":"袋","doseUnit":"g","todayInt":20250326,"visitIdCount":2,"groupNoCount":1,"total":1,"wmTotalPacks":75}]
  packDeliverFormRef.value?.open(selectedRows)
}

const handleSelectWithUnitTotal = (selectedArt: any) => {
  console.log('handleSelectWithUnitTotal', selectedArt)
}

const handleArtSelect = (selectedArt: any) => {
  console.log('handleArtSelect', selectedArt)
}

const handleExecuted = (obj: any) => {
  console.log('handleExecuted', obj)
}

const handleVisibleBillingEntryModal = () => {
  billingEntryModalRef.value.open(visitInfo.visitId)
}

const handleVisibleBatchChange = () => {
  batchChangeFormRef.value.init('000014', {artName: '测试', artId: 4161})
}

const cwBillTypeLs = [
  {
    title: getCwBillTypeName(CwBillType.SALE_OUT),
    value: CwBillType.SALE_OUT
  },
  {
    title: getCwBillTypeName(CwBillType.CONSUME_OUT),
    value: CwBillType.CONSUME_OUT
  }
]
</script>

<template>
  <a-button @click="handleVisibleQuestionForm">show question from</a-button>
  <question-form ref="questionFormRef" @questioned="questioned"/>
  <a-button type="primary" m-l-8 @click="handleVisibleArtSelectWithUnitTotal">
    条目选择带单位数量
  </a-button>
  <a-button type="primary" m-l-8 @click="handleVisibleExecForm">
    医嘱执行
  </a-button>
  <a-button type="primary" m-l-8 @click="handleVisiblePackDeliver">
    申请用药
  </a-button>
  <a-button type="primary" m-l-8 @click="handleVisibleBillingEntryModal">
    计费医嘱
  </a-button>
  <a-button type="primary" m-l-8 @click="handleVisibleSectionRouteConsumable">
    病区给药途径绑定
  </a-button>
  <a-button type="primary" m-l-8 @click="handleVisibleBatchChange">
    条目批号调整
  </a-button>
  <div h-8px />
  <a-form-item label="sectionArtSelect" name="artName">
    <section-art-select :section-id="sectionId" @selected="handleArtSelect"/>
  </a-form-item>
  <a-form-item label="wmDeptArtSelect" name="artName">
    <wm-dept-art-select :deptCode="wmDeptCode" :searchType="6" @selected="handleArtSelect"/>
  </a-form-item>
  <a-form-item label="wmOrgArtSelect" name="artName">
    <wm-org-art-select @selected="handleArtSelect"/>
  </a-form-item>
  <div h-8px />
  <PgUserInfo :user-info="visitInfo" :inline="true" :showCommunityPressureSoresRisk="showCommunityPressureSoresRisk" />
  <select-with-unit-total ref="artSelectWithUnitTotalRef" :artSearchType="1" @add-art="handleSelectWithUnitTotal"/>
  <section-route-consumable ref="sectionRouteConsumableRef" :section-id="sectionId" :section-name="sectionName"/>
  <ExecForm ref="execFormRef" :oe-type-id="oeTypeId"
            :section-id="sectionId" @executed="handleExecuted" :medicines-accounting-mode="medicinesAccountingMode"/>
  <pack-deliver-form
    ref="packDeliverFormRef"
    :section-id="sectionId"
    :oe-type-id="oeTypeId"
    :medicines-accounting-mode="medicinesAccountingMode"
    :pack-deliver-acc="packDeliverAcc"
    @submit="handleExecuted"/>
  <billing-entry-modal
    ref="billingEntryModalRef"
    :in-operating="-1"
    :section-id="sectionId"
    :gender-id="visitInfo.genderId"
    :default-freq-code="'st'"
    @oe-change="handleExecuted"/>
  <batch-change-form ref="batchChangeFormRef" width="800px" @close="handleExecuted"/>
</template>
