import { wrapCodeExample } from '@/utils/codeUtils'
// 包依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-hsd/visit": "^1.0.0"
  }
}`

export const importCode = `import { Visit } from '@mh-hsd/visit'`

// 基础用法代码
export const basicUsage = wrapCodeExample(`<template>
  <div>
    <Visit ref="visitRef" :visitId="visitId" @confirm="handleConfirm" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Visit } from '@mh-hsd/visit'

// 模拟用户存储
const visitId = ref('123456')
const visitRef = ref(null)

function handleConfirm(refType, value) {
  console.log('引用类型:', refType)
  console.log('引用数据:', value)
}
</script>`)

//弹窗用法代码
export const modalUsage = wrapCodeExample(`<template>
  <div>
    <Button type="primary" @click="showModal = true">打开诊疗查询</Button>
    <Visit
      :visitId="visitId"
      :orgId="orgId"
      type="modal"
      :visible="showModal"
      title="诊疗记录查看"
      :canRef="canRef"
      @confirm="handleConfirm"
      @close="showModal = false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Visit } from '@mh-hsd/visit'
import { Button } from 'ant-design-vue'
const visitId = ref(102)
const orgId = ref(1)
const canRef = ref(false)
const showModal = ref(false)

function handleConfirm(refType, value) {
  console.log('引用类型:', refType)
  console.log('引用数据:', value)
}
<\/script>`)

// 抽屉用法
export const drawerUsage = wrapCodeExample(`<template>
  <div>
    <Button type="primary" @click="showDrawer = true">打开诊疗查询</Button>
    <Visit
      :visitId="visitId"
      :orgId="orgId"
      type="drawer"
      :visible="showDrawer"
      title="诊疗记录查看"
      :canRef="canRef"
      @confirm="handleConfirm"
      @close="showDrawer = false"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Visit } from '@mh-hsd/visit'
import { Button } from 'ant-design-vue'
const visitId = ref(102)
const orgId = ref(1)
const canRef = ref(false)
const showDrawer = ref(false)
function handleConfirm(refType, value) {
  console.log('引用类型:', refType)
  console.log('引用数据:', value)
}
<\/script>`)
