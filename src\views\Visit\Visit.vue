<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

import BasicVisitExample from './examples/BasicVisitExample.vue'
import ModalVisitExample from './examples/ModalVisitExample.vue'
import DrawerVisitExample from './examples/DrawerVisitExample.vue'

const { Paragraph } = Typography
const activeKey = ref('inline')
</script>

<template>
  <Card title="Visit 诊疗查询组件" class="mb-16px">
    <Paragraph>诊疗查询组件，支持多种展示模式：内嵌、弹窗、抽屉式。</Paragraph>
    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane key="inline" tab="内嵌模式">
        <BasicVisitExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="modal" tab="弹窗模式">
        <ModalVisitExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="drawer" tab="抽屉模式">
        <DrawerVisitExample />
      </Tabs.TabPane>
    </Tabs>
  </Card>
</template>

<style scoped>
:deep(.ant-tabs-left > .ant-tabs-content-holder) {
  border-left: 1px solid #f0f0f0;
  padding-left: 16px;
}
:deep(.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab) {
  padding: 8px 16px;
}
:deep(.ant-card-body) {
  padding: 24px;
}
</style>
