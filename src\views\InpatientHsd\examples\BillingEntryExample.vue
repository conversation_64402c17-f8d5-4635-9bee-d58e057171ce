<script setup lang="ts">
import { BillingEntryModal, BillingEntry } from '@mh-inpatient-hsd/billing-entry'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, modalUsage, importCode, packageJsonCode } from './code/BillingEntryCode'

const { Title, Paragraph } = Typography

// 引用
const billingEntryModalRef = ref()
const billingEntryRef = ref()

// 模拟数据
const visitInfo = ref({
  visitId: 123456,
  genderId: 1
})

const sectionId = ref(40)

// 打开计费医嘱弹窗
const handleVisibleBillingEntryModal = () => {
  billingEntryModalRef.value.open(visitInfo.value.visitId)
}

// 医嘱变更回调
const handleOeChange = () => {
  message.success('医嘱已更新')
}
</script>

<template>
  <Card title="基础用法 - 内嵌模式" class="mb-16px">
    <div mb-16px>
      <Title :level="4">基础用法</Title>
      <billing-entry 
        ref="billingEntryRef" 
        :section-id="sectionId" 
        :visit-id="visitInfo.visitId" 
        :gender-id="visitInfo.genderId" 
        :default-freq-code="'st'" 
        @oe-change="handleOeChange" 
      />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <Card title="弹窗模式" class="mb-16px">
    <div mb-16px>
      <Title :level="4">弹窗模式</Title>
      <Button type="primary" @click="handleVisibleBillingEntryModal">打开计费医嘱弹窗</Button>
      <billing-entry-modal
        ref="billingEntryModalRef"
        :in-operating="-1"
        :section-id="sectionId"
        :gender-id="visitInfo.genderId"
        :default-freq-code="'st'"
        @oe-change="handleOeChange"
      />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="modalUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>



