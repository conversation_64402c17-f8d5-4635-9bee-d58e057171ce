<script setup lang="ts">
import { onMounted, nextTick } from 'vue'
import hljs from 'highlight.js/lib/core'
import xml from 'highlight.js/lib/languages/xml'
import javascript from 'highlight.js/lib/languages/javascript'
import json from 'highlight.js/lib/languages/json'
import 'highlight.js/styles/atom-one-dark.css'

// 注册需要的语言
hljs.registerLanguage('xml', xml)
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('json', json)

// 更新代码高亮
const updateHighlight = () => {
  nextTick(() => {
    document.querySelectorAll('pre code').forEach(block => {
      hljs.highlightElement(block as HTMLElement)
    })
  })
}

// 初始化时更新高亮
onMounted(() => {
  updateHighlight()
})

defineExpose({
  updateHighlight
})
</script>

<template>
  <!-- 这是一个功能性组件，没有UI -->
</template>

<style scoped>
pre {
  margin: 0;
  padding: 16px;
  overflow: auto;
  font-size: 14px;
  line-height: 1.5;
  background-color: #282c34;
  border-radius: 6px;
}

code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}
</style>
