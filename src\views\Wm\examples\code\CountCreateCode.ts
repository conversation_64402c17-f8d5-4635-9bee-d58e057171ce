import { wrapCodeExample } from '@/utils/codeUtils'

// 创建盘点单示例代码
export const createUsage = wrapCodeExample(`<template>
  <WmCountCreate
    buttonText="新建盘点"
    buttonType="primary"
    :defaultDeptCode="deptCode"
    :onlyShowWithStock="true"
    @success="handleCreateSuccess"
    @cancel="handleCreateCancel"
  />
</template>

<script setup>
import { WmCountCreate } from '@mh-wm/count'
import '@mh-wm/count/index.css'  // 引入样式文件
import { ref } from 'vue'

const deptCode = ref('1001')

const handleCreateSuccess = (countId) => {
  console.log('创建盘点单成功，盘点单ID:', countId)
}

const handleCreateCancel = () => {
  console.log('取消创建盘点单')
}
</script>`)

// 导入代码
export const importCode = `import { WmCountCreate } from '@mh-wm/count'
import '@mh-wm/count/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/count": "^1.0.0"
  }
}`
