<script setup lang="ts">
import filters from './utils/filters.ts'
import { Table, Button, Col, Form, FormItem, Modal, Row, Radio, RadioGroup, InputNumber, message } from 'ant-design-vue'
import {DoubleRightOutlined} from '@ant-design/icons-vue'
import type { TableColumnType } from 'ant-design-vue'
import { oeABUPSumFeeLsApi, oeFeeRefundLsApi } from '@mh-inpatient-hsd/util'
import { sectionInfoApi } from "@mh-hip/util"

const props = defineProps({
  sectionId: {
    type: Number,
    default: null
  },
})

const columns: TableColumnType[] = [
  {
    title: '执行流水',
    dataIndex: 'execSeqid',
    align: 'center',
    width: 180
  },
  {
    title: '费用日期',
    dataIndex: 'bsnDate',
    align: 'center',
    width: 90
  },
  // {
  //   title: '项目编码',
  //   dataIndex: 'artCode',
  //   align: 'left',
  //   width: 120,
  //   ellipsis: true
  // },
  {
    title: '医保项目编码',
    dataIndex: 'miCode',
    align: 'left',
    width: 130,
    ellipsis: true
  },
  {
    title: '项目内容',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    align: 'center',
    width: 50
  },
  {
    title: '单价',
    dataIndex: 'price',
    align: 'right',
    width: 90
  },
  {
    title: '初始数量',
    dataIndex: 'oriTotal',
    align: 'right',
    width: 90
  },
  {
    title: '当前数量',
    dataIndex: 'curTotal',
    align: 'right',
    width: 90
  },
  {
    title: '冲红数量',
    dataIndex: 'refundTotal',
    align: 'center',
    width: 150
  }
]

const emit = defineEmits(['page'])
const visible = ref(false);
const confirmLoading = ref<boolean>(false)
const dataSource = ref<any>([])
const inputRefundKey = ref()
const inputRef = ref({})
const execSeqid = ref()
const lineNo = ref()
const bsnDate = ref()
const searchFormModel = reactive<any>({})
const formModel = reactive<any>({})
const oeDate = 1
const all = 0
const drugAccountingMode = ref<boolean>(false)

const open = (seqId: any, no: any, initBsnDate: any) => {
  execSeqid.value = seqId
  lineNo.value = no
  bsnDate.value = initBsnDate
  getDataSource()
  getSection()
  searchFormModel.dateType = oeDate
  visible.value = true
}

const getSection = async () => {
  drugAccountingMode.value = false
  formModel.returnToWm = 0
  sectionInfoApi({ sectionId: props.sectionId }).then((data: any) => {
    if (data) {
      drugAccountingMode.value = data.medicinesAccountingMode === 1
      formModel.returnToWm = data.stockReturnMode ? Number(data.stockReturnMode) : 0
    }
  })
}

const getDataSource = async () => {
  dataSource.value = []
  oeABUPSumFeeLsApi({execSeqid: execSeqid.value, lineNo: lineNo.value}).then((list: any) => {
    if (list) {
      dataSource.value = list
    }
  })
}

const disableRefundBtn = computed(() => {
  if (dataSource.value && dataSource.value.length > 0) {
    const refundRows = dataSource.value.filter((item: any) => item.refundTotal > 0)
    return refundRows.length === 0
  }
  return true
})

function handleCancel() {
  visible.value = false
  emit('page')
}

function handleRefundAll() {
  oeLs.value.forEach((item: any) => {
    if (item.curTotal > 0) {
      item.refundTotal = item.curTotal
    }
  })
}

const handleRefundOe = () => {
  if (disableRefundBtn.value) {
    message.warning('请设置待冲红数量')
    return
  }
  const refundLs = dataSource.value.filter((item: any) => item.refundTotal > 0)
  confirmLoading.value = true
  const params = {
    srcExecSeqid: execSeqid.value,
    srcLineNo: lineNo.value,
    refundLs: refundLs,
    returnToWm: formModel.returnToWm
  }
  oeFeeRefundLsApi(params).then(() => {
    message.success('费用冲红完成')
    getDataSource()
  }).finally(() => {
    confirmLoading.value = false
  })
}

const handleSetRowRefundTotal = (keyStr: String) => {
  const row = dataSource.value.find(item => item.keyStr === keyStr)
  if (row) {
    row.refundTotal = row.curTotal
  }
}

const onEnterRefundTotal = (refundTotal: number, keyStr: String) => {
  inputRefundKey.value = getKey(refundTotal, keyStr)
  onChangeRefundTotal(refundTotal, keyStr, true)
}

const onChangeRefundTotal = (refundTotal: number, keyStr: String, enter?: boolean) => {
  const key = getKey(refundTotal, keyStr)
  if (!enter && inputRefundKey.value === key) {
    return
  } else {
    inputRefundKey.value = key
  }
  const sumFee = dataSource.value.find(item => item.keyStr === keyStr)
  if (refundTotal && refundTotal >= 0) {
    if (!sumFee) {
      message.warning('未匹配到划价单明细')
      return
    }
    if (sumFee.curTotal > 0) {
      if (refundTotal > sumFee.curTotal) {
        message.warning('不能超出原划价单数量')
        sumFee.refundTotal = sumFee.curTotal
        const refundInput = inputRef.value['refund_' + keyStr]
        if (refundInput) {
          refundInput.focus()
        }
      } else {
        sumFee.refundTotal = refundTotal
      }
    }
  } else if (refundTotal !== 0) {
    sumFee.refundTotal = undefined
  }
}

const getKey = (refundTotal: any, keyStr: String) => {
  let value = refundTotal
  if (filters.isNumber(refundTotal)) {
    value = Number(refundTotal).toFixed(4)
  }
  return keyStr + '_' + value
}

const setInputRef = (key: any, ref: any) => {
  inputRef.value[key] = ref
}

const oeLs = computed(() => {
  let oeLs = dataSource.value
  if (searchFormModel.dateType === oeDate) {
    oeLs = dataSource.value.filter(item => item.bsnDate === bsnDate.value)
  }
  return oeLs
})

const visibleReturnMode = computed(() => {
  const rows = dataSource.value.filter(item => item.stockReq === 1)
  return rows && rows.length > 0
})

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" title="医嘱费用冲红" width="1300px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Form :model="formModel" w-full>
        <Row>
          <Col flex="auto">
          </Col>
          <Col flex="400px" p-t-1 v-if="visibleReturnMode && !drugAccountingMode">
            <Form-item label="退药模式" name="returnToWm" :colon="false" :label-col="{ span: 6 }">
              <Radio-group v-model:value="formModel.returnToWm">
                <Radio :value="1">
                  退回药房
                </Radio>
                <Radio :value="0">
                  退回病区
                </Radio>
                <Radio :value="2">
                  使用/破损
                </Radio>
              </Radio-group>
            </Form-item>
          </Col>
          <Col flex="100px">
            <Button type="dashed" @click="handleCancel">
              关闭
            </Button>
          </Col>
          <Col flex="100px">
            <Button @click="handleRefundOe" :loading="confirmLoading" type="primary" danger :disabled="disableRefundBtn">
              费用冲红
            </Button>
          </Col>
        </Row>
      </Form>
    </template>
    <div class="content-req">
      <div>
        <Form :label-col="{ span: 6 }" :model="searchFormModel" w-full>
          <Row>
            <Col flex="400px" p-t-1>
              <Radio-group v-model:value="searchFormModel.dateType">
                <Radio :value="oeDate">
                  医嘱日期
                </Radio>
                <Radio :value="all">
                  全部
                </Radio>
              </Radio-group>
            </Col>
            <Col flex="auto">
              <div class="op-btn">
                <Button @click="handleRefundAll" danger :disabled="!dataSource || dataSource.length === 0">
                  <template #icon>
                    <DoubleRightOutlined/>
                  </template>
                  全部冲红
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
      </div>
      <Table
        :rowKey="(record: any) => record.keyStr"
        :columns="columns"
        :has-surely="false"
        :data-source="oeLs"
        :scroll="{ y: 'calc(100vh - 240px)'}">
        <template #bodyCell="{ text, column, record, index }">
          <template v-if="column.dataIndex === 'artDesc'">
            <div>
              <span class="art-name">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
            </div>
            <div v-if="record.producer">{{ record.producer }}</div>
          </template>
          <template v-if="column.dataIndex === 'refundTotal'">
            <Input-number
                v-model:value="record.refundTotal"
                placeholder="请录入冲红数量"
                :min="0"
                :max="record.curTotal > 0 ? record.curTotal : 0"
                style="width: 95%;"
                :ref="el => setInputRef('refund_' + record.keyStr, el)"
                @blur="onChangeRefundTotal($event.target.value, record.keyStr)"
                @pressEnter="onEnterRefundTotal($event.target.value, record.keyStr)"/>
          </template>
          <template v-if="column.dataIndex === 'curTotal'">
            <span v-if="text > 0">{{ text }}</span>
            <span v-else class="total-le0">{{ text }}</span>
            <DoubleRightOutlined v-if="text > 0" @click="handleSetRowRefundTotal(record.keyStr)" m-l-1 class="total-click"/>
          </template>
          <template v-else-if="['oriTotal', 'amount'].includes(column.dataIndex)">
            <span v-if="text > 0">{{ text }}</span>
            <span v-else class="total-le0">{{ text }}</span>
          </template>
        </template>
      </Table>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.content-req {
  height: calc(100vh - 160px);
  overflow: auto;
}
.art-name {
  font-weight: bold;
}
.total-gt0 {
  color: #018338;
}
.total-le0 {
  color: #ff0000;
}
.total-click {
  color: #ff6666;
  cursor: pointer;
}
.same-art-feeLs {
  font-weight: bold;
  font-size: 20px;
  padding: 20px 0px 10px 5px;
}
.op-btn {
  text-align: right;
  button {
    margin-right: 10px;
  }
}
</style>
