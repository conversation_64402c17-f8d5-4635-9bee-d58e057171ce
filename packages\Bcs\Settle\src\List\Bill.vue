<script lang="ts" setup>
import { add, Api, Format } from '@idmy/core'
import { BillDetail } from '@mh-bcs/bill'
import { PaidStatusHelp } from '@mh-bcs/help'
import { listBillIdsByCashIdFromCashBill, listBillsByCashIdFromCashBill } from '@mh-bcs/util'
import { Table, TabPane, Tabs } from 'ant-design-vue'

defineProps({
  cashId: { type: Number, required: true },
  visitId: { type: Number },
})

const columns = [
  { align: 'center', dataIndex: 'billId', title: '划价流水', width: 90 },
  { align: 'center', dataIndex: 'billType', title: '划价类型', width: 100 },
  { align: 'left', dataIndex: 'notes', minWidth: 140, title: '摘要' },
  { align: 'center', dataIndex: 'applyDeptName', title: '开单科室', width: 110 },
  { align: 'center', dataIndex: 'clinicianName', title: '开单医生', width: 80 },
  { align: 'center', dataIndex: 'billDate', title: '开单日期', width: 90 },
  { align: 'right', dataIndex: 'discount', title: '优惠', width: 70 },
  { align: 'right', dataIndex: 'amount', title: '金额', width: 90 },
]

const tabKey = ref('detail')
</script>
<template>
  <div class="oh">
    <Api ref="apiRef" v-slot="{ output }" :load="() => listBillsByCashIdFromCashBill(cashId)" spin type="Array">
      <Tabs v-model:activeKey="tabKey" :destroy-inactive-tab-pane="false" animated>
        <TabPane key="detail" tab="划价单明细">
          <Api v-slot="{ output }" :load="() => listBillIdsByCashIdFromCashBill(cashId)" first spin type="Array">
            <BillDetail :billIds="output as number[]" :maxHeight="400" :visitId="visitId" />
          </Api>
        </TabPane>
        <TabPane key="bill" tab="划价单">
          <Table :columns="columns" :dataSource="output" :max-height="400" :pagination="false" rowKey="billId" size="small">
            <template #headerCell="{ column }">
              <PaidStatusHelp v-if="column.dataIndex === 'paidStatus'" />
            </template>
            <template #bodyCell="{ column: col, record: row }">
              <Format v-if="col.dataIndex === 'billType'" :value="row.billType" type="Enum" params="BillType" />
              <Format v-if="col.dataIndex === 'amount'" :value="row.amount" type="Currency" />
              <Format v-if="col.dataIndex === 'discount'" :value="add(row.discounted ?? 0, row.derated ?? 0)" type="Currency" />
            </template>
            <template #expandedRowRender="{ record }">
              <BillDetail :showHeader="false" :showNo="false" :billIds="[record.billId]" :visitId="record.visitId" />
            </template>
          </Table>
        </TabPane>
      </Tabs>
    </Api>
  </div>
</template>
