<script setup lang="ts">
import { shallowRef, ref, computed, watch, onMounted, h } from 'vue'
import { CheckCircleOutlined, CloseCircleOutlined, WarningOutlined, ExclamationCircleOutlined, InfoCircleOutlined, AlertOutlined } from '@ant-design/icons-vue'
import { Modal, Form, FormItem, Input, Descriptions, DescriptionsItem, Button, Spin, message, Checkbox, Card, Tag, Divider, Alert, Tabs, Badge } from 'ant-design-vue'
import { Input as AntInput } from 'ant-design-vue'
const { TextArea } = AntInput
const { TabPane } = Tabs
import type { FormInstance } from 'ant-design-vue'
import type { Rule } from 'ant-design-vue/es/form'
import { preAnalysis, preFeedback, inAnalysis, inFeedback, isEnabledDip } from '@mh-mi/util'
import { Currents } from '@mh-base/core'
import type { TriggerScene, ViolationItem, FeedbackItem, FeedbackParams, AnalysisParams } from './types'

const props = withDefaults(
  defineProps<{
    trigScen: TriggerScene
    label?: string
    modalWidth?: string | number
    modalTitle?: string
    defaultEnabled?: boolean
    storageKey?: string // 用于localStorage存储的key，如果不传入，会自动生成一个包含trigScen的key
  }>(),
  {
    label: '事前事中分析',
    modalWidth: '700px',
    modalTitle: '事前事中分析',
    defaultEnabled: false,
    storageKey: '', // 默认为空，会在getStorageKey函数中生成一个包含trigScen的key
  }
)

const emit = defineEmits(['close', 'success', 'error', 'change'])

// 控制状态
const enabled = ref(props.defaultEnabled)
const open = shallowRef(false)
const loading = shallowRef(false)
const saveLoading = shallowRef(false)
const formLoading = shallowRef(false)
const msg = shallowRef<string>('')
const result = shallowRef<ViolationItem[]>([])
const dspoVisible = shallowRef<boolean>(false)
const dspoFormRef = ref<FormInstance>()
const dspoForm = ref<any>({})
const isInAnalysis = ref(false) // 是否是事中分析
const activeTabKey = ref<string>('') // 当前激活的Tab
const currentFeedback = ref<string>('') // 当前编辑的反馈内容
const apiEnabledDip = ref<boolean | null>(null) // API返回的DIP启用状态

// 表单验证规则
const rules: Record<string, Rule[]> = {
  dspoWayRea: [{ required: true, message: '请输入处理原因', trigger: 'blur' }],
  feedback: [{ required: true, message: '请输入反馈内容', trigger: 'blur' }],
}

// 获取localStorage中的存储key
const getStorageKey = () => {
  // 如果用户提供了storageKey，则使用用户提供的key
  // 否则，使用默认的key前缀'dip_enabled'
  const keyPrefix = props.storageKey || 'dip_enabled'
  // 使用trigScen和当前用户ID组合成唯一的key
  return `${keyPrefix}_${props.trigScen}_${Currents.id}`
}

// 根据当前分析类型动态生成弹窗标题
const dynamicModalTitle = computed(() => {
  return isInAnalysis.value ? '事中分析' : '事前分析'
})

// 根据API返回的启用状态决定是否显示控件
const showControl = computed(() => {
  // 如果API返回禁用，则不显示控件
  return apiEnabledDip.value !== false
})

// 计算是否所有反馈都已完成
const allFeedbackCompleted = computed(() => {
  if (!result.value || result.value.length === 0) return false

  // 检查每个违规项是否都有非空的反馈内容
  return result.value.every(item => {
    const hasFeedback = !!item.feedback && item.feedback.trim() !== ''
    return hasFeedback
  })
})

// 获取当前选中的违规项
const currentViolation = computed(() => {
  if (!activeTabKey.value || !result.value || result.value.length === 0) return null
  return result.value.find(item => item.jrId === activeTabKey.value) || null
})

// 初始化违规项的反馈状态
const initViolationFeedback = () => {
  if (!result.value || result.value.length === 0) return

  // 为每个违规项添加反馈字段和完成状态
  result.value.forEach(item => {
    item.feedback = item.feedback || ''
    item.completed = !!item.feedback
  })

  // 强制更新result数组，触发UI更新
  result.value = [...result.value]

  // 设置第一个违规项为当前选中项
  if (result.value.length > 0) {
    activeTabKey.value = result.value[0].jrId
    currentFeedback.value = result.value[0].feedback || ''

    // 打印日志，帮助调试
    console.log('初始化违规项反馈状态：', {
      resultLength: result.value.length,
      activeTabKey: activeTabKey.value,
      firstItem: result.value[0],
    })
  }
}

// 保存当前反馈内容
const saveCurrentFeedback = () => {
  if (!currentViolation.value) return false

  // 更新当前违规项的反馈内容和完成状态
  const feedbackText = currentFeedback.value.trim()
  currentViolation.value.feedback = feedbackText
  currentViolation.value.completed = !!feedbackText

  // 强制更新result数组，触发UI更新
  result.value = [...result.value]

  // 打印日志，帮助调试
  console.log('保存反馈内容:', {
    jrId: currentViolation.value.jrId,
    feedback: feedbackText,
    completed: currentViolation.value.completed,
    allCompleted: result.value.every(item => item.completed),
  })

  return !!feedbackText
}

// 切换Tab时保存当前反馈并加载新的反馈
const handleTabChange = (key: string) => {
  // 先保存当前反馈
  saveCurrentFeedback()

  // 设置新的当前Tab
  activeTabKey.value = key

  // 加载新Tab的反馈内容
  const newViolation = result.value.find(item => item.jrId === key)
  if (newViolation) {
    currentFeedback.value = newViolation.feedback || ''
  } else {
    currentFeedback.value = ''
  }
}

// 获取严重程度的文本描述
const getSeverityText = (sevDeg: string) => {
  switch (sevDeg) {
    case '1':
      return '明确违规'
    case '2':
      return '高度可疑'
    case '3':
      return '轻度可疑'
    default:
      return '未知'
  }
}

// 获取严重程度的颜色
const getSeverityColor = (sevDeg: string) => {
  switch (sevDeg) {
    case '1':
      return 'error'
    case '2':
      return 'warning'
    case '3':
      return 'processing'
    default:
      return 'default'
  }
}

// 获取严重程度的图标
const getSeverityIcon = (sevDeg: string) => {
  switch (sevDeg) {
    case '1':
      return h(ExclamationCircleOutlined)
    case '2':
      return h(WarningOutlined)
    case '3':
      return h(InfoCircleOutlined)
    default:
      return h(AlertOutlined)
  }
}

// 获取Tab标题
const getTabTitle = (item: ViolationItem, index: number) => {
  // 获取最新的完成状态
  const isCompleted = !!item.feedback && item.feedback.trim() !== ''

  // 使用h函数创建带有Badge的标题
  return h('div', { class: 'tab-title' }, [
    h(Badge, {
      status: isCompleted ? 'success' : 'processing',
      text: `违规项 ${index + 1}`,
      class: isCompleted ? 'completed-tab' : '',
    }),
  ])
}

// 从localStorage中获取启用状态
const getEnabledFromStorage = () => {
  const key = getStorageKey()
  // key一定不为空，因为getStorageKey函数会生成一个默认的key

  try {
    const storedValue = localStorage.getItem(key)
    if (storedValue === null) return props.defaultEnabled
    return storedValue === 'true'
  } catch (e) {
    console.error('从localStorage获取启用状态失败:', e)
    return props.defaultEnabled
  }
}

// 保存启用状态到localStorage
const saveEnabledToStorage = (value: boolean) => {
  const key = getStorageKey()
  // key一定不为空，因为getStorageKey函数会生成一个默认的key

  try {
    localStorage.setItem(key, String(value))
  } catch (e) {
    console.error('保存启用状态到localStorage失败:', e)
  }
}

// 组件挂载时从localStorage中获取启用状态，并调用API获取参数配置
onMounted(() => {
  // 无论是否提供了storageKey，都从localStorage中获取启用状态
  enabled.value = getEnabledFromStorage()

  // 调用API获取是否启用DIP事前事中分析
  isEnabledDip()
    .then((isEnabled: boolean) => {
      console.log('是否启用DIP事前事中分析:', isEnabled)
      // 保存API返回的启用状态
      apiEnabledDip.value = isEnabled
      // 注意：不再覆盖本地设置，而是通过actualDisplayMode控制开关的显示
    })
    .catch((error: any) => {
      console.error('获取DIP启用状态失败:', error)
      // 出错时默认为启用
      apiEnabledDip.value = true
    })
})

// 监听启用状态变化
watch(enabled, newValue => {
  emit('change', newValue)
  // 保存到localStorage，无论是否提供了storageKey
  saveEnabledToStorage(newValue)
})

// 监听反馈内容变化
watch(
  currentFeedback,
  () => {
    // 当反馈内容变化时，自动保存
    saveCurrentFeedback()
  },
  { immediate: false }
)

/**
 * 调用事前分析
 * @param visitId 就诊ID
 * @param oeLs 医嘱列表，可以是数字数组（医嘱ID列表）或对象数组（完整医嘱信息）
 * @param cashId 收费ID（可选）
 * @returns 分析结果
 */
async function callPreDip(visitId: number, oeLs: number[] | any[], cashId?: number) {
  // 检查API返回的启用状态
  if (apiEnabledDip.value === false) {
    emit('success', { success: true, message: '事前分析已在系统中禁用' })
    return { success: true, message: '事前分析已在系统中禁用' }
  }

  // 如果未启用，直接返回成功
  if (!enabled.value) {
    emit('success', { success: true, message: '事前分析已禁用' })
    return { success: true, message: '事前分析已禁用' }
  }

  // 重置状态
  result.value = []
  activeTabKey.value = ''
  currentFeedback.value = ''
  msg.value = ''

  // 设置为事前分析
  isInAnalysis.value = false
  open.value = true
  loading.value = true
  try {
    // 确保oeLs是数组
    const oeList = Array.isArray(oeLs) ? oeLs : [oeLs]

    const params: AnalysisParams = {
      visit_id: visitId,
      oe_list: oeList,
      org_id: Currents.tenantId,
      user_id: Currents.id,
      trig_scen: props.trigScen,
      cash_id: cashId,
    }
    const res: any = await preAnalysis(params)
    console.log('事前分析结果：', res)
    loading.value = false

    // 处理返回结果，API已经在内部处理了code，所以直接使用返回的数据
    if (Array.isArray(res) && res.length > 0) {
      // 直接返回了违规数组
      result.value = res
    } else if (res && Array.isArray(res.result) && res.result.length > 0) {
      // 有些情况下可能包装在result字段中
      result.value = res.result
    } else if (res && Array.isArray(res.data) && res.data.length > 0) {
      // 有些情况下可能包装在data字段中
      result.value = res.data
    } else {
      // 没有违规项，显示成功提示
      msg.value = '校验通过'
      message.success(msg.value)
      // 不自动关闭，让用户看到结果后手动关闭
      loading.value = false
      emit('success', { success: true, message: msg.value })
      return { success: true, message: msg.value }
    }

    // 初始化违规项的反馈状态
    initViolationFeedback()
  } catch (error: any) {
    console.error(error)
    loading.value = false
    msg.value = error.message || '分析过程中发生错误'
    message.error(msg.value)
    return { success: false, message: msg.value }
  }
}

/**
 * 调用事中分析
 * @param visitId 就诊ID
 * @param oeLs 医嘱列表，可以是数字数组（医嘱ID列表）或对象数组（完整医嘱信息）
 * @param cashId 收费ID（可选）
 * @returns 分析结果
 */
async function callInDip(visitId: number, oeLs: number[] | any[], cashId?: number) {
  // 检查API返回的启用状态
  if (apiEnabledDip.value === false) {
    emit('success', { success: true, message: '事中分析已在系统中禁用' })
    return { success: true, message: '事中分析已在系统中禁用' }
  }

  // 如果未启用，直接返回成功
  if (!enabled.value) {
    emit('success', { success: true, message: '事中分析已禁用' })
    return { success: true, message: '事中分析已禁用' }
  }

  // 重置状态
  result.value = []
  activeTabKey.value = ''
  currentFeedback.value = ''
  msg.value = ''

  // 设置为事中分析
  isInAnalysis.value = true
  open.value = true
  loading.value = true
  try {
    // 确保oeLs是数组
    const oeList = Array.isArray(oeLs) ? oeLs : [oeLs]

    const params: AnalysisParams = {
      visit_id: visitId,
      oe_list: oeList,
      org_id: Currents.tenantId,
      user_id: Currents.id,
      trig_scen: props.trigScen,
      cash_id: cashId,
    }
    const res: any = await inAnalysis(params)
    console.log('事中分析结果：', res)
    loading.value = false

    // 处理返回结果，API已经在内部处理了code，所以直接使用返回的数据
    if (Array.isArray(res) && res.length > 0) {
      // 直接返回了违规数组
      result.value = res
    } else if (res && Array.isArray(res.result) && res.result.length > 0) {
      // 有些情况下可能包装在result字段中
      result.value = res.result
    } else if (res && Array.isArray(res.data) && res.data.length > 0) {
      // 有些情况下可能包装在data字段中
      result.value = res.data
    } else {
      // 没有违规项，显示成功提示
      msg.value = '校验通过'
      message.success(msg.value)
      // 不自动关闭，让用户看到结果后手动关闭
      loading.value = false
      emit('success', { success: true, message: msg.value })
      return { success: true, message: msg.value }
    }

    // 初始化违规项的反馈状态
    initViolationFeedback()
  } catch (error: any) {
    console.error(error)
    loading.value = false
    msg.value = error.message || '分析过程中发生错误'
    message.error(msg.value)
    return { success: false, message: msg.value }
  }
}

/**
 * 处理确认操作
 * @param flag 处理方式：1-继续执行，2-返回修改
 */
function handleConfirm(flag: number) {
  // 保存当前编辑的反馈
  saveCurrentFeedback()

  // 强制更新result数组，确保UI状态同步
  result.value = [...result.value]

  saveLoading.value = true
  if (flag === 1) {
    // 继续执行，检查是否所有反馈都已完成
    const allCompleted = result.value.every(item => !!item.feedback && item.feedback.trim() !== '')
    if (!allCompleted) {
      message.error('请为所有违规项填写反馈内容')
      saveLoading.value = false
      return
    }

    // 需要输入处理原因
    dspoVisible.value = true
    dspoForm.value = {}
    saveLoading.value = false
  } else {
    // 返回修改，直接提交反馈
    const params: FeedbackItem[] = []
    result.value.forEach(item => {
      params.push({
        warnRsltId: item.jrId,
        dspoWay: 2,
        dspoWayRea: item.feedback || '',
      })
    })

    const feedbackParams: FeedbackParams = {
      warnType: 1,
      warns: params,
    }

    // 根据当前分析类型选择不同的反馈方法
    const feedbackMethod = isInAnalysis.value ? inFeedback : preFeedback

    feedbackMethod(feedbackParams)
      .then((res: any) => {
        saveLoading.value = false
        console.log('反馈结果：', res)
        // API已经在内部处理了code，所以直接处理成功情况
        message.warning('用户选择返回修改')
        handleClose()
        emit('error', { success: false, message: '用户选择返回修改', action: 'modify' })
      })
      .catch((error: any) => {
        saveLoading.value = false
        message.error(error.message || '反馈失败')
      })
  }
}

/**
 * 处理原因确认
 */
function handleDeopConfirm() {
  dspoFormRef.value?.validate().then(() => {
    formLoading.value = true
    const params: FeedbackItem[] = []
    result.value.forEach(item => {
      params.push({
        warnRsltId: item.jrId,
        dspoWay: 1,
        dspoWayRea: `${item.feedback || ''} | ${dspoForm.value.dspoWayRea}`, // 合并每个违规项的反馈内容和总体处理原因
      })
    })

    const feedbackParams: FeedbackParams = {
      warnType: 1,
      warns: params,
    }

    // 根据当前分析类型选择不同的反馈方法
    const feedbackMethod = isInAnalysis.value ? inFeedback : preFeedback

    feedbackMethod(feedbackParams)
      .then(() => {
        formLoading.value = false
        // API已经在内部处理了code，所以直接处理成功情况
        message.success('反馈成功')
        handleDspoClose()
        handleClose()
        emit('success', { success: true, message: '反馈成功', action: 'continue', reason: dspoForm.value.dspoWayRea })
      })
      .catch((error: any) => {
        message.error(error.message || '反馈失败')
        formLoading.value = false
      })
  })
}

/**
 * 关闭处理原因弹窗
 */
function handleDspoClose() {
  dspoVisible.value = false
  dspoForm.value = {}
  saveLoading.value = false
}

/**
 * 关闭主弹窗
 * 无论何时都可以关闭弹窗
 */
function handleClose() {
  open.value = false
  // 重置所有状态
  result.value = []
  activeTabKey.value = ''
  currentFeedback.value = ''
  msg.value = ''
  loading.value = false
  saveLoading.value = false
  emit('close')

  // 打印日志，帮助调试
  console.log('弹窗已关闭，状态已重置')
}

/**
 * 切换启用状态
 */
function toggleEnabled() {
  enabled.value = !enabled.value
  emit('change', enabled.value)
}

// 暴露方法给外部调用
defineExpose({
  callPreDip, // 事前分析方法
  callInDip, // 事中分析方法
  toggleEnabled,
})
</script>

<template>
  <!-- 控制界面，只有在系统配置允许时才显示 -->
  <div v-if="showControl" class="dip-control">
    <Checkbox v-model:checked="enabled">{{ label }}</Checkbox>
  </div>

  <!-- 分析结果弹窗 -->
  <Modal v-model:open="open" :title="dynamicModalTitle" :closable="false" :mask-closable="false" :width="modalWidth" centered @cancel="handleClose">
    <template #footer>
      <div v-if="result && result.length > 0" class="footer-buttons">
        <Button type="primary" :loading="saveLoading" :disabled="!allFeedbackCompleted" @click="handleConfirm(1)" class="continue-button">
          <template #icon>
            <check-circle-outlined />
          </template>
          继续执行
        </Button>
        <Button danger :loading="saveLoading" @click="handleConfirm(2)" class="modify-button">
          <template #icon>
            <close-circle-outlined />
          </template>
          返回修改
        </Button>
      </div>
      <Button v-else type="primary" @click="handleClose">
        <template #icon>
          <close-circle-outlined />
        </template>
        关闭
      </Button>
    </template>

    <!-- 内容区域 -->
    <Spin :spinning="loading" tip="正在调用接口进行分析中...">
      <template v-if="result && result.length > 0">
        <Tabs v-model:activeKey="activeTabKey" type="card" class="violation-tabs" @change="handleTabChange" :animated="false">
          <TabPane v-for="(item, index) in result" :key="item.jrId" :tab="getTabTitle(item, index)" :forceRender="true">
            <Card :key="item.jrId" class="violation-card" :bordered="true">
              <template #title>
                <div class="violation-title">
                  <span class="violation-index">{{ index + 1 }}</span>
                  <span class="violation-rule-name">{{ item.ruleName }}</span>
                  <Tag :color="getSeverityColor(item.sevDeg)" class="violation-severity">
                    {{ getSeverityText(item.sevDeg) }}
                  </Tag>
                </div>
              </template>

              <div class="violation-content">
                <div class="violation-item">
                  <div class="violation-label">违规内容：</div>
                  <div class="violation-value">{{ item.volaCont }}</div>
                </div>

                <Divider class="violation-divider" />

                <div class="violation-item">
                  <div class="violation-label">违规依据：</div>
                  <div class="violation-value">{{ item.volaEvid }}</div>
                </div>

                <Divider class="violation-divider" />

                <div class="feedback-section">
                  <div class="feedback-title">请填写反馈内容：</div>
                  <TextArea
                    v-model:value="currentFeedback"
                    :rows="4"
                    :maxlength="200"
                    placeholder="请输入对此违规项的反馈内容"
                    show-count
                    class="feedback-input"
                    @change="saveCurrentFeedback"
                    @blur="saveCurrentFeedback"
                  />
                </div>
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </template>
      <div v-else class="empty-result">
        <Alert v-if="msg" :message="msg" type="success" show-icon class="empty-message" />
        <div v-else class="empty-placeholder">暂无分析结果</div>
      </div>
    </Spin>
  </Modal>

  <!-- 输入原因弹窗 -->
  <Modal v-model:open="dspoVisible" title="请输入处理原因" centered :closable="false" :mask-closable="false" @cancel="handleDspoClose">
    <template #footer>
      <div class="footer-buttons">
        <Button danger :loading="formLoading" @click="handleDspoClose" class="cancel-button">
          <template #icon>
            <close-circle-outlined />
          </template>
          取消
        </Button>
        <Button type="primary" :loading="formLoading" @click="handleDeopConfirm" class="confirm-button">
          <template #icon>
            <check-circle-outlined />
          </template>
          确认
        </Button>
      </div>
    </template>

    <Alert message="请说明继续执行的原因" description="为了保证医疗安全，请详细说明您选择继续执行的原因，这将被记录在系统中。" type="info" show-icon class="mb-16px" />

    <Form ref="dspoFormRef" :model="dspoForm" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <FormItem label="处理原因" name="dspoWayRea">
        <TextArea v-model:value="dspoForm.dspoWayRea" :maxlength="200" placeholder="请输入处理原因" :rows="4" show-count />
      </FormItem>
    </Form>
  </Modal>
</template>

<style scoped lang="less">
.dip-control {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
}

.dip-label {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

// 违规列表样式
.violation-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.violation-tabs {
  margin-bottom: 16px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 16px;
  }

  :deep(.ant-tabs-tab) {
    padding: 8px 16px;
    transition: all 0.3s;

    &.ant-tabs-tab-active {
      background-color: #e6f7ff;
    }
  }
}

.tab-title {
  display: flex;
  align-items: center;
}

.completed-tab {
  color: #52c41a;
  font-weight: bold;
}

.violation-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.violation-title {
  display: flex;
  align-items: center;
}

.violation-index {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #1890ff;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  margin-right: 8px;
}

.violation-rule-name {
  flex: 1;
  font-weight: bold;
  margin-right: 8px;
}

.violation-severity {
  font-size: 12px;
}

.violation-content {
  padding: 8px 0;
}

.violation-item {
  display: flex;
  margin-bottom: 8px;
}

.violation-label {
  width: 80px;
  color: rgba(0, 0, 0, 0.65);
  text-align: right;
  padding-right: 8px;
}

.violation-value {
  flex: 1;
  color: rgba(0, 0, 0, 0.85);
}

.violation-divider {
  margin: 12px 0;
}

.feedback-section {
  margin-top: 16px;
}

.feedback-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
}

.feedback-input {
  width: 100%;
  border-radius: 4px;

  &:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// 空结果样式
.empty-result {
  padding: 24px 0;
  text-align: center;
}

.empty-message {
  max-width: 400px;
  margin: 0 auto;
}

.empty-placeholder {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

// 按钮样式
.footer-buttons {
  display: flex;
  justify-content: flex-end;
}

.continue-button,
.modify-button,
.confirm-button,
.cancel-button {
  margin-left: 8px;
}

// 工具类
.mb-16px {
  margin-bottom: 16px;
}

.mt-16px {
  margin-top: 16px;
}
</style>
