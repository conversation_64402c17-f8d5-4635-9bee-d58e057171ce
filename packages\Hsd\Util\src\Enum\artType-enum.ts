/**
 * 条目类别
 * 11-西药 12-中成药 13-中药饮片 14-材料 16-营养 20-诊查 21-治疗 22-检验 23-检查 24-手术 25-麻醉 26-护理 27-诊疗 28-输血 29-输氧 31-其他 32-转科
 * 33-术后 34-出院 35-转院 36-死亡 37-产后 38-宣教 41-病危通知 42-解除病危 43-膳食
 */
export enum ArtTypeEnum {
  West_Drug = 11,
  Chinese_Patent_Drug = 12,
  Chinese_Drug = 13,
  Material = 14,
  Nutrition = 16,
  Diagnosis = 20,
  Treatment = 21,
  Clinic = 22,
  Inspection = 23,
  Surgery = 24,
  Anesthesia = 25,
  Nursing = 26,
  Visit = 27,
  Blood = 28,
  Oxygen = 29,
  Other = 31,
  Transfer = 32,
  Post_Operation = 33,
  Out_Hospital = 34,
  Transfer_Hospital = 35,
  Death = 36,
  Post_Operative = 37,
  Education = 38,
  Danger_Notice = 41,
  Danger_Release = 42,
  Diet = 43
}

// 创建一个映射对象，用于根据编码获取枚举成员
const artTypeMap = Object.entries(ArtTypeEnum).reduce((acc: any, [key, value]) => {
  acc[value] = key; // 将编码作为键，枚举成员名称作为值
  return acc;
}, {} as { [key: number]: keyof typeof ArtTypeEnum })

// 函数：根据编码获取枚举值
export function getArtTypeNameByCode(code: number): string | undefined {
  return artTypeMap[code]
}
