import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { SectionArtSelect, ArtSelect, WmDeptArtSelect, WmOrgArtSelect, SelectWithUnitTotal } from '@mh-inpatient-hsd/selector'
import '@mh-inpatient-hsd/selector/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "@mh-inpatient-hsd/util": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}`

// 科室物品选择器
export const sectionArtSelectUsage = wrapCodeExample(`<template>
  <Form :model="formState" layout="vertical">
    <Form.Item label="科室物品" name="sectionArtId">
      <SectionArtSelect
        v-model:value="formState.sectionArtId"
        :section-id="sectionId"
        placeholder="请选择科室物品"
        @change="handleChange"
        @selected="handleSelected"
      />
    </Form.Item>
  </Form>
</template>

<script setup>
import { SectionArtSelect } from '@mh-inpatient-hsd/selector'
import '@mh-inpatient-hsd/selector/index.css'
import { Form, message } from 'ant-design-vue'
import { ref } from 'vue'

const formState = ref({
  sectionArtId: undefined
})

// 模拟数据
const sectionId = 40

// 选择回调
const handleChange = (value, option) => {
  console.log('选择值:', value)
  console.log('选择项:', option)
  message.success(\`已选择: \${option?.label || value}\`)
}

// 选择物品回调
const handleSelected = (selectedArt) => {
  console.log('选择物品:', selectedArt)
  message.success(\`已选择物品: \${selectedArt?.artName || '未知物品'}\`)
}
</script>`)



// 物品选择器
export const artSelectUsage = wrapCodeExample(`<template>
  <Form :model="formState" layout="vertical">
    <Form.Item label="物品" name="artId">
      <ArtSelect
        v-model:value="formState.artId"
        placeholder="请选择物品"
        @change="handleChange"
        @selected="handleSelected"
      />
    </Form.Item>
  </Form>
</template>

<script setup>
import { ArtSelect } from '@mh-inpatient-hsd/selector'
import '@mh-inpatient-hsd/selector/index.css'
import { Form, message } from 'ant-design-vue'
import { ref } from 'vue'

const formState = ref({
  artId: undefined
})

// 选择回调
const handleChange = (value, option) => {
  console.log('选择值:', value)
  console.log('选择项:', option)
  message.success(\`已选择: \${option?.label || value}\`)
}

// 选择物品回调
const handleSelected = (selectedArt) => {
  console.log('选择物品:', selectedArt)
  message.success(\`已选择物品: \${selectedArt?.artName || '未知物品'}\`)
}
</script>`)

// 药库科室物品选择器
export const wmDeptArtSelectUsage = wrapCodeExample(`<template>
  <Form :model="formState" layout="vertical">
    <Form.Item label="药库科室物品" name="wmDeptArtId">
      <WmDeptArtSelect
        v-model:value="formState.wmDeptArtId"
        :deptCode="wmDeptCode"
        :searchType="6"
        placeholder="请选择药库科室物品"
        @selected="handleSelected"
      />
    </Form.Item>
  </Form>
</template>

<script setup>
import { WmDeptArtSelect } from '@mh-inpatient-hsd/selector'
import '@mh-inpatient-hsd/selector/index.css'
import { Form, message } from 'ant-design-vue'
import { ref } from 'vue'

const formState = ref({
  wmDeptArtId: undefined
})

// 模拟数据
const wmDeptCode = '000013'

// 选择物品回调
const handleSelected = (selectedArt) => {
  console.log('选择物品:', selectedArt)
  message.success(\`已选择物品: \${selectedArt?.artName || '未知物品'}\`)
}
</script>`)

// 药库机构物品选择器
export const wmOrgArtSelectUsage = wrapCodeExample(`<template>
  <Form :model="formState" layout="vertical">
    <Form.Item label="药库机构物品" name="wmOrgArtId">
      <WmOrgArtSelect
        v-model:value="formState.wmOrgArtId"
        placeholder="请选择药库机构物品"
        @selected="handleSelected"
      />
    </Form.Item>
  </Form>
</template>

<script setup>
import { WmOrgArtSelect } from '@mh-inpatient-hsd/selector'
import '@mh-inpatient-hsd/selector/index.css'
import { Form, message } from 'ant-design-vue'
import { ref } from 'vue'

const formState = ref({
  wmOrgArtId: undefined
})

// 选择物品回调
const handleSelected = (selectedArt) => {
  console.log('选择物品:', selectedArt)
  message.success(\`已选择物品: \${selectedArt?.artName || '未知物品'}\`)
}
</script>`)

// 条目选择带单位数量
export const selectWithUnitTotalUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleArtSelectWithUnitTotal">打开条目选择带单位数量弹窗</Button>
  <SelectWithUnitTotal
    ref="artSelectWithUnitTotalRef"
    :artSearchType="1"
    @add-art="handleAddArt"
  />
</template>

<script setup>
import { SelectWithUnitTotal } from '@mh-inpatient-hsd/selector'
import '@mh-inpatient-hsd/selector/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const artSelectWithUnitTotalRef = ref()

// 打开条目选择带单位数量弹窗
const handleVisibleArtSelectWithUnitTotal = () => {
  artSelectWithUnitTotalRef.value.open()
}

// 添加物品回调
const handleAddArt = (selectedArt) => {
  console.log('添加物品:', selectedArt)
  message.success(\`已添加物品: \${selectedArt?.artName || '未知物品'}\`)
}
</script>`)
