<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useIntersectionObserver } from '@vueuse/core'
import { Button } from 'ant-design-vue'
import type { PropType } from 'vue'
import { getBindKey } from '../Util/KeyMapUtil'

// 定义组件属性
const props = defineProps({
  // 继承Button组件的所有属性
  type: {
    type: String as PropType<'primary' | 'ghost' | 'dashed' | 'link' | 'text' | 'default'>,
    default: 'default',
  },
  size: {
    type: String as PropType<'large' | 'middle' | 'small'>,
    default: 'middle',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: [Boolean, Object],
    default: false,
  },
  shape: {
    type: String as PropType<'default' | 'circle' | 'round'>,
    default: 'default',
  },
  icon: {
    type: Object,
    default: null,
  },
  ghost: {
    type: <PERSON><PERSON>an,
    default: false,
  },
  danger: {
    type: Boolean,
    default: false,
  },
  block: {
    type: Boolean,
    default: false,
  },
  // 功能键（快捷键）
  functionKey: {
    type: String,
    default: '',
  },
  // 是否在按钮文本中显示快捷键
  showFunctionKey: {
    type: Boolean,
    default: true,
  },
  // 按钮标识，用于从localStorage中获取bindKey
  btnKey: {
    type: String,
    default: '',
  },
  // 页面标识，用于从localStorage中获取bindKey
  pageKey: {
    type: String,
    default: '',
  },
})

// 定义事件
const emit = defineEmits(['click'])

// 按钮引用
const buttonRef = ref()

// 获取插槽内容
const slots = defineSlots()

// 是否在视口中可见
const isVisible = ref(false)

// 计算实际的功能键
const actualFunctionKey = computed(() => {
  // 如果设置了btnKey和pageKey，则从localStorage中获取bindKey
  if (props.btnKey && props.pageKey) {
    return getBindKey(props.pageKey, props.btnKey, props.functionKey)
  }
  // 否则使用functionKey属性
  return props.functionKey
})

// 使用Intersection Observer检测组件是否在视口中可见
const { stop } = useIntersectionObserver(
  buttonRef,
  ([{ isIntersecting }]) => {
    isVisible.value = isIntersecting
  },
  { threshold: 0.1 } // 当10%的组件可见时触发
)

// 处理按钮点击
const handleClick = (event: MouseEvent) => {
  emit('click', event)
}

// 处理键盘事件
const handleKeyDown = (event: KeyboardEvent) => {
  const currentFunctionKey = actualFunctionKey.value
  if (!currentFunctionKey || props.disabled) return

  // 获取按键组合
  let keyCombo = ''

  // 添加修饰键
  if (event.ctrlKey) keyCombo += 'Ctrl + '
  if (event.altKey) keyCombo += 'Alt + '
  if (event.shiftKey) keyCombo += 'Shift + '
  if (event.metaKey) keyCombo += 'Meta + '

  // 添加主键
  if (event.key === ' ') {
    keyCombo += 'Space'
  } else if (event.key.length === 1) {
    keyCombo += event.key.toUpperCase()
  } else {
    keyCombo += event.key
  }

  // 去除末尾空格
  keyCombo = keyCombo.trim()

  // 检查是否匹配
  if (keyCombo === currentFunctionKey) {
    // 阻止默认行为
    event.preventDefault()

    // 如果按钮未禁用且未处于加载状态，则触发点击事件
    if (!props.disabled && !props.loading) {
      // 创建一个合成的点击事件
      const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        view: window,
      })

      // 触发按钮点击
      buttonRef.value?.$el.dispatchEvent(clickEvent)
    }
  }
}

// 根据可见性和功能键添加或移除键盘事件监听
const addKeyboardListener = () => {
  if (actualFunctionKey.value && isVisible.value) {
    window.addEventListener('keydown', handleKeyDown)
  }
}

const removeKeyboardListener = () => {
  window.removeEventListener('keydown', handleKeyDown)
}

// 监听可见性变化
watch(isVisible, newValue => {
  if (newValue && actualFunctionKey.value) {
    // 组件变为可见，添加事件监听
    addKeyboardListener()
  } else {
    // 组件变为不可见，移除事件监听
    removeKeyboardListener()
  }
})

// 监听功能键变化
watch(actualFunctionKey, (newValue, oldValue) => {
  // 如果之前有功能键，先移除旧的事件监听
  if (oldValue) {
    removeKeyboardListener()
  }

  // 如果现在有功能键且组件可见，添加新的事件监听
  if (newValue && isVisible.value) {
    addKeyboardListener()
  }
})

// 监听btnKey和pageKey变化
watch([() => props.btnKey, () => props.pageKey], () => {
  // 移除旧的事件监听
  removeKeyboardListener()

  // 如果有功能键且组件可见，添加新的事件监听
  if (actualFunctionKey.value && isVisible.value) {
    addKeyboardListener()
  }
})

// 组件挂载时添加事件监听（如果可见且有功能键）
onMounted(() => {
  if (actualFunctionKey.value && isVisible.value) {
    addKeyboardListener()
  }
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  removeKeyboardListener()
  // 停止Intersection Observer
  stop()
})

// 暴露方法
defineExpose({
  $el: buttonRef,
})
</script>

<template>
  <Button ref="buttonRef" :type="type" :size="size" :disabled="disabled" :loading="loading" :shape="shape" :icon="icon" :ghost="ghost" :danger="danger" :block="block" @click="handleClick">
    <template v-if="$slots.default">
      <slot v-if="!showFunctionKey || !actualFunctionKey"></slot>
      <template v-else>
        <slot></slot>
        <span class="function-key-text" v-if="actualFunctionKey"> ({{ actualFunctionKey }})</span>
      </template>
    </template>
  </Button>
</template>

<style scoped>
.function-key-text {
  opacity: 0.7;
  font-size: 0.9em;
  margin-left: 4px;
}
</style>
