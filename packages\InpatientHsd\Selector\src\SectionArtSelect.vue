<script setup lang="ts">
import { sectionArtPageApi } from "@mh-inpatient-hsd/util"
import { BaseAutoComplete } from "@mh-base/core"

const props = defineProps({
  // 查询范围： 无：所有，1：catType.in(201,301)，2：catType.eq(301)+stockReq=1, 3:查机构项目物价, 4:artType.in(14,26,29)（材料、护理、输氧）, 5:stockReq=1+catType.ne 301过滤耗材, 6:stockReq=1 药品+耗材
  sectionId: {
    type: Number,
    default: null
  }
})
const emit = defineEmits(['selected'])

const artDataModel = reactive({ // 门诊挂号收费项目
  columns: [
    { title: '条目ID', dataIndex: 'artId', width: 100, align: 'right' },
    { title: '品名', dataIndex: 'artName', width: 150 },
    { title: '规格', dataIndex: 'artSpec', width: 100 },
    { title: '生产厂家', dataIndex: 'producer', width: 150 },
    { title: '医保编码', dataIndex: 'miCode', width: 150 },
    { title: '库存包装数', dataIndex: 'totalPacks', width: 80, align: 'right' },
    { title: '包装单位', dataIndex: 'packUnit', width: 75, align: 'center' }
  ],
  searchFormModel: {
    order: 'asc',
    sidx: 't_article.art_id',
    artName: ''
  },
  options: [],
  pagination: {
    pageSize: 10, // 每页条数
    pageNum: 1, // 当前页码
    pages: 0, // 总页数
    total: 0, // 总条数
    showTotal: total => `共：${total} 条`,
    onChange: async (current, pageSize) => {
      artDataModel.pagination.pageSize = pageSize
      artDataModel.pagination.pageNum = current
      await artDataModel.loadOptions()
    }
  },
  loadOptions: async () => {
    artDataModel.searchFormModel.sectionId = props.sectionId
    const { list, pageNum, pages, total } = await sectionArtPageApi({
      ...artDataModel.searchFormModel,
      ...artDataModel.pagination
    })
    artDataModel.options = list
    artDataModel.pagination.pageNum = Number(pageNum)
    artDataModel.pagination.pages = Number(pages)
    artDataModel.pagination.total = Number(total)
  },
  onSearch: async (value: string) => {
    artDataModel.pagination.pageNum = 1
    artDataModel.pagination.artName = value
    await artDataModel.loadOptions()
  },
  onSelect: (item: any) => {
    if (item) {
      artDataModel.searchFormModel.artName = item.artName +  (item.artSpec ? ' ' + item.artSpec : '') + (item.producer? ' ' + item.producer: '')
      emit('selected', item)
    }
  },
})

const init = () => {
  artDataModel.searchFormModel.artName = undefined
  artDataModel.options = []
}

defineExpose({
  init
})
</script>

<template>
  <div>
    <base-auto-complete
      style="width: 100%"
      v-model:value="artDataModel.searchFormModel.artName"
      keyColumn="artId"
      :columns="artDataModel.columns"
      :options="artDataModel.options"
      :pagination="artDataModel.pagination"
      @onSearch="artDataModel.onSearch"
      @onSelect="artDataModel.onSelect"
    />
  </div>
</template>
