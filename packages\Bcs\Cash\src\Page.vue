<script lang="ts" setup>
import { Enum } from '@idmy/antd'
import { add, Api, Data, Format, subtract } from '@idmy/core'
import { Currents } from '@mh-base/core'
import { openBillFeeTypePercentage } from '@mh-bcs/bill'
import { pageCash } from '@mh-bcs/util'
import { PaymentTypeEnum } from '@mh-hip/payment-type'
import Table from '@surely-vue/table'
import { Button, Col, Form, FormItem, Input, InputNumber, Popover, RangePicker, Row, Tag, Tooltip } from 'ant-design-vue'
import { isNil } from 'lodash-es'
import Count from './Count.vue'

defineProps({
  showCond: { type: Boolean, default: true },
  showCount: { type: Boolean, default: true },
})

const inputParams = ref({
  params: {
    cashTypes: [],
    actionType: undefined,
    cashId: undefined,
    isCheck: 'false',
    clinicianName: undefined,
    payer: undefined,
    createdAts: [],
    payee: undefined,
    statuses: ['ING', 'OK'],
  },
  sorts: ['createdAt', 'desc'],
})

const apiRef = ref()
const countRef = ref()
const [onLoad] = useLoading(async (args?: Data) => {
  apiRef.value?.onLoad(args)
  countRef.value?.onLoad(inputParams.value)
}, true)

const columns = [
  {
    align: 'center',
    customRender: ({ index }: any) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '#',
    width: 45,
  },
  { align: 'center', dataIndex: 'cashId', fixed: 'left', title: '结算流水', width: 90 },
  { align: 'center', dataIndex: 'payer', title: '患者姓名', width: 90 },
  { align: 'center', dataIndex: 'clinicianName', title: '开单医生', width: 90 },
  { align: 'center', dataIndex: 'payee', title: '收费员', width: 90 },
  { align: 'right', dataIndex: 'totalAmount', title: '总费用', width: 90, show: cfg.tenant.enableDiscount },
  { align: 'right', dataIndex: 'discounts', title: '优惠金额', width: 80, show: cfg.tenant.enableDiscount },
  { align: 'right', dataIndex: 'amount', title: '结算金额', width: 100 },
  { align: 'right', dataIndex: 'miFundAmt', title: '医保支付', width: 80 },
  { align: 'right', dataIndex: 'selfFee', title: '自费金额', width: 80 },
  { align: 'center', dataIndex: 'createdAt', title: '创建时间', width: 170 },
  { align: 'center', dataIndex: 'status', title: '结算状态', width: 80 },
  { align: 'center', dataIndex: 'invoiceCount', title: '发票数', width: 60, show: cfg.tenant.enableInvoice },
  { align: 'center', dataIndex: 'billCount', title: '划价单数', width: 80 },
  { align: 'center', dataIndex: 'cashType', title: '收费类型', width: 100 },
  { align: 'center', dataIndex: 'checkId', title: '已交账', width: 70, show: cfg.tenant.enableCheck },
  { align: 'center', dataIndex: 'paymentTypes', title: '支付方式', width: 120, ellipsis: true },
  { align: 'center', dataIndex: 'insuranceName', title: '医保险种', width: 120, ellipsis: true },
  { align: 'center', dataIndex: 'cashDate', title: '结算日期', width: 100 },
  { align: 'center', dataIndex: 'visitId', title: '诊疗流水', width: 90 },
  { dataIndex: 'notes', ellipsis: true, minWidth: 150, title: '备注' },
  { align: 'center', dataIndex: 'op', fixed: 'right', title: '操作', width: 100 },
]

defineExpose({
  onLoad,
})
</script>

<template>
  <Api ref="apiRef" v-slot="{ input, output, page }" :browser="false" :immediate="false" :input="inputParams" :load="pageCash" spin>
    <Form v-if="showCond" :colon="false" :model="input.params" class="oh" @finish="onLoad({ pageNo: 1 })">
      <Row :gutter="24">
        <Col :span="6">
          <FormItem label="创建日期">
            <RangePicker v-model:value="input.params.createdAts" class="w-100%" show-time value-format="YYYY-MM-DD HH:mm:ss" @change="onLoad()" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="结算流水">
            <InputNumber v-model:value="input.params.cashId" :controls="false" class="w-100%" placeholder="结算流水" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="患者姓名">
            <Input v-model:value="input.params.payer" class="w-100%" placeholder="患者姓名" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="开单医生">
            <Input v-model:value="input.params.clinicianName" class="w-100%" placeholder="开单医生" />
          </FormItem>
        </Col>
      </Row>
      <Row :gutter="24">
        <Col :span="6">
          <FormItem>
            <template #label> 收费员<a @click="input.params.payee = Currents.name">「我」</a></template>
            <Input v-model:value="input.params.payee" allowClear class="w-100%" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="诊疗流水">
            <InputNumber v-model:value="input.params.visitId" :controls="false" class="w-100%" placeholder="诊疗流水" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="交易类型">
            <Enum v-model="input.params.actionType" :colour="false" class="w-100%" clazz="ActionType" placeholder="交易类型" @change="onLoad()" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="支付方式">
            <PaymentTypeEnum v-model="input.params.paymentType" @change="onLoad()" />
          </FormItem>
        </Col>
      </Row>
      <Row :gutter="24">
        <Col :span="6" v-if="cfg.tenant.enableCheck">
          <FormItem label="是否交账">
            <Enum v-model="input.params.isCheck" :colour="false" class="w-100%" clazz="Boolean" @change="onLoad()" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem label="结算状态">
            <Enum v-model="input.params.statuses" :clearable="false" class="w-100%" clazz="CashStatus" multiple placeholder="结算状态" @change="onLoad()" />
          </FormItem>
        </Col>
        <Col :span="6">
          <FormItem>
            <Button html-type="submit" type="primary">查询</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <Enum v-model="input.params.cashTypes" :colour="false" class="w-100%" clazz="CashType" placeholder="收费类型" @change="onLoad()" component="Checkbox" mt-8px />
    <Count v-if="showCount" v-show="!input.params?.cashId" ref="countRef" mt-16px />
    <Table
      :columns="columns.filter(row => row.show !== false)"
      bordered
      size="small"
      :dataSource="(output as any).list"
      :pagination="{...page, showTotal: (total: number) => `共${total}条`}"
      :scroll="{ x: 700 }"
      rowKey="cashId"
    >
      <template #headerCell="{ column }">
        <Tooltip v-if="column.dataIndex === 'miFundAmt'">
          <template #title> 医保支付 = 医保统筹 + 医保个账 + 医保共济</template>
          医保支付<a>?</a>
        </Tooltip>
        <Tooltip v-if="column.dataIndex === 'selfFee'">
          <template #title> 自费金额 = 结算金额 - 医保支付</template>
          自费金额<a>?</a>
        </Tooltip>
        <Tooltip v-if="cfg.tenant.enableDiscount && column.dataIndex === 'amount'">
          <template #title> 结算金额 = 总费用 - 优惠金额</template>
          结算金额<a>?</a>
        </Tooltip>
      </template>
      <template #bodyCell="{ column: col, record: row }">
        <Format v-if="col.dataIndex === 'actionType'" :bordered="false" :component="Tag" :value="row.actionType" params="ActionType" type="Enum" />
        <Format v-else-if="col.dataIndex === 'cashType'" :value="row.cashType" params="CashType" type="Enum" />
        <Format v-else-if="col.dataIndex === 'paymentTypes'" :value="row.paymentTypes" params="PaymentType" type="Enum" />
        <Format v-else-if="col.dataIndex === 'insuranceTypeId'" :value="row.insuranceTypeId" params="InsuranceType" type="Dict" />
        <template v-else-if="col.dataIndex === 'miFundAmt'">
          <span v-if="isNil(row.miFundAmt) && isNil(row.miAcctAmt) && isNil(row.familyAcctAmt)">-</span>
          <Popover v-else>
            <template #content>
              <Format :value="row.miFundAmt ?? 0" component="div" prefix="统筹：" type="Currency" />
              <Format :value="row.miAcctAmt ?? 0" component="div" prefix="个账：" type="Currency" />
              <Format :value="row.familyAcctAmt ?? 0" component="div" prefix="共济：" type="Currency" />
            </template>
            <Format :value="add(row.miFundAmt, row.miAcctAmt, row.familyAcctAmt)" type="Currency" />
          </Popover>
        </template>
        <Format v-else-if="col.dataIndex === 'selfFee'" :value="subtract(row.amount, add(row.miFundAmt, row.miAcctAmt, row.familyAcctAmt))" type="Currency" />
        <Format v-else-if="col.dataIndex === 'createdAt'" :value="row.createdAt" type="Datetime" />
        <Format v-else-if="col.dataIndex === 'cashDate'" :value="String(row.cashDate)" type="Date" />
        <Format v-else-if="col.dataIndex === 'refundedAmount'" :value="-row.refundedAmount" type="Currency" />
        <Format v-else-if="col.dataIndex === 'discounts'" :value="add(row.discounted ?? 0, row.derated ?? 0)" type="Currency" />
        <Format v-else-if="col.dataIndex === 'totalAmount'" :value="row.totalAmount" type="Currency" />
        <Format v-else-if="col.dataIndex === 'amount'" :value="row.amount" type="Currency" />
        <Format v-else-if="col.dataIndex === 'checkId'" :value="!!row.checkId" type="Boolean" />
        <Format component="a" v-else-if="col.dataIndex === 'visitId'" :value="row.visitId" @click="openBillFeeTypePercentage({ visitId: row.visitId })" />
        <Format v-else-if="col.dataIndex === 'status'" :bordered="false" :component="Tag" :value="row.status" params="CashStatus" type="Enum" />
        <slot :row="row" :col="col" name="bodyCell" />
      </template>
    </Table>
  </Api>
</template>
