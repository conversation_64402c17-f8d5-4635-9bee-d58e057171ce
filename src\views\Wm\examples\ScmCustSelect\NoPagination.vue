<script setup lang="ts">
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { Card, Typography, Divider } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, onMounted } from 'vue'
import { noPaginationUsage, importCode, packageJsonCode } from '../code/ScmCustSelectCode'

const { Title } = Typography

// 单选值，设置默认值
const custId = ref<number>(126)
</script>

<template>
  <Card title="非分页加载" class="mb-16px">
    <div mb-16px>
      <Title :level="4">禁用分页，使用前端过滤</Title>
      <ScmCustSelect v-model="custId" :enablePagination="false" style="width: 100%" />
      <div mt-8px>选中的供应商ID: {{ custId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        禁用分页加载时，组件会一次性加载所有供应商数据，并在输入内容时使用前端过滤而不是向后端发送请求
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="noPaginationUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
