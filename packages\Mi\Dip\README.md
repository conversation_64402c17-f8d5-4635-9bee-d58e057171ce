# Dip 事前事中分析组件

事前事中分析组件，用于医保业务场景中进行事前事中分析并提供反馈。该组件可以展示分析结果，并允许用户进行相应的操作，如继续执行或返回修改。

## 安装

```bash
npm install @mh-mi/dip
```

## 使用

### 系统配置

组件会自动调用API获取系统配置参数`INPATIENT_ENABLED_DIP`，该参数用于控制是否启用DIP事前事中分析。如果系统配置为禁用，则无论组件本身的启用状态如何，调用`callPreDip`和`callInDip`方法时都会直接返回成功，不会执行实际的分析逻辑。

### 基础用法 - 隐藏模式（默认）

```vue
<template>
  <dip
    ref="dipRef"
    :trig-scen="5"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Dip } from '@mh-mi/dip'

const dipRef = ref()

// 调用方法
const handleCallDip = (visitId: number, oeLs: any) => {
  dipRef.value.callInDip(visitId, oeLs)
}
</script>
```

### 开关模式

```vue
<template>
  <dip
    ref="dipRef"
    :trig-scen="5"
    display-mode="switch"
    label="启用事前事中分析"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Dip } from '@mh-mi/dip'

const dipRef = ref()

// 调用方法
const handleCallDip = (visitId: number, oeLs: any) => {
  dipRef.value.callInDip(visitId, oeLs)
}

// 状态变化回调
const onChange = (enabled: boolean) => {
  console.log('事前事中分析状态:', enabled ? '启用' : '禁用')
}
</script>
```

### 复选框模式

```vue
<template>
  <dip
    ref="dipRef"
    :trig-scen="5"
    display-mode="checkbox"
    label="启用事前事中分析"
    @change="onChange"
  />
</template>
```

## API

### Props

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| trigScen | 触发场景 | TriggerScene | - |
| displayMode | 显示模式 | 'switch' \| 'checkbox' \| 'hidden' | 'hidden' |
| label | 标签文本 | string | '事前事中分析' |
| modalWidth | 弹窗宽度 | string \| number | '700px' |
| modalTitle | 弹窗标题 | string | '事前事中分析' |
| defaultEnabled | 默认启用状态 | boolean | true |
| storageKey | localStorage存储key前缀，用于记住用户选择的启用状态，为空时不使用localStorage | string | '' |

### 触发场景（trigScen）

| 值 | 说明 |
| --- | --- |
| 1 | 门诊处方签名 |
| 2 | 门诊预结算 |
| 3 | 住院医嘱签名 |
| 4 | 住院预结算 |

### Events

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| close | 关闭弹窗时触发 | - |
| success | 分析成功时触发 | { success: boolean, message: string, action?: 'continue' \| 'modify', reason?: string } |
| change | 启用状态变化时触发 | (enabled: boolean) |

### Methods

| 方法名 | 说明 | 参数 | 返回值 |
| --- | --- | --- | --- |
| callPreDip | 调用事前分析 | (visitId: number, oeLs: any, cashId?: number) | Promise<{ success: boolean, message: string }> |
| callInDip | 调用事中分析 | (visitId: number, oeLs: any, cashId?: number) | Promise<{ success: boolean, message: string }> |
| toggleEnabled | 切换启用状态 | - | void |

## 依赖

- Vue 3.x
- ant-design-vue 4.x
- @ant-design/icons-vue 7.x
- @mh-mi/util
