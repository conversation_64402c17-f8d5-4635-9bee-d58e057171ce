import { ForbiddenRoute, NotFoundRoute } from '@mh-base/core'
import { RouteRecordRaw } from 'vue-router'
import Base from './Base'
import Bcs from './Bcs'
import Dmy from './Dmy'
import Hip from './Hip'
// import Inpatient from './Inpatient'
import InpatientHsd from './InpatientHsd'
import Mi from './Mi'
import Wm from './Wm'
import Hsd from './Hsd'


export const routes: Array<RouteRecordRaw> = [
  {
    path: '',
    redirect: '/dmy/Core/doc',
  },
  {
    path: '/test',
    name: 'Test',
    children: [
      {
        path: 'wm-dept-art-select',
        name: 'TestWmDeptArtSelect',
        component: () => import('../views/TestWmDeptArtSelect.vue'),
        meta: {
          title: '测试 WmDeptArtSelect 组件'
        }
      }
    ]
  },
  Dmy,
  Base,
  Hip,
  Hsd,
  // Inpatient,
  InpatientHsd,
  Mi,
  Wm,
  Bcs,
  ForbiddenRoute,
  NotFoundRoute,
]
