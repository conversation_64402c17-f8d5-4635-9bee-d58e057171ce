<script setup lang="ts">
import { Card, Typography, Divider, Button, message, Space, Alert } from 'ant-design-vue'
import { ref } from 'vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'
import { propsAndEvents, importCode, packageJsonCode, publishCommands, buildProcess } from '@/views/Wm/examples/code/MakerDeptArtCode'

const { Title, Paragraph, Text } = Typography

// 配置参数
const deptCode = ref('000013')
const searchType = ref(6)

// 组件引用
const makerDeptArtRef = ref()
const drugRef = ref()
const materialRef = ref()
const allRef = ref()

// 添加品种结果
const addResults = ref([])

// 通用添加品种回调
const handleAddArt = (artData: any) => {
  console.log('添加品种:', artData)
  addResults.value.unshift({
    type: '通用',
    time: new Date().toLocaleTimeString(),
    data: artData
  })
  message.success(`成功添加品种: ${artData.artName}`)
}

// 药品添加回调
const handleAddDrug = (artData: any) => {
  console.log('添加药品:', artData)
  addResults.value.unshift({
    type: '药品',
    time: new Date().toLocaleTimeString(),
    data: artData
  })
  message.success(`成功添加药品: ${artData.artName}`, 2)
}

// 耗材添加回调
const handleAddMaterial = (artData: any) => {
  console.log('添加耗材:', artData)
  addResults.value.unshift({
    type: '耗材',
    time: new Date().toLocaleTimeString(),
    data: artData
  })
  message.success(`成功添加耗材: ${artData.artName}`, 2)
}

// 药品+耗材添加回调
const handleAddAll = (artData: any) => {
  console.log('添加品种（药品+耗材）:', artData)
  addResults.value.unshift({
    type: '药品+耗材',
    time: new Date().toLocaleTimeString(),
    data: artData
  })
  message.success(`成功添加: ${artData.artName}`, 2)
}

// 药房添加回调
const handlePharmacyAdd = (artData: any) => {
  console.log('药房添加品种:', artData)
  addResults.value.unshift({
    type: '药房',
    time: new Date().toLocaleTimeString(),
    data: artData
  })
  message.success(`药房成功添加: ${artData.artName}`, 2)
}

// 手术室添加回调
const handleOperatingRoomAdd = (artData: any) => {
  console.log('手术室添加品种:', artData)
  addResults.value.unshift({
    type: '手术室',
    time: new Date().toLocaleTimeString(),
    data: artData
  })
  message.success(`手术室成功添加: ${artData.artName}`, 2)
}

// 清空结果
const clearResults = () => {
  addResults.value = []
  message.success('结果已清空')
}
</script>

<template>
  <Card title="Props & Events - MakerDeptArt 组件属性和事件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">组件属性和事件演示</Title>
      <Paragraph>
        MakerDeptArt 组件支持多种配置属性和事件，可以根据不同的业务场景进行灵活配置。
        以下演示了不同的 Props 配置和 Events 处理方式。
      </Paragraph>

      <!-- 基础使用 -->
      <div class="example-section">
        <Title :level="5">基础使用</Title>
        <MakerDeptArt
          :deptCode="deptCode"
          :searchType="searchType"
          @addArt="handleAddArt"
          ref="makerDeptArtRef"
        />
      </div>

      <Divider />

      <!-- 不同搜索类型示例 -->
      <div class="search-type-examples">
        <Title :level="5">不同搜索类型示例</Title>
        <Paragraph>
          通过配置 <Text code>searchType</Text> 属性，可以控制组件搜索的品种类型：
        </Paragraph>

        <!-- 只搜索药品 -->
        <div class="example-item">
          <Title :level="6">只搜索药品 (searchType: 1)</Title>
          <MakerDeptArt
            :deptCode="deptCode"
            :searchType="1"
            @addArt="handleAddDrug"
            ref="drugRef"
          />
        </div>

        <!-- 只搜索耗材 -->
        <div class="example-item">
          <Title :level="6">只搜索耗材 (searchType: 2)</Title>
          <MakerDeptArt
            :deptCode="deptCode"
            :searchType="2"
            @addArt="handleAddMaterial"
            ref="materialRef"
          />
        </div>

        <!-- 搜索药品+耗材 -->
        <div class="example-item">
          <Title :level="6">搜索药品+耗材 (searchType: 6)</Title>
          <MakerDeptArt
            :deptCode="deptCode"
            :searchType="6"
            @addArt="handleAddAll"
            ref="allRef"
          />
        </div>
      </div>

      <Divider />

      <!-- 不同部门示例 -->
      <div class="dept-examples">
        <Title :level="5">不同部门示例</Title>
        <Paragraph>
          通过配置 <Text code>deptCode</Text> 属性，可以控制不同部门的品种权限：
        </Paragraph>

        <div class="example-item">
          <Title :level="6">药房 (deptCode: '000013')</Title>
          <MakerDeptArt
            deptCode="000013"
            :searchType="6"
            @addArt="handlePharmacyAdd"
          />
        </div>

        <div class="example-item">
          <Title :level="6">手术室 (deptCode: '000020')</Title>
          <MakerDeptArt
            deptCode="000020"
            :searchType="6"
            @addArt="handleOperatingRoomAdd"
          />
        </div>
      </div>

      <Divider />

      <!-- 添加结果展示 -->
      <div class="results-section">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <Title :level="5" style="margin: 0;">添加结果展示</Title>
          <Button v-if="addResults.length > 0" @click="clearResults" danger size="small">清空结果</Button>
        </div>

        <div v-if="addResults.length > 0" class="results-list">
          <div v-for="(result, index) in addResults.slice(0, 10)" :key="index" class="result-item">
            <div class="result-header">
              <span class="result-type">{{ result.type }}</span>
              <span class="result-time">{{ result.time }}</span>
            </div>
            <div class="result-content">
              <Text strong>{{ result.data.artName }}</Text>
              <Text type="secondary"> - {{ result.data.artSpec }}</Text>
              <Text type="secondary"> - {{ result.data.producer }}</Text>
            </div>
            <div class="result-details">
              <Text type="secondary">
                整包: {{ result.data.totalPacks || 0 }} {{ result.data.packUnit || '' }}
                {{ result.data.totalCells ? `, 拆零: ${result.data.totalCells} ${result.data.cellUnit || ''}` : '' }}
              </Text>
            </div>
          </div>
        </div>

        <div v-else class="empty-results">
          <Text type="secondary">暂无添加结果，请使用上方组件添加品种</Text>
        </div>
      </div>

      <!-- 属性说明 -->
      <div class="props-description">
        <Title :level="5">属性说明</Title>
        <Alert
          message="Props 属性"
          type="info"
          show-icon
          style="margin-bottom: 16px;"
        >
          <template #description>
            <ul style="margin: 8px 0; padding-left: 20px;">
              <li><Text code>deptCode</Text>: string - 部门编码，用于品种权限控制</li>
              <li><Text code>searchType</Text>: number - 搜索类型，1-药品，2-耗材，6-药品+耗材</li>
            </ul>
          </template>
        </Alert>

        <Alert
          message="Events 事件"
          type="success"
          show-icon
        >
          <template #description>
            <ul style="margin: 8px 0; padding-left: 20px;">
              <li><Text code>@addArt</Text>: (artData) => void - 添加品种事件，返回品种数据</li>
            </ul>
            <div style="margin-top: 12px;">
              <Text strong>artData 数据结构：</Text>
              <pre style="background: #f6f8fa; padding: 8px; border-radius: 4px; margin-top: 8px; font-size: 12px;">{{
`{
  artId: number,           // 品种ID
  artName: string,         // 品种名称
  artSpec: string,         // 规格
  producer: string,        // 生产厂家
  packUnit: string,        // 包装单位
  cellUnit: string,        // 拆零单位
  packCells: number,       // 包装数量
  splittable: number,      // 是否可拆零 (0-不可拆零, 1-可拆零)
  totalPacks: number,      // 整包数量
  totalCells: number,      // 拆零数量
  artData: object         // 完整品种数据
}`
              }}</pre>
            </div>
          </template>
        </Alert>
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="propsAndEvents"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>
</template>

<style scoped>
.mb-16px {
  margin-bottom: 16px;
}

.example-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.search-type-examples,
.dept-examples {
  margin: 24px 0;
}

.example-item {
  margin-bottom: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.example-item:last-child {
  margin-bottom: 0;
}

.results-section {
  margin: 24px 0;
}

.results-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.result-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
}

.result-item:last-child {
  border-bottom: none;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.result-type {
  display: inline-block;
  padding: 2px 8px;
  background: #1890ff;
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.result-time {
  font-size: 12px;
  color: #999;
}

.result-content {
  margin-bottom: 4px;
  line-height: 1.5;
}

.result-details {
  font-size: 12px;
  line-height: 1.4;
}

.empty-results {
  text-align: center;
  padding: 40px;
  color: #999;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.props-description {
  margin: 24px 0;
}

.props-description pre {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  line-height: 1.4;
  overflow-x: auto;
}
</style>
