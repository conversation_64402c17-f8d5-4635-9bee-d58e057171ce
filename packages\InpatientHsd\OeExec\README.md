# OeExec 目录说明

本目录包含住院医嘱执行与用药申请的核心业务组件，主要包括医嘱执行（计费）和药品申领两大功能。

## 组件一览

### 1. ExecForm.vue
- **功能定位**：医嘱执行计费的主入口。
- **主要功能**：
  - 支持按次/按天执行医嘱，自动生成执行费用。
  - 支持批量处理大量医嘱，分批渲染，提升性能。
  - 处理特殊医嘱（如出院医嘱、皮试医嘱）执行逻辑。
  - 计费前可调整执行方案、执行日期、治疗时间等。
  - 计费结果通过弹窗日志组件展示，便于追踪。
- **典型场景**：护士站、病区等场所对患者医嘱进行批量执行与计费。

### 2. PackDeliverForm.vue
- **功能定位**：医嘱药品申领（领药）入口。
- **主要功能**：
  - 支持按医嘱批量申领药品，自动匹配药房库存。
  - 可选择领药仓库，实时显示库存数量。
  - 支持药品库存不足时的二次确认与提示。
  - 支持按诊疗分组批量提交申领请求。
  - 申领结果通过弹窗日志组件展示。
- **典型场景**：护士站、药房等场所根据医嘱进行药品申领。

### 3. ExecRoutePlanForm.vue
- **功能定位**：医嘱执行明细及耗材/药品调整子组件。
- **主要功能**：
  - 展示每条医嘱的执行明细、涉及药品/耗材、库存等。
  - 支持明细的增删改，灵活调整执行内容。
- **典型场景**：医嘱执行前对明细进行个性化调整。

### 4. ExecResultLog.vue
- **功能定位**：执行/申领结果日志弹窗组件。
- **主要功能**：
  - 以列表形式展示每条医嘱的执行/申领结果。
  - 支持成功与失败分组展示，便于快速定位问题。
  - 支持滚动定位、自动浮顶返回顶部。
- **典型场景**：医嘱执行或药品申领后，反馈操作结果。

## 依赖说明
- 依赖 [ant-design-vue](https://www.antdv.com/) 组件库。
- 依赖 dayjs 进行日期处理。
- 依赖项目内 @mh-inpatient-hsd/util、@mh-wm/util 等业务API。

## 使用建议
- 组件均为业务弹窗组件，建议通过 `ref` 调用其 `open` 方法进行弹窗操作。
- 具体参数和事件请参考各组件源码及注释。

---
如需二次开发或集成，请详细阅读各组件源码及注释。 