<script setup lang="ts">
import { Enum } from '@idmy/antd'
import { newError } from '@idmy/core'
import { allOrgPaymentTypes } from '@mh-hip/util'

const { current, type, filter } = defineProps({
  type: { type: String as PropType<'INPATIENT' | 'OUTPATIENT' | 'DEPOSIT'>, default: 'OUTPATIENT' },
  current: { type: Boolean, default: true },
  colour: { type: Boolean, default: false },
  multiple: { type: Boolean, default: false },
  filter: { type: Function, default: () => () => true },
})

const emit = defineEmits(['change'])

const paymentTypes = ref<string[]>([])
current &&
  allOrgPaymentTypes().then(rows => {
    if (rows.length === 0) {
      throw newError('当前机构没有设置支付方式')
    } else {
      paymentTypes.value = rows
        .filter(item => {
          if (type === 'OUTPATIENT') {
            return item.forOutpatient === 1
          } else if (type === 'INPATIENT') {
            return item.forInpatient === 1
          } else if (type === 'DEPOSIT') {
            return item.forOthers === 1
          } else {
            return true
          }
        })
        .map(row => row.paymentCode)
    }
  })

const paymentTypeFilter = row => paymentTypes.value.includes(row.key) && filter(row)

const modelValue = defineModel({ type: String })
</script>

<template>
  <Enum v-model="modelValue" :colour="colour" :multiple="multiple" :filter="paymentTypeFilter" class="w-100%" clazz="PaymentType" placeholder="支付方式" @change="emit('change', $event)" />
</template>
