export function findSectionRouteConsumableLsByIdApi(params: any) {
  return http.post('/hip-base/sectionRouteConsumable/listBySectionIdRouteId', params, { appKey: 'hip' })
}
export function saveSectionRouteConsumableApi(params: any) {
  return http.post('/hip-base/sectionRouteConsumable/save', params, { appKey: 'hip' })
}
export function delSectionRouteConsumableApi(params: any) {
  return http.post('/hip-base/sectionRouteConsumable/del', params, { appKey: 'hip' })
}
