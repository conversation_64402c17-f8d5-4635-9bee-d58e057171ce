@import "./motion.css";
html {
  --text-color: rgba(0,0,0,.85);
  --text-color-1: rgba(0,0,0,.45);
  --text-color-2: rgba(0,0,0,.2);
  --bg-color: #fff;
  --hover-color:rgba(0,0,0,.025);
  --bg-color-container: #f5f5f5;
  --c-shadow: 2px 0 8px 0 rgba(29,35,41,.05);
}

html.dark{
  --text-color: rgba(229, 224, 216, 0.85);
  --text-color-1: rgba(229, 224, 216, 0.45);
  --text-color-2: rgba(229, 224, 216, 0.45);
  --bg-color: rgb(36, 37, 37);
  --hover-color:rgb(42, 44, 55);
  --bg-color-container: rgb(42, 44, 44);
  --c-shadow: rgba(13, 13, 13, 0.65) 0 2px 8px 0;
}

body{
  color: var(--text-color);
  background-color: var(--bg-color);
  text-rendering: optimizeLegibility;
  overflow: hidden;
}

#app, body, html{
  height: 100%;
}

#app{
  overflow-x: hidden;
}
*, :after, :before{
  box-sizing: border-box;
}

.ant-form-item {
  margin-bottom: 10px;
}

.content {
  background-color: #eff1f9;
  min-height: 300px;
}
.art-select-container {
  padding: 0;
  max-width: calc(100% - 300px);
  overflow: auto;
  .custom-art-header {
    position: fixed !important;
    z-index: 200 !important;
    background: #F0F0F0 !important;
    color: #000000 !important;
    font-weight: 700 !important;
    max-width: calc(100% - 300px);
  }
  .rc-virtual-list-holder {
    max-height: 400px !important;
  }
  .ant-select-dropdown .ant-select-item-option .ant-select-item-option-content {
    align-items: center;
    white-space: pre-wrap;
    display: flex;
  }
  .ant-select-item-option:nth-child(2n) {
    background: rgba(0, 0, 0, 0.04);
  }
  .ant-select-item-option-active {
    background: var(--pro-ant-color-primary) !important;
  }
  .ant-select-item-option-active .ant-select-item-option-content, .ant-select-item-option-active .ant-select-item-option-content > div{
    color: #fff !important;
  }

  .custom-art-check {
    width: 40px;
    float: left;
    text-align: center;
  }
  .custom-art-name {
    width: 200px;
    float: left;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-spec {
    width: 140px;
    float: left;
    text-align: center;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-price {
    width: 80px;
    float: left;
    padding-right: 10px;
    text-align: center;
  }
  .custom-art-producer {
    width: 250px;
    float: left;
    padding-right: 10px;
    white-space: break-spaces;
    word-break: break-word;
  }
  .custom-art-other {
    width: 80px;
    float: left;
    padding-right: 10px;
    text-align: center;
  }
  .custom-art-footer {
    position: absolute !important;
    bottom: 0;
    width: 100%;
    background: #FFFFFF !important;
    border-radius: 0;
  }
}
.cancel-row {

  color: #A6A6A6 !important;

  .surely-table-cell-content {
    text-decoration-color: #807d7d;
    text-decoration-line: line-through;
    text-decoration-style: solid;
    text-decoration-thickness: auto;
  }
}
.summary-row {
  color: #000000 !important;
  font-weight: bold;
}

.pointer {
  cursor: pointer;
}
