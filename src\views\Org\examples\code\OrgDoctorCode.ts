import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 选择指定组织机构下的医生 -->
  <OrgDoctor
    v-model="doctorId"
    :orgId="orgId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDoctor } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)
// 医生ID
const doctorId = ref()
</script>`)

// 当前用户
export const currentUserUsage = wrapCodeExample(`<template>
  <!-- 不指定组织机构ID，使用当前用户token中的orgId -->
  <OrgDoctor
    v-model="currentUserDoctorId"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDoctor } from '@mh-hip/org'
import { ref } from 'vue'

// 医生ID
const currentUserDoctorId = ref()
</script>`)

// 多选模式
export const multipleUsage = wrapCodeExample(`<template>
  <!-- 多选模式 -->
  <OrgDoctor
    v-model="doctorIds"
    :orgId="orgId"
    multiple
    style="width: 100%"
  />
</template>

<script setup>
import { OrgDoctor } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)
// 多选值
const doctorIds = ref([])
</script>`)

// 医师编码
export const showCodeUsage = wrapCodeExample(`<template>
  <!-- 不显示医师编码 -->
  <OrgDoctor
    v-model="doctorId"
    :orgId="orgId"
    :showCode="false"
    style="width: 200px"
  />

  <!-- 显示医师编码 -->
  <OrgDoctor
    v-model="doctorId"
    :orgId="orgId"
    :showCode="true"
    style="width: 200px"
  />
</template>

<script setup>
import { OrgDoctor } from '@mh-hip/org'
import { ref } from 'vue'

// 组织机构ID
const orgId = ref(1)
// 医生ID
const doctorId = ref()
</script>`)

// 引入组件
export const importCode = `import { OrgDoctor } from '@mh-hip/org'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-hip/org": "^1.0.8"
  }
}`
