<script lang="ts" setup>
import { Api, Format } from '@idmy/core'
import { sumGeEndDateUnpaidAmt } from '@mh-bcs/util'
import dayjs from 'dayjs'

defineProps({
  visitId: { type: Number, required: true },
  endDate: { type: Number, required: true },
})
</script>

<template>
  <Api v-slot="{ output: amt }" :input="{ endDate }" :load="() => sumGeEndDateUnpaidAmt(visitId, endDate)" input-watch type="Number">
    <Format :value="amt" prefix="剩余未结算：" type="Currency" value-class="error text-22px font-bold" />
  </Api>
  <Format :value="dayjs(String(endDate))" ml-16px prefix="截止日期：" type="Date" />
</template>
