<script setup lang="ts">
import { Card, Typography, Divider, Button, message, Table, Space, Form, Input, DatePicker } from 'ant-design-vue'
import { ref, reactive, h } from 'vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'
import { fullExample, importCode, packageJsonCode, publishCommands, buildProcess } from '@/views/Wm/examples/code/MakerDeptArtCode'
import dayjs from 'dayjs'

const { Title, Paragraph, Text } = Typography

// 组件引用
const makerDeptArtRef = ref()

// 部门编码
const deptCode = ref('000013')

// 表单数据
const formState = reactive({
  reqNo: 'REQ' + Date.now(),
  reqDate: dayjs(),
  deptName: '药房',
  operator: '张三',
  items: [],
  remark: ''
})

// 表格列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
    customRender: ({ index }: { index: number }) => index + 1
  },
  {
    title: '品名',
    dataIndex: 'artName',
    key: 'artName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 120,
  },
  {
    title: '生产厂家',
    dataIndex: 'producer',
    key: 'producer',
    width: 120,
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ record }: any) => {
      return record.totalPacks ? `${record.totalPacks} ${record.packUnit || ''}` : '-'
    }
  },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    key: 'totalCells',
    width: 100,
    customRender: ({ record }: any) => {
      return record.totalCells ? `${record.totalCells} ${record.cellUnit || ''}` : '-'
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    customRender: ({ record }: any) => {
      return h(Space, {}, [
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => editItem(record)
        }, '编辑'),
        h(Button, {
          type: 'link',
          size: 'small',
          danger: true,
          onClick: () => deleteItem(record)
        }, '删除')
      ])
    }
  }
]

// 添加品种
const handleAddArt = (formData: any) => {
  const newItem = {
    id: Date.now().toString(),
    ...formData,
    artName: formData.artName || '',
    artSpec: formData.artSpec || '',
    producer: formData.producer || ''
  }

  formState.items.push(newItem)
  message.success('品种添加成功')
}

// 编辑品种
const editItem = (record: any) => {
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.setFormData(record)
    message.info('品种数据已回填到表单，修改后点击添加按钮更新')
  }
}

// 删除品种
const deleteItem = (record: any) => {
  const index = formState.items.findIndex((item: any) => item.id === record.id)
  if (index !== -1) {
    formState.items.splice(index, 1)
    message.success('品种删除成功')
  }
}

// 提交表单
const handleSubmit = () => {
  if (formState.items.length === 0) {
    message.warning('请至少添加一个品种')
    return
  }

  console.log('提交申请:', formState)
  message.success('申请提交成功')
}

// 重置表单
const handleReset = () => {
  formState.items = []
  formState.reqNo = 'REQ' + Date.now()
  formState.reqDate = dayjs()
  formState.remark = ''
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.clearForm()
  }
  message.success('表单已重置')
}

// 清空品种列表
const clearItems = () => {
  formState.items = []
  message.success('品种列表已清空')
}

// 加载示例数据
const loadExampleData = () => {
  const exampleItems = [
    {
      id: 'example1',
      artId: 1001,
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '哈药集团制药总厂',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 24,
      splittable: 1,
      totalPacks: 10,
      totalCells: 5
    },
    {
      id: 'example2',
      artId: 1002,
      artName: '头孢克肟胶囊',
      artSpec: '0.1g*12粒',
      producer: '石药集团',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 12,
      splittable: 1,
      totalPacks: 5,
      totalCells: 8
    }
  ]

  formState.items = exampleItems
  message.success('示例数据已加载')
}

// 导出数据
const exportData = () => {
  if (formState.items.length === 0) {
    message.warning('没有数据可导出')
    return
  }

  const exportData = {
    ...formState,
    reqDate: formState.reqDate.format('YYYY-MM-DD')
  }

  const dataStr = JSON.stringify(exportData, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `request-${formState.reqNo}-${new Date().getTime()}.json`
  link.click()
  URL.revokeObjectURL(url)
  message.success('数据导出成功')
}
</script>

<template>
  <Card title="完整示例 - 在表单中使用 MakerDeptArt 组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">申请单表单示例</Title>
      <Paragraph>
        这是一个完整的表单示例，展示了如何在实际业务场景中使用 MakerDeptArt 组件。
        包含了表单验证、数据管理、编辑功能等完整的业务流程。
      </Paragraph>

      <!-- 申请单表单 -->
      <Form :model="formState" layout="vertical" class="request-form">
        <div class="form-header">
          <Title :level="5">申请单信息</Title>
          <div class="form-row">
            <div class="form-item">
              <Form.Item label="申请单号" name="reqNo">
                <Input v-model:value="formState.reqNo" />
              </Form.Item>
            </div>
            <div class="form-item">
              <Form.Item label="申请日期" name="reqDate">
                <DatePicker v-model:value="formState.reqDate" style="width: 100%" />
              </Form.Item>
            </div>
            <div class="form-item">
              <Form.Item label="申请部门" name="deptName">
                <Input v-model:value="formState.deptName" />
              </Form.Item>
            </div>
            <div class="form-item">
              <Form.Item label="操作员" name="operator">
                <Input v-model:value="formState.operator" />
              </Form.Item>
            </div>
          </div>
        </div>

        <Divider />

        <!-- 品种录入组件 -->
        <Form.Item label="品种录入">
          <MakerDeptArt
            :deptCode="deptCode"
            :searchType="6"
            @addArt="handleAddArt"
            ref="makerDeptArtRef"
          />
        </Form.Item>

        <!-- 已添加品种列表 -->
        <Form.Item label="申请品种列表">
          <div class="items-header">
            <Text strong>已添加品种 ({{ formState.items.length }})</Text>
            <Space>
              <Button @click="loadExampleData" type="primary" ghost size="small">加载示例</Button>
              <Button v-if="formState.items.length > 0" @click="exportData" size="small">导出</Button>
              <Button v-if="formState.items.length > 0" @click="clearItems" danger size="small">清空</Button>
            </Space>
          </div>

          <Table
            v-if="formState.items.length > 0"
            :dataSource="formState.items"
            :columns="columns"
            rowKey="id"
            :pagination="false"
            :scroll="{ x: 1000 }"
            size="small"
          />

          <div v-else class="empty-state">
            <Text type="secondary">暂无品种，请使用上方组件添加品种</Text>
          </div>
        </Form.Item>

        <!-- 备注 -->
        <Form.Item label="备注" name="remark">
          <Input.TextArea
            v-model:value="formState.remark"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </Form.Item>

        <!-- 操作按钮 -->
        <Form.Item>
          <Space>
            <Button type="primary" @click="handleSubmit">提交申请</Button>
            <Button @click="handleReset">重置表单</Button>
          </Space>
        </Form.Item>
      </Form>

      <div class="mt-16px tip-text">
        <i class="tip-icon">💡</i>
        这个示例展示了 MakerDeptArt 组件在实际业务表单中的使用方式，包括数据绑定、编辑回填、表单验证等功能。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="fullExample"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>
</template>

<style scoped>
.mb-16px {
  margin-bottom: 16px;
}

.mt-16px {
  margin-top: 16px;
}

.request-form {
  background: #fafafa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.form-header {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.form-item {
  min-width: 200px;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  background: #f9f9f9;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
}

.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  font-style: normal;
  margin-right: 8px;
  flex-shrink: 0;
}
</style>
