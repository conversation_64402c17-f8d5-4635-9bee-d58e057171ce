#!/usr/bin/env node

/**
 * WholeBox组件打包测试脚本
 * 验证组件是否可以正常打包和发布
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync, statSync } from 'fs'
import { resolve, dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

console.log('🧪 WholeBox 组件打包测试\n')

// 检查必要文件
const requiredFiles = [
  'package.json',
  'src/index.vue',
  'index.ts',
  'README.md'
]

console.log('📋 检查必要文件...')
let allFilesExist = true

requiredFiles.forEach(file => {
  const filePath = resolve(__dirname, file)
  if (existsSync(filePath)) {
    const stats = statSync(filePath)
    console.log(`✅ ${file} (${(stats.size / 1024).toFixed(1)}KB)`)
  } else {
    console.log(`❌ ${file} - 文件不存在`)
    allFilesExist = false
  }
})

if (!allFilesExist) {
  console.log('\n❌ 必要文件检查失败，请确保所有文件都存在')
  process.exit(1)
}

// 检查package.json配置
console.log('\n📦 检查package.json配置...')
const packageJsonPath = resolve(__dirname, 'package.json')
const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'))

const requiredFields = ['name', 'version', 'main', 'module', 'types']
const missingFields = requiredFields.filter(field => !packageJson[field])

if (missingFields.length > 0) {
  console.log(`❌ package.json缺少必要字段: ${missingFields.join(', ')}`)
  process.exit(1)
}

console.log(`✅ 组件名称: ${packageJson.name}`)
console.log(`✅ 版本号: ${packageJson.version}`)
console.log(`✅ 主入口: ${packageJson.main}`)
console.log(`✅ ES模块: ${packageJson.module}`)
console.log(`✅ 类型声明: ${packageJson.types}`)

// 检查依赖
console.log('\n🔗 检查依赖配置...')
if (packageJson.peerDependencies) {
  console.log('✅ peerDependencies配置正确')
  Object.entries(packageJson.peerDependencies).forEach(([name, version]) => {
    console.log(`   ${name}: ${version}`)
  })
} else {
  console.log('⚠️  未配置peerDependencies')
}

if (packageJson.dependencies) {
  console.log('✅ dependencies配置:')
  Object.entries(packageJson.dependencies).forEach(([name, version]) => {
    console.log(`   ${name}: ${version}`)
  })
}

// 测试构建命令
console.log('\n🔨 测试构建命令...')

try {
  // 切换到项目根目录
  const rootDir = resolve(__dirname, '../../..')
  process.chdir(rootDir)
  
  console.log('正在执行构建命令...')
  
  // 执行构建命令（仅构建，不发布）
  const buildCommand = 'pnpm run publish:component Wm/WholeBox --no-publish'
  console.log(`执行命令: ${buildCommand}`)
  
  const output = execSync(buildCommand, { 
    encoding: 'utf-8',
    stdio: 'pipe',
    timeout: 60000 // 60秒超时
  })
  
  console.log('✅ 构建命令执行成功')
  console.log('构建输出:')
  console.log(output)
  
} catch (error) {
  console.log('❌ 构建命令执行失败')
  console.log('错误信息:', error.message)
  
  if (error.stdout) {
    console.log('标准输出:', error.stdout)
  }
  
  if (error.stderr) {
    console.log('错误输出:', error.stderr)
  }
  
  process.exit(1)
}

// 检查构建产物
console.log('\n📁 检查构建产物...')
const distPath = resolve(__dirname, 'dist')

if (existsSync(distPath)) {
  console.log('✅ dist目录存在')
  
  const expectedFiles = [
    'es/index.js',
    'umd/index.js',
    'index.d.ts',
    'index.css'
  ]
  
  expectedFiles.forEach(file => {
    const filePath = resolve(distPath, file)
    if (existsSync(filePath)) {
      const stats = statSync(filePath)
      console.log(`✅ ${file} (${(stats.size / 1024).toFixed(1)}KB)`)
    } else {
      console.log(`⚠️  ${file} - 文件不存在`)
    }
  })
} else {
  console.log('⚠️  dist目录不存在，可能构建未完成')
}

// 验证组件导出
console.log('\n🔍 验证组件导出...')
const indexPath = resolve(__dirname, 'index.ts')
if (existsSync(indexPath)) {
  const indexContent = readFileSync(indexPath, 'utf-8')
  console.log('✅ index.ts内容:')
  console.log(indexContent)
} else {
  console.log('❌ index.ts文件不存在')
}

// 生成测试报告
console.log('\n📊 测试报告')
console.log('=' * 50)
console.log(`组件名称: ${packageJson.name}`)
console.log(`版本号: ${packageJson.version}`)
console.log(`测试时间: ${new Date().toLocaleString()}`)
console.log(`Node版本: ${process.version}`)
console.log(`平台: ${process.platform}`)

// 生成使用示例
console.log('\n💡 使用示例:')
console.log(`
// 安装组件
pnpm add ${packageJson.name}

// 在Vue项目中使用
import { WholeBox } from '${packageJson.name}'

// 或者
import WholeBox from '${packageJson.name}'

// 在模板中使用
<template>
  <WholeBox ref="wholeBoxRef" @split-pack-success="handleSuccess" />
</template>
`)

console.log('\n✨ WholeBox组件打包测试完成！')
console.log('\n🚀 下一步操作:')
console.log('1. 检查构建产物是否正确')
console.log('2. 在测试项目中验证组件功能')
console.log('3. 执行发布命令: pnpm publish:component Wm/WholeBox')
console.log('4. 在其他项目中安装和使用组件')

process.exit(0)
