<!--处方扫码组件-->
<script setup lang="ts">
import { reactive, ref, computed, nextTick, h } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'

import {
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Checkbox,
  Select,
  Row,
  Col,
  Table,
  Space,
  message,
  Tooltip,
  Switch
} from 'ant-design-vue'
import { createVNode } from 'vue'
import { ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons-vue'

// 导入API，使用原始API名称
import {
  wmBillInfoApi as infoApi,
  wmBillDetailListApi,
  trackCodeListByWbSeqIdsApi,
  trackCodeAddCodeApi as addCodeA<PERSON>,
  trackCodeDelCodeApi as delCodeApi,
  trackCodeDelCodeByWbSeqIdApi as delAllCodeApi,
  trackCodeAllConfirmByWbSeqIdApi,
  trackCodeSaveBillApi as saveBillApi,
  trackCodeSubmitBillApi as submitBill<PERSON><PERSON>,
  trackCodeSetNoTrackCodeApi as setNoTrack<PERSON>ode<PERSON><PERSON>,
  trackCodeClearNoTrackCodeApi,
  trackCodeSetDisassembledApi,
  trackCodeClearDisassembledApi
} from '@mh-wm/util'

const props = defineProps({
  // 对话框宽度
  modalWidth: {
    type: [Number, String],
    default: 1400,
  },
  // 是否只添加识别追溯码（默认为false，即未识别的追溯码也会绑定到当前选中行）
  onlyAddRecognizedTrackCode: {
    type: Boolean,
    default: false,
  },
  // 是否启用"只添加识别追溯码"复选框（默认为false，即不显示复选框）
  enableOnlyAddRecognizedTrackCodeOption: {
    type: Boolean,
    default: false,
  },
  // 是否启用自动关闭提示（默认为true，即显示自动关闭提示）
  enableAutoClosePrompt: {
    type: Boolean,
    default: false,
  },
  // 是否检查追溯码完整性（默认为false，即允许未完成时提交）
  checkTrackCodeComplete: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['success', 'cancel'])
const modalVisible = ref(false)

// 库存变化类型(101-调拨入库,102-调拨出库,103-报溢入库,104-报损出库,105-销毁出库,106-其他入库,107-其他出库,108-采购入库,109-采购退出,110-赠品入库,111-赠品退出)
const inventoryChangeTypeLs = reactive([
  {
    label: '调拨入库',
    value: 101
  }, {
    label: '调拨出库',
    value: 102
  }, {
    label: '报溢入库',
    value: 103
  }, {
    label: '报损出库',
    value: 104
  }, {
    label: '销毁出库',
    value: 105
  }, {
    label: '其他入库',
    value: 106
  }, {
    label: '其他出库',
    value: 107
  }, {
    label: '采购入库',
    value: 108
  }, {
    label: '采购退出',
    value: 109
  }, {
    label: '赠品入库',
    value: 110
  }, {
    label: '赠品退出',
    value: 111
  }
])

const formRef = ref()
const formState = reactive({
  wbSeqid: 0,
  wmbillTypeName: '',
  bsnTypeName: '',
  deptName: '',
  trackcodeStatusName: '',
  inventoryChangeType: undefined
})
const rules: Record<string, Rule[]> = {
  bsnType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  custId: [
    { required: true, message: '请选择往来单位', trigger: 'change' }
  ],
  custName: [
    { required: true, message: '请选择往来单位', trigger: 'change' }
  ],
  bsnDate: [
    { required: true, message: '请选择业务日期', trigger: 'change' }
  ]
}

interface tableModel {
  loading?: boolean,
  columns: any[],
  dataSource: any[],
  allTrackCodes?: any[], // 存储所有追溯码数据
  selectedRowKeys?: any[],
  rowSelection?: any,
  pagination: any,
  loadDataSource?: () => Promise<void>,
  selectRow?: (record: any) => void,
  customRow?: (record: any) => any
}

const currentLineNo = ref()
const currentArtInfo = reactive({
  artName: '',
  artSpec: '',
  producer: '',
  noTrackCode: false,
  artIsDisassembled: false, // 拆零上报
  trackCodePrefix: '', // 追溯码前缀（前7位）
  scannedPrefixes: new Set(), // 已扫描的追溯码前缀集合
  totalPacks: 0, // 整包数量
  totalCells: 0, // 拆零数量
  collectedPacks: 0, // 已采整包数
  collectedCells: 0, // 已采拆零数
  packCells: 0 // 每包拆零数量
})

// 追溯码明细表格
const trackCodeDetailTableModel = reactive<tableModel>({
  loading: false,
  columns: [{
    title: '药品追溯码',
    dataIndex: 'trackCode',
    width: 200,
    resizable: true,
    customRender: ({ text, record }) => {
      // 获取追溯码前7位
      const prefix = text.substring(0, 7)

      // 获取当前药品的所有前缀
      const drugPrefixes = new Set()
      trackCodeDetailTableModel.allTrackCodes?.forEach((item: any) => {
        if (item.lineNo === record.lineNo) {
          drugPrefixes.add(item.trackCode.substring(0, 7))
        }
      })

      // 根据前缀数量确定颜色
      let color = '#52c41a' // 绿色（默认，只有一种前缀）
      if (drugPrefixes.size === 2) {
        color = '#faad14' // 橙色（两种前缀）
      } else if (drugPrefixes.size > 2) {
        color = '#f5222d' // 红色（三种及以上前缀）
      }

      // 创建带颜色的前缀和普通的后缀
      return h('span', {}, [
        h('span', { style: { color, fontWeight: 'bold' } }, prefix),
        h('span', {}, text.substring(7))
      ])
    }
  }, {
    title: '拆零',
    dataIndex: 'isDisassembled',
    width: 80,
    align: 'center'
  }, {
    title: '拆零数量',
    dataIndex: 'totalCells',
    width: 80,
    align: 'right',
    customRender: ({ text }) => {
      // 拆零数量为0不显示
      return text > 0 ? text : ''
    }
  },
  // 以下列暂时注释掉，不显示但保留代码
  /*{
    title: '包装级别',
    dataIndex: 'packLevel',
    width: 120,
    align: 'right'
  }, {
    title: '品名|规格|厂家',
    dataIndex: 'artName',
    width: 300,
    resizable: true
  }, {
    title: '单位',
    dataIndex: 'packCells',
    width: 100,
    resizable: true,
    align: 'right'
  }, {
    title: '生产批号',
    dataIndex: 'batchNo',
    width: 80,
    resizable: true
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 80,
    resizable: true
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 80,
    resizable: true,
    align: 'center'
  },*/ {
    title: '操作',
    dataIndex: 'action',
    width: 60,
    fixed: 'right',
    align: 'center'
  }],
  dataSource: [],
  allTrackCodes: [], // 存储所有追溯码数据
  loadDataSource: async () => {
    trackCodeDetailTableModel.loading = true
    try {
      const response = await trackCodeListByWbSeqIdsApi({
        wbSeqids: [formState.wbSeqid],
      })
      // 存储所有追溯码数据
      trackCodeDetailTableModel.allTrackCodes = Array.isArray(response) ? response : []
      // 通过计算属性过滤当前药品的追溯码
      updateFilteredTrackCodes()
      trackCodeDetailTableModel.selectedRowKeys = []
      trackCodeDetailTableModel.loading = false

      // 更新追溯码缓存
      updateTrackCodeCache()
    } catch (err) {
      console.log(err)
      trackCodeDetailTableModel.loading = false
    }
  },
  pagination: false
})

// 单据明细
const billDetailTableModel = reactive<tableModel>({
  loading: false,
  columns: [{
    title: '品名|规格|厂家',
    dataIndex: 'artName',
    width: 300,
    fixed: 'left',
    resizable: true
  }, {
    title: '整包数',
    dataIndex: 'totalPacks',
    width: 70,
    fixed: 'left',
    align: 'right',
    customRender: ({ text }) => {
      // 整包数为0或不存在时不显示
      return text > 0 ? text : ''
    }
  }, {
    title: '拆零数',
    dataIndex: 'totalCells',
    width: 70,
    fixed: 'left',
    align: 'right',
    customRender: ({ text }) => {
      // 拆零数为0或不存在时不显示
      return text > 0 ? text : ''
    }
  }, {
    title: '已采整包',
    dataIndex: 'collectedPacks',
    width: 80,
    fixed: 'left',
    align: 'right',
    customRender: ({ text, record }) => {
      // 整包数为0或不存在时不显示已采整包
      if (record.totalPacks <= 0) return ''

      // 已采数量为0时不显示
      if (!text || text <= 0) return ''

      // 计算是否已完成采集
      const isPacksComplete = record.totalPacks > 0 ? (record.collectedPacks >= record.totalPacks) : true

      // 创建带动画效果的数字
      return h('span', {
        class: 'collected-count',
        style: {
          color: isPacksComplete ? '#52c41a' : 'inherit',
          animation: record.animatePacks ? 'count-change 0.5s' : 'none'
        }
      }, text)
    }
  }, {
    title: '已采拆零',
    dataIndex: 'collectedCells',
    width: 80,
    fixed: 'left',
    align: 'right',
    customRender: ({ text, record }) => {
      // 拆零数为0且没有设置拆零上报时不显示已采拆零
      if (record.totalCells <= 0 && !record.artIsDisassembled) return ''

      // 已采数量为0时不显示
      if (!text || text <= 0) return ''

      // 计算是否已完成采集
      let isCellsComplete = false

      // 如果设置了拆零上报标志，只要有拆零采集就算完成
      if (record.artIsDisassembled) {
        isCellsComplete = record.collectedCells > 0
      } else {
        // 否则按正常逻辑检查
        isCellsComplete = record.totalCells > 0 ? (record.collectedCells >= record.totalCells) : true
      }

      // 创建带动画效果的数字
      return h('span', {
        class: 'collected-count',
        style: {
          color: isCellsComplete ? '#52c41a' : 'inherit',
          animation: record.animateCells ? 'count-change 0.5s' : 'none'
        }
      }, text)
    }
  }, {
    title: '采集结果',
    dataIndex: 'collectStatus',
    width: 80,
    fixed: 'left',
    align: 'center',
    customRender: ({ record }) => {
      // 如果设置为无追溯码，直接显示无追溯码状态
      if (record.noTrackCode) {
        return h('span', { style: { color: '#722ed1', fontWeight: 'bold' } }, '无追溯码')
      }

      // 如果设置了拆零上报标志
      if (record.artIsDisassembled) {
        // 如果是拆零上报且totalPacks>0，则要求拆零数合计等于totalPacks
        if (record.totalPacks > 0 && record.packCells === 1) {
          if (record.collectedCells >= record.totalPacks) {
            return h('span', { style: { color: '#52c41a' } }, '✓ 拆零上报')
          } else if (record.collectedCells > 0) {
            return h('span', { style: { color: '#1890ff' } }, `拆零上报中(${record.collectedCells}/${record.totalPacks})`)
          } else {
            return h('span', { style: { color: '#1890ff' } }, '待拆零上报')
          }
        } else {
          // 否则只要有拆零数量即可
          if (record.collectedCells > 0) {
            return h('span', { style: { color: '#52c41a' } }, '✓ 拆零上报')
          } else {
            return h('span', { style: { color: '#1890ff' } }, '待拆零上报')
          }
        }
      }

      // 计算是否已完成采集
      const isPacksComplete = record.totalPacks > 0 ? (record.collectedPacks >= record.totalPacks) : true
      const isCellsComplete = record.totalCells > 0 ? (record.collectedCells >= record.totalCells) : true
      const isComplete = isPacksComplete && isCellsComplete

      // 计算是否有采集
      const hasCollected = (record.collectedPacks > 0 || record.collectedCells > 0)

      // 未采集
      if (!hasCollected) {
        return h('span', { style: { color: '#999' } }, '未采集')
      }

      // 采集完且正常
      if (isComplete && !record.hasMultiplePrefixes) {
        return h('span', { style: { color: '#52c41a' } }, '✓ 已完成')
      }

      // 采集中
      if (!isComplete && !record.hasMultiplePrefixes) {
        return h('span', { style: { color: '#1890ff' } }, '采集中')
      }

      // 采集异常（超量或有多个前缀）
      const isOverCollected = (record.totalPacks > 0 && record.collectedPacks > record.totalPacks) ||
                             (record.totalCells > 0 && record.collectedCells > record.totalCells)

      if (isOverCollected || record.hasMultiplePrefixes) {
        // 创建带提示的异常状态
        return h(
          'Tooltip',
          {
            title: record.hasMultiplePrefixes
              ? '该药品采集了多种不同前缀的追溯码，请检查'
              : '采集数量超过了应采数量，请检查'
          },
          [h('span', { style: { color: '#faad14', cursor: 'pointer' } }, '⚠ 异常')]
        )
      }

      return null
    }
  // }, {
  //   title: '整包数量',
  //   dataIndex: 'totalPacks',
  //   width: 80,
  //   resizable: true,
  //   align: 'right'
  // }, {
  //   title: '拆零数量',
  //   dataIndex: 'totalCells',
  //   width: 80,
  //   resizable: true,
  //   align: 'right'
  }, {
    title: '生产批号',
    dataIndex: 'batchNo',
    width: 110,
    resizable: true
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 100,
    resizable: true,
    align: 'center'
  }, {
    title: '单位',
    dataIndex: 'packCells',
    width: 90,
    resizable: true,
    align: 'right'
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 80,
    resizable: true
  }],
  dataSource: [],
  loadDataSource: async () => {
    billDetailTableModel.loading = true
    try {
      const response = await wmBillDetailListApi({
        wbSeqid: formState.wbSeqid,
        setProdNo: true,
      })
      billDetailTableModel.dataSource = response
      billDetailTableModel.selectedRowKeys = []
      if (billDetailTableModel.dataSource.length > 0) {
        const list = billDetailTableModel.dataSource.filter(p => !p.noTrackCode)
        console.log(list)
        if (list.length > 0) {
          billDetailTableModel.selectRow(list[0])
        }
      }
      billDetailTableModel.loading = false
    } catch (err) {
      console.log(err)
      billDetailTableModel.loading = false
    }
  },
  selectedRowKeys: [],
  rowSelection: computed(() => {
    return {
      type: 'radio',
      selectedRowKeys: billDetailTableModel.selectedRowKeys,
      onChange: (selectedRowKeys: any) => {
        billDetailTableModel.selectedRowKeys = selectedRowKeys
      },
    }
  }),
  selectRow: (record) => {
    // 重置追溯码前缀信息
    currentArtInfo.trackCodePrefix = ''
    currentArtInfo.scannedPrefixes.clear()

    // 更新当前药品信息
    // 使用解构赋值确保所有属性都被正确复制，包括artIsDisassembled
    const {
      artName, artSpec, producer, noTrackCode,
      artIsDisassembled = false, // 默认为false，如果record中没有该属性
      totalPacks, totalCells, collectedPacks, collectedCells, packCells
    } = record

    // 明确设置每个属性，而不是使用Object.assign
    currentArtInfo.artName = artName
    currentArtInfo.artSpec = artSpec
    currentArtInfo.producer = producer
    currentArtInfo.noTrackCode = noTrackCode
    currentArtInfo.artIsDisassembled = artIsDisassembled
    currentArtInfo.totalPacks = totalPacks
    currentArtInfo.totalCells = totalCells
    currentArtInfo.collectedPacks = collectedPacks
    currentArtInfo.collectedCells = collectedCells
    currentArtInfo.packCells = packCells

    // 添加日志，帮助调试
    console.log('选中行数据:', {
      lineNo: record.lineNo,
      artName: record.artName,
      artIsDisassembled: record.artIsDisassembled,
      currentArtInfo: { ...currentArtInfo }
    })

    currentLineNo.value = record.lineNo

    // 更新选中行
    const selectedRowKeys = [...billDetailTableModel.selectedRowKeys]
    // 如果当前行已经被选中，则不做任何操作，保持选中状态
    if (!selectedRowKeys.includes(record.lineNo)) {
      // 如果当前行未被选中，则清空之前的选中状态，选中当前行
      selectedRowKeys.splice(0, selectedRowKeys.length)
      selectedRowKeys.push(record.lineNo)
      billDetailTableModel.selectedRowKeys = selectedRowKeys
    }

    // 使用计算属性过滤追溯码，而不是重新加载数据
    updateFilteredTrackCodes()

    // 处理拆零情况
    searchFormModel.totalCells = 0
    // 当拆零数量大于0或者设置了拆零上报标志时，显示拆零选项
    if (record.totalCells > 0 || record.artIsDisassembled) {
      searchFormModel.isDisassembled = true

      // 特殊情况：totalPacks>0，packCells=1且设置了拆零上报时，使用totalPacks减去已采集数量
      if (record.artIsDisassembled && record.totalPacks > 0 && record.packCells === 1) {
        // 计算剩余的数量 = totalPacks - 已采集拆零数量
        const remainingPacks = record.totalPacks - (record.collectedCells || 0)
        // 如果剩余数量大于0，则使用剩余数量，否则使用总数量
        searchFormModel.totalCells = remainingPacks > 0 ? remainingPacks : record.totalPacks
      }
      // 如果有拆零数量，则计算剩余的拆零数量
      else if (record.totalCells > 0) {
        // 计算剩余的拆零数量 = 总拆零数量 - 已采集拆零数量
        const remainingCells = record.totalCells - (record.collectedCells || 0)
        // 如果剩余拆零数量大于0，则使用剩余数量，否则使用总数量（可能是重新采集）
        searchFormModel.totalCells = remainingCells > 0 ? remainingCells : record.totalCells
      } else {
        searchFormModel.totalCells = 1
      }

      // 无论是否有拆零数量，都将焦点设置到追溯码输入框，方便用户直接扫码
      nextTick(() => {
        trackCodeRef.value?.focus()
      })
    } else {
      searchFormModel.isDisassembled = false
      trackCodeRef.value?.focus()
    }
  },
  customRow: (record) => {
    return {
      onClick: () => {
        billDetailTableModel.selectRow(record)
      },
    }
  },
  pagination: false
})

const trackCodeRef = ref()
const totalCellsRef = ref()
const btnOkRef = ref()
const isDisassembledRef = ref()
const isViewRef = ref(false)
const titleRef = ref('')
const visitIdRef = ref()

// 计算是否有任何追溯码被采集
const hasAnyTrackCodes = computed(() => {
  return trackCodeDetailTableModel.allTrackCodes && trackCodeDetailTableModel.allTrackCodes.length > 0
})

// 更新过滤后的追溯码数据
const updateFilteredTrackCodes = () => {
  if (currentLineNo.value) {
    // 过滤当前药品的追溯码
    if (trackCodeDetailTableModel.allTrackCodes && trackCodeDetailTableModel.allTrackCodes.length > 0) {
      trackCodeDetailTableModel.dataSource = trackCodeDetailTableModel.allTrackCodes.filter(
        (item: any) => item.lineNo === currentLineNo.value
      )

      // 计算已采集的拆零数量总和
      let collectedCellsSum = 0
      trackCodeDetailTableModel.dataSource.forEach((item: any) => {
        if (item.isDisassembled) {
          collectedCellsSum += (item.totalCells || 0)
        }
      })

      // 更新当前药品的已采集拆零数量
      const currentDrug = billDetailTableModel.dataSource.find((item: any) => item.lineNo === currentLineNo.value)
      if (currentDrug) {
        currentDrug.collectedCells = collectedCellsSum
      }
    } else {
      trackCodeDetailTableModel.dataSource = []
    }
  } else {
    trackCodeDetailTableModel.dataSource = []
  }


}

// 更新追溯码缓存
const updateTrackCodeCache = () => {
  // 清空缓存
  trackCodeCache.clear()

  // 将所有追溯码添加到缓存
  if (trackCodeDetailTableModel.allTrackCodes) {
    trackCodeDetailTableModel.allTrackCodes.forEach((item: any) => {
      trackCodeCache.add(item.trackCode, item.lineNo, item.artName)
    })
  }
}

// 更新药品的采集结果和已采数量
const updateCollectedCounts = () => {
  // 重置所有药品的采集数量
  billDetailTableModel.dataSource.forEach((drug: any) => {
    drug.collectedPacks = 0
    drug.collectedCells = 0
    drug.collectedCount = 0
    drug.hasMultiplePrefixes = false
  })

  // 根据追溯码更新药品的采集数量
  if (trackCodeDetailTableModel.allTrackCodes) {
    // 创建药品前缀集合映射
    const drugPrefixes: Record<number, Set<string>> = {}

    trackCodeDetailTableModel.allTrackCodes.forEach((item: any) => {
      const lineNo = item.lineNo
      const drug = billDetailTableModel.dataSource.find((d: any) => d.lineNo === lineNo)

      if (drug) {
        // 更新整包数和拆零数
        if (item.isDisassembled) {
          // 拆零模式
          if (drug.collectedCells !== undefined) {
            drug.collectedCells += (item.totalCells || 0)
          } else {
            drug.collectedCells = (item.totalCells || 0)
          }
        } else {
          // 整包模式
          if (drug.collectedPacks !== undefined) {
            drug.collectedPacks += 1
          } else {
            drug.collectedPacks = 1
          }
        }

        // 兼容旧版本，保留collectedCount字段
        if (drug.collectedCount) {
          drug.collectedCount += 1
        } else {
          drug.collectedCount = 1
        }

        // 记录药品的追溯码前缀
        const prefix = item.trackCode.substring(0, 7)
        if (!drugPrefixes[lineNo]) {
          drugPrefixes[lineNo] = new Set()
        }
        drugPrefixes[lineNo].add(prefix)
      }
    })

    // 更新药品的多前缀标志
    Object.entries(drugPrefixes).forEach(([lineNo, prefixes]) => {
      const drug = billDetailTableModel.dataSource.find((d: any) => d.lineNo === Number(lineNo))
      if (drug) {
        drug.hasMultiplePrefixes = prefixes.size > 1
      }
    })
  }

  // 刷新表格数据
  billDetailTableModel.dataSource = [...billDetailTableModel.dataSource]
}

// 追溯码缓存，用于存储所有已扫描的追溯码
const trackCodeCache = {
  // 存储格式: { [trackCode]: { lineNo, artName } }
  cache: {} as Record<string, { lineNo: number, artName: string }>,

  // 添加追溯码
  add(trackCode: string, lineNo: number, artName: string) {
    this.cache[trackCode] = { lineNo, artName }
  },

  // 删除追溯码
  remove(trackCode: string) {
    delete this.cache[trackCode]
  },

  // 获取所有追溯码
  getAll() {
    return this.cache
  },

  // 检查追溯码是否存在
  has(trackCode: string) {
    return !!this.cache[trackCode]
  },

  // 清空缓存
  clear() {
    this.cache = {}
  }
}

// 追溯码处理队列
const trackCodeQueue = reactive({
  queue: [] as string[],
  processing: false,

  // 添加追溯码到队列
  add(trackCode: string) {
    this.queue.push(trackCode)
    this.processQueue()
  },

  // 处理队列
  async processQueue() {
    if (this.processing || this.queue.length === 0) return

    this.processing = true
    const trackCode = this.queue.shift()

    try {
      await processTrackCode(trackCode as string)
    } catch (error) {
      console.log('处理追溯码出错:', error)
    } finally {
      this.processing = false
      this.processQueue() // 处理下一个
    }
  }
})

const init = async (title: string, wbSeqid: number, visitId: number, isView: boolean) => {
  titleRef.value = title
  visitIdRef.value = visitId
  currentLineNo.value = null
  // 重置currentArtInfo
  currentArtInfo.artName = ''
  currentArtInfo.artSpec = ''
  currentArtInfo.producer = ''
  currentArtInfo.noTrackCode = false
  currentArtInfo.artIsDisassembled = false // 重置拆零上报标志
  currentArtInfo.trackCodePrefix = ''
  currentArtInfo.scannedPrefixes.clear()
  currentArtInfo.totalPacks = 0
  currentArtInfo.totalCells = 0
  currentArtInfo.collectedPacks = 0
  currentArtInfo.collectedCells = 0

  // 清空追溯码缓存
  trackCodeCache.clear()

  // 重置searchFormModel
  searchFormModel.isDisassembled = false
  searchFormModel.totalCells = 0
  searchFormModel.trackCode = ''
  searchFormModel.onlyAddRecognized = props.onlyAddRecognizedTrackCode

  // 初始化formState
  formState.wbSeqid = wbSeqid

  const response = await infoApi({
    wbSeqid: wbSeqid
  })
  isViewRef.value = isView
  Object.assign(formState, response)

  billDetailTableModel.dataSource = []
  trackCodeDetailTableModel.dataSource = []
  await billDetailTableModel.loadDataSource()
  await trackCodeDetailTableModel.loadDataSource()

  // 更新追溯码缓存
  updateTrackCodeCache()

  // 更新药品的采集结果和已采数量
  updateCollectedCounts()

  // 显示对话框
  modalVisible.value = true

  // 自动聚焦到追溯码输入框
  nextTick(() => {
    trackCodeRef.value?.focus()
  })
}

const searchFormModel = reactive({
  isDisassembled: false,
  totalCells: 0,
  trackCode: '',
  onlyAddRecognized: false // 是否只添加识别追溯码
})

// 处理追溯码的核心函数
const processTrackCode = async (trackCode: string) => {
  if (billDetailTableModel.selectedRowKeys.length === 0) {
    message.error('请选择要采集的药品')
    return
  }

  // 验证追溯码长度
  if (trackCode.length < 19 || trackCode.length > 27) {
    message.warning('追溯码长度应在19-27位之间，请检查')
    return
  }

  // 提取追溯码前7位
  const prefix = trackCode.substring(0, 7)

  // 检查追溯码是否已经扫描过（当前药品）
  const isTrackCodeAlreadyScanned = trackCodeDetailTableModel.dataSource.some(
    item => item.trackCode === trackCode
  )

  if (isTrackCodeAlreadyScanned) {
    message.warning(`追溯码 ${trackCode} 已经扫描过，请勿重复扫描`)
    return
  }

  // 获取所有已扫描的追溯码（全局缓存）
  const allScannedTrackCodes = trackCodeCache.getAll()

  // 检查追溯码是否已被其他药品使用
  if (allScannedTrackCodes[trackCode] && allScannedTrackCodes[trackCode].lineNo !== currentLineNo.value) {
    const usedDrug = billDetailTableModel.dataSource.find(
      item => item.lineNo === allScannedTrackCodes[trackCode].lineNo
    )

    if (usedDrug) {
      message.warning(`追溯码 ${trackCode} 已被药品"${usedDrug.artName}"使用，请检查`)
      return
    }
  }

  // 智能匹配药品
  const matchedDrugs = findMatchingDrugs(prefix)

  // 如果只匹配到一种药品，自动选择该药品
  if (matchedDrugs.length === 1) {
    // 如果匹配的药品不是当前选中的药品，自动切换
    if (matchedDrugs[0].lineNo !== currentLineNo.value) {
      // 选择匹配的药品
      billDetailTableModel.selectRow(matchedDrugs[0])
      console.log(`追溯码前7位 ${prefix} 匹配到药品 "${matchedDrugs[0].artName}"，已自动选择`)
    }
  } else if (matchedDrugs.length > 1) {
    // 如果匹配到多种药品，使用当前选中的药品
    // message.info(`追溯码前7位 ${prefix} 匹配到多种药品，将使用当前选中的药品`)
  } else if (matchedDrugs.length === 0 && searchFormModel.onlyAddRecognized) {
    // 如果没有匹配到任何药品，且设置了只添加识别追溯码，则不绑定
    message.warning(`追溯码前7位 ${prefix} 未匹配到任何药品，已跳过`)
    return
  }

  // 获取当前药品信息（可能已经被上面的代码更新）
  const currentArt = billDetailTableModel.dataSource.find(item => item.lineNo === currentLineNo.value)
  if (!currentArt) {
    message.error('未找到当前选中的药品')
    return
  }

  // 如果是第一次扫描该药品的追溯码，记录前缀
  if (!currentArtInfo.trackCodePrefix && !currentArtInfo.scannedPrefixes.size) {
    currentArtInfo.trackCodePrefix = prefix
    currentArtInfo.scannedPrefixes.add(prefix)
  }
  // 检查追溯码前7位与药品匹配
  else if (currentArtInfo.trackCodePrefix && prefix !== currentArtInfo.trackCodePrefix) {
    // 记录不同的前缀
    currentArtInfo.scannedPrefixes.add(prefix)

    // 根据前缀数量确定提示级别
    if (currentArtInfo.scannedPrefixes.size === 2) {
      message.warning(`检测到第二种追溯码前缀(${prefix})，与之前的前缀(${currentArtInfo.trackCodePrefix})不同，请注意检查`)
    } else if (currentArtInfo.scannedPrefixes.size > 2) {
      message.error(`检测到多种追溯码前缀，当前前缀(${prefix})，请特别注意检查`)
    }

    // 不阻止继续扫描，只做提醒
  }

  // 添加追溯码
  try {
    await addCodeApi({
      wbSeqid: formState.wbSeqid,
      lineNo: currentLineNo.value,
      trackCode: trackCode,
      isDisassembled: searchFormModel.isDisassembled ? 1 : 0,
      totalCells: searchFormModel.totalCells.toString()
    })

    // 更新药品已采集数量
    const billDetailTableDataSource = billDetailTableModel.dataSource
    const billDetailItem = billDetailTableDataSource.find(item => item.lineNo === currentLineNo.value)
    if (billDetailItem) {
      // 更新整包数和拆零数
      if (searchFormModel.isDisassembled) {
        // 拆零模式
        if (billDetailItem.collectedCells !== undefined) {
          billDetailItem.collectedCells += searchFormModel.totalCells
        } else {
          billDetailItem.collectedCells = searchFormModel.totalCells
        }
        // 添加动画效果
        billDetailItem.animateCells = true
        setTimeout(() => {
          billDetailItem.animateCells = false
        }, 500)
      } else {
        // 整包模式
        if (billDetailItem.collectedPacks !== undefined) {
          billDetailItem.collectedPacks += 1
        } else {
          billDetailItem.collectedPacks = 1
        }
        // 添加动画效果
        billDetailItem.animatePacks = true
        setTimeout(() => {
          billDetailItem.animatePacks = false
        }, 500)
      }

      // 兼容旧版本，保留collectedCount字段
      if (billDetailItem.collectedCount) {
        billDetailItem.collectedCount = billDetailItem.collectedCount + 1
      } else {
        billDetailItem.collectedCount = 1
      }

      billDetailTableModel.dataSource = [...billDetailTableDataSource]

      // 添加到追溯码缓存
      trackCodeCache.add(trackCode, currentLineNo.value, billDetailItem.artName)

      // 创建新的追溯码记录
      const newTrackCode = {
        wbSeqid: formState.wbSeqid,
        lineNo: currentLineNo.value,
        trackCode: trackCode,
        isDisassembled: searchFormModel.isDisassembled ? 1 : 0,
        totalCells: searchFormModel.isDisassembled ? searchFormModel.totalCells : 0,
        artName: billDetailItem.artName,
        artSpec: billDetailItem.artSpec,
        producer: billDetailItem.producer
      }

      // 添加到追溯码列表
      if (trackCodeDetailTableModel.allTrackCodes) {
        trackCodeDetailTableModel.allTrackCodes.push(newTrackCode)
      } else {
        trackCodeDetailTableModel.allTrackCodes = [newTrackCode]
      }

      // 更新过滤后的追溯码数据
      updateFilteredTrackCodes()

      // 确保右侧表格显示当前药品的追溯码
      if (trackCodeDetailTableModel.dataSource.length === 0 && currentLineNo.value) {
        // 如果右侧表格没有数据，重新过滤一次
        trackCodeDetailTableModel.dataSource = trackCodeDetailTableModel.allTrackCodes.filter(
          (item: any) => item.lineNo === currentLineNo.value
        )
      }

      // 重新选中当前行，以便更新拆零数量
      if (currentLineNo.value) {
        const currentDrug = billDetailTableModel.dataSource.find((item: any) => item.lineNo === currentLineNo.value)
        if (currentDrug) {
          // 不直接调用selectRow以避免重置追溯码输入框，只更新拆零数量
          if (currentDrug.totalCells > 0 || currentDrug.artIsDisassembled) {
            // 特殊情况：totalPacks>0，packCells=1且设置了拆零上报时，使用totalPacks减去已采集数量
            if (currentDrug.artIsDisassembled && currentDrug.totalPacks > 0 && currentDrug.packCells === 1) {
              const remainingPacks = currentDrug.totalPacks - (currentDrug.collectedCells || 0)
              // 如果剩余数量大于0，则使用剩余数量，否则使用总数量
              searchFormModel.totalCells = remainingPacks > 0 ? remainingPacks : currentDrug.totalPacks
            }
            // 否则按正常逻辑计算剩余拆零数量
            else if (currentDrug.totalCells > 0) {
              // 计算剩余的拆零数量 = 总拆零数量 - 已采集拆零数量
              const remainingCells = currentDrug.totalCells - (currentDrug.collectedCells || 0)
              // 如果剩余拆零数量大于0，则使用剩余数量，否则使用总数量
              searchFormModel.totalCells = remainingCells > 0 ? remainingCells : currentDrug.totalCells
            } else {
              searchFormModel.totalCells = 1
            }
          }
        }
      }

      // 判断是否需要自动切换到下一种药品
      const hasMultiplePrefixes = currentArtInfo.scannedPrefixes.size > 1

      // 计算是否已完成采集
      let isCollectionComplete = false

      // 如果设置了拆零上报标志
      if (billDetailItem.artIsDisassembled) {
        // 如果是拆零上报且totalPacks>0，则要求拆零数合计等于totalPacks
        if (billDetailItem.totalPacks > 0 && billDetailItem.packCells === 1) {
          isCollectionComplete = billDetailItem.collectedCells >= billDetailItem.totalPacks
        } else {
          // 否则只要有拆零数量即可
          isCollectionComplete = billDetailItem.collectedCells > 0
        }

        // 添加日志，帮助调试
        console.log('拆零上报模式下判断是否完成采集:', {
          artName: billDetailItem.artName,
          totalPacks: billDetailItem.totalPacks,
          collectedCells: billDetailItem.collectedCells,
          isCollectionComplete
        })
      } else {
        // 否则按正常逻辑检查整包和拆零数量
        const isPacksComplete = billDetailItem.totalPacks > 0 ?
          (billDetailItem.collectedPacks >= billDetailItem.totalPacks) : true
        const isCellsComplete = billDetailItem.totalCells > 0 ?
          (billDetailItem.collectedCells >= billDetailItem.totalCells) : true
        isCollectionComplete = isPacksComplete && isCellsComplete
      }

      // 记录药品是否有多个前缀
      billDetailItem.hasMultiplePrefixes = hasMultiplePrefixes

      // 在采集完成时自动切换到下一个药品，不管是否有多个前缀
      if (isCollectionComplete) {
        if (hasMultiplePrefixes) {
          // 采集完成但有多个前缀，给出提示但不阻止切换
          message.warning('该药品采集了多种不同前缀的追溯码，请注意检查')
        }
        // 自动切换到下一个药品
        selectNextRow(currentLineNo.value + 1)
      }

      // 检查是否所有药品都已满足追溯码要求
      await checkAllDrugsComplete()
    }
  } catch (error) {
    console.log('添加追溯码失败:', error)
    message.error('添加追溯码失败:' + error)
  }
}

// 处理用户输入的追溯码
const onAddArt = async () => {
  if (billDetailTableModel.selectedRowKeys.length === 0) {
    message.error('请选择要采集的药品')
    return
  }

  if (!searchFormModel.trackCode) {
    message.error('请录入追溯码')
    return
  }

  // 获取并清空输入框
  const inputText = searchFormModel.trackCode.trim()
  console.log('输入的追溯码:', inputText)
  searchFormModel.trackCode = ''
  trackCodeRef.value?.focus()

  // 检查是否是多行文本（多个追溯码）
  if (inputText.includes('\n') || inputText.includes('\r')) {
    // 按行分割，处理每一行（处理不同的换行符）
    const trackCodes = inputText.split(/[\r\n]+/).filter(code => code.trim() !== '')
    console.log('检测到多行追溯码:', trackCodes)

    // 将每个追溯码添加到处理队列
    for (const code of trackCodes) {
      trackCodeQueue.add(code.trim())
    }

    // message.info(`已添加${trackCodes.length}个追溯码到处理队列`)
  } else {
    // 单个追溯码，直接添加到队列
    trackCodeQueue.add(inputText)
  }
}

const selectNextRow = (lineNo) => {
  const billDetailTableDataSource = billDetailTableModel.dataSource
  const nextItem = billDetailTableDataSource.find(item => item.lineNo === lineNo)
  if (nextItem) {
    if (nextItem.noTrackCode) {
      selectNextRow(nextItem.lineNo + 1)
    } else {
      billDetailTableModel.selectRow(nextItem)
    }
  }
}

// 删除追溯码
const onDelArt = async (record: any) => {
  try {
    // 删除追溯码
    await delCodeApi({
      wbSeqid: record.wbSeqid,
      lineNo: record.lineNo,
      trackCode: record.trackCode
    })

    // 更新药品已采集数量
    const billDetailTableDataSource = billDetailTableModel.dataSource
    const billDetailItem = billDetailTableDataSource.find(item => item.lineNo === record.lineNo)
    if (billDetailItem) {
      // 更新整包数和拆零数
      if (record.isDisassembled) {
        // 拆零模式
        if (billDetailItem.collectedCells !== undefined) {
          billDetailItem.collectedCells -= (record.totalCells || 0)
          if (billDetailItem.collectedCells < 0) billDetailItem.collectedCells = 0
          // 添加动画效果
          billDetailItem.animateCells = true
          setTimeout(() => {
            billDetailItem.animateCells = false
          }, 500)
        }
      } else {
        // 整包模式
        if (billDetailItem.collectedPacks !== undefined) {
          billDetailItem.collectedPacks -= 1
          if (billDetailItem.collectedPacks < 0) billDetailItem.collectedPacks = 0
          // 添加动画效果
          billDetailItem.animatePacks = true
          setTimeout(() => {
            billDetailItem.animatePacks = false
          }, 500)
        }
      }

      // 兼容旧版本，保留collectedCount字段
      if (billDetailItem.collectedCount) {
        billDetailItem.collectedCount = billDetailItem.collectedCount - 1
        if (billDetailItem.collectedCount < 0) billDetailItem.collectedCount = 0
      }

      // 如果删除的是最后一个追溯码，清空记录的前缀信息
      if ((billDetailItem.collectedPacks === 0 && billDetailItem.collectedCells === 0) ||
          billDetailItem.collectedCount === 0) {
        currentArtInfo.trackCodePrefix = ''
        currentArtInfo.scannedPrefixes.clear()
      }
    }
    billDetailTableModel.dataSource = [...billDetailTableDataSource]

    // 从追溯码缓存中移除
    trackCodeCache.remove(record.trackCode)

    // 从追溯码列表中移除
    if (trackCodeDetailTableModel.allTrackCodes) {
      const index = trackCodeDetailTableModel.allTrackCodes.findIndex(
        (item: any) => item.trackCode === record.trackCode
      )
      if (index !== -1) {
        trackCodeDetailTableModel.allTrackCodes.splice(index, 1)
        // 更新过滤后的追溯码数据
        updateFilteredTrackCodes()
      }
    }

    // 自动聚焦到追溯码输入框
    searchFormModel.trackCode = ''
    trackCodeRef.value?.focus()

    message.success('追溯码删除成功')
  } catch (error) {
    console.error('删除追溯码失败:', error)
    message.error('删除追溯码失败')

    // 如果删除失败，重新加载数据
    await trackCodeDetailTableModel.loadDataSource()
  }
}

const loading = ref(false)

const onSave = () => {
  formRef.value.validate().then(async () => {
    await saveBillApi(formState)
    message.success('保存成功')
    onClose()
  }).catch(error => {
    console.log('error', error)
  })
}

const onSubmit = () => {
  // 检查是否有未完成的药品（排除noTrackCode=1的药品）
  const drugsNeedTrackCode = billDetailTableModel.dataSource.filter(drug => !drug.noTrackCode)
  const incompleteDrugs = drugsNeedTrackCode.filter(drug => {
    // 如果设置了拆零上报标志
    if (drug.artIsDisassembled) {
      // 如果是拆零上报且totalPacks>0，则要求拆零数合计等于totalPacks
      if (drug.totalPacks > 0 && drug.packCells === 1) {
        return drug.collectedCells < drug.totalPacks
      } else {
        // 否则只要有拆零数量即可
        return drug.collectedCells <= 0
      }
    } else {
      // 否则按正常逻辑检查整包和拆零数量
      const isPacksComplete = drug.totalPacks > 0 ? (drug.collectedPacks >= drug.totalPacks) : true
      const isCellsComplete = drug.totalCells > 0 ? (drug.collectedCells >= drug.totalCells) : true
      return !isPacksComplete || !isCellsComplete
    }
  })

  // 如果有未完成的药品，弹出确认对话框
  if (incompleteDrugs.length > 0) {
    // 如果启用了追溯码完整性检查，直接提示用户不能提交
    if (props.checkTrackCodeComplete) {
      message.warning(`还有${incompleteDrugs.length}种药品未完成追溯码扫描，请先扫描完所有追溯码再提交`)
      return
    }

    Modal.confirm({
      title: '确认提交',
      icon: createVNode(ExclamationCircleOutlined),
      content: `还有${incompleteDrugs.length}种药品未完成追溯码扫描，确定要提交吗？`,
      okText: '确认提交',
      okType: 'primary',
      cancelText: '继续扫描',
      onOk() {
        // 用户确认提交
        submitBill()
      },
      onCancel() {
        // 用户取消提交，继续扫描
        message.info('已取消提交，请继续扫描追溯码')
      },
    })
  } else {
    // 所有药品都已完成，直接提交
    submitBill()
  }
}

// 提交单据
const submitBill = () => {
  formRef.value.validate().then(async () => {
    await submitBillApi(formState)
    message.success('提交成功')
    onClose()
    emit('success', visitIdRef.value)
  }).catch(error => {
    console.log('error', error)
  })
}

// 查找匹配追溯码前缀的药品
const findMatchingDrugs = (trackCodePrefix: string) => {
  const matchedDrugs: any[] = []

  // 遍历所有药品
  billDetailTableModel.dataSource.forEach(drug => {
    // 跳过不需要追溯码的药品
    if (drug.noTrackCode) {
      return
    }

    // 检查药品是否有prodNoLs
    if (drug.prodNoLs && Array.isArray(drug.prodNoLs) && drug.prodNoLs.length > 0) {
      // 遍历prodNoLs数组
      for (const prodNo of drug.prodNoLs) {
        // 将prodNo转为字符串并处理
        let prodNoStr = String(prodNo)
        // 如果长度小于7位，前面补0
        while (prodNoStr.length < 7) {
          prodNoStr = '0' + prodNoStr
        }

        // 检查是否匹配
        if (prodNoStr === trackCodePrefix) {
          matchedDrugs.push(drug)
          break
        }
      }
    }
  })

  return matchedDrugs
}

// 检查是否所有药品都已满足追溯码要求
const checkAllDrugsComplete = async () => {
  // 获取所有需要追溯码的药品（排除noTrackCode=1的药品）
  const drugsNeedTrackCode = billDetailTableModel.dataSource.filter(drug => !drug.noTrackCode)

  // 如果没有需要追溯码的药品，直接返回
  if (drugsNeedTrackCode.length === 0) {
    return
  }

  // 检查是否所有药品都已满足追溯码要求
  const allComplete = drugsNeedTrackCode.every(drug => {
    // 如果设置了拆零上报标志
    if (drug.artIsDisassembled) {
      // 如果是拆零上报且totalPacks>0，则要求拆零数合计等于totalPacks
      if (drug.totalPacks > 0 && drug.packCells === 1) {
        return drug.collectedCells >= drug.totalPacks
      } else {
        // 否则只要有拆零数量即可
        return drug.collectedCells > 0
      }
    } else {
      // 否则按正常逻辑检查整包和拆零数量
      const isPacksComplete = drug.totalPacks > 0 ? (drug.collectedPacks >= drug.totalPacks) : true
      const isCellsComplete = drug.totalCells > 0 ? (drug.collectedCells >= drug.totalCells) : true
      return isPacksComplete && isCellsComplete
    }
  })

  // 检查是否有异常情况（不同前缀的追溯码）
  const hasAbnormalPrefixes = drugsNeedTrackCode.some(drug => drug.hasMultiplePrefixes === true)

  // 如果所有药品都已满足追溯码要求且没有异常情况，通知后端并弹出提示
  if (allComplete && !hasAbnormalPrefixes) {
    // 调用API通知后端所有码都绑定成功
    try {
      await trackCodeAllConfirmByWbSeqIdApi({
        wbSeqid: formState.wbSeqid
      })
      console.log('已通知后端所有追溯码绑定成功')
    } catch (error) {
      console.error('通知后端追溯码绑定成功失败:', error)
    }

    // 只有当enableAutoClosePrompt为true时，才显示自动关闭提示
    if (props.enableAutoClosePrompt) {
      // 创建倒计时
      let secondsToGo = 5

      // 创建倒计时提示
      const modal = Modal.confirm({
        title: '追溯码扫描完成',
        icon: createVNode(CheckCircleOutlined, { style: { color: '#52c41a' } }),
        content: `所有药品的追溯码都已扫描完成，将在 ${secondsToGo} 秒后自动关闭窗口`,
        okText: '立即关闭',
        cancelText: '取消关闭',
        okButtonProps: {
          type: 'primary'
        },
        cancelButtonProps: {
          danger: true
        },
        onOk: () => {
          // 提交
          onSubmit()
          // 清除定时器
          clearInterval(interval)
        },
        onCancel: () => {
          // 取消提交，继续调整
          message.info('已取消自动提交，您可以继续调整')
          // 清除定时器
          clearInterval(interval)
        }
      })

      // 更新倒计时
      const interval = setInterval(() => {
        secondsToGo -= 1
        modal.update({
          content: `所有药品的追溯码都已扫描完成，将在 ${secondsToGo} 秒后自动提交`
        })

        if (secondsToGo <= 0) {
          clearInterval(interval)
          // 提交
          onSubmit()
          // 关闭弹窗
          modal.destroy()
        }
      }, 1000)
    } else {
      // 如果禁用了自动关闭提示，则只显示一个普通的成功消息
      message.success('所有药品的追溯码都已扫描完成')
    }
  }
}

// 清除所有追溯码
const onClearAllTrackCodes = () => {
  // 显示确认对话框
  Modal.confirm({
    title: '确认清除所有追溯码',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确定要清除所有已扫描的追溯码吗？此操作不可撤销。',
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        // 调用API清除所有追溯码
        await delAllCodeApi({
          wbSeqid: formState.wbSeqid
        })

        // 清空追溯码缓存
        trackCodeCache.clear()

        // 清空追溯码列表
        trackCodeDetailTableModel.allTrackCodes = []
        trackCodeDetailTableModel.dataSource = []

        // 重置药品的采集数量
        billDetailTableModel.dataSource.forEach(drug => {
          drug.collectedPacks = 0
          drug.collectedCells = 0
          drug.collectedCount = 0
          drug.hasMultiplePrefixes = false
        })

        // 重新加载数据
        await billDetailTableModel.loadDataSource()
        await trackCodeDetailTableModel.loadDataSource()

        // 显示成功消息
        message.success('所有追溯码已清除')

        // 自动聚焦到追溯码输入框
        trackCodeRef.value?.focus()
      } catch (error) {
        console.error('清除所有追溯码失败:', error)
        message.error('清除所有追溯码失败')
      }
    },
    onCancel() {
      // 用户取消操作
      message.info('已取消清除所有追溯码')
    },
  })
}

const onClose = () => {
  modalVisible.value = false
  emit('cancel')
}

// 处理粘贴事件
const handlePaste = (e: ClipboardEvent) => {
  // 阻止默认粘贴行为
  e.preventDefault()

  // 获取剪贴板数据
  const clipboardData = e.clipboardData
  if (!clipboardData) return

  // 获取文本
  const pastedText = clipboardData.getData('text')
  console.log('粘贴的文本:', pastedText)
  if (!pastedText) return

  // 检查是否包含换行符
  if (pastedText.includes('\n') || pastedText.includes('\r')) {
    // 多行文本，直接处理
    const trackCodes = pastedText.split(/[\r\n]+/).filter(code => code.trim() !== '')
    console.log('检测到多行追溯码:', trackCodes)

    // 将每个追溯码添加到处理队列
    for (const code of trackCodes) {
      trackCodeQueue.add(code.trim())
    }

    // message.info(`已添加${trackCodes.length}个追溯码到处理队列`)
  } else {
    // 单行文本，设置到输入框
    searchFormModel.trackCode = pastedText.trim()
  }
}

const changeIsDisassembled = (checkedValue) => {
  console.log(checkedValue)
  console.log(searchFormModel.isDisassembled)

  // 如果选中拆零，聚焦到拆零数量输入框
  if (searchFormModel.isDisassembled) {
    nextTick(() => {
      totalCellsRef.value?.focus()
    })
  } else {
    // 如果取消拆零，聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })
  }
}

// 处理"只添加识别追溯码"复选框的变化
const changeOnlyAddRecognized = (checkedValue) => {
  console.log('只添加识别追溯码:', checkedValue)
  searchFormModel.onlyAddRecognized = checkedValue
  // 自动聚焦到追溯码输入框
  nextTick(() => {
    trackCodeRef.value?.focus()
  })
}

const onSetNoTrackCode = () => {
  // 显示确认对话框
  Modal.confirm({
    title: '确认设置为无追溯码',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要将药品"${currentArtInfo.artName}"设置为无追溯码吗？操作后该条目不需要再扫追溯码。`,
    okText: '确认',
    okType: 'danger',
    cancelText: '取消',
    async onOk() {
      try {
        await setNoTrackCodeApi({
          wbSeqid: formState.wbSeqid,
          lineNo: currentLineNo.value
        })

        // 更新当前药品信息
        currentArtInfo.noTrackCode = true

        // 更新药品列表中的数据
        const billDetailTableDataSource = billDetailTableModel.dataSource
        const billDetailItem = billDetailTableDataSource.find(item => item.lineNo === currentLineNo.value)
        if (billDetailItem) {
          billDetailItem.noTrackCode = true
          billDetailTableModel.dataSource = [...billDetailTableDataSource]
        }

        // 显示成功消息
        message.success(`已将药品"${currentArtInfo.artName}"设置为无追溯码`)

        // 自动选择下一个药品
        selectNextRow(currentLineNo.value + 1)
      } catch (error) {
        console.error('设置无追溯码失败:', error)
        message.error('设置无追溯码失败')
      }
    },
    onCancel() {
      // 用户取消操作
      message.info('已取消设置无追溯码')
    },
  })
}

// 设置为有追溯码
const onClearNoTrackCode = () => {
  try {
    trackCodeClearNoTrackCodeApi({
      wbSeqid: formState.wbSeqid,
      lineNo: currentLineNo.value
    }).then(() => {
      // 更新当前药品信息
      currentArtInfo.noTrackCode = false

      // 更新药品列表中的数据
      const billDetailTableDataSource = billDetailTableModel.dataSource
      const billDetailItem = billDetailTableDataSource.find(item => item.lineNo === currentLineNo.value)
      if (billDetailItem) {
        billDetailItem.noTrackCode = false
        billDetailTableModel.dataSource = [...billDetailTableDataSource]
      }

      // 显示成功消息
      message.success(`已将药品"${currentArtInfo.artName}"设置为有追溯码`)
    }).catch(error => {
      console.error('设置有追溯码失败:', error)
      message.error('设置有追溯码失败')
    })
  } catch (error) {
    console.error('设置有追溯码失败:', error)
    message.error('设置有追溯码失败')
  }
}

// 设置拆零上报
const onSetDisassembled = () => {
  // 显示确认对话框
  Modal.confirm({
    title: '确认设置拆零上报',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要将药品"${currentArtInfo.artName}"设置为拆零上报吗？`,
    okText: '确认',
    okType: 'primary',
    cancelText: '取消',
    async onOk() {
      try {
        await trackCodeSetDisassembledApi({
          wbSeqid: formState.wbSeqid,
          lineNo: currentLineNo.value
        })

        // 更新当前药品信息
        currentArtInfo.artIsDisassembled = true

        // 更新药品列表中的数据
        const billDetailTableDataSource = billDetailTableModel.dataSource
        const billDetailItem = billDetailTableDataSource.find(item => item.lineNo === currentLineNo.value)
        if (billDetailItem) {
          billDetailItem.artIsDisassembled = true
          billDetailTableModel.dataSource = [...billDetailTableDataSource]

          // 自动勾选拆零复选框
          searchFormModel.isDisassembled = true

          // 特殊情况：totalPacks>0，packCells=1时，使用totalPacks减去已采集数量
          if (billDetailItem.totalPacks > 0 && billDetailItem.packCells === 1) {
            // 计算剩余的数量 = totalPacks - 已采集拆零数量
            const remainingPacks = billDetailItem.totalPacks - (billDetailItem.collectedCells || 0)
            // 如果剩余数量大于0，则使用剩余数量，否则使用总数量
            searchFormModel.totalCells = remainingPacks > 0 ? remainingPacks : billDetailItem.totalPacks
          }
          // 如果有拆零数量，则计算剩余的拆零数量
          else if (billDetailItem.totalCells > 0) {
            // 计算剩余的拆零数量 = 总拆零数量 - 已采集拆零数量
            const remainingCells = billDetailItem.totalCells - (billDetailItem.collectedCells || 0)
            // 如果剩余拆零数量大于0，则使用剩余数量，否则使用总数量（可能是重新采集）
            searchFormModel.totalCells = remainingCells > 0 ? remainingCells : billDetailItem.totalCells
          } else {
            searchFormModel.totalCells = 1
          }

          // 自动聚焦到追溯码输入框，方便用户直接扫码
          nextTick(() => {
            trackCodeRef.value?.focus()
          })
        }

        // 显示成功消息
        message.success(`已将药品"${currentArtInfo.artName}"设置为拆零上报`)
      } catch (error) {
        console.error('设置拆零上报失败:', error)
        message.error('设置拆零上报失败')
      }
    },
    onCancel() {
      // 用户取消操作
      message.info('已取消设置拆零上报')
    },
  })
}

// 取消拆零上报
const onClearDisassembled = () => {
  try {
    trackCodeClearDisassembledApi({
      wbSeqid: formState.wbSeqid,
      lineNo: currentLineNo.value
    }).then(() => {
      // 更新当前药品信息
      currentArtInfo.artIsDisassembled = false

      // 更新药品列表中的数据
      const billDetailTableDataSource = billDetailTableModel.dataSource
      const billDetailItem = billDetailTableDataSource.find(item => item.lineNo === currentLineNo.value)
      if (billDetailItem) {
        billDetailItem.artIsDisassembled = false
        billDetailTableModel.dataSource = [...billDetailTableDataSource]
      }

      // 显示成功消息
      message.success(`已取消药品"${currentArtInfo.artName}"的拆零上报`)
    }).catch(error => {
      console.error('取消拆零上报失败:', error)
      message.error('取消拆零上报失败')
    })
  } catch (error) {
    console.error('取消拆零上报失败:', error)
    message.error('取消拆零上报失败')
  }
}

// 打开对话框
const open = (title: string, wbSeqid: number, visitId: number, isView: boolean = false) => {
  init(title, wbSeqid, visitId, isView)
}

// 关闭对话框
const close = () => {
  onClose()
}

// 暴露方法
defineExpose({
  init,
  open,
  close
})
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    @ok="onSubmit"
    @cancel="onClose"
    :maskClosable="false"
    :keyboard="false"
    :title="titleRef"
    :width="modalWidth"
    :style="{ top: '20px', maxHeight: 'calc(100vh - 40px)' }"
    class="recipe-track-code-modal"
    :closeIcon="false"
  >
    <template #title>
      <div class="custom-modal-title">
        <span>{{ titleRef }}</span>
      </div>
    </template>
    <template #footer>
      <Button key="back" @click="onClose" v-if="isViewRef">关闭</Button>
      <Button key="back" @click="onClose" v-if="!isViewRef">取消</Button>
      <Button :loading="loading" @click="onSave" v-if="!isViewRef">保存</Button>
      <Button key="submit" type="primary" :loading="loading" @click="onSubmit" v-if="!isViewRef">提交</Button>
    </template>
    <div class="bg-fff recipe-track-code-content">
      <div class="recipe-track-code-header">
        <Row>
          <Col flex="1000px">
            <Form layout="inline" ref="formRef" :model="formState" :rules="rules">
              <Form.Item label="单据类型">
                {{ formState.wmbillTypeName }}
              </Form.Item>
              <Form.Item label="业务类型">
                {{ formState.bsnTypeName }}
              </Form.Item>
              <Form.Item label="仓库">
                {{ formState.deptName }}
              </Form.Item>
              <Form.Item label="单据流水">
                {{ formState.wbSeqid }}
              </Form.Item>
              <Form.Item label="追溯码状态">
                {{ formState.trackcodeStatusName }}
              </Form.Item>
              <Form.Item label="库存变更类型" name="inventoryChangeType">
                <Select v-model:value="formState.inventoryChangeType" placeholder="请选择库存变更类型" style="width: 100px" :disabled="isViewRef">
                  <Select.Option v-for="item in inventoryChangeTypeLs" :key="item.value" :value="item.value">{{ item.label }}</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col flex="auto">
          </Col>
          <Col flex="100px">
            <Button danger @click="onClearAllTrackCodes" v-show="hasAnyTrackCodes">全部清除</Button>
          </Col>
        </Row>
      </div>
      <!-- 表格区域 -->
      <div class="recipe-track-code-tables">
        <Row>
          <Col :span="16">
            <Table
              :loading="billDetailTableModel.loading"
              :columns="billDetailTableModel.columns"
              :dataSource="billDetailTableModel.dataSource"
              :rowSelection="billDetailTableModel.rowSelection"
              :customRow="billDetailTableModel.customRow"
              :pagination="billDetailTableModel.pagination"
              rowKey="lineNo"
              :scroll="{ y: 'calc(100vh - 300px)' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'artName'">
                  {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
                </template>
                <template v-if="column.dataIndex === 'packCells'">
                  {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
                </template>
              </template>
            </Table>
          </Col>
          <Col :span="8">
            <Table
              :loading="trackCodeDetailTableModel.loading"
              :columns="trackCodeDetailTableModel.columns"
              :dataSource="trackCodeDetailTableModel.dataSource"
              :pagination="trackCodeDetailTableModel.pagination"
              :rowKey="(item) => item.trackCode"
              :scroll="{ y: 'calc(100vh - 300px)' }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'isDisassembled'">
                  {{ record.isDisassembled === 1 ? '是' : '' }}
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <Space>
                    <a class="c-error" @click="onDelArt(record)" v-if="!isViewRef">移除</a>
                  </Space>
                </template>
              </template>
            </Table>
          </Col>
        </Row>
      </div>

      <!-- 当前选中药品信息和追溯码输入区域 -->
      <div class="recipe-track-code-input-area">
        <div class="current-art-info">
          <div style="font-size: large;">{{ currentArtInfo.artName }} {{ currentArtInfo.artSpec }} {{ currentArtInfo.producer }}</div>
          <div class="track-code-form-container">
            <Form
                layout="inline"
                :model="searchFormModel"
                v-if="!isViewRef"
                class="track-code-form"
                autocomplete="off"
            >
              <Form.Item label="拆零" name="isDisassembled" v-if="currentArtInfo.totalCells > 0 || currentArtInfo.artIsDisassembled">
                <Checkbox ref="isDisassembledRef" v-model:checked="searchFormModel.isDisassembled" @change="changeIsDisassembled"></Checkbox>
              </Form.Item>
              <Form.Item label="拆零数量" name="totalCells" v-if="(currentArtInfo.totalCells > 0 || currentArtInfo.artIsDisassembled) && searchFormModel.isDisassembled">
                <InputNumber v-model:value="searchFormModel.totalCells" ref="totalCellsRef" @pressEnter="trackCodeRef.focus()" autocomplete="off"/>
              </Form.Item>
              <Form.Item label="追溯码" name="trackCode">
                <Input
                  v-model:value="searchFormModel.trackCode"
                  ref="trackCodeRef"
                  @pressEnter="onAddArt"
                  style="width: 400px"
                  placeholder="请输入或粘贴追溯码，支持多行文本"
                  @paste="handlePaste"
                  autocomplete="off"
                />
              </Form.Item>
              <Form.Item name="onlyAddRecognized" v-if="props.enableOnlyAddRecognizedTrackCodeOption" class="only-add-recognized-item">
                <div class="switch-container">
                  <Switch v-model:checked="searchFormModel.onlyAddRecognized" @change="changeOnlyAddRecognized" />
                  <span class="switch-label">只添加识别追溯码</span>
                </div>
              </Form.Item>
            </Form>
            <div class="action-buttons-container">
              <!-- 拆零上报按钮组 -->
              <div class="button-group" v-if="currentArtInfo.totalPacks > 0 && (!currentArtInfo.packCells || currentArtInfo.packCells === 1)">
                <Button
                  v-if="!currentArtInfo.artIsDisassembled"
                  danger
                  @click="onSetDisassembled"
                >拆零上报</Button>
                <Button
                  v-if="currentArtInfo.artIsDisassembled"
                  @click="onClearDisassembled"
                >取消拆零上报</Button>
              </div>

              <!-- 无追溯码按钮组 -->
              <div class="button-group">
                <Button
                  v-if="!currentArtInfo.noTrackCode"
                  ref="btnOkRef"
                  @click="onSetNoTrackCode"
                  danger
                >设置为无追溯码</Button>
                <Button
                  v-if="currentArtInfo.noTrackCode"
                  @click="onClearNoTrackCode"
                >设置为有追溯码</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}

.recipe-track-code-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
}

.recipe-track-code-header {
  padding: 8px 0;
  flex-shrink: 0;
}

.recipe-track-code-tables {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  margin-top: 20px;
  margin-bottom: 20px;
}

.recipe-track-code-input-area {
  flex-shrink: 0;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  margin-top: auto;
}

.current-art-info {
  margin-bottom: 8px;
}

.track-code-form-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.track-code-form {
  display: flex;
  flex-wrap: wrap;
  flex: 1;

  :deep(.ant-form-item) {
    margin-right: 16px;
  }

  .only-add-recognized-item {
    margin-left: 30px;
  }

  .switch-container {
    display: flex;
    align-items: center;

    .switch-label {
      margin-left: 8px;
      font-weight: bold;
      color: #1890ff;
    }
  }
}

.action-buttons-container {
  margin-left: 20px;
  display: flex;
  justify-content: flex-end;
  min-width: 240px;

  .button-group {
    margin-left: 10px;

    &:first-child {
      margin-left: 0;
    }
  }
}

.custom-modal-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.collected-count {
  display: inline-block;
  font-weight: bold;
}

@keyframes count-change {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.5);
  }
  100% {
    transform: scale(1);
  }
}

// 调整Modal样式，使其能够撑满屏幕高度
:deep(.recipe-track-code-modal) {
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 40px);
    height: calc(100vh - 40px);
  }

  .ant-modal-body {
    flex: 1;
    overflow: auto;
    padding: 12px;
    display: flex;
    flex-direction: column;
  }

  .ant-row {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .ant-col {
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .ant-table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .ant-table {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .ant-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .ant-table-body {
    flex: 1;
    overflow: auto !important;
    min-height: 400px !important;
  }

  .ant-pagination {
    margin: 8px 0 !important;
  }
}
</style>
