<script setup lang="ts">
import { ReqComponent } from '@mh-wm/req-component'
import { Card, Typography, Divider, Button, message, Table, Input, InputNumber, Space } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, h } from 'vue'
import { basicUsage, methodsUsage, editFunctionUsage, importCode, packageJsonCode, publishCommands, buildProcess } from '../code/ReqComponentCode'

const { Title, Paragraph, Text } = Typography

// 仓库编码
const deptCode = ref('000013')

// 已添加的品种列表
const addedItems = ref([])

// 编辑状态管理
const editingKey = ref('')

// ReqComponent组件引用
const reqComponentRef = ref()

// 当前正在表单中编辑的记录ID
const formEditingId = ref('')

// 判断是否在编辑状态
const isEditing = (record: any) => record.id === editingKey.value

// 表格列定义
const columns = [
  { title: '序号', dataIndex: 'id', key: 'id', width: 80 },
  { title: '品种名称', dataIndex: 'artName', key: 'artName', width: 200 },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec', width: 150 },
  {
    title: '生产批号',
    dataIndex: 'batchNo',
    key: 'batchNo',
    width: 120,
    customRender: ({ text, record }: any) => {
      if (isEditing(record)) {
        return h(Input, {
          value: text,
          onChange: (e: any) => {
            record.batchNo = e.target.value
          }
        })
      }
      return text
    }
  },
  {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    key: 'dateManufactured',
    width: 120,
    customRender: ({ text, record }: any) => {
      if (isEditing(record)) {
        return h(Input, {
          value: text,
          placeholder: 'YYYYMMDD',
          onChange: (e: any) => {
            record.dateManufactured = e.target.value
          }
        })
      }
      return text
    }
  },
  {
    title: '有效期至',
    dataIndex: 'expiry',
    key: 'expiry',
    width: 120,
    customRender: ({ text, record }: any) => {
      if (isEditing(record)) {
        return h(Input, {
          value: text,
          placeholder: 'YYYYMMDD',
          onChange: (e: any) => {
            record.expiry = e.target.value
          }
        })
      }
      return text
    }
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ text, record }: any) => {
      if (isEditing(record)) {
        return h(InputNumber, {
          value: text,
          min: 0,
          precision: 0,
          style: { width: '80px' },
          onChange: (value: any) => {
            record.totalPacks = value
          }
        })
      }
      return text
    }
  },
  { title: '包装单位', dataIndex: 'packUnit', key: 'packUnit', width: 100 },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    key: 'totalCells',
    width: 100,
    customRender: ({ text, record }: any) => {
      if (isEditing(record)) {
        return h(InputNumber, {
          value: text,
          min: 0,
          style: { width: '80px' },
          onChange: (value: any) => {
            record.totalCells = value
          }
        })
      }
      return text || '-'
    }
  },
  { title: '拆零单位', dataIndex: 'cellUnit', key: 'cellUnit', width: 100 },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right' as const,
    customRender: ({ record }: any) => {
      const editable = isEditing(record)
      if (editable) {
        return h(Space, { size: 'small' }, [
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => saveItem(record)
          }, { default: () => '保存' }),
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => cancelEdit()
          }, { default: () => '取消' })
        ])
      } else {
        return h(Space, { size: 'small' }, [
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => editItem(record)
          }, { default: () => '编辑' }),
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => loadToForm(record)
          }, { default: () => '回填表单' }),
          h(Button, {
            type: 'link',
            size: 'small',
            danger: true,
            onClick: () => removeItem(record.id)
          }, { default: () => '删除' })
        ])
      }
    }
  }
]

// 添加品种回调
const handleAddArt = (formData: any) => {
  // 检查是否是更新操作
  if (formEditingId.value) {
    // 更新现有记录
    const index = addedItems.value.findIndex(item => item.id === formEditingId.value)
    if (index !== -1) {
      const updatedItem = {
        ...addedItems.value[index],
        ...formData,
        artName: formData.artData.artName,
        artSpec: formData.artData.artSpec
      }
      addedItems.value[index] = updatedItem
      message.success(`成功更新品种: ${updatedItem.artName}`)
      formEditingId.value = ''
      return
    }
  }

  // 添加新记录
  const newItem = {
    id: Date.now(), // 使用时间戳确保唯一性
    ...formData,
    artName: formData.artData.artName,
    artSpec: formData.artData.artSpec
  }

  // 添加到列表
  addedItems.value.push(newItem)

  // 提示添加成功
  message.success(`成功添加品种: ${newItem.artName}`)

  // 处理添加品种的逻辑，例如保存到数据库等
  console.log('添加品种成功，表单数据：', formData)
}

// 删除品种
const removeItem = (id: number) => {
  const index = addedItems.value.findIndex((item: any) => item.id === id)
  if (index !== -1) {
    addedItems.value.splice(index, 1)
    message.success('删除成功')
  }
}

// 清空列表
const clearItems = () => {
  addedItems.value = []
  editingKey.value = ''
  formEditingId.value = ''
  message.success('列表已清空')
}

// 回填数据到表单
const loadToForm = (record: any) => {
  // 将数据回填到ReqComponent组件中
  if (reqComponentRef.value && typeof reqComponentRef.value.setFormData === 'function') {
    reqComponentRef.value.setFormData(record)
    formEditingId.value = record.id
    message.info('已将数据加载到表单中，修改后点击"添加"按钮将更新此记录')
  }
}

// 编辑品种（行内编辑）
const editItem = (record: any) => {
  // 保存原始数据用于取消编辑时恢复
  record.originalData = { ...record }
  editingKey.value = record.id
}

// 保存编辑
const saveItem = (record: any) => {
  // 验证生产日期格式
  if (record.dateManufactured && !/^\d{8}$/.test(record.dateManufactured)) {
    message.error('生产日期格式必须是8位数字(YYYYMMDD)')
    return
  }

  // 验证有效期格式
  if (record.expiry && !/^\d{8}$/.test(record.expiry)) {
    message.error('有效期至格式必须是8位数字(YYYYMMDD)')
    return
  }

  // 验证有效期是否大于生产日期
  if (record.dateManufactured && record.expiry) {
    if (parseInt(record.expiry) <= parseInt(record.dateManufactured)) {
      message.error('有效期至必须大于生产日期')
      return
    }
  }

  // 验证数量
  if ((!record.totalPacks || record.totalPacks <= 0) && (!record.totalCells || record.totalCells <= 0)) {
    message.error('整包数量和拆零数量至少填写一个且大于0')
    return
  }

  // 删除原始数据
  delete record.originalData
  editingKey.value = ''
  message.success('保存成功')
}

// 取消编辑
const cancelEdit = () => {
  // 恢复原始数据
  const editingRecord = addedItems.value.find(item => item.id === editingKey.value)
  if (editingRecord && editingRecord.originalData) {
    Object.assign(editingRecord, editingRecord.originalData)
    delete editingRecord.originalData
  }
  editingKey.value = ''
  message.info('已取消编辑')
}

// 取消表单编辑模式
const cancelFormEdit = () => {
  formEditingId.value = ''
  // 清空表单
  if (reqComponentRef.value && typeof reqComponentRef.value.clearFormData === 'function') {
    reqComponentRef.value.clearFormData()
  }
  message.info('已退出编辑模式')
}
</script>

<template>
  <Card title="基础用法" class="mb-16px">
    <div mb-16px>
      <Title :level="4">品种申请表单</Title>
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <Text type="secondary">仓库编码: {{ deptCode }}</Text>
        <div v-if="formEditingId" style="display: flex; align-items: center; gap: 8px;">
          <Text type="warning" style="background-color: #fff7e6; padding: 4px 8px; border-radius: 4px; border: 1px solid #ffd591;">
            正在编辑模式 - 点击"添加"将更新记录
          </Text>
          <Button size="small" @click="cancelFormEdit">取消编辑</Button>
        </div>
      </div>
      <Divider style="margin: 8px 0" />
      <ReqComponent
        ref="reqComponentRef"
        :deptCode="deptCode"
        @addArt="handleAddArt"
      />
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        填写表单后点击"添加"按钮，将品种添加到下方列表中。列表支持：1）行内编辑 2）回填到表单编辑 3）删除操作。
      </div>
    </div>

    <div class="added-items-container" v-if="addedItems.length > 0">
      <div class="header-with-actions">
        <Title :level="4">已添加品种列表</Title>
        <Button type="primary" danger @click="clearItems">清空列表</Button>
      </div>
      <Table :dataSource="addedItems" :columns="columns" rowKey="id" :scroll="{ x: 1400 }" />
    </div>
    <div v-else class="empty-list">
      <Paragraph>暂无添加的品种，请使用上方表单添加品种</Paragraph>
    </div>

    <Divider />

    <Title :level="4">基础使用示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <!-- 组件方法示例 -->
  <Card title="组件方法示例" class="mb-16px">
    <Title :level="4">组件方法调用</Title>
    <Paragraph>
      ReqComponent组件提供了多个方法供外部调用，包括获取选中数据、清空表单数据、设置表单数据等。
    </Paragraph>
    <CodeDemoVue :usage="methodsUsage" />
  </Card>

  <!-- 编辑功能示例 -->
  <Card title="编辑功能示例" class="mb-16px">
    <Title :level="4">列表编辑功能</Title>
    <Paragraph>
      展示如何实现品种列表的编辑功能，包括行内编辑和表单回填编辑两种方式。
    </Paragraph>
    <div class="feature-list">
      <Title :level="5">功能特性</Title>
      <ul>
        <li><Text strong>行内编辑</Text>：直接在表格中编辑字段值</li>
        <li><Text strong>表单编辑</Text>：将数据回填到ReqComponent表单中进行编辑</li>
        <li><Text strong>数据验证</Text>：编辑时进行数据格式和逻辑验证</li>
        <li><Text strong>状态管理</Text>：清晰的编辑状态管理和切换</li>
        <li><Text strong>操作反馈</Text>：每个操作都有相应的消息提示</li>
      </ul>
    </div>
    <CodeDemoVue :usage="editFunctionUsage" />
  </Card>

  <!-- 打包发布指令 -->
  <Card title="打包发布指令" class="mb-16px">
    <Title :level="4">组件打包</Title>
    <Paragraph>
      开发完成后，使用以下命令打包并发布组件：
    </Paragraph>

    <div class="command-section">
      <Title :level="5">打包命令</Title>
      <div class="code-block">
        <pre>{{ publishCommands }}</pre>
      </div>
    </div>

    <div class="process-section">
      <Title :level="5">打包流程</Title>
      <div class="process-description">
        <pre>{{ buildProcess }}</pre>
      </div>
    </div>

    <div class="usage-section">
      <Title :level="5">使用说明</Title>
      <Paragraph>
        <Text strong>1. 开发阶段：</Text> 使用 <Text code>pnpm publish:dev-component Wm/ReqComponent</Text> 发布开发版本
      </Paragraph>
      <Paragraph>
        <Text strong>2. 测试阶段：</Text> 使用 <Text code>pnpm publish:test-component Wm/ReqComponent</Text> 发布测试版本
      </Paragraph>
      <Paragraph>
        <Text strong>3. 正式发布：</Text> 使用 <Text code>pnpm publish:component Wm/ReqComponent</Text> 发布正式版本
      </Paragraph>
      <Paragraph>
        <Text strong>4. 安装使用：</Text> 在其他项目中使用 <Text code>pnpm add @mh-wm/req-component</Text> 安装组件
      </Paragraph>
    </div>
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: #fff;
  text-align: center;
  line-height: 16px;
  font-style: normal;
  font-size: 12px;
  margin-right: 8px;
}

.added-items-container {
  margin-top: 16px;
}

.header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.empty-list {
  padding: 24px;
  text-align: center;
  background-color: #fafafa;
  border-radius: 4px;
  margin-top: 16px;
}

/* 打包指令相关样式 */
.command-section,
.process-section,
.usage-section {
  margin-bottom: 24px;
}

.code-block,
.process-description {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  margin: 12px 0;
  overflow-x: auto;
}

.code-block pre,
.process-description pre {
  margin: 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 13px;
  line-height: 1.45;
  color: #24292e;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.usage-section .ant-typography {
  margin-bottom: 8px;
}

/* 功能特性列表样式 */
.feature-list {
  margin: 16px 0;
}

.feature-list ul {
  margin: 8px 0;
  padding-left: 20px;
}

.feature-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}
</style>
