<script setup lang="ts">
import { OrgDoctor } from '@mh-hip/org'
import { Card, Typography, Divider, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { currentUserUsage, importCode, packageJsonCode } from '../code/OrgDoctorCode'

const { Title } = Typography

// 不指定组织机构ID的医生选择
const currentUserDoctorId = ref<number>()
</script>

<template>
  <Card title="不指定组织机构ID，使用当前用户token中的orgId" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="24">
        <div>
          <div mb-8px>使用当前用户token中的医生：</div>
          <OrgDoctor v-model="currentUserDoctorId" style="width: 100%" />
          <div mt-8px>选中的医生ID: {{ currentUserDoctorId }}</div>
          <div mt-8px class="tip-text">
            <i class="tip-icon">i</i>
            不指定组织机构ID时，将使用当前用户token中的orgId
          </div>
        </div>
      </Col>
    </Row>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="currentUserUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
