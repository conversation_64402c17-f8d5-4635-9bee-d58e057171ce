import { add, Data, format, http, Message, subtract, useLoading } from '@idmy/core'
import type { LodopOptions } from '@mh-base/core'
import { getSql, listSql, lodapPrint } from '@mh-base/core'
import { CashType, getPrevCashId, PaymentType } from '@mh-bcs/util'
import qrcode from 'qrcode'
import type { InjectionKey } from 'vue'


export const refundInjectKey: InjectionKey<any> = Symbol('refundInjectKey')

export function refundBefore(blueCashId: number, redBillIds: number[]): Promise<number> {
  return http.post('/api/bcs/Refund/refundBefore', { blueCashId, redBillIds }, { appKey: 'bcs' })
}

export function refundCancel(redCashId: number): Promise<void> {
  return http.post('/api/bcs/Refund/refundCancel', { redCashId }, { appKey: 'bcs' })
}

export function refundPayment(redCashId: number, cashId: number, lineNo: number | null, paymentType: PaymentType, amount: number) {
  return http.post('/api/bcs/Refund/refundPayment', { redCashId, cashId, lineNo, paymentType, amount }, { appKey: 'bcs' })
}

export function refundFinish(redCashId: number): Promise<Data> {
  return http.post('/api/bcs/Refund/refundFinish', { redCashId }, { appKey: 'bcs' })
}

async function getRefundData(cashId: number): Promise<Data> {
  const data = await getSql(`
    select
      org.org_name org,
      uc.User_Name userName,
      d.Clinician_Name clinicianName,
      o.Dept_Name dept,
      c.Cash_ID cashId,
      c.amount amt,
      c.Original_Cash_ID oCashId,
      c.Time_Validated cashAt,
      c.Visit_ID visitId,
      v.Patient_Name name,
      v.Gender_ID gender,
      t.Acct_Pay_Amt acctAmt,
      t.Fund_Pay_Amt fundAmt,
      t.Multi_Aid_Amt multiAidAmt,
      t.Acct_Balance acctBalance
    from microhis_bcs.t_cash c
         inner join hip_mdi.t_org org on org.org_id = c.org_id
         inner join microhis_hsd.t_visit v on c.Visit_ID = v.Visit_ID
         left join hip_mdi.t_clinician d on d.Clinician_ID = v.Clinician_ID
         left join hip_mdi.t_org_dept o on v.Dept_Code = o.Dept_Code and o.Org_ID = c.Org_ID
         left join microhis_mcisp.t_trans t on t.Cash_ID = c.Cash_ID and t.Trans_Status = 2 and t.Amount < 0
         left join hip_mdi.t_user_code uc on uc.User_ID = c.User_ID
    where c.Validated_Flag = 1
      and c.Action_Type = 2
      and c.cash_id = ${cashId}`)
  data.fundAmt = data.fundAmt ?? 0
  data.acctAmt = data.acctAmt ?? 0
  data.multiAidAmt = data.multiAidAmt ?? 0
  const miAmt = add(data.fundAmt, data.acctAmt, data.multiAidAmt)
  data.name = `姓名：${data.name ?? ''} ${data.gender ? '男' : '女'}`
  data.clinician = `医生：${data.clinicianName ?? ''} ${data.dept ?? ''}`
  data.oCashId = `原票据号：${data.oCashId}`
  data.cashId = `票据号：${data.cashId}`
  data.payMode = `结算方式：${miAmt > 0 ? '医保' : '自费'}`
  data.payee = `退费员：${data.userName}`
  data.amount = `合计金额：${format(data.amt, 'Currency')}`
  data.miFundPay = `医保统筹：${format(add(data.fundAmt, data.multiAidAmt), 'Currency')}`
  data.miAccountPay = `医保个账：${format(data.acctAmt, 'Currency')}`
  data.selfFee = `个人缴费：${format(subtract(data.amt, miAmt), 'Currency')}`
  data.date = `退费时间：${format(data.cashAt, 'Datetime')}`
  data.printTime = `打印时间：${format(Date.now(), 'Datetime')}`

  const prevCashId = await getPrevCashId(cashId)
  const inv = await getSql(`select check_code checkCode, Inv_No invNo, Inv_Code invCode, url
                            from microhis_bcs.t_invoice
                            where Cash_ID = ${prevCashId}
                              and Dropped_Flag = 1`)
  if (inv.invNo) {
    data.invNo = inv.invNo ? '0' + inv.invNo : ''
    data.invCode = inv.invCode ?? ''
    data.checkCode = inv.checkCode ?? ''
    if (inv.url) {
      data.qrcode = await new Promise((resolve, reject) => {
        qrcode.toDataURL(inv.url, { margin: 0 }, (err: any, url: string) => {
          url ? resolve(url) : reject(err)
        })
      })
    }
  }
  const arr = await listSql(`select cft.Amount + cft.Derated + cft.Discounted amount, ft.Fee_Type_Name artName
                             from microhis_bcs.t_cash_feetype cft,
                                  hip_mdi.t_fee_type ft
                             where cft.Fee_Type_ID = ft.Fee_Type_ID
                               and Cash_ID = ${cashId} `)
  arr.forEach(item => {
    item.amount = format(item.amount, 'Currency')
  })
  data.items = arr
  return data
}

const [printRefund] = useLoading(async (cashId: number, cashType: CashType, options?: LodopOptions) => {
  const msg = Message.loading({ content: '正在打印……', duration: 0 })
  try {
    const data = await getRefundData(cashId)
    await lodapPrint(`outpatient:refund`, data, options)
  } catch {
  } finally {
    msg()
  }
})

export { printRefund }
