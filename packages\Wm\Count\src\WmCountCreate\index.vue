<script setup lang="ts">
import { ref, reactive, watch, onMounted, h, nextTick, computed } from 'vue'
import { Button, Modal, Input, Tabs, message, Checkbox, Space, Spin, Row, Col } from 'ant-design-vue'
import { STable } from '@surely-vue/table'
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue'
import { createCountApi, deptArtLsApi } from '@mh-wm/util'
import { PharmacySelect } from '@mh-wm/pharmacy'
import { Currents } from '@mh-base/core'
import { StockReqCat } from '@mh-hip/art-cat'
import { ArtSubTypeDict } from '@mh-hip/art-sub-type'
import { WmDeptArtSelect } from '@mh-inpatient-hsd/selector'
import type { PropType } from 'vue'

// 定义组件属性
const props = defineProps({
  // 按钮文本
  buttonText: {
    type: String,
    default: '创建盘点',
  },
  // 按钮类型
  buttonType: {
    type: String as PropType<'primary' | 'ghost' | 'dashed' | 'link' | 'text' | 'default'>,
    default: 'default',
  },
  // 按钮大小
  buttonSize: {
    type: String as PropType<'large' | 'middle' | 'small'>,
    default: 'middle',
  },
  // 默认选中的仓库编码
  defaultDeptCode: {
    type: String,
    default: '',
  },
  // 是否只显示有库存的品种
  onlyShowWithStock: {
    type: Boolean,
    default: false,
  },
  // 对话框宽度
  modalWidth: {
    type: [Number, String],
    default: 1200,
  },
})

// 定义事件
const emit = defineEmits(['success', 'cancel'])

// 对话框可见性
const visible = ref(false)
// 加载状态
const loading = ref(false)
// 提交按钮加载状态
const submitLoading = ref(false)
// 确认对话框可见性
const confirmVisible = ref(false)
// 临时备注
const tempNotes = ref('')
// 确认对话框加载状态
const confirmLoading = ref(false)
// 品种选择组件引用
const artSelectRef = ref()
// 表格容器引用
const tableContainerRef = ref()
// 表单数据
const formState = reactive({
  countType: 2, // 固定为品种盘点
  deptCode: props.defaultDeptCode,
  counterName: '', // 将在初始化时设置为当前用户
  counterId: '', // 将在初始化时设置为当前用户ID
  notes: '',
  hasStock: props.onlyShowWithStock,
  artIds: [] as number[],
  artCatId: undefined, // 条目类型
  artSubTypeId: undefined, // 条目亚类
})

// 验证表单数据
const validateForm = () => {
  if (!formState.deptCode) {
    message.error('请选择盘点仓库')
    return false
  }

  if (formState.artIds.length === 0) {
    message.error('请添加盘点品种')
    return false
  }

  return true
}
// 不再需要用户列表
// 当前激活的页签（固定为1）
const activeTabKey = ref('1')
// 页码列表（固定只有一个页码）
const pageList = ref<number[]>([1])
// 品种列表（按页码分组）
const artListByPage = reactive(new Map<number, any[]>())
// 过滤关键字
const filterKeyword = ref('')
// 过滤后的品种列表（用于显示）
const filteredArtListByPage = computed(() => {
  const result = new Map<number, any[]>()

  // 如果没有过滤关键字，直接返回原始数据
  if (!filterKeyword.value.trim()) {
    return artListByPage
  }

  // 对每个页码的数据进行过滤
  for (const [page, arts] of artListByPage.entries()) {
    const keyword = filterKeyword.value.toUpperCase().trim()
    const filtered = arts.filter((art: any) => {
      // 检查条目ID（转为字符串进行比较）
      const artIdMatch = art.artId && art.artId.toString().toUpperCase().includes(keyword)

      // 检查品名
      const artNameMatch = art.artName && art.artName.toUpperCase().includes(keyword)

      // 检查规格
      const artSpecMatch = art.artSpec && art.artSpec.toUpperCase().includes(keyword)

      // 检查厂家
      const producerMatch = art.producer && art.producer.toUpperCase().includes(keyword)

      // 检查qsCode1
      const qsCode1Match = art.qsCode1 && art.qsCode1.toUpperCase().includes(keyword)

      // 检查qsCode2
      const qsCode2Match = art.qsCode2 && art.qsCode2.toUpperCase().includes(keyword)

      // 任一条件匹配即可
      return artIdMatch || artNameMatch || artSpecMatch || producerMatch || qsCode1Match || qsCode2Match
    })
    result.set(page, filtered)
  }

  return result
})

// 品种选择相关变量已移除，使用WmDeptArtSelect组件

// 品种表格列定义
const columns: any[] = [
  {
    title: '行号',
    dataIndex: 'lineNo',
    width: 60,
    align: 'right',
    customRender: ({ index }: any) => {
      return index + 1
    },
  },
  {
    title: '条目ID',
    dataIndex: 'artId',
    width: 80,
    resizable: true,
  },
  {
    title: '品名|规格|厂家',
    dataIndex: 'artName',
    resizable: true,
    ellipsis: true,
    sorter: {
      compare: (a: any, b: any) => {
        // 先按品名排序
        const nameCompare = a.artName.localeCompare(b.artName)
        if (nameCompare !== 0) return nameCompare

        // 如果品名相同，按规格排序
        if (a.artSpec && b.artSpec) {
          const specCompare = a.artSpec.localeCompare(b.artSpec)
          if (specCompare !== 0) return specCompare
        }

        // 如果规格也相同，按厂家排序
        if (a.producer && b.producer) {
          return a.producer.localeCompare(b.producer)
        }

        return 0
      },
      multiple: 3,
    },
    customRender: ({ record }: any) => {
      if (!record || !record.artName) return ''
      const artSpec = record.artSpec ? `|${record.artSpec}` : ''
      const producer = record.producer ? `|${record.producer}` : ''
      return h(
        'div',
        {
          class: 'art-info-cell',
          title: `${record.artName}${artSpec}${producer}`, // 鼠标悬停时显示完整内容
        },
        `${record.artName}${artSpec}${producer}`
      )
    },
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    width: 100,
    resizable: true,
    align: 'right',
    sorter: {
      compare: (a: any, b: any) => (a.totalPacks || 0) - (b.totalPacks || 0),
      multiple: 2,
    },
    customRender: ({ record }: any) => {
      if (!record) return ''
      const totalPacks = record.totalPacks !== undefined && record.totalPacks !== null ? record.totalPacks : 0
      return totalPacks
    },
  },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    width: 100,
    resizable: true,
    align: 'right',
    sorter: {
      compare: (a: any, b: any) => (a.totalCells || 0) - (b.totalCells || 0),
      multiple: 1,
    },
    customRender: ({ record }: any) => {
      if (!record) return ''
      const totalCells = record.totalCells !== undefined && record.totalCells !== null ? record.totalCells : 0
      return totalCells
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 120,
    resizable: true,
    align: 'center',
    customRender: ({ record }: any) => {
      if (!record) return ''
      return h(
        'a',
        {
          class: 'remove-link',
          onClick: (e: Event) => {
            e.preventDefault()
            removeArt(record)
          },
          href: 'javascript:void(0)',
        },
        '移除'
      )
    },
  },
]

// 添加类型加载状态
const addTypeLoading = ref(false)
// 表格数据变化标志
const tableDataChanged = ref(false)

// 标准化品种数据的辅助函数
const normalizeArtData = (art: any) => {
  if (!art) return null

  return {
    ...art,
    // 确保必要的字段存在
    artId: art.artId,
    artName: art.artName || '未知品种',
    artSpec: art.artSpec || '',
    producer: art.producer || '',
    totalPacks: art.totalPacks !== undefined ? art.totalPacks : 0,
    packUnit: art.packUnit || '',
    totalCells: art.totalCells !== undefined ? art.totalCells : 0,
    cellUnit: art.cellUnit || '',
    // 添加一个唯一标识，用于表格渲染
    key: art.artId,
    // 操作字段，与新的列定义匹配
    action: 'remove',
  }
}

// 过滤搜索处理函数
const onFilterSearch = (value: string) => {
  filterKeyword.value = value
  console.log('过滤关键字:', value)
}

// 过滤变化处理函数
const onFilterChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  filterKeyword.value = target.value
}

// 滚动表格到底部
const scrollTableToBottom = () => {
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    // 延迟执行，确保表格数据已经渲染
    // 增加延迟时间，确保表格数据完全渲染
    setTimeout(() => {
      if (tableContainerRef.value) {
        // 尝试多种可能的选择器，按优先级排序
        const selectors = ['.surely-table-body', '.ant-table-body', '.surely-table-content', '.ant-table-content', '.surely-table-body-inner', '.ant-table-body-inner']

        let scrollElement: HTMLElement | null = null

        // 尝试所有可能的选择器
        for (const selector of selectors) {
          const element = tableContainerRef.value.querySelector(selector) as HTMLElement
          if (element) {
            scrollElement = element
            console.log('找到滚动元素:', selector)
            break
          }
        }

        if (scrollElement) {
          // 滚动到底部
          // 使用requestAnimationFrame确保在下一帧渲染前执行滚动
          requestAnimationFrame(() => {
            if (scrollElement) {
              scrollElement.scrollTop = scrollElement.scrollHeight
              console.log('滚动到底部, 高度:', scrollElement.scrollHeight)
            }
          })
        } else {
          console.warn('未找到表格滚动容器')
        }
      }
    }, 200) // 增加延迟时间到200ms，确保表格数据完全渲染
  })
}

// 初始化组件
onMounted(() => {
  // 设置当前用户为盘点人
  setCurrentUserAsCounter()
})

// 监听表格数据变化，自动滚动到底部
watch(
  () => artListByPage,
  () => {
    tableDataChanged.value = true
    // 当表格数据变化时，滚动到底部
    scrollTableToBottom()
  },
  { deep: true }
)

// 设置当前用户为盘点人
const setCurrentUserAsCounter = () => {
  if (Currents.id) {
    formState.counterId = Currents.id
    formState.counterName = `用户${Currents.id}`
    console.log('设置当前用户为盘点人:', formState.counterName, formState.counterId)
  } else {
    console.warn('未能获取当前用户信息')
  }
}

// 盘点人相关代码已移除，使用当前用户

// 打开对话框
const open = (deptCode?: string) => {
  visible.value = true
  resetForm()

  if (deptCode) {
    formState.deptCode = deptCode
  }
}

// 关闭对话框
const close = () => {
  visible.value = false
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  formState.deptCode = props.defaultDeptCode
  // 重新设置当前用户为盘点人
  setCurrentUserAsCounter()
  formState.notes = ''
  formState.hasStock = props.onlyShowWithStock
  formState.artIds = []
  formState.artCatId = undefined
  formState.artSubTypeId = undefined

  // 重置页码和品种列表
  pageList.value = [1]
  activeTabKey.value = '1'
  artListByPage.clear()
  artListByPage.set(1, [])

  // 重置搜索已移除
}

// 提交表单
const handleSubmit = () => {
  // 验证表单
  if (!validateForm()) {
    return
  }

  // 打开确认对话框
  confirmVisible.value = true
  // 清空临时备注
  tempNotes.value = ''
}

// 确认创建盘点单
const confirmSubmit = async () => {
  confirmLoading.value = true

  try {
    // API返回的直接是data，框架已经处理了code不为0的情况
    const response = await createCountApi({
      entity: {
        countType: formState.countType,
        deptCode: formState.deptCode,
        counterName: formState.counterName,
        counterId: formState.counterId,
        notes: tempNotes.value, // 使用确认对话框中输入的备注
      },
      artIds: formState.artIds,
    })

    message.success('创建盘点单成功')
    confirmVisible.value = false
    visible.value = false
    emit('success', response.countId)
  } catch (error) {
    console.error('创建盘点单失败:', error)
    message.error('创建盘点单失败')
  } finally {
    confirmLoading.value = false
  }
}

// 取消确认
const cancelConfirm = () => {
  confirmVisible.value = false
}

// 处理品种选择
const handleArtSelect = (selectedArt: any) => {
  if (!selectedArt) return

  // 打印选中的品种数据，用于调试
  console.log('选中的品种数据:', selectedArt)

  // 检查是否已经添加过
  if (formState.artIds.includes(selectedArt.artId)) {
    message.warning('该品种已添加')
    return
  }

  // 获取当前激活的页码
  const currentPage = parseInt(activeTabKey.value)

  // 标准化品种数据，确保数据结构一致
  const artData = normalizeArtData(selectedArt)

  // 获取当前页的品种列表（创建一个新数组以确保响应式更新）
  const currentPageArts = [...(artListByPage.get(currentPage) || [])]

  // 添加品种到当前页
  currentPageArts.push(artData)

  // 更新Map（使用新数组以触发响应式更新）
  artListByPage.set(currentPage, [...currentPageArts])

  // 打印更新后的数据，用于调试
  console.log('更新后的品种列表:', artListByPage.get(currentPage))

  // 更新artIds
  formState.artIds.push(selectedArt.artId)

  // 直接调用滚动函数，确保滚动到底部
  scrollTableToBottom()

  // 提示添加成功
  message.success(`成功添加品种: ${artData.artName}`)
}

// 添加品种到盘点列表的功能已由handleArtSelect函数实现

// 从盘点列表中移除品种
const removeArt = (art: any) => {
  if (!art || !art.artId) {
    console.error('移除品种失败: 无效的品种数据', art)
    return
  }

  // 获取当前激活的页码
  const currentPage = parseInt(activeTabKey.value)

  // 获取当前页的品种列表
  const currentPageArts = artListByPage.get(currentPage) || []

  // 打印移除前的数据，用于调试
  console.log('移除前的品种列表:', [...currentPageArts])
  console.log('要移除的品种:', art)

  // 从当前页移除品种（创建一个新数组以确保响应式更新）
  const updatedArts = currentPageArts.filter((item: any) => item.artId !== art.artId)

  // 更新Map（使用新数组以触发响应式更新）
  artListByPage.set(currentPage, [...updatedArts])

  // 打印移除后的数据，用于调试
  console.log('移除后的品种列表:', artListByPage.get(currentPage))

  // 更新artIds
  const index = formState.artIds.indexOf(art.artId)
  if (index !== -1) {
    formState.artIds.splice(index, 1)
  }

  // 提示移除成功
  message.success(`成功移除品种: ${art.artName || '未知品种'}`)
}

// 添加新页
const addNewPage = () => {
  // 显示"正在开发中"的提示，不添加新页码
  message.info('多页码功能正在开发中')
}

// 清空所有品种
const clearAllArts = () => {
  // 检查是否有品种可以清空
  const currentPage = parseInt(activeTabKey.value)
  const currentPageArts = artListByPage.get(currentPage) || []

  if (currentPageArts.length === 0) {
    message.info('当前没有已选品种')
    return
  }

  Modal.confirm({
    title: '确认清空',
    content: '确定要清空所有已选品种吗？',
    onOk: () => {
      // 打印清空前的数据，用于调试
      console.log('清空前的品种列表:', artListByPage)

      // 清空所有品种
      artListByPage.clear()

      // 只保留第一页（固定只有一个页码）
      pageList.value = [1]

      // 使用空数组初始化第一页（确保响应式更新）
      artListByPage.set(1, [])

      // 设置当前激活的页码为第一页
      activeTabKey.value = '1'

      // 清空artIds
      formState.artIds = []

      // 打印清空后的数据，用于调试
      console.log('清空后的品种列表:', artListByPage)

      // 提示清空成功
      message.success('已清空所有品种')
    },
  })
}

// 按类型添加品种
const addArtByType = async () => {
  if (!formState.deptCode) {
    message.warning('请先选择盘点仓库')
    return
  }

  if (!formState.artCatId && !formState.artSubTypeId) {
    message.warning('请选择至少一种分类进行筛选')
    return
  }

  addTypeLoading.value = true

  try {
    const params: any = {
      deptCode: formState.deptCode,
    }

    // 添加条目类型和条目亚类
    if (formState.artCatId) {
      params.artCatId = formState.artCatId
    }

    if (formState.artSubTypeId) {
      params.artSubTypeId = formState.artSubTypeId
    }

    // 是否只显示有库存的品种
    if (formState.hasStock) {
      params.hasStock = true
    }

    // API返回的直接是data，框架已经处理了code不为0的情况
    const artList = (await deptArtLsApi(params)) || []

    if (artList.length === 0) {
      message.info('未找到符合条件的品种')
      addTypeLoading.value = false
      return
    }

    // 获取当前激活的页码
    const currentPage = parseInt(activeTabKey.value)

    // 过滤掉已经添加的品种
    const newArts = artList.filter((art: any) => !formState.artIds.includes(art.artId))

    if (newArts.length === 0) {
      message.info('所有品种已添加')
      addTypeLoading.value = false
      return
    }

    // 获取当前页的品种列表
    let currentPageArts = artListByPage.get(currentPage) || []

    // 标准化品种数据，确保数据结构一致
    const processedArts = newArts.map((art: any) => normalizeArtData(art))

    // 创建一个新数组以确保响应式更新
    const updatedArts = [...currentPageArts, ...processedArts]

    // 更新Map（使用新数组以触发响应式更新）
    artListByPage.set(currentPage, [...updatedArts])

    // 打印更新后的数据，用于调试
    console.log('按类型添加后的品种列表:', artListByPage.get(currentPage))

    // 更新artIds
    newArts.forEach((art: any) => {
      formState.artIds.push(art.artId)
    })

    // 直接调用滚动函数，确保滚动到底部
    scrollTableToBottom()

    message.success(`成功添加${newArts.length}个品种`)
  } catch (error) {
    console.error('按类型添加品种失败:', error)
    message.error('按类型添加品种失败')
  } finally {
    addTypeLoading.value = false
  }
}

// 仓库变更处理
const handleDeptChange = (value: string) => {
  // 确保formState.deptCode被更新
  formState.deptCode = value

  // 清空已选品种
  artListByPage.clear()
  pageList.value = [1]
  artListByPage.set(1, [])
  activeTabKey.value = '1'
  formState.artIds = []

  // 重置条目类型和亚类
  formState.artCatId = undefined
  formState.artSubTypeId = undefined
}

// 处理表格变化事件
const handleTableChange = (pagination: any, filters: any, sorter: any, extra: any) => {
  console.log('表格变化:', pagination, filters, sorter, extra)
  // 这里可以根据需要处理排序逻辑
}

// 获取总条数
const getTotalCount = () => {
  const currentPage = parseInt(activeTabKey.value)
  // filteredArtListByPage是一个计算属性，返回的是一个Map
  const filteredMap = filteredArtListByPage.value
  const currentPageArts = filteredMap.get(currentPage) || []
  return currentPageArts.length
}

// 暴露方法
defineExpose({
  open,
  close,
})
</script>

<template>
  <div class="wm-count-create">
    <!-- 触发按钮 -->
    <Button :type="buttonType" :size="buttonSize" @click="() => open()">
      <template #icon><PlusOutlined /></template>
      {{ buttonText }}
    </Button>

    <!-- 创建盘点单对话框 -->
    <Modal v-model:open="visible" :title="'创建盘点单'" :width="modalWidth" :maskClosable="false" :destroyOnClose="true" @cancel="close" class="count-modal">
      <Spin :spinning="loading">
        <div class="count-form">
          <!-- 盘点仓库、条目类型和条目亚类 -->
          <Row class="form-row" :gutter="8" type="flex" align="middle">
            <Col :span="2">
              <div class="item-label">盘点仓库:</div>
            </Col>
            <Col :span="6">
              <PharmacySelect v-model="formState.deptCode" placeholder="请选择盘点仓库" style="width: 100%" mode="basic" @change="handleDeptChange" />
            </Col>
            <Col :span="2">
              <div class="item-label">条目类型:</div>
            </Col>
            <Col :span="3">
              <StockReqCat v-model="formState.artCatId" type="Select" style="width: 100%" />
            </Col>
            <Col :span="2">
              <div class="item-label">条目亚类:</div>
            </Col>
            <Col :span="3">
              <ArtSubTypeDict v-model="formState.artSubTypeId" type="Select" style="width: 100%" />
            </Col>
            <Col :span="3">
              <Checkbox v-model:checked="formState.hasStock">只盘有库存</Checkbox>
            </Col>
            <Col :span="3" style="text-align: right">
              <Button :loading="addTypeLoading" @click="addArtByType" type="primary"> 按类型添加 </Button>
            </Col>
          </Row>

          <!-- 说明备注已移至确认窗口 -->

          <!-- 盘点品种 -->
          <Row class="form-row" :gutter="8">
            <Col :span="2">
              <div class="item-label">盘点品种:</div>
            </Col>
            <Col :span="19">
              <!-- 品种选择组件 -->
              <div class="art-select-box">
                <WmDeptArtSelect :deptCode="formState.deptCode" ref="artSelectRef" @selected="handleArtSelect" :hasStock="formState.hasStock" />
              </div>
            </Col>
            <Col :span="3" style="text-align: right">
              <div class="art-select-box">
                <Button danger @click="clearAllArts">
                  <template #icon><DeleteOutlined /></template>
                  清空所有
                </Button>
              </div>
            </Col>
            <Col :span="24">
              <!-- 已选品种列表（分页） -->
              <div class="selected-arts" ref="tableContainerRef">
                <div class="tabs-header">
                  <Tabs
                    v-model:activeKey="activeTabKey"
                    type="editable-card"
                    @edit="
                      (_: string, action: string) => {
                        if (action === 'add') addNewPage()
                      }
                    "
                  >
                    <template #rightExtra>
                      <div class="filter-container">
                        <span class="total-count">共 {{ getTotalCount() }} 条</span>
                        <Input.Search v-model:value="filterKeyword" placeholder="搜索条目ID/品名/规格/厂家/助记码" style="width: 280px" allowClear @search="onFilterSearch" @change="onFilterChange" />
                      </div>
                    </template>
                    <Tabs.TabPane v-for="page in pageList" :key="page.toString()" :tab="`页码 ${page}`" :closable="false">
                      <div class="table-container">
                        <STable
                          :columns="columns"
                          :dataSource="filteredArtListByPage.get(page) || []"
                          :pagination="false"
                          :rowKey="(record: any) => record.artId || record.key"
                          size="small"
                          :scroll="{ y: 300 }"
                          :loading="false"
                          :locale="{ emptyText: '暂无数据，请添加盘点品种' }"
                          @change="handleTableChange"
                        />
                      </div>
                    </Tabs.TabPane>
                  </Tabs>
                </div>
              </div>
            </Col>
          </Row>
        </div>
      </Spin>

      <template #footer>
        <Space>
          <Button @click="close">取消</Button>
          <Button type="primary" :loading="submitLoading" @click="handleSubmit">创建盘点单</Button>
        </Space>
      </template>
    </Modal>

    <!-- 确认对话框 -->
    <Modal v-model:open="confirmVisible" title="请输入盘点说明" :maskClosable="false" :destroyOnClose="true" @cancel="cancelConfirm">
      <div class="confirm-form">
        <Input.TextArea v-model:value="tempNotes" placeholder="请输入盘点说明备注" :rows="4" :autoSize="{ minRows: 3, maxRows: 6 }" style="width: 100%" />
      </div>

      <template #footer>
        <Space>
          <Button @click="cancelConfirm">取消</Button>
          <Button type="primary" :loading="confirmLoading" @click="confirmSubmit">创建盘点单</Button>
        </Space>
      </template>
    </Modal>
  </div>
</template>

<style>
/* 组件样式已移至全局样式文件 */
/* 以下样式仅用于开发时参考，不会影响最终构建 */
:deep(.count-modal .ant-modal-body) {
  padding: 16px 12px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 确保Modal底部按钮与内容有足够间距 */
:deep(.count-modal .ant-modal-footer) {
  margin-top: 16px;
  border-top: 1px solid #f0f0f0;
  padding-top: 10px;
}

/* 表单样式 */
.count-form {
  padding: 0;
  max-width: 100%;
  margin: 0 auto;
}

.form-row {
  margin-bottom: 16px;
}

.form-item {
  display: flex;
  align-items: center;
}

.item-label {
  margin-right: 8px;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  min-width: 70px; /* 设置最小宽度，确保所有标签对齐 */
}

.art-select-box {
  margin-top: -5px;
}

.clear-button {
  margin-left: auto;
}

/* 品种名称样式 */
.art-name {
  font-weight: bold;
}

.art-spec,
.art-producer {
  font-size: 12px;
  color: #666;
}

.selected-arts {
  margin-top: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  max-height: calc(100vh - 350px); /* 限制最大高度，避免内容溢出 */
  overflow: hidden; /* 确保内容不会溢出容器 */
}

.tabs-header {
  padding: 8px;
}

.filter-container {
  margin-right: 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.total-count {
  margin-right: 12px;
  color: #666;
  font-size: 14px;
}

/* 确保过滤框在标签栏一行 */
:deep(.ant-tabs-nav-wrap) {
  flex: 1;
}

:deep(.ant-tabs-extra-content) {
  display: flex;
  align-items: center;
}

/* 确认对话框样式 */
.confirm-form {
  padding: 8px 0;
}

/* 移除链接样式 */
:deep(.remove-link) {
  color: #ff4d4f;
  text-decoration: none;
}

:deep(.remove-link:hover) {
  color: #ff7875;
  text-decoration: underline;
}

/* 品种信息单元格样式 */
:deep(.art-info-cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 确保表格单元格内容溢出时显示省略号 */
:deep(.surely-table-cell-content),
:deep(.ant-table-cell-content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 表格容器高度 */
.table-container {
  height: 330px; /* 减小高度，避免内容溢出 */
  position: relative;
}

/* 确保表格始终占满容器高度 */
:deep(.table-container .ant-table-wrapper),
:deep(.table-container .surely-table-wrapper) {
  height: 100%;
}

:deep(.table-container .ant-table-wrapper .ant-table),
:deep(.table-container .surely-table-wrapper .surely-table) {
  height: 100%;
}

/* 空数据状态下保持高度 */
:deep(.table-container .ant-table-placeholder),
:deep(.table-container .surely-table-placeholder) {
  height: 300px; /* 减小高度，与表格滚动区域一致 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 确保表格体占满剩余空间 */
:deep(.table-container .ant-table-body),
:deep(.table-container .surely-table-body) {
  height: calc(100% - 50px) !important; /* 减去表头高度 */
}
</style>
