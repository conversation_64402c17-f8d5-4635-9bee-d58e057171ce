import { DefaultLayout } from '@mh-base/core'

export default {
  path: '/Wm',
  meta: {
    title: '仓库组件',
  },
  component: DefaultLayout,
  children: [
    {
      path: '/Wm/All',
      component: () => import('@v/Wm/All.vue'),
      meta: {
        title: '仓库组件集合',
      },
    },
    {
      path: '/Wm/BatchAdjust',
      component: () => import('@v/Wm/BatchAdjust.vue'),
      meta: {
        title: '批号调整',
      },
    },
    {
      path: '/Wm/ScmCustSelect',
      component: () => import('@v/Wm/ScmCustSelect.vue'),
      meta: {
        title: '供应商选择',
      },
    },
    {
      path: '/Wm/Count',
      component: () => import('@v/Wm/examples/CountExample.vue'),
      meta: {
        title: '盘点组件',
      },
    },
    {
      path: '/Wm/PharmacySelect',
      component: () => import('@v/Wm/examples/PharmacySelectExample.vue'),
      meta: {
        title: '药房选择',
      },
    },
    {
      path: '/Wm/TrackCode',
      component: () => import('@v/Wm/TrackCode.vue'),
      meta: {
        title: '溯源码录入',
      },
    },
    {
      path: '/Wm/RecipeTrackCode',
      component: () => import('@v/Wm/examples/RecipeTrackCodeExample.vue'),
      meta: {
        title: '处方扫码组件',
      },
    },
    {
      path: '/Wm/BillsTrackCode',
      component: () => import('@v/Wm/examples/BillsTrackCodeExample.vue'),
      meta: {
        title: '多单据追溯码扫描组件',
      },
    },
    {
      path: '/Wm/Maker',
      component: () => import('@v/Wm/Maker.vue'),
      meta: {
        title: '采购制单组件',
      },
    },
    {
      path: '/Wm/MakerDeptArt',
      component: () => import('@v/Wm/examples/Maker/MakerDeptArt.vue'),
      meta: {
        title: '品种选择组件',
      },
    },

  ],
}
