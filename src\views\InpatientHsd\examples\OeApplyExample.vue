<script setup lang="ts">
import { QuestionForm } from '@mh-inpatient-hsd/oe-apply'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from './code/OeApplyCode'

const { Title, Paragraph } = Typography

// 引用
const questionFormRef = ref()

// 模拟数据
const visitId = 84503
const questionTypeIds = [79]

// 打开质疑弹窗
const handleVisibleQuestionForm = () => {
  questionFormRef.value.open(visitId, questionTypeIds)
}

// 质疑提交回调
const questioned = (data) => {
  message.success('质疑提交成功')
  console.log('质疑数据:', data)
}
</script>

<template>
  <Card title="医嘱核对 - 质疑弹窗" class="mb-16px">
    <div mb-16px>
      <Title :level="4">基础用法</Title>
      <Button type="primary" @click="handleVisibleQuestionForm">打开质疑弹窗</Button>
      <question-form ref="questionFormRef" @questioned="questioned"/>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

