<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './WholeBox/Basic.vue'
import ScenariosExample from './WholeBox/Scenarios.vue'
import TableExample from './WholeBox/Table.vue'
import ApiExample from './WholeBox/Api.vue'
import MethodsExample from './WholeBox/Methods.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>
      拆零盒整组件，用于药品库存管理系统中的整包数与拆零数转换操作。该组件提供了完整的拆零盒整界面，支持仓库总库存和批次库存的双向联动计算、实时数据校验、API集成等功能。支持药品、医疗器械等不同类型商品的拆零盒整操作，确保库存数据的准确性和一致性。
    </Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="scenarios" tab="多场景应用">
        <ScenariosExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="table" tab="表格集成">
        <TableExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="methods" tab="组件方法">
        <MethodsExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="api" tab="API集成">
        <ApiExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
