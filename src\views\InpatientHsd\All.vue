<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个组件示例
import BillingEntryExample from './examples/BillingEntryExample.vue'
import OeApplyExample from './examples/OeApplyExample.vue'
import OeExecExample from './examples/OeExecExample.vue'
import OeFeeChangeExample from './examples/OeFeeChangeExample.vue'
import OrderFeeChangeExample from './examples/OrderFeeChangeExample.vue'
import SectionRouteConsumableExample from './examples/SectionRouteConsumableExample.vue'
import SelectorExample from './examples/SelectorExample.vue'
import VisitInfoExample from './examples/VisitInfoExample.vue'
import VisitFormExample from './examples/VisitFormExample.vue'
import WmBillDetailExample from './examples/WmBillDetailExample.vue'
import ComponentPublishGuide from './examples/ComponentPublishGuide.vue'
import CodeHighlight from './examples/CodeHighlight.vue'

const { Title, Paragraph } = Typography

// 代码高亮组件引用
const codeHighlight = ref()

// 当前激活的tab
const activeKey = ref('billingentry')

// 标签页切换时更新高亮
const onTabChange = () => {
  codeHighlight.value?.updateHighlight()
}
</script>

<template>
  <Card title="住院相关组件" class="mb-16px">
    <Paragraph>住院相关组件，包括BillingEntry（计费医嘱）、OeApply（医嘱核对）、OeExec（医嘱执行）等组件。</Paragraph>

    <!-- 代码高亮组件 -->
    <CodeHighlight ref="codeHighlight" />

    <Tabs v-model:activeKey="activeKey" tabPosition="left" @change="onTabChange">
      <Tabs.TabPane key="billingentry" tab="计费医嘱">
        <BillingEntryExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="oeapply" tab="医嘱核对">
        <OeApplyExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="oeexec" tab="医嘱执行">
        <OeExecExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="oefeechange" tab="医嘱费用变更">
        <OeFeeChangeExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="orderfeechange" tab="医嘱执行费用变更">
        <OrderFeeChangeExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="sectionrouteconsumable" tab="病区给药途径绑定">
        <SectionRouteConsumableExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="selector" tab="选择器">
        <SelectorExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="visitinfo" tab="患者信息">
        <VisitInfoExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="visitform" tab="患者信息表单">
        <VisitFormExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="wmbilldetail" tab="病区库存台账">
        <WmBillDetailExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="componentpublish" tab="组件打包指令">
        <ComponentPublishGuide />
      </Tabs.TabPane>
    </Tabs>
  </Card>
</template>


