<script setup lang="ts">
import { Dict } from '@idmy/antd'
import { addDict, DictProps } from '@idmy/core'
import { allRoutetype } from '@mh-hip/util'

// 使用allRoutetype函数加载给药途径数据
addDict('Route', async (): Promise<DictProps[]> => {
  const data = await allRoutetype(true)
  return data.map(
    (row: any): DictProps => ({
      data: row,
      formatName: row.label,
      id: row.id,
      name: row.label,
      value: row.value,
    })
  )
})

const modelValue = defineModel({ type: Number })
</script>

<template>
  <Dict v-model="modelValue" clazz="Route" />
</template>
