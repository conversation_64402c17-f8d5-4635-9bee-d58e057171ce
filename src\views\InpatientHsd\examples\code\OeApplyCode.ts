import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { QuestionForm } from '@mh-inpatient-hsd/oe-apply'
import '@mh-inpatient-hsd/oe-apply/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/oe-apply": "^1.0.2",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 基础用法 - 质疑弹窗
export const basicUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleQuestionForm">打开质疑弹窗</Button>
  <question-form ref="questionFormRef" @questioned="questioned"/>
</template>

<script setup>
import { QuestionForm } from '@mh-inpatient-hsd/oe-apply'
import '@mh-inpatient-hsd/oe-apply/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const questionFormRef = ref()

// 模拟数据
const visitId = 84503
const questionTypeIds = [79]

// 打开质疑弹窗
const handleVisibleQuestionForm = () => {
  questionFormRef.value.open(visitId, questionTypeIds)
}

// 质疑提交回调
const questioned = (data) => {
  message.success('质疑提交成功')
  console.log('质疑数据:', data)
}
</script>`)

