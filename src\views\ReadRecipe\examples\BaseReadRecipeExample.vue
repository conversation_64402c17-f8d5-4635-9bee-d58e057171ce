<script setup lang="ts">
import { ref } from 'vue'
import { Card, Typography, Divider, Space, Tabs, Button } from 'ant-design-vue'
import { BaseReadRecipe } from '@mh-hsd/base'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'

// 导入代码示例
import {
  basicUsage,
  modalUsage,
  importCode,
  packageJsonCode
} from './code/BaseReadRecipeCode'

const { Paragraph } = Typography
const { TabPane } = Tabs

// 当前激活的tab
const activeKey = ref('basic')

// 组件引用
const modalRecipeRef = ref()

// 基础处方数据
const basicRecipeData = ref({
  recipeId: 1,
  rxNo: 'RX202412190001',
  patientName: '张三',
  patientGenderName: '男',
  ageOfYears: 35,
  ageOfDays: null,
  applyDeptname: '内科',
  clinicianName: '李医生',
  diseaseDiagName: '感冒',
  recipeTypeId: 1,
  timeCreated: new Date(),
  pastHistory: '无',
  allergicHistory: '无',
  catMark: '普通',
  catColorCode: '#FFFFFF',
  recipeGroupLs: [
    {
      groupNo: 1,
      routeName: '口服',
      freqCode: 'BID',
      notice: '饭后服用',
      recipeDetailLs: [
        {
          artId: 1,
          artName: '阿莫西林胶囊',
          artSpec: '0.25g',
          mealCells: 2,
          cellUnit: '粒',
          total: 24,
          unit: '粒',
          producer: '某某制药厂',
          amount: 15.60,
          groupMark: 1
        }
      ]
    }
  ],
  amount: 15.60,
  deliverType: 1,
  bseqid: 'B001'
})

// 处理按钮点击
const handleAction = () => {
  modalRecipeRef.value?.openModal()
}
</script>

<template>
  <Card :bodyStyle="{ padding: '0 20px' }">
    <Tabs v-model:activeKey="activeKey">
      <!-- 基础用法 -->
      <TabPane key="basic" tab="基础用法">
        <Paragraph>
          内联模式直接在页面中展示处方内容，适合处方详情页面或打印预览页面。
          组件会自动适应容器宽度。
        </Paragraph>

        <div style="border: 1px solid #f0f0f0; padding: 16px; border-radius: 6px;">
          <div style="margin-bottom: 8px; font-weight: bold;">内联模式展示：</div>
          <BaseReadRecipe
            type="inline"
            :recipe="basicRecipeData"
          />
        </div>

        <Divider />
        <CodeDemoVue
          :usage="basicUsage"
          :importCode="importCode"
          :packageJson="packageJsonCode"
        />
      </TabPane>

      <!-- 弹窗模式 -->
      <TabPane key="modal" tab="弹窗模式">
        <Paragraph>
          弹窗模式适合在列表页面或其他页面中快速查看处方详情，不影响当前页面的布局。
          支持自定义弹窗宽度，默认760px。
        </Paragraph>

        <Space style="margin-bottom: 16px">
          <Button type="primary" @click="() => handleAction()">
            打开处方弹窗（800px宽）
          </Button>
          <span style="color: #666; font-size: 13px">
            通过ref调用openModal方法打开弹窗
          </span>
        </Space>

        <BaseReadRecipe
          ref="modalRecipeRef"
          type="modal"
          :recipe="basicRecipeData"
          :modalWidth="800"
        />

        <Divider />
        <CodeDemoVue
          :usage="modalUsage"
          :importCode="importCode"
          :packageJson="packageJsonCode"
        />
      </TabPane>
    </Tabs>
  </Card>
</template>

<style scoped>
.base-read-recipe-example {
  padding: 1px;
}

.mb-16px {
  margin-bottom: 16px;
}

.mt-16px {
  margin-top: 16px;
}

/* API表格样式 */
table {
  font-size: 14px;
}

table code {
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}
</style>
