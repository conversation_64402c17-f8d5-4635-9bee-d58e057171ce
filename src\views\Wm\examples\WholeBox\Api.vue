<script setup lang="ts">
import { WholeBox } from '@mh-wm/whole-box'
import { Card, Typography, Divider, Button, message, Tag, Switch, Progress, Statistic, Row, Col } from 'ant-design-vue'
import { ApiOutlined, CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined } from '@ant-design/icons-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, reactive, computed, nextTick } from 'vue'
import { apiIntegration, advancedConfig } from '@/views/Wm/examples/code/WholeBoxCode'

const { Title, Paragraph, Text } = Typography

// WholeBox组件引用
const wholeBoxRef = ref()
const logContainer = ref()

// API状态监控
const apiStatus = reactive({
  connected: true,
  lastCall: null,
  successCount: 0,
  errorCount: 0,
  totalCalls: 0,
  avgResponseTime: 0,
  responseTimes: [] as number[]
})

// 系统状态
const systemStatus = reactive({
  cpuUsage: 45,
  memoryUsage: 62,
  networkLatency: 28,
  activeConnections: 156
})

// API调用日志
const apiLogs = ref<Array<{
  id: number
  timestamp: string
  type: 'info' | 'success' | 'error' | 'warning'
  message: string
  data?: any
  responseTime?: number
}>>([])

// 自动滚动开关
const autoScroll = ref(true)

// 实时监控开关
const realTimeMonitoring = ref(true)

// 计算性能指标
const performanceMetrics = computed(() => {
  const total = apiStatus.successCount + apiStatus.errorCount
  const successRate = total > 0 ? Math.round((apiStatus.successCount / total) * 100) : 100
  
  return {
    successRate,
    totalCalls: total,
    avgResponseTime: apiStatus.avgResponseTime,
    minResponseTime: apiStatus.responseTimes.length > 0 ? Math.min(...apiStatus.responseTimes) : 0,
    maxResponseTime: apiStatus.responseTimes.length > 0 ? Math.max(...apiStatus.responseTimes) : 0
  }
})

// 添加日志
const addLog = (type: 'info' | 'success' | 'error' | 'warning', message: string, data?: any, responseTime?: number) => {
  const log = {
    id: Date.now(),
    timestamp: new Date().toLocaleTimeString(),
    type,
    message,
    data,
    responseTime
  }
  
  apiLogs.value.unshift(log)
  
  // 只保留最近100条日志
  if (apiLogs.value.length > 100) {
    apiLogs.value = apiLogs.value.slice(0, 100)
  }
  
  // 自动滚动到顶部
  if (autoScroll.value) {
    nextTick(() => {
      if (logContainer.value) {
        logContainer.value.scrollTop = 0
      }
    })
  }
}

// 模拟API调用
const mockApiCall = async (shouldFail = false, delay = 500) => {
  const startTime = Date.now()
  
  addLog('info', '开始API调用...', { endpoint: '/clinics_wm/wmbill/performSplitPack' })
  
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const responseTime = Date.now() - startTime
      
      // 更新API状态
      apiStatus.lastCall = new Date().toLocaleString()
      apiStatus.totalCalls++
      apiStatus.responseTimes.push(responseTime)
      
      // 计算平均响应时间
      if (apiStatus.responseTimes.length > 10) {
        apiStatus.responseTimes = apiStatus.responseTimes.slice(-10)
      }
      apiStatus.avgResponseTime = Math.round(
        apiStatus.responseTimes.reduce((a, b) => a + b, 0) / apiStatus.responseTimes.length
      )
      
      if (shouldFail) {
        apiStatus.errorCount++
        const error = { 
          message: '模拟网络错误', 
          code: 500,
          details: '连接超时或服务器内部错误'
        }
        addLog('error', `API调用失败 (${responseTime}ms)`, error, responseTime)
        reject(error)
      } else {
        apiStatus.successCount++
        const result = { 
          success: true, 
          message: '拆零盒整操作成功',
          data: {
            deptTotalPacks: 5,
            deptTotalCells: 3,
            totalPacks: 2,
            totalCells: 8
          }
        }
        addLog('success', `API调用成功 (${responseTime}ms)`, result, responseTime)
        resolve(result)
      }
    }, delay)
  })
}

// 测试正常API调用
const testNormalApi = async () => {
  try {
    await mockApiCall(false, 800)
    message.success('正常API调用测试通过')
  } catch (error) {
    message.error('正常API调用测试失败')
  }
}

// 测试错误处理
const testErrorHandling = async () => {
  try {
    await mockApiCall(true, 1200)
  } catch (error) {
    addLog('success', '错误处理测试通过 - 正确捕获了错误')
    message.success('错误处理测试通过')
  }
}

// 测试并发调用
const testConcurrentCalls = async () => {
  addLog('info', '开始并发调用测试')
  
  try {
    const promises = [
      mockApiCall(false, 500),
      mockApiCall(false, 600),
      mockApiCall(false, 700),
      mockApiCall(true, 800),  // 一个失败的调用
      mockApiCall(false, 900)
    ]
    
    const results = await Promise.allSettled(promises)
    const successCount = results.filter(r => r.status === 'fulfilled').length
    const failureCount = results.filter(r => r.status === 'rejected').length
    
    addLog('success', `并发调用测试完成: ${successCount}成功, ${failureCount}失败`)
    message.success('并发调用测试完成')
  } catch (error) {
    addLog('error', '并发调用测试失败', error)
    message.error('并发调用测试失败')
  }
}

// 测试性能压力
const testPerformanceStress = async () => {
  addLog('info', '开始性能压力测试')
  message.info('开始性能压力测试，将进行20次快速调用...')
  
  const promises = []
  for (let i = 0; i < 20; i++) {
    promises.push(mockApiCall(Math.random() > 0.8, Math.random() * 300 + 100))
  }
  
  try {
    await Promise.allSettled(promises)
    addLog('success', '性能压力测试完成')
    message.success('性能压力测试完成')
  } catch (error) {
    addLog('error', '性能压力测试失败', error)
    message.error('性能压力测试失败')
  }
}

// 演示拆零盒整操作
const demoSplitPack = () => {
  const mockRecord = {
    artName: 'API演示药品',
    artSpec: '100mg',
    producer: 'API演示制药公司',
    packCells: 10,
    cellUnit: '片',
    packUnit: '盒',
    deptTotalPacks: 5,
    deptTotalCells: 3,
    totalPacks: 2,
    totalCells: 8,
    artId: 9999,
    batchNo: 'API001',
    expDate: '20251201'
  }
  
  addLog('info', '演示拆零盒整操作', mockRecord)
  
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(mockRecord)
  }
}

// 清空日志
const clearLogs = () => {
  apiLogs.value = []
  addLog('info', '日志已清空')
}

// 导出日志
const exportLogs = () => {
  if (apiLogs.value.length === 0) {
    message.warning('暂无日志可导出')
    return
  }
  
  const logData = apiLogs.value.map(log => 
    `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}${log.responseTime ? ` (${log.responseTime}ms)` : ''}`
  ).join('\n')
  
  const blob = new Blob([logData], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `wholebox-api-logs-${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
  
  message.success('日志导出成功')
}

// 重置统计数据
const resetStats = () => {
  Object.assign(apiStatus, {
    connected: true,
    lastCall: null,
    successCount: 0,
    errorCount: 0,
    totalCalls: 0,
    avgResponseTime: 0,
    responseTimes: []
  })
  
  addLog('info', '统计数据已重置')
  message.success('统计数据已重置')
}

// 模拟系统状态更新
const updateSystemStatus = () => {
  systemStatus.cpuUsage = Math.floor(Math.random() * 30) + 30
  systemStatus.memoryUsage = Math.floor(Math.random() * 40) + 40
  systemStatus.networkLatency = Math.floor(Math.random() * 50) + 10
  systemStatus.activeConnections = Math.floor(Math.random() * 100) + 100
}

// 启动实时监控
if (realTimeMonitoring.value) {
  setInterval(updateSystemStatus, 3000)
}

// 初始化日志
addLog('info', 'WholeBox API集成示例已加载')
</script>

<template>
  <Card title="API集成 - 拆零盒整组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">API集成与监控</Title>
      <Paragraph>
        展示WholeBox组件与后端API的集成方式，包括API调用监控、错误处理、性能分析和实时状态监控。
        通过模拟真实的API调用场景，验证组件的稳定性和可靠性。
      </Paragraph>

      <!-- API状态概览 -->
      <div class="status-overview">
        <Title :level="5">
          <ApiOutlined />
          API状态概览
        </Title>
        
        <Row :gutter="16" class="status-cards">
          <Col :xs="24" :sm="12" :md="6">
            <Card size="small">
              <Statistic
                title="连接状态"
                :value="apiStatus.connected ? '已连接' : '未连接'"
                :value-style="{ color: apiStatus.connected ? '#3f8600' : '#cf1322' }"
                :prefix="apiStatus.connected ? h(CheckCircleOutlined) : h(CloseCircleOutlined)"
              />
            </Card>
          </Col>
          
          <Col :xs="24" :sm="12" :md="6">
            <Card size="small">
              <Statistic
                title="成功率"
                :value="performanceMetrics.successRate"
                suffix="%"
                :value-style="{ color: performanceMetrics.successRate >= 90 ? '#3f8600' : '#cf1322' }"
              />
              <Progress 
                :percent="performanceMetrics.successRate" 
                size="small" 
                :stroke-color="performanceMetrics.successRate >= 90 ? '#52c41a' : '#ff4d4f'"
              />
            </Card>
          </Col>
          
          <Col :xs="24" :sm="12" :md="6">
            <Card size="small">
              <Statistic
                title="平均响应时间"
                :value="apiStatus.avgResponseTime"
                suffix="ms"
                :value-style="{ color: apiStatus.avgResponseTime <= 1000 ? '#3f8600' : '#cf1322' }"
                :prefix="h(ClockCircleOutlined)"
              />
            </Card>
          </Col>
          
          <Col :xs="24" :sm="12" :md="6">
            <Card size="small">
              <Statistic
                title="总调用次数"
                :value="performanceMetrics.totalCalls"
                :value-style="{ color: '#1890ff' }"
              />
              <div class="call-breakdown">
                <Text type="success">成功: {{ apiStatus.successCount }}</Text>
                <Text type="danger" style="margin-left: 8px;">失败: {{ apiStatus.errorCount }}</Text>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <!-- 系统监控 -->
      <div class="system-monitoring">
        <Title :level="5">系统监控</Title>
        
        <Row :gutter="16">
          <Col :xs="24" :sm="12" :md="6">
            <div class="monitor-item">
              <Text>CPU使用率</Text>
              <Progress 
                :percent="systemStatus.cpuUsage" 
                :stroke-color="systemStatus.cpuUsage > 80 ? '#ff4d4f' : '#52c41a'"
              />
            </div>
          </Col>
          
          <Col :xs="24" :sm="12" :md="6">
            <div class="monitor-item">
              <Text>内存使用率</Text>
              <Progress 
                :percent="systemStatus.memoryUsage"
                :stroke-color="systemStatus.memoryUsage > 80 ? '#ff4d4f' : '#52c41a'"
              />
            </div>
          </Col>
          
          <Col :xs="24" :sm="12" :md="6">
            <div class="monitor-item">
              <Text>网络延迟</Text>
              <div class="metric-value">{{ systemStatus.networkLatency }}ms</div>
            </div>
          </Col>
          
          <Col :xs="24" :sm="12" :md="6">
            <div class="monitor-item">
              <Text>活跃连接</Text>
              <div class="metric-value">{{ systemStatus.activeConnections }}</div>
            </div>
          </Col>
        </Row>
      </div>

      <Divider />

      <!-- API测试控制台 -->
      <div class="test-console">
        <Title :level="5">API测试控制台</Title>
        
        <div class="test-controls">
          <div class="control-group">
            <Text strong>基础测试</Text>
            <div class="control-buttons">
              <Button type="primary" @click="testNormalApi">
                正常调用测试
              </Button>
              <Button @click="testErrorHandling">
                错误处理测试
              </Button>
              <Button @click="demoSplitPack">
                演示拆零盒整
              </Button>
            </div>
          </div>
          
          <div class="control-group">
            <Text strong>压力测试</Text>
            <div class="control-buttons">
              <Button @click="testConcurrentCalls">
                并发调用测试
              </Button>
              <Button @click="testPerformanceStress">
                性能压力测试
              </Button>
            </div>
          </div>
          
          <div class="control-group">
            <Text strong>日志管理</Text>
            <div class="control-buttons">
              <Button @click="clearLogs">
                清空日志
              </Button>
              <Button @click="exportLogs">
                导出日志
              </Button>
              <Button @click="resetStats">
                重置统计
              </Button>
            </div>
          </div>
          
          <div class="control-group">
            <Text strong>设置</Text>
            <div class="control-switches">
              <div class="switch-item">
                <Switch v-model:checked="autoScroll" size="small" />
                <Text>自动滚动</Text>
              </div>
              <div class="switch-item">
                <Switch v-model:checked="realTimeMonitoring" size="small" />
                <Text>实时监控</Text>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Divider />

      <!-- API调用日志 -->
      <div class="api-logs">
        <Title :level="5">API调用日志</Title>
        
        <div class="log-container" ref="logContainer">
          <div 
            v-for="log in apiLogs" 
            :key="log.id"
            :class="['log-item', `log-${log.type}`]"
          >
            <div class="log-header">
              <span class="log-time">{{ log.timestamp }}</span>
              <Tag :color="log.type === 'success' ? 'green' : log.type === 'error' ? 'red' : log.type === 'warning' ? 'orange' : 'blue'">
                {{ log.type.toUpperCase() }}
              </Tag>
              <span v-if="log.responseTime" class="log-response-time">{{ log.responseTime }}ms</span>
            </div>
            <div class="log-message">{{ log.message }}</div>
            <div v-if="log.data" class="log-data">
              <details>
                <summary>查看详细数据</summary>
                <pre>{{ JSON.stringify(log.data, null, 2) }}</pre>
              </details>
            </div>
          </div>
        </div>
      </div>

      <Divider />

      <!-- 代码示例 -->
      <div class="code-section">
        <Title :level="5">代码示例</Title>
        
        <CodeDemoVue 
          title="API集成示例"
          :code="apiIntegration"
          description="完整的API集成和监控示例"
        />
        
        <CodeDemoVue 
          title="高级配置"
          :code="advancedConfig"
          description="组件的高级配置和自定义选项"
        />
      </div>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
  </Card>
</template>

<style scoped>
.status-overview {
  margin-bottom: 24px;
}

.status-cards {
  margin-top: 16px;
}

.call-breakdown {
  margin-top: 8px;
  font-size: 12px;
}

.system-monitoring {
  margin-bottom: 24px;
}

.monitor-item {
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin-top: 8px;
}

.test-console {
  margin-bottom: 24px;
}

.test-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.control-group {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.control-switches {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.switch-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.api-logs {
  margin-bottom: 24px;
}

.log-container {
  height: 400px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.log-item {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;

  &:last-child {
    border-bottom: none;
  }
}

.log-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.log-time {
  color: #666;
  font-family: monospace;
}

.log-response-time {
  color: #1890ff;
  font-weight: 600;
}

.log-message {
  color: #262626;
  line-height: 1.4;
}

.log-data {
  margin-top: 8px;

  details {
    cursor: pointer;
  }

  summary {
    color: #1890ff;
    font-size: 11px;
  }

  pre {
    background-color: #f6f8fa;
    padding: 8px;
    border-radius: 4px;
    font-size: 10px;
    overflow-x: auto;
    margin-top: 4px;
  }
}

.code-section {
  margin-top: 24px;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 12px;
}
</style>
