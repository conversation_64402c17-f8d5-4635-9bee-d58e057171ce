<script setup lang="ts">
import { Message } from '@idmy/core'
import { findDeptClinicianLsApi, findSectionNurseLsApi, mdiBasicDataApi } from '@mh-hip/util'
import { updateVisitPatientInfoApi, visitPatientInfoApi } from '@mh-inpatient-hsd/util'
import { Button, Cascader, Col, DatePicker, Descriptions, DescriptionsItem, Form, FormItem, Input, InputNumber, Modal, Row, Select, SelectOption } from 'ant-design-vue'
import type { ShowSearchType } from 'ant-design-vue/es/cascader'
import dayjs from 'dayjs'
import { nationalityList } from './utils/nationality'

const props = defineProps({
  showCommunityPressureSoresRisk: {
    type: Boolean,
    default: false
  }
})

const visible = ref(false)
const formModel = ref()
const formRef = ref()
const visitId = ref<any>()
const nationalityLs = nationalityList
const relationshipLs = ref<any>([])
const marriageStatusLs = ref<any>([])
const careerLs = ref<any>([])
const eduBackgroundLs = ref<any>([])
const degreeLs = ref<any>([])
const admissionWayLs = ref<any>([])
const zoneLs = ref<any>([])
const deptClinicianLs = ref([])
const sectionNurseLs = ref([])
const isCriticalPatientLs = [
  {
    value: 0,
    label: '/',
  },
  {
    value: 1,
    label: '病重',
  },
  {
    value: 2,
    label: '病危',
  },
]

// const outOfBedRisk = '坠床风险'
// const fallDownRisk = '跌倒风险'
// const lostRisk = '走失风险'
// const pressureSoresRisk = '压疮风险'
// const tubeSplitRisk = '脱管风险'
// const riskLs = [outOfBedRisk, fallDownRisk, lostRisk, pressureSoresRisk, tubeSplitRisk]
// const riskLevelLs = [{
//   value: 0,
//   label: '无'
// }, {
//   value: 1,
//   label: '低'
// }, {
//   value: 2,
//   label: '中'
// }, {
//   value: 3,
//   label: '高'
// }, {
//   value: 4,
//   label: '极高'
// }]

const updateLoading = ref<boolean>(false)

const open = async (initVisitId: any, deptCode: any, sectionId: any) => {
  formModel.value = {
    // riskLs: []
  }
  visitId.value = initVisitId
  visible.value = true
  await getDeptClinician(deptCode)
  await getSectionNurse(sectionId)
  await getBaseData()
  await getVisitPatientInfo()
}

async function getBaseData() {
  mdiBasicDataApi({ dataTypeLs: ['zone', 'relationship', 'mariageStatus', 'career', 'eduBackground', 'eduDegree', 'admissionWay'] }).then((res: any) => {
    if (res) {
      zoneLs.value = res['zone']
      relationshipLs.value = res['relationship']
      marriageStatusLs.value = res['mariageStatus']
      careerLs.value = res['career']
      eduBackgroundLs.value = res['eduBackground']
      degreeLs.value = res['eduDegree']
      admissionWayLs.value = res['admissionWay']
    }
  })
}

async function getVisitPatientInfo() {
  const res: any = await visitPatientInfoApi({ visitId: visitId.value })
  if (res) {
    formModel.value = res
    if (res.timeEnter) {
      formModel.value.timeEnterDate = dayjs(res.timeEnter)
    } else {
      formModel.value.timeEnterDate = null
      formModel.value.timeEnter = null
    }
    if (res.treatmentTime) {
      formModel.value.treatmentTimeDate = dayjs(res.treatmentTime)
    } else {
      formModel.value.treatmentTimeDate = null
      formModel.value.treatmentTime = null
    }
    formModel.value.familyZoneCodeLs = res.familyZoneCodeLs && res.familyZoneCodeLs.length > 0 ? res.familyZoneCodeLs : []
    formModel.value.livingZoneCodeLs = res.livingZoneCodeLs && res.livingZoneCodeLs.length > 0 ? res.livingZoneCodeLs : []
    formModel.value.companyZoneCodeLs = res.companyZoneCodeLs && res.companyZoneCodeLs.length > 0 ? res.companyZoneCodeLs : []
    // formModel.value.riskLs = []
    // if (res.outOfBedRisk) {
    //   formModel.value.riskLs.push(outOfBedRisk)
    // }
    // if (res.fallDownRisk) {
    //   formModel.value.riskLs.push(fallDownRisk)
    // }
    // if (res.lostRisk) {
    //   formModel.value.riskLs.push(lostRisk)
    // }
    // if (res.pressureSoresRisk) {
    //   formModel.value.riskLs.push(pressureSoresRisk)
    // }
    // if (res.tubeSplitRisk) {
    //   formModel.value.riskLs.push(tubeSplitRisk)
    // }
  }
}

const getDeptClinician = async (deptCode: string) => {
  deptClinicianLs.value = []
  findDeptClinicianLsApi({ S_EQ_t_clinician_dept__Dept_Code: deptCode, S_EQ_t_clinician__Clinician_Type_ID: 1 }).then((res: any) => {
    if (res) {
      deptClinicianLs.value = res
    }
  })
}

const getSectionNurse = async (sectionId: number) => {
  sectionNurseLs.value = []
  findSectionNurseLsApi({ sectionId: sectionId }).then((res: any) => {
    if (res) {
      sectionNurseLs.value = res
    }
  })
}

function handleCancel() {
  visible.value = false
}

const handleUpdate = async () => {
  await formRef.value?.validate()
  updateLoading.value = true
  // let outOfBed = 0
  // let fallDown = 0
  // let lost = 0
  // let pressureSores = 0
  // let tubeSplit = 0
  // if (formModel.value.riskLs && formModel.value.riskLs.length > 0) {
  //   if (formModel.value.riskLs.includes(outOfBedRisk)) {
  //     outOfBed = 1
  //   }
  //   if (formModel.value.riskLs.includes(fallDownRisk)) {
  //     fallDown = 1
  //   }
  //   if (formModel.value.riskLs.includes(lostRisk)) {
  //     lost = 1
  //   }
  //   if (formModel.value.riskLs.includes(pressureSoresRisk)) {
  //     pressureSores = 1
  //   }
  //   if (formModel.value.riskLs.includes(tubeSplitRisk)) {
  //     tubeSplit = 1
  //   }
  // }
  formModel.value.timeEnter = formModel.value.timeEnterDate ? formModel.value.timeEnterDate.format('YYYY-MM-DD HH:mm:ss') : null
  formModel.value.treatmentTime = formModel.value.treatmentTimeDate ? formModel.value.treatmentTimeDate.format('YYYY-MM-DD HH:mm:ss') : null
  // formModel.value.outOfBedRisk = outOfBed
  // formModel.value.fallDownRisk = fallDown
  // formModel.value.lostRisk = lost
  // formModel.value.pressureSoresRisk = pressureSores
  // formModel.value.tubeSplitRisk = tubeSplit
  updateVisitPatientInfoApi(formModel.value)
    .then(() => {
      Message.success('患者信息更新完成')
      visible.value = false
    })
    .finally(() => {
      updateLoading.value = false
    })
}

const filter: ShowSearchType['filter'] = (inputValue, path) => {
  return path.some(option => option.title.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
}

const handleZoneChange = (v: any, prop: String) => {
  const zoneCode = getZoneCode(v)
  if (prop === 'familyZonecode') {
    formModel.value.familyZonecode = zoneCode
  } else if (prop === 'livingZonecode') {
    formModel.value.livingZonecode = zoneCode
  } else if (prop === 'companyZonecode') {
    formModel.value.companyZonecode = zoneCode
  }
}

function getZoneCode(zone: any) {
  if (zone) {
    return zone[zone.length - 1]
  }
  return null
}

function filterOption(input: any, option: any) {
  return option.text && option.text.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

defineExpose({
  open,
})
</script>

<template>
  <Modal v-model:open="visible" title="信息修改" width="1300px" @cancel="handleCancel" :mask-closable="false">
    <template #footer>
      <Button type="dashed" @click="handleCancel"> 关闭 </Button>
      <Button @click="handleUpdate" :loading="updateLoading" type="primary"> 更新 </Button>
    </template>
    <div>
      <Form ref="formRef" :model="formModel">
        <Descriptions size="small" bordered :column="4" title="在院信息" p-t-5>
          <Descriptions-item>
            <template #label>管床医生</template>
            <Form-item>
              <Select v-model:value="formModel.bedsideCid" placeholder="请选择管床医生" style="width: 100%" show-search :filter-option="filterOption">
                <Select-option v-for="item in deptClinicianLs" :key="item.clinicianId" :value="item.clinicianId" :text="item.clinicianName">
                  {{ item.clinicianName }}
                </Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>主治医师</template>
            <Form-item>
              <Select v-model:value="formModel.doctorCid" placeholder="请选择主治医师" style="width: 100%" show-search :filter-option="filterOption">
                <Select-option v-for="item in deptClinicianLs" :key="item.clinicianId" :value="item.clinicianId" :text="item.clinicianName">
                  {{ item.clinicianName }}
                </Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>责任护士</template>
            <Form-item>
              <Select v-model:value="formModel.nurseCid" placeholder="请选择责任护士" style="width: 100%" show-search :filter-option="filterOption">
                <Select-option v-for="item in sectionNurseLs" :key="item.clinicianId" :value="item.clinicianId" :text="item.clinicianName">
                  {{ item.clinicianName }}
                </Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item> </Descriptions-item>
          <Descriptions-item>
            <template #label>入区时间</template>
            <Form-item>
              <Date-picker class="flex" show-time v-model:value="formModel.timeEnterDate" format="MM-DD HH:mm" :allow-clear="false" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>入院途径</template>
            <Form-item>
              <Select v-model:value="formModel.admissionWayId">
                <Select-option v-for="item in admissionWayLs" :key="item.admissionWayId" :value="item.admissionWayId">{{ item.admissionWayName }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>身高cm</template>
            <Form-item>
              <Input-number v-model:value="formModel.heightCm" :max="300" style="width: 100%" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>体重kg</template>
            <Form-item>
              <Input-number v-model:value="formModel.weightKg" :max="3000" style="width: 100%" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>呼吸率</template>
            <Form-item>
              <Input-number v-model:value="formModel.rr" :max="3000" style="width: 100%" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>脉搏率</template>
            <Form-item>
              <Input-number v-model:value="formModel.pulse" :max="3000" style="width: 100%" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>血压</template>
            <Form-item>
              <Row>
                <Col :span="11">
                  <Input-number v-model:value="formModel.dbp" w-73px placeholder="舒张压" :max="3000" />
                </Col>
                <Col :span="2" p-l-1.5 p-t-1> / </Col>
                <Col :span="11">
                  <Input-number v-model:value="formModel.sbp" w-73px placeholder="收缩压" :max="3000" />
                </Col>
              </Row>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>腕带编号</template>
            <Form-item>
              <Input v-model:value="formModel.wristbandId" :max-length="40" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>重危</template>
            <Form-item>
              <Select v-model:value="formModel.isCriticalPatient">
                <Select-option v-for="item in isCriticalPatientLs" :key="item.value" :value="item.value">{{ item.label }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>抢救日数</template>
            <Form-item>
              <Input-number v-model:value="formModel.rescueDays" :max="999" :min="0" style="width: 100%" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>手术时间</template>
            <Form-item>
              <Date-picker class="flex" show-time v-model:value="formModel.treatmentTimeDate" format="MM-DD HH:mm" :allow-clear="true" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item> </Descriptions-item>
          <!--          <Descriptions-item :span="3">-->
          <!--            <template #label>风险</template>-->
          <!--            <Form-item>-->
          <!--              <Date-pickercheckbox-group v-model:value="formModel.riskLs" :options="riskLs" />-->
          <!--            </Form-item>-->
          <!--          </Descriptions-item>-->
          <Descriptions-item :span="4">
            <template #label>风险</template>
            <Row>
              <Col flex="130px">
                <Form-item label="坠床" :label-col="{ style: { width: '60px' } }" :wrapper-col="{ style: { width: '70px' } }">
                  <Input-number style="width: 95%" v-model:value="formModel.outOfBedRisk" />
                </Form-item>
              </Col>
              <Col flex="130px">
                <Form-item label="跌倒" :label-col="{ style: { width: '60px' } }" :wrapper-col="{ style: { width: '70px' } }">
                  <Input-number style="width: 95%" v-model:value="formModel.fallDownRisk" />
                </Form-item>
              </Col>
              <Col flex="130px">
                <Form-item label="走失" :label-col="{ style: { width: '60px' } }" :wrapper-col="{ style: { width: '70px' } }">
                  <Input-number style="width: 95%" v-model:value="formModel.lostRisk" />
                </Form-item>
              </Col>
              <Col flex="130px">
                <Form-item :label="`${props.showCommunityPressureSoresRisk ? '院内' : ''}压疮`" :label-col="{ style: { width: '60px' } }" :wrapper-col="{ style: { width: '70px' } }">
                  <Input-number style="width: 95%" v-model:value="formModel.pressureSoresRisk" />
                </Form-item>
              </Col>
              <Col flex="130px" v-if="props.showCommunityPressureSoresRisk">
                <Form-item label="院外压疮" :label-col="{ style: { width: '60px' } }" :wrapper-col="{ style: { width: '70px' } }">
                  <Input-number style="width: 95%" v-model:value="formModel.communityPressureSoresRisk" />
                </Form-item>
              </Col>
              <Col flex="130px">
                <Form-item label="脱管" :label-col="{ style: { width: '60px' } }" :wrapper-col="{ style: { width: '70px' } }">
                  <Input-number style="width: 95%" v-model:value="formModel.tubeSplitRisk" />
                </Form-item>
              </Col>
              <Col flex="130px">
                <Form-item label="自杀" :label-col="{ style: { width: '60px' } }" :wrapper-col="{ style: { width: '70px' } }">
                  <Input-number style="width: 95%" v-model:value="formModel.suicideRisk" />
                </Form-item>
              </Col>
            </Row>
          </Descriptions-item>
        </Descriptions>
        <Descriptions size="small" bordered :column="4" title="患者信息" p-t-8>
          <Descriptions-item>
            <template #label>患者电话</template>
            <Form-item>
              <Input v-model:value="formModel.telNo" :maxlength="64" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item> </Descriptions-item>
          <Descriptions-item> </Descriptions-item>
          <Descriptions-item> </Descriptions-item>
          <Descriptions-item>
            <template #label>民族</template>
            <Form-item>
              <Select v-model:value="formModel.nationalityCode">
                <Select-option v-for="item in nationalityLs" :key="item.nationalityCode" :value="item.nationalityCode">{{ item.nationalityName }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>家属</template>
            <Form-item>
              <Input v-model:value="formModel.contactorName" :maxlength="50" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>家属电话</template>
            <Form-item>
              <Input v-model:value="formModel.contactorTel" :maxlength="64" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>与患者关系</template>
            <Form-item>
              <Select v-model:value="formModel.relationshipId">
                <Select-option v-for="item in relationshipLs" :key="item.relationshipId" :value="item.relationshipId">{{ item.relationshipName }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>婚姻状况</template>
            <Form-item>
              <Select v-model:value="formModel.marriageStatusId">
                <Select-option v-for="item in marriageStatusLs" :key="item.marriageStatusId" :value="item.marriageStatusId">{{ item.marriageStatusName }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>职业</template>
            <Form-item>
              <Select v-model:value="formModel.careerId">
                <Select-option v-for="item in careerLs" :key="item.careerId" :value="item.careerId">{{ item.careerName }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>学历</template>
            <Form-item>
              <Select v-model:value="formModel.eduBackgroundId">
                <Select-option v-for="item in eduBackgroundLs" :key="item.eduBackgroundId" :value="item.eduBackgroundId">{{ item.eduBackgroundName }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>学位</template>
            <Form-item>
              <Select v-model:value="formModel.degreeId" :allow-clear="true">
                <Select-option v-for="item in degreeLs" :key="item.degreeId" :value="item.degreeId">{{ item.degreeName }}</Select-option>
              </Select>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item :span="3">
            <template #label>户籍地址</template>
            <Form-item>
              <Row>
                <Col :span="10">
                  <Cascader
                    :options="zoneLs"
                    :fieldNames="{ label: 'title', value: 'value', children: 'children' }"
                    v-model:value="formModel.familyZoneCodeLs"
                    placeholder="请选择地区"
                    :show-search="{ filter }"
                    @change="handleZoneChange($event, 'familyZonecode')"
                  >
                  </Cascader>
                </Col>
                <Col :span="14">
                  <Input v-model:value="formModel.familyAddr" :maxlength="80" placeholder="请录入详细地址" />
                </Col>
              </Row>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>邮编</template>
            <Form-item>
              <Input v-model:value="formModel.familyZipcode" :maxlength="10" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item :span="3">
            <template #label>现居地址</template>
            <Form-item>
              <Row>
                <Col :span="10">
                  <Cascader
                    :options="zoneLs"
                    :fieldNames="{ label: 'title', value: 'value', children: 'children' }"
                    v-model:value="formModel.livingZoneCodeLs"
                    placeholder="请选择地区"
                    :show-search="{ filter }"
                    @change="handleZoneChange($event, 'livingZonecode')"
                  >
                  </Cascader>
                </Col>
                <Col :span="14">
                  <Input v-model:value="formModel.livingAddr" :maxlength="80" placeholder="请录入详细地址" />
                </Col>
              </Row>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>邮编</template>
            <Form-item>
              <Input v-model:value="formModel.livingZipcode" :maxlength="10" />
            </Form-item>
          </Descriptions-item>
          <Descriptions-item :span="3">
            <template #label>单位通讯地址</template>
            <Form-item>
              <Row>
                <Col :span="10">
                  <Cascader
                    :options="zoneLs"
                    :fieldNames="{ label: 'title', value: 'value', children: 'children' }"
                    v-model:value="formModel.companyZoneCodeLs"
                    placeholder="请选择地区"
                    :show-search="{ filter }"
                    @change="handleZoneChange($event, 'companyZonecode')"
                  >
                  </Cascader>
                </Col>
                <Col :span="14">
                  <Input v-model:value="formModel.companyAddr" :maxlength="80" placeholder="请录入详细地址" />
                </Col>
              </Row>
            </Form-item>
          </Descriptions-item>
          <Descriptions-item>
            <template #label>邮编</template>
            <Form-item>
              <Input v-model:value="formModel.companyZipcode" :maxlength="10" />
            </Form-item>
          </Descriptions-item>
        </Descriptions>
      </Form>
    </div>
  </Modal>
</template>
