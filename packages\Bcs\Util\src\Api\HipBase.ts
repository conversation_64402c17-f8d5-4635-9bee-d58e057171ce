import { Data, http } from '@idmy/core'

async function allPaymentTypes(): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/paymenttype/findAll', {}, { appKey: 'hip' })
  return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.paymentCode, id: row.paymentId, value: row.paymentCode, label: row.paymentName, data: row }))
}

async function allInsuranceTypes(): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/insurancetype/findAll', {}, { appKey: 'hip' })
  return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.insuranceCode, id: row.insuranceTypeId, value: row.insuranceTypeId, label: row.insuranceName, data: row }))
}

async function allMedicalTypes(): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/medicaltype/findAll', {}, { appKey: 'hip' })
  return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.medTypeCode, id: row.medTypeId, value: row.medTypeId, label: row.medTypeName, data: row }))
}

async function allMiPaymentMethods(): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/mipaymethod/findAll', {}, { appKey: 'hip' })
  return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.miPaymethodCode, id: row.miPaymethodId, value: row.miPaymethodCode, label: row.miPaymethodName, data: row }))
}

async function allFeeCats(): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/feecat/findAll', {}, { appKey: 'hip' })
  return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.feeCatCode, id: row.feeCatId, value: row.feeCatId, label: row.feeCatName, data: row }))
}

async function allFeeTypes(): Promise<Data[]> {
  const arr = await http.post<Data[]>('/hip-base/feecat/findAll', {}, { appKey: 'hip' })
  return arr.filter(row => row.disabled !== 1).map(row => ({ code: row.feeTypeCode, id: row.feTypeId, value: row.feeTypeId, label: row.feeTypeName, data: row }))
}


export type HipType = 'PaymentType' | 'InsuranceType' | 'MedicalType' | 'MiPaymentMethod' | 'FeeCat' | 'FeeType'

const map = {
  PaymentType: allPaymentTypes,
  InsuranceType: allInsuranceTypes,
  MedicalType: allMedicalTypes,
  MiPaymentMethod: allMiPaymentMethods,
  FeeCat: allFeeCats,
  FeeType: allFeeTypes,
}

export function listDictsByHipType(type: HipType) {
  return map[type]
}
