<script setup lang="ts">
import { WmCountEdit } from '@mh-wm/count'
import { Card, Typography, Divider, Button, message, Input, Form } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { editUsage, importCode, packageJsonCode } from '../code/CountEditCode'

const { Title, Paragraph } = Typography

// 引用
const countEditRef = ref()
// 盘点单ID
const countId = ref<string | number>('123')

// 保存盘点结果成功回调
const handleEditSuccess = (id: number) => {
  message.success(`保存盘点结果成功，盘点单ID: ${id}`)
}

// 取消录入盘点结果回调
const handleEditCancel = () => {
  console.log('取消录入盘点结果')
}

// 复盘成功回调
const handleRecheckSuccess = (id: number) => {
  message.success(`复盘操作成功，盘点单ID: ${id}`)
}

// 复核成功回调
const handleFinishSuccess = (id: number) => {
  message.success(`复核操作成功，盘点单ID: ${id}`)
}

// 打开录入盘点结果对话框
const openEdit = () => {
  if (!countId.value) {
    message.warning('请输入盘点单ID')
    return
  }

  // 将输入的ID转换为数字
  const id = Number(countId.value)
  if (isNaN(id) || id <= 0) {
    message.error('请输入有效的盘点单ID')
    return
  }

  // 打开对应ID的盘点单
  countEditRef.value.open(id)
}
</script>

<template>
  <Card title="录入盘点结果" class="mb-16px">
    <div mb-16px>
      <Title :level="4">基础用法</Title>
      <Paragraph> 点击按钮打开录入盘点结果对话框，可以录入盘点结果。 </Paragraph>

      <div class="demo-container">
        <div class="input-container">
          <Form layout="inline">
            <Form.Item label="盘点单ID">
              <Input v-model:value="countId" placeholder="请输入盘点单ID" style="width: 200px" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" @click="openEdit">打开录入</Button>
            </Form.Item>
          </Form>
        </div>

        <Divider style="margin: 16px 0" />

        <div class="component-container">
          <WmCountEdit
            buttonText="录入盘点结果"
            buttonType="primary"
            @success="handleEditSuccess"
            @cancel="handleEditCancel"
            @recheck="handleRecheckSuccess"
            @finish="handleFinishSuccess"
            ref="countEditRef"
          />
          <div class="description">
            <p>组件提供了两种使用方式：</p>
            <ol>
              <li>直接点击组件按钮，然后在弹出的对话框中输入盘点单ID</li>
              <li>通过引用调用open方法，传入盘点单ID（如上方示例）</li>
            </ol>
          </div>
        </div>
      </div>

      <Divider />
      <CodeDemoVue :usage="editUsage" :importCode="importCode" :packageJson="packageJsonCode" />
    </div>
  </Card>
</template>

<style scoped>
.demo-container {
  margin: 16px 0;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.input-container {
  margin-bottom: 16px;
}

.component-container {
  display: flex;
  align-items: flex-start;
  gap: 24px;
}

.description {
  flex: 1;
  padding: 12px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.description p {
  margin-bottom: 8px;
  font-weight: 500;
}

.description ol {
  padding-left: 20px;
  margin: 0;
}

.description li {
  margin-bottom: 4px;
}
</style>
