<script setup lang="ts">
import { OrgSelect } from '@mh-hip/org'
import { Card, Typography, Divider, Switch, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { ownUsage, importCode, packageJsonCode } from '../code/OrgSelectCode'

const { Title } = Typography

// 是否只显示当前用户的组织机构
const isOwn = ref(false)

// 选择值
const userOrgValue = ref<any>()
</script>

<template>
  <Card title="仅显示当前用户有权限的组织机构" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="24">
        <div>
          <div mb-8px>
            <span mr-8px>组织机构（</span>
            <Switch v-model:checked="isOwn" size="small" />
            <span ml-8px>{{ isOwn ? '只显示' : '显示所有' }}当前用户的组织机构）</span>
          </div>
          <OrgSelect v-model="userOrgValue" :own="isOwn" style="width: 100%" />
          <div mt-8px>选中值: {{ userOrgValue }}</div>
        </div>
      </Col>
    </Row>
    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="ownUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
