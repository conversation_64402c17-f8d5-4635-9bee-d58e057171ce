<script setup lang="ts">
import filters from './utils/filters'
import { index as VisitForm } from "@mh-inpatient-hsd/visit-form"
import { Divide<PERSON>, <PERSON><PERSON><PERSON>, Button } from 'ant-design-vue'

const visitFormRef = ref<InstanceType<typeof VisitForm>>()
const props = defineProps({
  userInfo: {
    type: Object,
  },
  inline: {
    type: Boolean,
    default: false
  },
  hiddenEditBtn: {
    type: Boolean,
    default: false
  },
  hiddenTimeAdmission: {
    type: Boolean,
    default: false
  },
  hiddenPatientName: {
    type: Boolean,
    default: false
  },
  showCommunityPressureSoresRisk: {
    type: <PERSON>olean,
    default: false
  }
})

const handleVisibleVisitFom = (userInfo: any) => {
  visitFormRef.value?.open(userInfo.visitId, userInfo.deptCode, userInfo.sectionId)
}

</script>

<template>
  <div v-if="userInfo" class="base-desc-content" :class="{ 'desc-inline': inline }">
    <div v-if="userInfo && userInfo.visitId" class="flex items-center" text-14px>
      <div class="flex items-center" style="color: #4E5552; font-weight: bold;">
        <img v-user-avatr="userInfo" width="20" height="20">
        <span v-if="!hiddenPatientName && userInfo.patientName" m-x-8px>{{ userInfo.patientName }}</span>
        <span v-if="userInfo.ageOfYears || userInfo.ageOfDays" m-x-8px>{{ filters.formatAge(userInfo.ageOfYears, userInfo.ageOfDays) }}</span>
        <span v-if="userInfo.genderName" m-x-8px>{{ userInfo.genderName }}</span>
      </div>
      <div flex items-center>
        <Divider type="vertical" />
        <span m-x-8px v-if="!hiddenPatientName && userInfo.bedNo">{{ userInfo.bedNo }}床</span>
        <span v-if="userInfo.nursingLevel" m-x-8px>{{ userInfo.nursingLevel }}</span>
        <span v-if="userInfo.diet" m-x-8px>{{ userInfo.diet }}</span>
        <span v-if="userInfo.deptName" m-x-8px>
          <span v-for="(dept, index) in userInfo.deptNames" :key="index">
            {{ index > 0 ? '|' : '' }}{{ dept }}
          </span>
        </span>
        <template v-if="userInfo.timeAdmission && !hiddenTimeAdmission">
          <Divider type="vertical" />
          <span m-x-8px>{{ filters.dateFormatMDHM(userInfo.timeAdmission) }}入院</span>
        </template>
        <template v-if="userInfo.hospitalizedDays">
          <Divider type="vertical" />
          <span m-x-8px>住院第{{ userInfo.deptDays }}/{{ userInfo.hospitalizedDays }}天</span>
        </template>
        <template v-if="userInfo.totalAmount">
          <Divider type="vertical" />
          <span m-x-8px>￥{{ userInfo.totalAmount }}</span>
        </template>
        <template v-if="userInfo.admDiags">
          <Divider type="vertical" />
          <Tooltip placement="topLeft" :title="userInfo.admDiags">
            <span v-if="hiddenEditBtn" line-clamp-1 max-w-100px m-x-8px style="color: #F93307">{{ userInfo.admDiags }}</span>
            <span v-else line-clamp-1 max-w-500px m-x-8px style="color: #F93307">{{ userInfo.admDiags }}</span>
          </Tooltip>
        </template>
<!--  userInfo.visitStatus !== 3 出院状态的判断组件不做了，有的医院出院还要改      <Button type="primary" style="margin-left: 10px;" @click="handleVisibleVisitFom(userInfo)" v-if="!hiddenEditBtn && userInfo.visitStatus !== 3">-->
        <Button type="primary" style="margin-left: 10px;" @click="handleVisibleVisitFom(userInfo)" v-if="!hiddenEditBtn">
          信息修改
        </Button>
      </div>
    </div>
    <visit-form ref="visitFormRef" :showCommunityPressureSoresRisk="props.showCommunityPressureSoresRisk" />
  </div>
</template>

<style lang="less" scoped>
.base-desc-content {
  font-size: 0;
  background-color: #fff;
  padding: 12px 16px;
  z-index: 10;
  margin-bottom: 12px;

  &.desc-inline {
    padding: 0;
    margin-bottom: 0;
  }
}
.mr10 {
  margin-right: 10px;
}
</style>
