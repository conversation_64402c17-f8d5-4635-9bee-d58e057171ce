import { GENDER, MDTRT_CERT_TYPE } from '../Enum'

// 支持的医保接口版本号列表
export const SUPPORTED_MI_VERSIONS = ['v1_1', 'vSDDW_DLL']

// 处理证件类型
export const formatCertType = (type: string | number | null | undefined): string => {
  if (!type) return '-'

  // 先尝试直接从枚举中获取
  if (type.toString() in MDTRT_CERT_TYPE) {
    return MDTRT_CERT_TYPE[type.toString()]
  }

  // 如果枚举中没有，则转为数字对10取余后再查询
  const typeNum = Number(type) % 10
  return MDTRT_CERT_TYPE[typeNum] || type.toString()
}

// 格式化性别
export const formatGender = (gender: string | number | null | undefined): string => {
  if (!gender) return '-'
  return GENDER[gender] || gender.toString()
}

/**
 * 获取医保响应数据
 * @param resp 医保响应对象
 * @returns 解析后的医保响应数据
 */
export const getMiResp = (resp: any) => {
  const version = resp?.provider?.interfaceVersionNo

  // 检查版本号是否在支持列表中
  if (SUPPORTED_MI_VERSIONS.includes(version)) {
    if (!resp?.miResp) {
      // 返回空对象，但包含版本号
      return { version }
    }
    try {
      var miResp = JSON.parse(resp.miResp)
      // 将版本号添加到返回结果中
      miResp.version = version
      console.log('医保结果:', miResp)
      return miResp
    } catch (e) {
      throw new Error(`医保响应数据格式错误: ${e.message}`)
    }
  } else {
    throw new Error(`[${version}]版本号不支持，支持的版本号: ${SUPPORTED_MI_VERSIONS.join(', ')}`)
  }
}
