<script setup lang="ts">
import { Select, Spin } from 'ant-design-vue'
import { Data, http } from '@idmy/core'
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
import { scmCustSearchApi, scmCustFindAllApi, orgPartnerPageApi, orgPartnerFindAllApi } from '@mh-wm/util'
import { debounce } from 'lodash-es'

// 是否为开发环境
const isDev = process.env.NODE_ENV === 'development'

const props = defineProps({
  // 是否允许清除
  allowClear: {
    type: Boolean,
    default: true,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请输入供应商名称/编码/五笔/拼音码',
  },
  // 宽度
  width: {
    type: [String, Number],
    default: '100%',
  },
  // 显示字段
  showField: {
    type: String,
    default: 'custName', // custName, custCode
  },
  // 搜索类型
  searchType: {
    type: String,
    default: 'all', // all, name, code, wubi, pinyin
  },
  // 每页显示条数
  pageSize: {
    type: Number,
    default: 20,
  },
  // 是否默认加载第一页数据
  loadFirstPage: {
    type: Boolean,
    default: true,
  },
  // 是否启用分页加载
  enablePagination: {
    type: Boolean,
    default: true,
  },
  // 分页加载方式：scroll(滚动加载) 或 button(按钮加载)
  paginationMode: {
    type: String,
    default: 'scroll', // scroll, button
    validator: (value: string) => ['scroll', 'button'].includes(value),
  },
  // 最大页数限制，防止加载过多数据
  maxPages: {
    type: Number,
    default: 5,
  },
  // 是否使用机构供应商API
  orgPartner: {
    type: Boolean,
    default: false,
  },
})

const modelValue = defineModel<any>()

// 供应商列表
const custList = ref<any[]>([])
const loading = ref(false)
const searchKeyword = ref('')

// 所有供应商数据（用于前端过滤）
const allCustData = ref<any[]>([])
const allDataLoaded = ref(false)

// 分页相关状态
const currentPage = ref(1)
const totalPages = ref(1)
const hasMore = ref(true)
const loadingMore = ref(false)

// 计算样式
const selectStyle = computed(() => {
  if (typeof props.width === 'number') {
    return { width: `${props.width}px` }
  }
  return { width: props.width }
})

// 加载所有供应商数据（用于前端过滤）
const loadAllCustData = async () => {
  console.log('🔍 开始加载所有供应商数据', props.orgPartner ? '(机构供应商)' : '(普通供应商)')
  loading.value = true

  try {
    // 构建查询参数
    const params: any = {
      // 可以添加一些基本过滤条件，如供应商类型等
      // S_EQ_t_scm_cust__Cust_Type_ID: 2,
    }

    console.log('📤 请求参数:', params)

    // 根据是否使用机构供应商API决定调用哪个API
    const response = props.orgPartner
      ? await orgPartnerFindAllApi(params) // 当使用机构供应商API时
      : await scmCustFindAllApi(params)

    console.log('📥 API响应:', response)

    // 处理返回的数据
    let dataList: any[] = []

    // 直接检查是否有list字段
    if (response && Array.isArray(response.list)) {
      console.log('📋 供应商列表原始数据(直接list):', response.list)
      dataList = response.list
    }
    // 检查标准的data.list结构
    else if (response && response.data && Array.isArray(response.data.list)) {
      console.log('📋 供应商列表原始数据(data.list):', response.data.list)
      dataList = response.data.list
    }
    // 检查是否直接返回了数组
    else if (response && Array.isArray(response)) {
      console.log('📋 供应商列表原始数据(直接数组):', response)
      dataList = response
    }
    // 如果是标准响应但没有找到列表
    else if (response && response.code === 0) {
      console.warn('⚠️ API响应成功但未找到列表数据:', response)
      dataList = []
    } else {
      console.warn('⚠️ API响应异常或列表为空:', response)
      dataList = []
    }

    // 保存所有数据
    allCustData.value = dataList
    allDataLoaded.value = true
    console.log('✅ 所有供应商数据加载完成, 总数:', allCustData.value.length)

    // 初始显示所有数据
    filterCustList('')
  } catch (error) {
    console.error('❌ 加载所有供应商数据失败:', error)
    allCustData.value = []
    custList.value = []
  } finally {
    loading.value = false
  }
}

// 前端过滤供应商列表
const filterCustList = (keyword: string = '') => {
  console.log('🔍 前端过滤供应商, 关键字:', keyword)

  if (!allDataLoaded.value || allCustData.value.length === 0) {
    console.log('⚠️ 所有数据尚未加载，无法进行过滤')
    return
  }

  // 如果关键字为空，显示所有数据
  if (!keyword || keyword.trim() === '') {
    const mappedList = allCustData.value.map((item: any) => ({
      value: item.custId,
      label: props.showField === 'custCode'
        ? `${item.custCode || ''} - ${item.custName || ''}`
        : item.custCode ? `${item.custName || ''} (${item.custCode})` : `${item.custName || ''}`,
      data: item,
    }))
    custList.value = mappedList
    console.log('✅ 显示所有供应商数据, 总数:', custList.value.length)
    return
  }

  // 转换关键字为大写，用于不区分大小写的比较
  const upperKeyword = keyword.trim().toUpperCase()

  // 根据搜索类型和关键字过滤数据
  const filteredData = allCustData.value.filter((item: any) => {
    // 根据搜索类型决定搜索哪些字段
    if (props.searchType === 'name') {
      return item.custName && item.custName.toUpperCase().includes(upperKeyword)
    } else if (props.searchType === 'code') {
      return item.custCode && item.custCode.toUpperCase().includes(upperKeyword)
    } else if (props.searchType === 'wubi') {
      return item.qsCode1 && item.qsCode1.toUpperCase().includes(upperKeyword)
    } else if (props.searchType === 'pinyin') {
      return item.qsCode2 && item.qsCode2.toUpperCase().includes(upperKeyword)
    } else {
      // 默认搜索所有字段
      return (
        (item.custName && item.custName.toUpperCase().includes(upperKeyword)) ||
        (item.custCode && item.custCode.toUpperCase().includes(upperKeyword)) ||
        (item.qsCode1 && item.qsCode1.toUpperCase().includes(upperKeyword)) ||
        (item.qsCode2 && item.qsCode2.toUpperCase().includes(upperKeyword))
      )
    }
  })

  // 映射过滤后的数据为选项格式
  const mappedList = filteredData.map((item: any) => ({
    value: item.custId,
    label: props.showField === 'custCode'
      ? `${item.custCode || ''} - ${item.custName || ''}`
      : item.custCode ? `${item.custName || ''} (${item.custCode})` : `${item.custName || ''}`,
    data: item,
  }))

  custList.value = mappedList
  console.log('✅ 过滤后的供应商数据, 总数:', custList.value.length)
}

// 搜索供应商
const searchCust = async (keyword: string = '', page: number = 1, isLoadMore: boolean = false) => {
  console.log('🔍 开始搜索供应商, 关键字:', keyword, '页码:', page, '加载更多:', isLoadMore, props.orgPartner ? '(机构供应商)' : '(普通供应商)')

  // 如果禁用分页，使用前端过滤
  if (!props.enablePagination) {
    if (!allDataLoaded.value) {
      // 如果所有数据尚未加载，先加载所有数据
      await loadAllCustData()
    } else {
      // 已加载所有数据，直接过滤
      filterCustList(keyword)
    }
    return
  }

  // 以下是原有的分页加载逻辑
  if (isLoadMore) {
    loadingMore.value = true
  } else {
    loading.value = true
    // 如果不是加载更多，重置分页状态
    if (page === 1) {
      currentPage.value = 1
      hasMore.value = true
      allDataLoaded.value = false
    }
  }

  try {
    // 构建查询参数
    const params: any = {
      pageSize: props.pageSize,
      pageNum: page,
    }

    // 如果有关键字，添加到查询参数
    if (keyword && keyword.trim() !== '') {
      params.keyword = keyword
      // params.S_EQ_t_scm_cust__Cust_Type_ID = 2
      // 根据搜索类型添加额外参数
      if (props.searchType === 'name') {
        params.searchField = 'custName'
      } else if (props.searchType === 'code') {
        params.searchField = 'custCode'
      } else if (props.searchType === 'wubi') {
        params.searchField = 'qsCode1'
      } else if (props.searchType === 'pinyin') {
        params.searchField = 'qsCode2'
      }
    } else {
      // 如果没有关键字，确保不传递searchField参数
      delete params.searchField
      // 如果是清除后的查询，可以添加一个标记
      params.isInitialLoad = true
    }

    console.log('📤 请求参数:', params)

    // 根据是否使用机构供应商API决定调用哪个API
    const response = props.orgPartner
      ? await orgPartnerPageApi(params) // 当使用机构供应商API时
      : await scmCustSearchApi(params)

    console.log('📥 API响应:', response)

    // 根据API返回的不同数据结构处理数据
    let dataList: any[] = []
    let total = 0
    let pages = 1

    // 直接检查是否有list字段
    if (response && Array.isArray(response.list)) {
      console.log('📋 供应商列表原始数据(直接list):', response.list)
      dataList = response.list
      total = response.total || 0
      pages = response.pages || Math.ceil(total / props.pageSize) || 1
    }
    // 检查标准的data.list结构
    else if (response && response.data && Array.isArray(response.data.list)) {
      console.log('📋 供应商列表原始数据(data.list):', response.data.list)
      dataList = response.data.list
      total = response.data.total || 0
      pages = response.data.pages || Math.ceil(total / props.pageSize) || 1
    }
    // 检查是否直接返回了数组
    else if (response && Array.isArray(response)) {
      console.log('📋 供应商列表原始数据(直接数组):', response)
      dataList = response
      total = response.length
      pages = 1
    }
    // 如果是标准响应但没有找到列表
    else if (response && response.code === 0) {
      console.warn('⚠️ API响应成功但未找到列表数据:', response)
      dataList = []
    } else {
      console.warn('⚠️ API响应异常或列表为空:', response)
      dataList = []
    }

    // 更新分页状态
    totalPages.value = pages
    currentPage.value = page
    hasMore.value = page < Math.min(pages, props.maxPages)
    allDataLoaded.value = !hasMore.value

    console.log('📄 分页信息:', {
      currentPage: currentPage.value,
      totalPages: totalPages.value,
      hasMore: hasMore.value,
      maxPages: props.maxPages,
    })

    // 如果找到了数据，进行映射
    if (dataList.length > 0) {
      const mappedList = dataList.map((item: any) => ({
        value: item.custId,
        label: props.showField === 'custCode'
          ? `${item.custCode || ''} - ${item.custName || ''}`
          : item.custCode ? `${item.custName || ''} (${item.custCode})` : `${item.custName || ''}`,
        data: item,
      }))

      console.log('🔄 转换后的选项列表:', mappedList)

      // 如果是加载更多，追加到现有列表
      if (isLoadMore) {
        // 移除之前的特殊选项（如果有）
        const filteredList = custList.value.filter((item: any) => item.value !== 'load-more' && item.value !== 'no-more' && item.value !== 'loading')
        custList.value = [...filteredList, ...mappedList]
      } else {
        custList.value = mappedList
      }

      // 添加特殊选项
      if (props.enablePagination) {
        if (hasMore.value) {
          if (loadingMore.value) {
            // 加载中状态
            custList.value.push({
              value: 'loading',
              label: '加载中...',
              disabled: true,
              className: 'loading-option',
            })
          } else if (props.paginationMode === 'button') {
            // 按钮模式：添加加载更多选项
            custList.value.push({
              value: 'load-more',
              label: '点击加载更多',
              disabled: false,
              className: 'load-more-option',
            })
          }
        } else if (allDataLoaded.value && custList.value.length > 0) {
          // 已加载全部数据
          custList.value.push({
            value: 'no-more',
            label: '已加载全部数据',
            disabled: true,
            className: 'no-more-option',
          })
        }
      }

      console.log('✅ custList更新完成, 长度:', custList.value.length)
    } else {
      if (!isLoadMore) {
        custList.value = []
      }
      console.log('⚠️ 没有找到供应商数据', isLoadMore ? '不更新列表' : '清空选项列表')
    }
  } catch (error) {
    console.error('❌ 搜索供应商失败:', error)
    if (!isLoadMore) {
      custList.value = []
    }
  } finally {
    if (isLoadMore) {
      loadingMore.value = false
    } else {
      loading.value = false
    }
  }
}

// 使用debounce防止频繁请求
const debouncedSearch = debounce(searchCust, 300)

// 组件销毁时取消未完成的请求
onBeforeUnmount(() => {
  // 取消debounce中未执行的函数
  if (debouncedSearch.cancel) {
    debouncedSearch.cancel()
  }
})

// 处理搜索
const handleSearch = (value: string) => {
  console.log('🔎 用户输入搜索关键字:', value)
  searchKeyword.value = value

  // 如果禁用分页，使用前端过滤
  if (!props.enablePagination) {
    if (allDataLoaded.value) {
      // 如果已加载所有数据，直接过滤
      filterCustList(value)
    } else {
      // 如果尚未加载所有数据，先加载再过滤
      loadAllCustData().then(() => {
        filterCustList(value)
      })
    }
  } else {
    // 使用分页时，搜索时重置为第一页
    debouncedSearch(value, 1, false)
  }
}

// 处理选择
const handleSelect = (value: any, option: any) => {
  console.log('✓ 用户选择了值:', value, '选项:', option)

  // 处理特殊选项
  if (value === 'load-more') {
    // 阻止选择"加载更多"选项
    if (modelValue.value === 'load-more') {
      modelValue.value = undefined
    }

    // 触发加载更多
    loadMore()
    return
  }

  // 阻止选择其他特殊选项
  if (value === 'no-more' || value === 'loading') {
    if (modelValue.value === value) {
      modelValue.value = undefined
    }
    return
  }

  // 正常选择处理逻辑
}

// 处理清除
const handleClear = () => {
  console.log('🧹 用户清除了选择')

  // 清空搜索关键字
  searchKeyword.value = ''

  // 如果禁用分页，使用前端过滤显示所有数据
  if (!props.enablePagination) {
    if (allDataLoaded.value) {
      // 如果已加载所有数据，显示所有数据
      filterCustList('')
    } else {
      // 如果尚未加载所有数据，先加载所有数据
      loadAllCustData()
    }
  } else {
    // 使用分页时，重置分页状态
    currentPage.value = 1
    hasMore.value = true
    allDataLoaded.value = false

    // 重新加载第一页数据
    console.log('📥 清除后重新加载第一页数据')
    searchCust('', 1, false)
  }
}

// 加载更多数据
const loadMore = () => {
  if (loadingMore.value || !hasMore.value) return

  const nextPage = currentPage.value + 1
  if (nextPage <= Math.min(totalPages.value, props.maxPages)) {
    console.log('📥 加载更多数据, 页码:', nextPage)
    searchCust(searchKeyword.value, nextPage, true)
  } else {
    console.log('🛑 已达到最大页数限制:', props.maxPages)
    hasMore.value = false
    allDataLoaded.value = true
  }
}

// 处理下拉菜单滚动（使用防抖处理）
const handlePopupScroll = debounce((e: Event) => {
  if (!props.enablePagination || props.paginationMode !== 'scroll') return
  if (loadingMore.value || !hasMore.value) return

  const target = e.target as HTMLElement
  if (!target) return

  // 检查是否滚动到底部
  const { scrollTop, clientHeight, scrollHeight } = target
  if (scrollTop + clientHeight >= scrollHeight - 50) {
    // 距离底部50px时触发
    console.log('📜 滚动到底部，加载更多数据')
    loadMore()
  }
}, 100)

// 处理下拉框显示状态变化
const handleDropdownVisibleChange = (visible: boolean) => {
  console.log('🔽 下拉框显示状态变化:', visible)

  // 当下拉框打开且没有数据时，加载第一页数据
  if (visible && custList.value.length === 0) {
    console.log('📥 下拉框打开时没有数据，加载第一页数据')
    // 重置分页状态
    currentPage.value = 1
    hasMore.value = true
    allDataLoaded.value = false
    // 加载数据
    searchCust('', 1, false)
  }
}

// 监听modelValue变化
watch(modelValue, newVal => {
  console.log('📊 modelValue变化:', newVal)
})

// 监听custList变化
watch(
  custList,
  newVal => {
    console.log('📑 custList变化, 新长度:', newVal.length)
  },
  { deep: true }
)

// 调试方法：打印当前组件状态
const debugState = () => {
  console.group('🔍 ScmCustSelect组件状态')
  console.log('props:', {
    allowClear: props.allowClear,
    disabled: props.disabled,
    multiple: props.multiple,
    placeholder: props.placeholder,
    width: props.width,
    showField: props.showField,
    searchType: props.searchType,
    pageSize: props.pageSize,
    loadFirstPage: props.loadFirstPage,
    orgPartner: props.orgPartner,
  })
  console.log('modelValue:', modelValue.value)
  console.log('custList:', custList.value)
  console.log('custList长度:', custList.value.length)
  console.log('loading:', loading.value)
  console.log('searchKeyword:', searchKeyword.value)
  console.groupEnd()
}

// 初始化时加载数据
onMounted(() => {
  console.log('🚀 组件已挂载, loadFirstPage:', props.loadFirstPage)

  if (props.loadFirstPage) {
    if (!props.enablePagination) {
      // 如果禁用分页，加载所有数据
      console.log('📥 开始加载所有供应商数据')
      loadAllCustData()
    } else {
      // 使用分页时，加载第一页数据
      console.log('📥 开始加载第一页数据')
      searchCust()
    }
  }

  // 2秒后打印组件状态，确保数据已加载
  const debugTimer = setTimeout(() => {
    debugState()
  }, 2000)

  // 清理定时器
  onBeforeUnmount(() => {
    clearTimeout(debugTimer)
  })
})

// 暴露方法给父组件
defineExpose({
  searchCust,
  loadMore,
  loadAllCustData,
  filterCustList,
  debugState,
  // 暴露状态
  getCurrentPage: () => currentPage.value,
  getTotalPages: () => totalPages.value,
  getHasMore: () => hasMore.value,
  getAllDataLoaded: () => allDataLoaded.value,
  getAllCustData: () => allCustData.value,
})
</script>

<template>
  <div>
    <Spin :spinning="loading">
      <Select
        v-model:value="modelValue"
        :mode="props.multiple ? 'multiple' : undefined"
        :options="custList"
        :placeholder="placeholder"
        :disabled="disabled"
        :allow-clear="allowClear"
        :style="selectStyle"
        :show-search="true"
        :filter-option="false"
        :not-found-content="loading ? '加载中...' : custList.length === 0 ? (searchKeyword ? '未找到匹配结果' : '请输入关键字搜索') : ''"
        @search="handleSearch"
        @select="handleSelect"
        @clear="handleClear"
        @popupScroll="handlePopupScroll"
        @dropdownVisibleChange="handleDropdownVisibleChange"
      >
        <!-- 不使用自定义下拉菜单渲染，改为在选项列表中添加加载更多选项 -->
      </Select>
    </Spin>

    <!-- 调试信息，始终显示，但可以通过样式控制是否可见 -->
    <!-- <div class="debug-info" :style="{ display: isDev ? 'block' : 'none' }">
      <div style="font-size: 12px; color: #999; margin-top: 4px">
        <span>选项数量: {{ custList.length }}</span>
        <span style="margin-left: 8px">已选值: {{ modelValue }}</span>
        <span style="margin-left: 8px">页码: {{ currentPage }}/{{ totalPages }}</span>
        <span style="margin-left: 8px">{{ hasMore ? '有更多' : '无更多' }}</span>
      </div>
    </div> -->
  </div>
</template>

<style scoped>
/* 加载更多按钮样式 */
.load-more-wrapper {
  padding: 8px;
  text-align: center;
}

.load-more-divider {
  height: 1px;
  margin: 4px 0;
  background-color: #f0f0f0;
}

.load-more-button {
  color: #1890ff;
  cursor: pointer;
  padding: 4px 0;
  transition: all 0.3s;
}

.load-more-button:hover {
  background-color: #f5f5f5;
}

.load-more-end {
  color: #999;
  padding: 4px 0;
  font-size: 12px;
}

.load-more-loading {
  color: #1890ff;
  padding: 4px 0;
  font-size: 12px;
}

/* 全局样式，影响下拉选项 */
:global(.loading-option) {
  color: #1890ff;
  text-align: center;
  font-size: 12px;
  padding: 8px;
  background-color: #f5f5f5;
}

:global(.load-more-option) {
  color: #1890ff;
  text-align: center;
  font-size: 12px;
  padding: 8px;
  cursor: pointer;
  background-color: #f5f5f5;
}

:global(.load-more-option:hover) {
  background-color: #e6f7ff;
}

:global(.no-more-option) {
  color: #999;
  text-align: center;
  font-size: 12px;
  padding: 8px;
  background-color: #f5f5f5;
}
</style>
