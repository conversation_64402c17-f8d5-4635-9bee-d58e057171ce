<script setup lang="ts">
import { OrgSelect, OrgDoctor } from '@mh-hip/org'
import { Card, Typography, Divider, Switch, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { multipleUsage, importCode, packageJsonCode } from '../code/OrgDoctorCode'

const { Title } = Typography

// 选中的组织机构ID
const orgId = ref<number>()

// 是否多选
const isMultiple = ref(false)

// 多选值
const doctorIds = ref<string[]>([])
</script>

<template>
  <Card title="多选模式" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="12">
        <div>
          <div mb-8px>组织机构：</div>
          <OrgSelect v-model="orgId" style="width: 100%" />
          <div mt-8px>选中的组织机构ID: {{ orgId }}</div>
        </div>
      </Col>
      <Col :span="12">
        <div>
          <div mb-8px>
            <span mr-8px>多选医生：</span>
            <Switch v-model:checked="isMultiple" size="small" />
            <span ml-8px>{{ isMultiple ? '多选' : '单选' }}</span>
          </div>
          <OrgDoctor v-model="doctorIds" :orgId="orgId" :multiple="isMultiple" style="width: 100%" />
          <div mt-8px>选中的医生ID: {{ doctorIds }}</div>
        </div>
      </Col>
    </Row>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="multipleUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
