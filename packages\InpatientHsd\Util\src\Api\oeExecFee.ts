export function userPendingManualLsWithStartLineApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/userPendingManualLsWithStartLine', params, { appKey: 'inpatientHsd' })
}
export function oeExecFeeInfoApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/info', params, { appKey: 'inpatientHsd' })
}
export function oeFeeAddArtApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/addArt', params, { appKey: 'inpatientHsd' })
}
export function oeFeeChangeTotalApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/changeTotal', params, { appKey: 'inpatientHsd' })
}
export function oeFeeRefundLsApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/refundOeFeeLs', params, { appKey: 'inpatientHsd' })
}
export function artPriceApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/artPrice', params, { appKey: 'inpatientHsd' })
}
export function oeSameArtFeeLsApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/oeSameArtFeeLs', params, { appKey: 'inpatientHsd' })
}
export function oeABUPSumFeeLsApi(params: any): Promise<any> {
  return http.post('/hsd/oeExecFee/oeABUPSumFeeLs', params, { appKey: 'inpatientHsd' })
}
