export const basicUsage = {
  usage: `<template>
  <div>
    <!-- 品种选择录入组件 -->
    <div class="maker-dept-art-section">
      <h4>品种选择录入</h4>
      <p style="color: #666; margin-bottom: 16px;">
        支持品种搜索、数量输入和快速添加，适用于各种品种录入场景。
      </p>
      <MakerDeptArt
        :deptCode="deptCode"
        :searchType="searchType"
        @addArt="handleAddArt"
        ref="makerDeptArtRef"
      />
    </div>

    <!-- 已添加品种列表 -->
    <div class="added-items-section">
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <h4>已添加品种列表 ({{ addedItems.length }})</h4>
        <a-space>
          <a-button @click="setTestData" type="primary" ghost>加载测试数据</a-button>
          <a-button v-if="addedItems.length > 0" @click="clearAll" danger>清空所有</a-button>
        </a-space>
      </div>

      <a-table
        v-if="addedItems.length > 0"
        :dataSource="addedItems"
        :columns="columns"
        rowKey="id"
        :scroll="{ x: 1000 }"
        :pagination="{ pageSize: 10, showSizeChanger: true }"
      />

      <div v-else style="text-align: center; padding: 40px; color: #999;">
        <div style="margin-bottom: 16px;">暂无数据，请使用上方表单添加品种</div>
        <a-button @click="setTestData" type="primary" ghost>或点击加载测试数据</a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { message, Button, Space } from 'ant-design-vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

// 配置参数
const deptCode = ref('000013') // 部门编码
const searchType = ref(6) // 搜索类型：6-药品+耗材

// 组件引用
const makerDeptArtRef = ref()

// 已添加的品种列表
const addedItems = ref([])

// 添加品种回调
const handleAddArt = (artData) => {
  console.log('添加品种成功，表单数据：', artData)

  // 新增记录
  const newItem = {
    id: Date.now().toString(),
    ...artData,
    // 从artData中提取品种信息
    artName: artData.artName || '',
    artSpec: artData.artSpec || '',
    producer: artData.producer || '',
  }

  addedItems.value.push(newItem)
  message.success(\`成功添加品种: \${artData.artName}\`)
}

// 删除记录
const deleteRecord = (record) => {
  const index = addedItems.value.findIndex(item => item.id === record.id)
  if (index !== -1) {
    addedItems.value.splice(index, 1)
    message.success('删除成功')
  }
}

// 清空所有数据
const clearAll = () => {
  addedItems.value = []
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.clearForm()
  }
  message.success('已清空所有数据')
}

// 设置测试数据
const setTestData = () => {
  const testData = [
    {
      id: 'test1',
      artId: 1001,
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '哈药集团制药总厂',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 24,
      splittable: 1,
      totalPacks: 10,
      totalCells: 5
    },
    {
      id: 'test2',
      artId: 1002,
      artName: '头孢克肟胶囊',
      artSpec: '0.1g*12粒',
      producer: '石药集团',
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 12,
      splittable: 1,
      totalPacks: 5,
      totalCells: 8
    }
  ]
  addedItems.value = testData
  message.success('测试数据已加载')
}

// 表格列定义
const columns = [
  {
    title: '品名',
    dataIndex: 'artName',
    key: 'artName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 120,
  },
  {
    title: '生产厂家',
    dataIndex: 'producer',
    key: 'producer',
    width: 120,
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ record }) => {
      return \`\${record.totalPacks || 0} \${record.packUnit || ''}\`
    }
  },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    key: 'totalCells',
    width: 100,
    customRender: ({ record }) => {
      if (record.packCells && record.packCells > 1) {
        return \`\${record.totalCells || 0} \${record.cellUnit || ''}\`
      }
      return '-'
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    customRender: ({ record }) => {
      return h(Button, {
        type: 'link',
        size: 'small',
        danger: true,
        onClick: () => deleteRecord(record)
      }, '删除')
    }
  }
]
</script>

<style scoped>
.maker-dept-art-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.added-items-section {
  margin-top: 24px;
}
</style>

<!--
组件特性说明：
1. 支持部门编码配置，用于品种权限控制
2. 支持搜索类型配置：6-药品+耗材，1-药品，2-耗材
3. 自动验证品种选择和数量输入
4. 支持整包数量和拆零数量输入
5. 支持回车键快速添加和表单导航
6. 当品种信息为空时自动弹出机构商品设置窗口
-->`,
  importCode: `// 导入组件
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

// 或使用别名导入
import MakerDeptArt from '@/packages/Wm/Maker/src/makerDeptArt.vue'

// 使用组件
const makerDeptArtRef = ref()`,
  packageJson: `{
  "dependencies": {
    "@mh-wm/maker": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "ant-design-vue": "^4.0.0",
    "vue": "^3.0.0"
  }
}`
}

export const methodsUsage = {
  usage: `<template>
  <div>
    <!-- 品种选择录入组件 -->
    <MakerDeptArt
      :deptCode="deptCode"
      :searchType="6"
      @addArt="handleAddArt"
      ref="makerDeptArtRef"
    />

    <!-- 方法调用按钮 -->
    <div class="action-buttons">
      <a-space wrap>
        <a-button @click="clearForm">清空表单</a-button>
        <a-button @click="getFormData">获取表单数据</a-button>
        <a-button @click="validateForm">验证表单</a-button>
        <a-button @click="focusArtSelect">聚焦品种选择框</a-button>
      </a-space>

      <div style="margin-top: 16px;">
        <a-space wrap>
          <a-button @click="setFormData1" type="primary">设置表单数据（可拆零）</a-button>
          <a-button @click="setFormData2" type="primary">设置表单数据（不可拆零）</a-button>
          <a-button @click="setFormData3" type="primary">设置表单数据（注射剂）</a-button>
        </a-space>
      </div>
    </div>

    <!-- 方法说明 -->
    <div class="methods-description">
      <h4>可用方法说明：</h4>
      <ul>
        <li><code>clearForm()</code> - 清空表单数据，重置所有输入字段</li>
        <li><code>getFormData()</code> - 获取当前表单数据，返回包含品种信息和数量的对象</li>
        <li><code>setFormData(data)</code> - 设置表单数据，用于编辑时回填数据</li>
        <li><code>validateForm()</code> - 验证表单数据是否有效，检查必填项</li>
        <li><code>focusArtSelect()</code> - 聚焦到品种选择框，便于用户操作</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

const deptCode = ref('000013')
const makerDeptArtRef = ref()

// 添加品种回调
const handleAddArt = (artData) => {
  console.log('添加品种成功:', artData)
  message.success(\`品种 "\${artData.artName}" 添加成功\`)
}

// 清空表单
const clearForm = () => {
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.clearForm()
    message.success('表单已清空')
  } else {
    message.error('组件引用不存在')
  }
}

// 获取表单数据
const getFormData = () => {
  if (makerDeptArtRef.value) {
    const data = makerDeptArtRef.value.getFormData()
    console.log('当前表单数据:', data)
    if (data && data.artData) {
      message.success('已获取表单数据，请查看控制台')
    } else {
      message.warning('当前表单为空或未选择品种')
    }
    return data
  } else {
    message.error('组件引用不存在')
  }
}

// 验证表单
const validateForm = () => {
  if (makerDeptArtRef.value) {
    const isValid = makerDeptArtRef.value.validateForm()
    if (isValid) {
      message.success('表单验证通过！')
    } else {
      message.error('表单验证失败，请检查必填项')
    }
    return isValid
  } else {
    message.error('组件引用不存在')
  }
}

// 聚焦到品种选择框
const focusArtSelect = () => {
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.focusArtSelect()
    message.info('已聚焦到品种选择框')
  } else {
    message.error('组件引用不存在')
  }
}

// 设置表单数据1（可拆零药品）
const setFormData1 = () => {
  if (makerDeptArtRef.value) {
    const mockData = {
      artData: {
        artId: 1001,
        artName: '阿莫西林胶囊',
        artSpec: '0.25g*24粒',
        producer: '哈药集团制药总厂',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 24,
        splittable: 1
      },
      totalPacks: 10,
      totalCells: 5
    }
    makerDeptArtRef.value.setFormData(mockData)
    message.success('表单数据已设置（阿莫西林胶囊）')
  }
}

// 设置表单数据2（不可拆零）
const setFormData2 = () => {
  if (makerDeptArtRef.value) {
    const mockData = {
      artData: {
        artId: 1003,
        artName: '胰岛素注射液',
        artSpec: '3ml:300IU',
        producer: '诺和诺德',
        packUnit: '支',
        cellUnit: '支',
        packCells: 1,
        splittable: 0
      },
      totalPacks: 20
    }
    makerDeptArtRef.value.setFormData(mockData)
    message.success('表单数据已设置（胰岛素注射液，不可拆零）')
  }
}

// 设置表单数据3（注射剂）
const setFormData3 = () => {
  if (makerDeptArtRef.value) {
    const mockData = {
      artData: {
        artId: 1004,
        artName: '生理氯化钠注射液',
        artSpec: '250ml',
        producer: '科伦药业',
        packUnit: '袋',
        cellUnit: '袋',
        packCells: 1,
        splittable: 0
      },
      totalPacks: 50
    }
    makerDeptArtRef.value.setFormData(mockData)
    message.success('表单数据已设置（生理氯化钠注射液）')
  }
}
</script>

<style scoped>
.action-buttons {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.methods-description {
  margin-top: 20px;
  padding: 16px;
  background: #fff7e6;
  border-radius: 6px;
  border: 1px solid #ffd591;
}

.methods-description ul {
  margin: 8px 0;
  padding-left: 20px;
}

.methods-description li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.methods-description code {
  background: #f6f8fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'SFMono-Regular', Consolas, monospace;
  color: #d73a49;
}
</style>`,
  importCode: `// 组件方法调用示例
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

const makerDeptArtRef = ref()

// 通过 ref 调用组件方法
makerDeptArtRef.value?.clearForm()
makerDeptArtRef.value?.getFormData()
makerDeptArtRef.value?.validateForm()
makerDeptArtRef.value?.focusArtSelect()
makerDeptArtRef.value?.setFormData(data)`,
  packageJson: `{
  "dependencies": {
    "@mh-wm/maker": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "ant-design-vue": "^4.0.0",
    "vue": "^3.0.0"
  },
  "devDependencies": {
    "@types/vue": "^3.0.0",
    "typescript": "^5.0.0"
  }
}`
}

export const fullExample = {
  usage: `<template>
  <div class="maker-dept-art-demo">
    <!-- 组件使用 -->
    <MakerDeptArt
      :deptCode="deptCode"
      :searchType="searchType"
      @addArt="handleAddArt"
      ref="makerDeptArtRef"
    />

    <!-- 操作按钮 -->
    <div class="actions">
      <a-button @click="clearForm">清空表单</a-button>
      <a-button @click="getFormData">获取数据</a-button>
      <a-button @click="validateForm">验证表单</a-button>
      <a-button @click="setExampleData">设置示例数据</a-button>
    </div>

    <!-- 结果展示 -->
    <div class="results">
      <h3>添加的品种列表</h3>
      <a-table
        :columns="columns"
        :dataSource="addedArts"
        :pagination="false"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

// 配置
const deptCode = ref('000013')
const searchType = ref(6)
const makerDeptArtRef = ref()

// 数据
const addedArts = ref([])

// 表格列定义
const columns = [
  { title: '品种名称', dataIndex: 'artName', key: 'artName' },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec' },
  { title: '生产厂家', dataIndex: 'producer', key: 'producer' },
  { title: '整包数量', dataIndex: 'totalPacks', key: 'totalPacks' },
  { title: '拆零数量', dataIndex: 'totalCells', key: 'totalCells' }
]

// 事件处理
const handleAddArt = (artData) => {
  addedArts.value.push(artData)
  message.success(\`成功添加品种: \${artData.artName}\`)
}

// 方法
const clearForm = () => {
  makerDeptArtRef.value?.clearForm()
}

const getFormData = () => {
  const data = makerDeptArtRef.value?.getFormData()
  console.log('表单数据:', data)
}

const validateForm = () => {
  const isValid = makerDeptArtRef.value?.validateForm()
  message.info(\`验证结果: \${isValid ? '通过' : '失败'}\`)
}

const setExampleData = () => {
  const exampleData = {
    artData: {
      artId: 12345,
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '石药集团'
    },
    totalPacks: 5,
    totalCells: 10
  }
  makerDeptArtRef.value?.setFormData(exampleData)
}
</script>

<style scoped>
.maker-dept-art-demo {
  padding: 20px;
}

.actions {
  margin: 16px 0;
}

.actions .ant-btn {
  margin-right: 8px;
}

.results {
  margin-top: 20px;
}
</style>`,
  importCode: `// 完整示例导入
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

// 类型定义
interface ArtData {
  artId: number
  artName: string
  artSpec: string
  producer: string
  totalPacks: number
  totalCells: number
}`,
  packageJson: `{
  "name": "maker-dept-art-example",
  "version": "1.0.0",
  "dependencies": {
    "@mh-wm/maker": "^1.0.0",
    "ant-design-vue": "^4.0.0",
    "vue": "^3.0.0"
  }
}`
}

export const publishCommands = `# 在项目根目录下执行以下命令打包并发布组件

# 正式版本
pnpm publish:component Wm/Maker

# 测试版本
pnpm publish:test-component Wm/Maker

# 开发版本
pnpm publish:dev-component Wm/Maker

# 安装组件
pnpm add @mh-wm/maker`

export const buildProcess = `打包命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库`

// 独立的导入代码示例
export const importCode = `// 导入组件
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

// 或使用别名导入
import MakerDeptArt from '@/packages/Wm/Maker/src/makerDeptArt.vue'

// 使用组件
const makerDeptArtRef = ref()

// 组件方法调用
makerDeptArtRef.value?.clearForm()
makerDeptArtRef.value?.getFormData()
makerDeptArtRef.value?.validateForm()`

// 独立的 package.json 配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/maker": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "ant-design-vue": "^4.0.0",
    "vue": "^3.0.0"
  },
  "devDependencies": {
    "@types/vue": "^3.0.0",
    "typescript": "^5.0.0"
  }
}`

// Props 和 Events 使用示例
export const propsAndEvents = {
  usage: `<template>
  <div>
    <!-- 基础使用 -->
    <MakerDeptArt
      :deptCode="deptCode"
      :searchType="searchType"
      @addArt="handleAddArt"
      ref="makerDeptArtRef"
    />

    <!-- 不同搜索类型示例 -->
    <div class="search-type-examples">
      <h4>不同搜索类型示例：</h4>

      <!-- 只搜索药品 -->
      <div class="example-item">
        <h5>只搜索药品 (searchType: 1)</h5>
        <MakerDeptArt
          :deptCode="deptCode"
          :searchType="1"
          @addArt="handleAddDrug"
          ref="drugRef"
        />
      </div>

      <!-- 只搜索耗材 -->
      <div class="example-item">
        <h5>只搜索耗材 (searchType: 2)</h5>
        <MakerDeptArt
          :deptCode="deptCode"
          :searchType="2"
          @addArt="handleAddMaterial"
          ref="materialRef"
        />
      </div>

      <!-- 搜索药品+耗材 -->
      <div class="example-item">
        <h5>搜索药品+耗材 (searchType: 6)</h5>
        <MakerDeptArt
          :deptCode="deptCode"
          :searchType="6"
          @addArt="handleAddAll"
          ref="allRef"
        />
      </div>
    </div>

    <!-- 不同部门示例 -->
    <div class="dept-examples">
      <h4>不同部门示例：</h4>

      <div class="example-item">
        <h5>药房 (deptCode: '000013')</h5>
        <MakerDeptArt
          deptCode="000013"
          :searchType="6"
          @addArt="handlePharmacyAdd"
        />
      </div>

      <div class="example-item">
        <h5>手术室 (deptCode: '000020')</h5>
        <MakerDeptArt
          deptCode="000020"
          :searchType="6"
          @addArt="handleOperatingRoomAdd"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import MakerDeptArt from '@packages/Wm/Maker/src/makerDeptArt.vue'

// 配置参数
const deptCode = ref('000013') // 部门编码
const searchType = ref(6) // 搜索类型

// 组件引用
const makerDeptArtRef = ref()
const drugRef = ref()
const materialRef = ref()
const allRef = ref()

// 通用添加品种回调
const handleAddArt = (artData) => {
  console.log('添加品种:', artData)
  message.success(\`成功添加品种: \${artData.artName}\`)
}

// 药品添加回调
const handleAddDrug = (artData) => {
  console.log('添加药品:', artData)
  message.success(\`成功添加药品: \${artData.artName}\`, 2)
}

// 耗材添加回调
const handleAddMaterial = (artData) => {
  console.log('添加耗材:', artData)
  message.success(\`成功添加耗材: \${artData.artName}\`, 2)
}

// 药品+耗材添加回调
const handleAddAll = (artData) => {
  console.log('添加品种（药品+耗材）:', artData)
  message.success(\`成功添加: \${artData.artName}\`, 2)
}

// 药房添加回调
const handlePharmacyAdd = (artData) => {
  console.log('药房添加品种:', artData)
  message.success(\`药房成功添加: \${artData.artName}\`, 2)
}

// 手术室添加回调
const handleOperatingRoomAdd = (artData) => {
  console.log('手术室添加品种:', artData)
  message.success(\`手术室成功添加: \${artData.artName}\`, 2)
}
</script>

<style scoped>
.search-type-examples,
.dept-examples {
  margin: 24px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.example-item {
  margin-bottom: 20px;
  padding: 16px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.example-item:last-child {
  margin-bottom: 0;
}

.example-item h5 {
  margin: 0 0 12px 0;
  color: #495057;
  font-weight: 600;
}
</style>

<!--
Props 说明：
- deptCode: string - 部门编码，用于品种权限控制
- searchType: number - 搜索类型，1-药品，2-耗材，6-药品+耗材

Events 说明：
- addArt: (artData) => void - 添加品种事件，返回品种数据

artData 数据结构：
{
  artId: number,           // 品种ID
  artName: string,         // 品种名称
  artSpec: string,         // 规格
  producer: string,        // 生产厂家
  packUnit: string,        // 包装单位
  cellUnit: string,        // 拆零单位
  packCells: number,       // 包装数量
  splittable: number,      // 是否可拆零 (0-不可拆零, 1-可拆零)
  totalPacks: number,      // 整包数量
  totalCells: number,      // 拆零数量
  artData: object         // 完整品种数据
}
-->`,
  importCode: `// Props 和 Events 类型定义
interface MakerDeptArtProps {
  deptCode: string    // 部门编码，必填
  searchType: number  // 搜索类型，1-药品，2-耗材，6-药品+耗材
}

interface MakerDeptArtEvents {
  addArt: (artData: ArtData) => void
}

interface ArtData {
  artId: number
  artName: string
  artSpec: string
  producer: string
  packUnit: string
  cellUnit: string
  packCells: number
  splittable: number
  totalPacks: number
  totalCells: number
  artData: object
}`,
  packageJson: `{
  "dependencies": {
    "@mh-wm/maker": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "ant-design-vue": "^4.0.0",
    "vue": "^3.0.0"
  }
}`
}
