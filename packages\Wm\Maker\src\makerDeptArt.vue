<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { Button, Form, FormItem, InputNumber } from 'ant-design-vue'
import { WmDeptArtSelect } from '@mh-inpatient-hsd/selector'


const searchFormModel =  reactive({
packTotal:undefined,
packUnit:'',
splittable:undefined,
packCells:undefined,
cellTotal:'',
cellUnit:'',
})
// 定义props
const props = defineProps({
  deptCode: {
    type: String,
    default: '',
  },
  // 添加搜索类型支持
  searchType: {
    type: Number,
    default: 6, // 默认为药品+耗材
  },
})

// 定义组件事件
const emit = defineEmits(['addArt'])

// 表单数据
const formState = reactive({
  deptCode: props.deptCode || '',
  artIds: [] as number[],
})

// 组件引用
const artSelectRef = ref()
const packTotalRef = ref()
const cellTotalRef = ref()

// 选中的品种数据缓存
const selectedArtData = ref(null)

// 处理品种选择 - 完全参考 clinics-wm\scm-bill\scm-bill-dept-form.vue 的 handleArtSelect 方法
const handleArtSelect = async (selectedArt: any) => {
  if (selectedArt) {
    // 直接将选中的品种数据赋值给 selectedArtData，参考原文件逻辑
    selectedArtData.value = selectedArt

    // 更新表单模型中的包装信息
    searchFormModel.packUnit = selectedArt.packUnit || ''
    searchFormModel.cellUnit = selectedArt.cellUnit || ''
    searchFormModel.splittable = selectedArt.splittable || 0
    searchFormModel.packCells = selectedArt.packCells || 1

    // 清空数量输入框，参考原文件的逻辑：packTotal = null, cellTotal = null
    searchFormModel.packTotal = null
    searchFormModel.cellTotal = null

    // 聚焦到整包数量输入框
    nextTick(() => {
      packTotalRef.value?.$el.querySelector('input')?.focus()
    })
  } else {
    // 如果没有选中品种，重新初始化选择器，参考原文件逻辑
    artSelectRef.value?.init()
  }
}

// 处理整包数量回车事件 - 完全参考原文件的 handlePressPackTotal 方法
const handlePressPackTotal = () => {
  if (searchFormModel.splittable === 1 && searchFormModel.packCells > 1) {
    // 如果支持拆零且包装数大于1，聚焦到拆零数量输入框
    nextTick(() => {
      cellTotalRef.value?.$el.querySelector('input')?.focus()
    })
  } else {
    // 否则直接添加品种，参考原文件使用 nextTick 包装
    nextTick(() => {
      onAddArt()
    })
  }
}

// 聚焦到下一个输入框
const focusNext = (refName: string) => {
  nextTick(() => {
    if (refName === 'packTotalRef') {
      packTotalRef.value?.$el.querySelector('input')?.focus()
    } else if (refName === 'cellTotalRef') {
      cellTotalRef.value?.$el.querySelector('input')?.focus()
    }
  })
}

// 添加品种 - 完全参考原文件的 onAddArt 方法逻辑
const onAddArt = async () => {
  // 验证是否选择了品种，参考原文件使用 artId 验证
  if (!selectedArtData.value?.artId) {
    message.error('请选择商品')
    return
  }

  // 验证数量是否为空，参考原文件的验证逻辑
  if (!searchFormModel.packTotal && !searchFormModel.cellTotal) {
    message.error('请输入整包数量或拆零数量')
    return
  }

  // 构建表单数据，完全参考原文件的数据结构
  const formData = {
    ...selectedArtData.value,
    packTotal: searchFormModel.packTotal,
    cellTotal: searchFormModel.cellTotal,
    packUnit: searchFormModel.packUnit,
    cellUnit: searchFormModel.cellUnit,
    splittable: searchFormModel.splittable,
    packCells: searchFormModel.packCells,
  }

  // 发射事件，返回表单数据
  emit('addArt', formData)

  // 清空表单，参考原文件调用 cleanArtForm
  cleanArtForm()
}

// 清空表单方法 - 完全参考原文件的 cleanArtForm 方法
const cleanArtForm = () => {
  // 清空 selectedArtData，参考原文件将 searchFormModel.value = {}
  selectedArtData.value = null

  // 清空表单数据
  searchFormModel.packTotal = null
  searchFormModel.cellTotal = null
  searchFormModel.packUnit = ''
  searchFormModel.cellUnit = ''
  searchFormModel.splittable = undefined
  searchFormModel.packCells = undefined

  // 重新初始化选择器并聚焦，完全参考原文件逻辑
  artSelectRef.value?.init()
  artSelectRef.value?.focus()
}

// 兼容性方法，供外部调用
const clearForm = () => {
  cleanArtForm()
}

// 获取表单数据方法
const getFormData = () => {
  if (!selectedArtData.value?.artId) {
    return null
  }

  return {
    artData: selectedArtData.value,
    packTotal: searchFormModel.packTotal,
    cellTotal: searchFormModel.cellTotal,
    packUnit: searchFormModel.packUnit,
    cellUnit: searchFormModel.cellUnit,
    splittable: searchFormModel.splittable,
    packCells: searchFormModel.packCells,
    artId: selectedArtData.value.artId,
    artName: selectedArtData.value.artName,
    artSpec: selectedArtData.value.artSpec,
    producer: selectedArtData.value.producer
  }
}

// 设置表单数据方法（用于编辑时回填数据）
const setFormData = (data: any) => {
  if (!data) return

  console.log('setFormData 接收到的数据:', data)

  // 设置品种数据
  if (data.artData) {
    selectedArtData.value = data.artData
  } else if (data.artId) {
    // 如果直接传入的是包含artId的数据，构建artData
    selectedArtData.value = {
      artId: data.artId,
      artName: data.artName,
      artSpec: data.artSpec,
      producer: data.producer,
      packUnit: data.packUnit,
      cellUnit: data.cellUnit,
      packCells: data.packCells,
      splittable: data.splittable
    }
  }

  // 设置表单数据
  searchFormModel.packTotal = data.packTotal || data.totalPacks || null
  searchFormModel.cellTotal = data.cellTotal || data.totalCells || null
  searchFormModel.packUnit = data.packUnit || ''
  searchFormModel.cellUnit = data.cellUnit || ''
  searchFormModel.splittable = data.splittable || 0
  searchFormModel.packCells = data.packCells || 1

  console.log('设置后的 selectedArtData:', selectedArtData.value)
  console.log('设置后的 searchFormModel:', searchFormModel)

  // 设置品种选择组件的显示值
  if (artSelectRef.value && selectedArtData.value) {
    console.log('准备设置 WmDeptArtSelect 的值:', selectedArtData.value)
    console.log('artSelectRef.value:', artSelectRef.value)

    nextTick(() => {
      // 使用WmDeptArtSelect组件的setValue方法
      if (typeof artSelectRef.value.setValue === 'function') {
        console.log('调用 WmDeptArtSelect 的 setValue 方法，传入数据:', selectedArtData.value)
        artSelectRef.value.setValue(selectedArtData.value)
        console.log('成功调用 WmDeptArtSelect 的 setValue 方法')
      } else {
        console.warn('WmDeptArtSelect 组件没有 setValue 方法')
        console.log('可用的方法:', Object.keys(artSelectRef.value))
      }
    })
  } else {
    console.warn('artSelectRef 或 selectedArtData 不存在')
    console.log('artSelectRef.value:', artSelectRef.value)
    console.log('selectedArtData.value:', selectedArtData.value)
  }
}

// 验证表单方法
const validateForm = () => {
  if (!selectedArtData.value?.artId) {
    message.error('请选择商品')
    return false
  }

  if (!searchFormModel.packTotal && !searchFormModel.cellTotal) {
    message.error('请输入整包数量或拆零数量')
    return false
  }

  return true
}

// 聚焦到品种选择框
const focusArtSelect = () => {
  nextTick(() => {
    artSelectRef.value?.focus()
  })
}

// 暴露方法给父组件调用
defineExpose({
  clearForm,
  getFormData,
  setFormData,
  validateForm,
  focusArtSelect,
  onAddArt
})
</script>

<template>
  <Form layout="inline" :model="searchFormModel">
    <FormItem label="品种查询" name="keyword">
      <div class="art-select-box">
        <WmDeptArtSelect
          ref="artSelectRef"
          :deptCode="formState.deptCode"
          :searchType="props.searchType"
          @selected="handleArtSelect"
          style="width: 300px"
          placeholder="请选择品种"
          @keydown.enter="focusNext('packTotalRef')"
        />
      </div>
    </FormItem>
    <FormItem label="整包数量" name="packTotal">
          <InputNumber ref="packTotalRef" v-model:value="searchFormModel.packTotal" :min="0" :precision="0"
            :addon-after="searchFormModel.packUnit" style="width: 150px" @pressEnter="handlePressPackTotal" />
        </FormItem>
        <FormItem label="拆零数量" name="cellTotal"
          v-if="searchFormModel.splittable === 1 && searchFormModel.packCells > 1">
          <InputNumber ref="cellTotalRef" v-model:value="searchFormModel.cellTotal" style="width: 150px" :min="0"
            :addon-after="searchFormModel.cellUnit" @pressEnter="onAddArt" />
        </FormItem>
    <FormItem>
      <Button type="primary" @click="onAddArt">添加</Button>
    </FormItem>
  </Form>
</template>

<style scoped>
.art-select-box {
  display: inline-block;
}

/* 优化表单布局 */
:deep(.ant-form-item) {
  margin-bottom: 8px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

/* 输入框样式优化 */
:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-input-number-input) {
  text-align: right;
}

/* 按钮样式 */
:deep(.ant-btn) {
  font-weight: 500;
}
</style>

