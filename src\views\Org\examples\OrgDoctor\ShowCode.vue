<script setup lang="ts">
import { OrgSelect, OrgDoctor } from '@mh-hip/org'
import { Card, Typography, Divider, Switch, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { showCodeUsage, importCode, packageJsonCode } from '../code/OrgDoctorCode'

const { Title } = Typography

// 选中的组织机构ID
const orgId = ref<number>()

// 选中的医生ID
const doctorId = ref<number>()

// 是否显示医师编码
const showCode = ref(true)
</script>

<template>
  <Card title="医师编码显示控制" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="12">
        <div>
          <div mb-8px>组织机构：</div>
          <OrgSelect v-model="orgId" style="width: 100%" />
          <div mt-8px>选中的组织机构ID: {{ orgId }}</div>
        </div>
      </Col>
      <Col :span="12">
        <div>
          <div mb-8px>
            <span mr-8px>医生（</span>
            <Switch v-model:checked="showCode" size="small" />
            <span ml-8px>{{ showCode ? '显示' : '不显示' }}医师编码）</span>
          </div>
          <OrgDoctor v-model="doctorId" :orgId="orgId" :showCode="showCode" style="width: 100%" />
          <div mt-8px>选中的医生ID: {{ doctorId }}</div>
        </div>
      </Col>
    </Row>

    <div class="tip-text" mb-16px>
      <i class="tip-icon">i</i>
      由于医保光标原因，在一些强贯标要求功能中选择医师时，可以打开这个标志，让选择人第一时间知道医师是否已经维护贯标医师编码
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="showCodeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
