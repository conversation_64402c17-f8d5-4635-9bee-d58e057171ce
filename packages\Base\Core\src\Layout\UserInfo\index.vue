<script lang="ts" setup>
import { LogoutOutlined, UserOutlined } from '@ant-design/icons-vue'
import { clearAllCaches, clearAllCookies, Data, http, login, Modal, useCache, useLoading } from '@idmy/core'

import { Avatar, Popover, RadioButton, RadioGroup } from 'ant-design-vue'
import { ref } from 'vue'
import { changePrimaryColor, Currents } from '../../../'
import { LAYOUT_CACHE_KEY } from '../index'
import ResetPassword from '../ResetPassword/index.vue'

const getUser = (userId: number): Promise<Data> => http.post<Data>('/idm/user/info', { userId }, { appKey: 'idm' })

const currentLayout = useCache(LAYOUT_CACHE_KEY, 'default', -1, { global: true, never: true })

const user = ref<Data>({
  loginName: undefined,
})
useLoading(async () => {
  user.value = await getUser(Currents.id)
}, true)

const onLogout = () => {
  clearAllCookies()
  clearAllCaches()
  login.logout()
}

const openResetPassword = () => {
  Modal.open({
    component: ResetPassword,
    width: 2,
    title: '修改密码',
  })
}

const primaryColor = ref(localStorage.getItem('app-primary-color') ?? '#165DFF')
watch(
  () => primaryColor.value,
  color => {
    changePrimaryColor(color)
  }
)
</script>
<template>
  <Popover :arrow="false" :mouseEnterDelay="0" overlayClassName="user-popover-overlay" placement="bottomRight" trigger="hover">
    <template #content>
      <div class="user-dropdown-content">
        <div class="user-info-header">
          <Avatar :size="32" class="user-avatar-large">
            <template #icon>
              <UserOutlined />
            </template>
          </Avatar>
          <div class="user-info">
            <div class="user-name">{{ user.loginName }}</div>
            <div class="user-id">用户ID：{{ Currents.id }}</div>
          </div>
        </div>
        <div class="divider"></div>
        <div flex justify-center>
          <RadioGroup size="small" button-style="solid" v-model:value="primaryColor">
            <RadioButton value="#DF2A3F">红</RadioButton>
            <RadioButton value="#ED740C">橙</RadioButton>
            <RadioButton value="#00A13B">绿</RadioButton>
            <RadioButton value="#009B9B">青</RadioButton>
            <RadioButton value="#165DFF">蓝</RadioButton>
            <RadioButton value="#7E45E8">紫</RadioButton>
            <RadioButton value="#D78AB1">粉</RadioButton>
          </RadioGroup>
        </div>
        <div class="divider"></div>
        <div class="dropdown-list">
          <a class="dropdown-item" @click="openResetPassword">
            <UserOutlined />
            <span>修改密码</span>
          </a>
          <div class="divider"></div>
          <a class="dropdown-item" @click="onLogout">
            <LogoutOutlined />
            <span>退出登录</span>
          </a>
        </div>
      </div>
    </template>

    <a class="user-dropdown-link">
      <span class="username">{{ user.loginName }}</span>
      <Avatar :size="28" class="user-avatar">
        <template #icon>
          <UserOutlined />
        </template>
      </Avatar>
    </a>
  </Popover>
</template>
<style lang="less" scoped>
.user-dropdown-link {
  display: flex;
  align-items: center;
  height: 100%;
  padding-left: 12px;
  cursor: pointer;
  transition: all 0.3s;
  color: #fff;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.username {
  margin-right: 8px;
  font-size: 14px;
}

.user-avatar {
  background-color: rgba(255, 255, 255, 0.2);
  width: 44px;
  height: 44px;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-dropdown-content {
  width: 240px;

  .user-info-header {
    display: flex;
    align-items: flex-start;
    padding: 8px;

    .user-avatar-large {
      background-color: var(--primary-color);
      margin-right: 12px;
    }

    .user-info {
      flex: 1;
      overflow: hidden;

      .user-name {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        font-size: 14px;
        line-height: 1.5;
      }

      .user-id {
        color: rgba(0, 0, 0, 0.45);
        font-size: 12px;
        line-height: 1.5;
      }
    }
  }

  .divider {
    height: 1px;
    background: #f0f0f0;
    margin: 4px 0;
  }

  .dropdown-list {
    .dropdown-item {
      display: flex;
      align-items: center;
      padding: 8px;
      color: rgba(0, 0, 0, 0.85);
      transition: all 0.3s;
      cursor: pointer;
      font-size: 14px;

      span {
        margin-left: 4px;
      }

      &:hover {
        background: #f5f5f5;
      }
    }
  }
}
</style>
