import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { index as VisitInfo } from '@mh-inpatient-hsd/visit-info'
import '@mh-inpatient-hsd/visit-info/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/visit-info": "^1.0.0",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <VisitInfo :visit-id="visitId" :patient-info="patientInfo" />
</template>

<script setup>
import { index as VisitInfo } from '@mh-inpatient-hsd/visit-info'
import '@mh-inpatient-hsd/visit-info/index.css'
import { ref } from 'vue'

// 模拟数据
const visitId = 84503
const patientInfo = {
  visitId: 84503,
  patientId: 'P000123456',
  patientName: '张三',
  gender: '男',
  age: '45岁',
  bedNo: '1',
  sectionName: '内三科住院',
  admissionDate: '2025-03-14',
  diagnosisName: '高血压',
  doctorName: '李医生'
}
</script>`)
