# @mh-hsd/base

HSD业务域基础组件包

## 概述

本包提供了HSD（医院信息系统）业务域的核心基础组件，专注于处方相关功能的展示和交互。包含完整的处方阅读、显示和打印功能，支持西药、中药、治疗等多种处方类型。

## 功能特性

- 📋 **处方展示**：支持西药、中药、治疗处方的完整展示
- 🔄 **多种模式**：支持弹窗模式和内联模式两种显示方式
- 🎨 **专业样式**：符合医疗行业标准的处方单样式设计
- 💊 **药品详情**：完整的药品信息、用法用量、价格计算
- 🖋️ **电子签名**：支持医师电子签名显示
- ⚡ **Vue 3**：基于Vue 3 + TypeScript + Composition API

## 组件列表

### BaseReadRecipe 处方阅读组件

医疗处方的展示组件，支持完整的处方信息显示，包括患者信息、诊断信息、药品清单等。

#### 核心子组件
- **index.vue**：主容器组件，控制显示模式和弹窗逻辑
- **recipe.vue**：处方详情组件，负责具体的处方内容渲染

## 安装

```bash
# 使用 npm
npm install @mh-hsd/base

# 使用 yarn
yarn add @mh-hsd/base

# 使用 pnpm
pnpm add @mh-hsd/base
```


## 使用方法

### 引入组件

```typescript
import { BaseReadRecipe } from '@mh-hsd/base'
```

### 弹窗模式（默认）

```vue
<template>
  <div>
    <a-button type="primary" @click="openRecipe">查看处方</a-button>

    <BaseReadRecipe
      ref="recipeRef"
      type="modal"
      :recipe="recipeData"
      :modalWidth="800"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BaseReadRecipe } from '@mh-hsd/base'

const recipeRef = ref()

// 示例处方数据
const recipeData = ref({
  recipeId: 1,
  rxNo: 'RX202412190001',
  patientName: '张三',
  patientGenderName: '男',
  ageOfYears: 35,
  ageOfDays: null,
  applyDeptname: '内科',
  clinicianName: '李医生',
  diseaseDiagName: '感冒',
  recipeTypeId: 1, // 1-西药 2-中药 4-治疗
  timeCreated: new Date(),
  pastHistory: '无',
  allergicHistory: '无',
  recipeGroupLs: [
    {
      groupNo: 1,
      routeName: '口服',
      freqCode: 'BID',
      notice: '饭后服用',
      recipeDetailLs: [
        {
          artId: 1,
          artName: '阿莫西林胶囊',
          artSpec: '0.25g',
          mealCells: 2,
          cellUnit: '粒',
          total: 24,
          unit: '粒',
          producer: '某某制药厂',
          amount: 15.60
        }
      ]
    }
  ]
})

const openRecipe = () => {
  recipeRef.value?.openModal()
}
</script>
```

### 内联模式

```vue
<template>
  <div>
    <BaseReadRecipe
      type="inline"
      :recipe="recipeData"
    />
  </div>
</template>

<script setup lang="ts">
import { BaseReadRecipe } from '@mh-hsd/base'

// 处方数据...
const recipeData = ref({ /* 同上 */ })
</script>
```

## API 文档

### BaseReadRecipe Props

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 显示模式 | 'modal' \| 'inline' | 'modal' |
| recipe | 处方数据对象 | RecipeData | - |
| modalWidth | 弹窗模式下的宽度 | number \| string | 760 |
| filePath | 文件路径前缀（用于图片显示） | string | '/UserFiles' |

### BaseReadRecipe Methods

| 方法名 | 说明 | 参数 | 返回值 |
| --- | --- | --- | --- |
| openModal | 打开弹窗（仅在modal模式下有效） | - | void |

### RecipeData 数据结构

```typescript
interface RecipeData {
  recipeId: number                    // 处方ID
  rxNo: string                        // 处方号
  recipeTypeId: number                // 处方类型：1-西药 2-中药 4-治疗
  patientName: string                 // 患者姓名
  patientGenderName: string           // 患者性别
  ageOfYears?: number                 // 年龄（年）
  ageOfDays?: number                  // 年龄（天）
  applyDeptname: string               // 科室名称
  clinicianName: string               // 医生姓名
  diseaseDiagName: string             // 主要诊断
  symptomDiagName?: string            // 症状诊断（中药处方）
  timeCreated: Date                   // 创建时间
  pastHistory?: string                // 既往史
  allergicHistory?: string            // 过敏史
  times?: number                      // 剂数（中药）
  amount?: number                     // 单次金额
  totalAmount?: number                // 总金额
  timeSigned?: Date                   // 签名时间
  signatureUrl?: string               // 签名图片URL
  catColorCode?: string               // 处方分类颜色
  catMark?: string                    // 处方分类标记
  recipeGroupLs: RecipeGroup[]        // 处方组列表
}

interface RecipeGroup {
  groupNo: number                     // 组号
  routeName: string                   // 用法
  freqCode: string                    // 频次代码
  freqName?: string                   // 频次名称
  notice?: string                     // 注意事项
  dpm?: string                        // 煎药方法
  recipeDetailLs: RecipeDetail[]      // 药品明细列表
}

interface RecipeDetail {
  artId: number                       // 药品ID
  artName: string                     // 药品名称
  artSpec?: string                    // 规格
  mealCells: number                   // 单次用量
  cellUnit: string                    // 单次用量单位
  total: number                       // 总量
  unit: string                        // 总量单位
  producer?: string                   // 生产厂家
  amount?: number                     // 金额
  processMethod?: string              // 炮制方法（中药）
  stResult?: number                   // 皮试结果：1-阴性 2-阳性
  stResultName?: string               // 皮试结果名称
}
```

## 样式定制

### CSS类名

- `.bgfff`：白色背景
- `.custom-typename`：处方类型标签样式
- `.custom-art-content`：药品内容样式
- `.b-r-line-s`、`.b-r-line-c`、`.b-r-line-e`：处方连线样式

### 样式覆盖示例

```css
/* 自定义处方单样式 */
.bgfff {
  background-color: #fafafa;
}

/* 自定义处方类型标签 */
.custom-typename {
  background-color: #f0f8ff;
  border-color: #1890ff;
  color: #1890ff;
}

/* 自定义表格样式 */
:deep(.ant-table-cell) {
  padding: 8px !important;
  border-bottom: 1px solid #f0f0f0;
}
```
