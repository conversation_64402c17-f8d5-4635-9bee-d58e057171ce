import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 读卡按钮 -->
  <CardReaderButton
    business-type-id="104"
    :medical-type="21"
  />
</template>

<script setup>
import { CardReaderButton } from '@mh-mi/card-reader'
import { ref } from 'vue'
</script>`)

// 完整参数
export const fullParamsUsage = wrapCodeExample(`<template>
  <!-- 完整参数示例 -->
  <CardReaderButton
    business-type-id="104"
    :medical-type="21"
    :visit-id="visitId"
    :params="params"
    :host-url="hostUrl"
    :is-get-m-i="isGetMI"
    :is-m-i-open-select="isMIOpenSelect"
    :is-m-i-show-m-t="isMIShowMT"
    @success="onSuccess"
  />
</template>

<script setup>
import { CardReaderButton } from '@mh-mi/card-reader'
import { ref } from 'vue'

// 就诊ID
const visitId = ref(1001)

// 其他参数
const params = ref({
  // 可以传递其他业务参数
})

// 请求接口
const hostUrl = ref('')

// 是否调用医保
const isGetMI = ref(true)

// 医保调用后是否选择医保险种
const isMIOpenSelect = ref(true)

// 医保调用后是否显示慢特信息
const isMIShowMT = ref(true)

// 读卡成功回调
const onSuccess = (data) => {
  console.log('读卡成功', data)
}
</script>`)

// 引入组件
export const importCode = `import { CardReaderButton } from '@mh-mi/card-reader'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-mi/card-reader": "^1.0.18",
    "@mh-hip/util": "^1.0.0"
  }
}`
