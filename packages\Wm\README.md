
# 自动配置 tsconfig.json paths
运行 `scripts/generate-tsconfig-paths.js` tsconfig.json paths 会自动添加对应的配置

# 发布该目录下全部组件
pnpm publish:component Wm

# 组件说明
### 工具类
pnpm publish:component Wm/Util

### util
"@mh-wm/util": "^1.0.4",
import { CwBillType, getCwBillTypeName } from '@mh-wm/util'
const cwBillTypeLs = [
{
title: getCwBillTypeName(CwBillType.SALE_OUT),
value: CwBillType.SALE_OUT
},
{
title: getCwBillTypeName(CwBillType.CONSUME_OUT),
value: CwBillType.CONSUME_OUT
}
]

### 批号调整
# "@mh-wm/batch-adjust": "^1.0.4",
import { BatchAdjust } from '@mh-wm/batch-adjust'
pnpm publish:component Wm/BatchAdjust      # 批号调整

### 供应商组件
# "@mh-wm/scm-cust": "^1.0.0",
import { ScmCustSelect } from '@mh-wm/scm-cust'
pnpm publish:component Wm/ScmCust      # 供应商组件

### 采购制单组件
# "@mh-wm/maker": "^1.0.0",
import { Maker } from '@mh-wm/maker'
pnpm publish:component Wm/Maker      # 采购制单组件

