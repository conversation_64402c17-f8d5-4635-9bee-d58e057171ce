import { Data, Dialog } from '@idmy/core'
import { Currents } from '@mh-base/core'
import { getMiResp } from '../Util'
import { currentMiOrgSetting, EndpointType } from './MiOrgSetting'

export class MiRestCallError extends Error {
  constructor(message: string) {
    super(message)
  }
}

/**
 * 调用医保接口前获取配置并执行请求
 * @param params 请求参数
 */
async function callWithSetting(params: Data) {
  const setting: any = await currentMiOrgSetting()
  console.log('医保配置:', setting)
  try {
    // 添加通用参数
    const requestParams = {
      ...params,
      opter: Currents.id,
      org_id: Currents.tenantId,
      user_id: Currents.id,
    }
    console.log('医保请求:', requestParams)
    // 如果配置存在且为中心侧模式
    var resp = null
    if (setting?.endpointType === EndpointType.CENTER) {
      // 中心模式
      resp = await http.post('/api/mi_rest_call', requestParams, { ignoreError: false, appKey: 'mi' })
    } else {
      // 客户侧模式（默认）
      resp = await http.post('/api/mi_rest_call', requestParams, { encrypt: false, ignoreError: false, appKey: 'miLocal' })
    }
    console.log('医保响应:', resp)
    // 如果没有action属性或action的前四位不是数字，直接返回resp
    if (!params || !params.action || !/^\d{4}/.test(params.action)) {
      return resp
    }
    return getMiResp(resp)
  } catch (e: any) {
    await Dialog.error({ title: '医保异常', content: e.msg })
    throw new MiRestCallError(e.msg)
  }
}

/**
 * 获取读卡器配置
 * @returns
 */
export function getConfig() {
  return http.post('/api/mi/GetConfig', undefined, { ignoreError: false, appKey: 'miLocal' })
  // return Promise.resolve([
  //   {
  //     code: "3",
  //     name: "社保卡",
  //     cardReader: "CIS_CardReader",
  //     readCard: {
  //       type: "http",
  //       callFunction: null,
  //       callUrl: "http://127.0.0.1:50000/api/mi/ReadCard",
  //       isEncrypted: 0,
  //       isQrCode: 0,
  //       canFast: null
  //     },
  //     getPersonInfo: {
  //       type: "http",
  //       callFunction: null,
  //       callUrl: "http://127.0.0.1:50000/api/mi/GetPersonInfo",
  //       isEncrypted: 0,
  //       isQrCode: null,
  //       canFast: null
  //     },
  //     Others: '{ "readCardUrl": "http://scr.hun.hsip.gov.cn/hsa-hgs-adapt/api/card/initDll", "readCardUser": "431103|scr.hun.hsip.gov.cn" }  \n'
  //   },
  //   {
  //     code: "2",
  //     name: "身份证",
  //     cardReader: "CIS_CardReader",
  //     readCard: {
  //       type: "http",
  //       callFunction: null,
  //       callUrl: "http://127.0.0.1:50000/api/mi/ReadCard",
  //       isEncrypted: 0,
  //       isQrCode: 0,
  //       canFast: null
  //     },
  //     getPersonInfo: {
  //       type: "http",
  //       callFunction: null,
  //       callUrl: "http://127.0.0.1:50000/api/mi/GetPersonInfo",
  //       isEncrypted: 0,
  //       isQrCode: null,
  //       canFast: null
  //     },
  //     Others: '{ "readCardUrl": "http://scr.hun.hsip.gov.cn/hsa-hgs-adapt/api/card/initDll", "readCardUser": "431103|scr.hun.hsip.gov.cn" }  \n'
  //   },
  //   {
  //     code: "1",
  //     name: "电子医保",
  //     cardReader: "Nation_CIS_JX_GJ_v1.0.0",
  //     readCard: {
  //       type: "http",
  //       callFunction: null,
  //       callUrl: "http://127.0.0.1:50000/api/mi/ReadCard",
  //       isEncrypted: 0,
  //       isQrCode: 0,
  //       canFast: null
  //     },
  //     getPersonInfo: {
  //       type: "http",
  //       callFunction: null,
  //       callUrl: "http://127.0.0.1:50000/api/mi/GetPersonInfo",
  //       isEncrypted: 0,
  //       isQrCode: null,
  //       canFast: null
  //     },
  //     Others: null
  //   }
  // ])
}

/**
 * 获取人员信息
 * @param params 请求参数
 * @param cardConfig 卡片配置信息，包含 getPersonInfo 对象
 * @returns
 */
export function getPersonInfo(params: any, cardConfig?: any) {
  const requestParams = {
    ...params,
    opter: Currents.id,
    org_id: Currents.tenantId,
    user_id: Currents.id,
  }
  return http.post('/api/mi/GetPersonInfo', requestParams, { ignoreError: false, appKey: 'miLocal' })
  // return Promise.resolve({
  //   idetinfo: [],
  //   baseinfo: {
  //     certno: "******************",
  //     psn_no: "43000011100006595794",
  //     gend: "2",
  //     exp_content: null,
  //     brdy: "1986-09-12",
  //     naty: "01",
  //     psn_cert_type: "01",
  //     psn_name: "胡艳君",
  //     age: 38.5
  //   },
  //   insuinfo: [
  //     {
  //       insuplc_admdvs: "431103",
  //       psn_insu_date: "2024-07-01",
  //       cvlserv_flag: "0",
  //       balc: 0,
  //       emp_name: "永州市人和人力资源服务有限公司",
  //       psn_type: "11",
  //       psn_insu_stas: "1",
  //       insutype: "330",
  //       paus_insu_date: null
  //     },
  //     {
  //       insuplc_admdvs: "431103",
  //       psn_insu_date: "2024-07-01",
  //       cvlserv_flag: "0",
  //       balc: 172.32,
  //       emp_name: "永州市人和人力资源服务有限公司",
  //       psn_type: "11",
  //       psn_insu_stas: "1",
  //       insutype: "310",
  //       paus_insu_date: null
  //     }
  //   ]
  // })
}

/**
 * 读取卡片信息
 * @param params 读卡参数
 * @param url 读卡服务地址
 * @returns
 */
export function readCard(params: any, url?: string) {
  console.log(Currents)
  const requestParams = {
    ...params,
    opter: Currents.id,
    org_id: Currents.tenantId,
    user_id: Currents.id,
  }
  return http.post('/api/mi/ReadCard', requestParams, { ignoreError: false, appKey: 'miLocal' })
  // return Promise.resolve({
  //   mdtrt_cert_type: "2",
  //   mdtrt_cert_no: "******************",
  //   psn_cert_type: "01",
  //   certno: "******************",
  //   psn_cert_no: "******************",
  //   card_sn: null,
  //   psn_name: "胡艳君",
  //   age: "38",
  //   birthday: 19860912,
  //   gender: "2",
  //   nation: "汉族",
  //   address: "湖南省永州市冷水滩区书香名邸36栋1305号",
  //   insuplc_admdvs: null,
  //   read_time: "2025-03-27T16:40:33.746+08:00",
  //   extra_props: null
  // })
}

/**
 * 溯源码上传
 * @param params 上传参数
 */
export function uploadTrackCode(params: any) {
  return callWithSetting({
    action: 'UPLOAD_TRACK_CODE',
    wbSeqid: params.wbSeqid,
  })
}

/**
 * 上传条目
 */
export function artUpload(params?: any) {
  return callWithSetting({
    action: 'ART_UPLOAD',
    ...params, // 当有artId时才添加该参数
  })
}

/**
 * 结算清单上传
 * @param params 上传参数
 */
export function sheetUpload(params: any) {
  return callWithSetting({
    action: 'SHEET_UPLOAD',
    trans_id: params.transId,
  })
}

/**
 * 结算清单撤回
 * @param params 撤回参数
 */
export function sheetRevert(params: any) {
  return callWithSetting({
    action: 'SHEET_REVERT',
    trans_id: params.transId,
  })
}

/**
 * 结算清单质控
 * @param params 质控参数
 */
export function sheetQualityControl(params: any) {
  return callWithSetting({
    action: 'SHEET_QUALITY_CONTROL',
    trans_id: params.transId,
  })
}

/**
 * 结算清单提交
 * @param params 提交参数
 */
export function sheetSubmit(params: any) {
  return callWithSetting({
    action: 'SHEET_SUBMIT',
    trans_id: params.transId,
  })
}

/**
 * 医保对总账
 * @param params 检查参数
 */
export function transCheck(params: any) {
  return callWithSetting({
    action: 'TRANS_CHECK',
    expContent: JSON.stringify(params),
  })
}

/**
 * 医保对明细账
 * @param params 检查参数
 */
export function transCheckDetail(params: any) {
  return callWithSetting({
    action: 'TRANS_CHECK_DETAIL',
    expContent: JSON.stringify(params),
  })
}

/**
 * 医保冲正
 * @param params 冲正参数
 */
export function transReversal(params: any) {
  return callWithSetting({
    action: 'TRANS_REVERSAL',
    expContent: JSON.stringify(params),
  })
}

// 具体的接口方法将在这里添加，都需要通过 callWithSetting 调用
/**
 * 事前分析
 * @param params 分析参数
 * visit_id 当前就诊号
 * oe_list 当前医嘱列表
 * org_id 当前机构ID
 * user_id 当前用户ID
 * trig_scen 当前触发场景
 * 代码值（事前）	代码名称
 1	门诊处方签名
 2	门诊预结算
 3	住院医嘱签名
 4	住院预结算
 */
export function preAnalysis(params: any) {
  return callWithSetting({
    action: '3101',
    ...params,
  })
}

/**
 * 事前反馈
 * @param params 反馈参数
 */
export function preFeedback(params: any) {
  return callWithSetting({
    action: '3103',
    expContent: '1',
    ...params,
  })
}

// 具体的接口方法将在这里添加，都需要通过 callWithSetting 调用
/**
 * 事中分析
 * @param params 分析参数
 * visit_id 当前就诊号
 * oe_list 当前医嘱列表
 * org_id 当前机构ID
 * user_id 当前用户ID
 * trig_scen 当前触发场景
 * 代码值（事前）	代码名称
 1	门诊处方签名
 2	门诊预结算
 3	住院医嘱签名
 4	住院预结算
 */
export function inAnalysis(params: any) {
  return callWithSetting({
    action: '3102',
    ...params,
  })
}

/**
 * 事中反馈
 * @param params 反馈参数
 */
export function inFeedback(params: any) {
  return callWithSetting({
    action: '3103',
    expContent: '2',
    ...params,
  })
}

/**
 * 获取慢病信息
 * @param params
 * @returns
 */
export function getMiDisease(params: any) {
  return callWithSetting({
    action: '5301',
    ...params,
  })
}

/**
 * 获取医保签约信息
 * @param params
 * @returns
 */
export function getPersonSign(params: any) {
  return callWithSetting({
    action: '5302',
    ...params,
  })
}

// 注意：此函数已移至OrgParam.ts文件，这里保留是为了向后兼容
// 请使用OrgParam.ts中的findOrgParamByCode函数
/**
 * @deprecated 请使用OrgParam.ts中的findOrgParamByCode函数
 * 获取机构参数配置
 * @param orgId 机构ID
 * @param paramCodeLs 参数代码列表数组
 * @returns 参数配置信息
 */
export function findOrgParamByCode(orgId: number, paramCodeLs: string[]) {
  console.warn('警告：此函数已移至OrgParam.ts，请使用新的API')
  return http.post('/hip-base/orgparam/findByCode', { orgId, paramCodeLs }, { appKey: 'hip' })
}
