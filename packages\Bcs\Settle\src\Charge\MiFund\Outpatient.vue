<script lang="ts" setup>
import { Dict, View } from '@idmy/antd'
import { add, Api, Data, Message, useLoading } from '@idmy/core'
import { Currents } from '@mh-base/core'
import { CashTransStatusEnum, ChargeContext, chargeInjectKey, getVisit, MiTransStatusEnum, outpatientPreSettle, outpatientSettle, pay, updateMiStatus } from '@mh-bcs/util'
import { Button, Checkbox, Space } from 'ant-design-vue'
import { isNil } from 'lodash-es'
import MedicalInsurance from './MedicalInsurance.vue'
import MiTransAmt from './MiTransAmt.vue'

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const model = reactive({
  acctUsedFlag: Currents.tenantId === 360103001 || Currents.tenantId === 431100001,
  cashType: ctx.cashType,
  insuplcAdmdvs: undefined,
  cashId: ctx.cashId,
  insuranceTypeId: ctx.insuranceTypeId,
  medTypeId: ctx.medTypeId ?? 11,
  diseaseCode: ctx.diseaseCode,
  visitId: ctx.visitId,
  psnSetlway: 1,
})

const card = reactive(ctx.card ?? {})

const canPreSettle = computed(() => {
  if (settleLoading.value || isNil(model.insuranceTypeId) || isNil(card.readCard?.psn_cert_no)) {
    return false
  } else {
    return isNil(ctx.miTrans?.miTransId)
  }
})

const canSettle = computed(() => {
  if (preSettleLoading.value || isNil(ctx.miTrans?.miTransId)) {
    return false
  } else if (!ctx.isRefund && ctx.isZero && !ctx.isFullAmtMiPay) {
    return false
  } else {
    return ctx.miFundCashPayment?.status === CashTransStatusEnum.waiting && ctx.miTrans.transStatus > 0
  }
})

const [onSettle, settleLoading] = useLoading(async (isLoad = false) => {
  if (!canSettle.value) {
    return
  }
  Object.assign(
    ctx.miTrans,
    await outpatientSettle({
      card,
      psnSetlway: model.psnSetlway,
      mdtrt_cert_no: card.params.mdtrt_cert_no,
      mdtrt_cert_type: card.params.mdtrt_cert_type,
      miTransId: ctx.miTrans.miTransId,
    })
  )

  if (ctx.miFundCashPayment.status === CashTransStatusEnum.waiting && ctx.miTrans.transStatus === MiTransStatusEnum.settled) {
    await pay({
      cashId: ctx.cashId,
      payAmount: add(ctx.miTrans.fundPayAmt, ctx.miTrans.acctPayAmt, ctx.miTrans.multiAidAmt),
      miFundAmt: ctx.miTrans.fundPayAmt,
      miAcctAmt: ctx.miTrans.acctPayAmt,
      miMultiAidAmt: ctx.miTrans.multiAidAmt,
      paymentType: 'miFund',
    })
  }
  isLoad && (await ctx.onLoad())
})

ctx.miOnSettle = onSettle

const [onPreSettle, preSettleLoading] = useLoading(async () => {
  const { transId } = await pay({
    cashId: ctx.cashId,
    isPrePay: true,
    payAmount: 0,
    paymentType: 'miFund',
  })
  try {
    Object.assign(
      ctx.miTrans,
      await outpatientPreSettle({
        acctUsedFlag: model.acctUsedFlag,
        psnSetlway: model.psnSetlway,
        card,
        insuplcAdmdvs: model.insuplcAdmdvs,
        insuranceTypeId: model.insuranceTypeId,
        mdtrt_cert_no: card.params.mdtrt_cert_no,
        mdtrt_cert_type: card.params.mdtrt_cert_type,
        medTypeId: model.medTypeId,
        diseaseCode: model.diseaseCode,
        psn_no: card.miData?.baseinfo?.psn_no,
        transId,
      })
    )
    ctx.uploadFailedList = ctx.miTrans.upload_failed_list
  } catch (e) {
    console.error(e)
    return await ctx.onLoad()
  }

  if (!ctx.miTrans.miTransId) {
    Message.error('未返回医保交易ID')
    return await ctx.onLoad()
  }

  await updateMiStatus({
    miAcctAmt: ctx.miTrans.acctPayAmt ?? 0,
    miFundAmt: ctx.miTrans.fundPayAmt ?? 0,
    familyAcctAmt: ctx.miTrans.multiAidAmt ?? 0,
    miTransId: ctx.miTrans.miTransId,
    transId,
  })
  await ctx.onLoad()
  if (ctx.isRefund) {
    preSettleLoading.value = false
    await onSettle()
    await ctx.onLoad()
  }
})
ctx.preSettleLoading = preSettleLoading

const loaded = ({ output }: Data) => {
  ctx.visit = output
}

//region 读卡
const cardParams = computed(() => ({
  org_id: Currents.tenantId,
  user_id: Currents.id,
  medical_type: model.medTypeId,
  business_type_id: '104',
  xzbz: card.miData?.baseinfo?.expContent?.xzbz,
  visit_id: ctx.visitId,
}))

const onRead = async (val: any) => {
  if (val.miData?.baseinfo?.psn_cert_type && !val.readCard?.psn_cert_type) {
    val.readCard.psn_cert_type = val.miData.baseinfo?.psn_cert_type
    val.readCard.psn_cert_no = val.miData.baseinfo?.certno
    val.readCard.psn_no = val.miData.baseinfo?.psn_no
    val.readCard.psn_name = val.miData.baseinfo?.psn_name
    val.readCard.psn_type = val.miData.baseinfo?.psn_type
  }
  Object.assign(card, val)
}
//endregion
</script>
<template>
  <Api v-slot="{ output: visit }: Data" :load="() => getVisit(ctx.visitId)" first spin type="Object" @load="loaded">
    <div flex items-end justify-between>
      <div class="f1">
        <View :indicator="false" :loading="ctx.unpaidAmount < 0 || (ctx.isZero && !ctx.isFullAmtMiPay)" loading-area="content" right-class="f1" title="医保支付">
          <div v-if="visit.opDiseaseName" class="b error" mb-8px mr-16px text-18px>{{ visit.opDiseaseName }}</div>
          <MedicalInsurance v-model="model" :data="card.miData?.insuinfo" :miTransStatus="ctx.miTrans.transStatus" :patientId="visit.patientId" />
          <template #left>
            <MiTransAmt />
          </template>
          <template #right>
            <Space>
              <Dict v-model="model.psnSetlway" :clearable="false" class="w-120px!" clazz="MiPayMethod" size="small" />
              <Checkbox v-model:checked="model.acctUsedFlag">使用个人账户</Checkbox>
            </Space>
          </template>
        </View>
      </div>
      <div pl-8px w-64px>
        <Button :disabled="!canPreSettle" :loading="preSettleLoading" h-60px mb-4px type="primary" @click="onPreSettle()">预结 </Button>
        <Button :disabled="!canSettle" :loading="settleLoading" type="primary" @click="onSettle(true)">结算 </Button>
      </div>
    </div>
  </Api>
</template>
