<script setup lang="ts">
import { index as SectionRouteConsumable } from '@mh-inpatient-hsd/section-route-consumable'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from './code/SectionRouteConsumableCode'

const { Title, Paragraph } = Typography

// 引用
const sectionRouteConsumableRef = ref()

// 模拟数据
const sectionId = 40
const sectionName = '内三科住院'

// 打开病区给药途径绑定弹窗
const handleVisibleSectionRouteConsumable = () => {
  sectionRouteConsumableRef.value.open()
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>

<template>
  <Card title="病区给药途径绑定" class="mb-16px">
    <div mb-16px>
      <Title :level="4">病区给药途径绑定组件</Title>
      <Paragraph>用于管理病区与给药途径的绑定关系。</Paragraph>
      <Button type="primary" @click="handleVisibleSectionRouteConsumable">打开病区给药途径绑定弹窗</Button>
      <SectionRouteConsumable 
        ref="sectionRouteConsumableRef" 
        :section-id="sectionId" 
        :section-name="sectionName" 
        @close="handleClose"
      />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
