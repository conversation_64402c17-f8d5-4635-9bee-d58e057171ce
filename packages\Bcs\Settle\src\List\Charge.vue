<script lang="ts" setup>
import { Data } from '@idmy/core'
import { ChargeBefore } from '@mh-bcs/settle'
import { CashType } from '@mh-bcs/util'
import { PropType } from 'vue'
import Bill from './Bill.vue'

defineProps({
  cashType: { type: String as PropType<CashType>, default: 'OUTPATIENT' },
  billIds: { type: Array as PropType<number[]> },
  card: { type: Object as PropType<Data> },
  cashId: { type: Number as PropType<number> },
  showMiFund: { type: Boolean as PropType<boolean> },
  visitId: { type: Number as PropType<number> },
})
</script>
<template>
  <ChargeBefore
    v-slot="{ cashId }"
    :autoFinish="cfg.tenant.autoFinishSettle ?? false"
    :billIds="billIds"
    :card="card"
    :cashId="cashId"
    :cashType="cashType"
    :showMiFund="showMiFund"
    :visitId="visitId"
  >
    <div class="h-8px" />
    <Bill :cashId="cashId" :visitId="visitId" />
  </ChargeBefore>
</template>
