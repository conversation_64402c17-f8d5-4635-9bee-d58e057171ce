<template>
  <div class="header-right-container">
    <HeaderSearch />
    <HeaderNotice />
    <UserInfo />
  </div>
</template>

<script lang="ts" setup>
import HeaderNotice from '../HeaderNotice/index.vue'
import HeaderSearch from '../HeaderSearch/index.vue'
import UserInfo from '../UserInfo/index.vue'
</script>

<style lang="less" scoped>
.header-right-container {
  display: flex;
  align-items: center;
  height: 100%;
  color: #fff;
  padding-right: 8px;
}
</style>
