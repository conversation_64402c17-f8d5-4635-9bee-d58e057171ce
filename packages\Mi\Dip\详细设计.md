# 事前事中分析及反馈组件详细设计

## 1. 组件概述

事前事中分析及反馈组件是一个用于医保业务场景中进行事前事中分析并提供反馈的组件。该组件将展示分析结果，并允许用户进行相应的操作，如继续执行或返回修改。

## 2. 业务需求

### 2.1 核心功能

1. **事前事中分析**：
   - 接收就诊ID、医嘱列表等参数
   - 调用医保接口进行事前事中分析
   - 展示分析结果

2. **反馈处理**：
   - 允许用户选择"继续执行"或"返回修改"
   - 对于"继续执行"，需要输入处理原因
   - 提交反馈结果到医保系统

3. **结果展示**：
   - 展示规则名称、违规内容、严重程度、违规依据等信息
   - 根据分析结果的严重程度提供不同的视觉反馈

### 2.2 使用场景

1. 门诊处方签名
2. 门诊预结算
3. 住院医嘱签名
4. 住院预结算

## 3. 技术设计

### 3.1 组件结构

```
packages/Mi/Dip/
├── src/
│   ├── index.vue         # 主组件
│   ├── components/       # 子组件
│   │   ├── AnalysisResult.vue    # 分析结果展示组件
│   │   ├── FeedbackForm.vue      # 反馈表单组件
│   │   └── ResultItem.vue        # 结果项组件
│   ├── types/            # 类型定义
│   │   └── index.ts      # 类型定义文件
│   └── utils/            # 工具函数
│       └── index.ts      # 工具函数文件
├── index.ts              # 入口文件
├── package.json          # 包配置
└── README.md             # 文档
```

### 3.2 API 设计

#### Props

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| visitId | number | - | 诊疗ID，必填 |
| trigScen | number | - | 触发场景，必填 |
| type | number | - | 分析类型，必填，1为事前，2为事中 |
| displayMode | string | 'switch' | 显示模式，可选值：'switch'、'checkbox'、'hidden' |
| defaultEnabled | boolean | false | 默认是否启用分析，当displayMode为'hidden'时，此值默认为true |
| switchLabel | string | '启用事前事中分析' | 开关或复选框的标签文本 |

#### Events

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| close | 关闭弹窗时触发 | - |
| confirm | 点击确认按钮时触发 | (result: AnalysisResult[]) |
| cancel | 点击取消按钮时触发 | (result: AnalysisResult[]) |
| feedback-success | 反馈成功时触发 | (response: any) |
| feedback-error | 反馈失败时触发 | (error: any) |
| change | 启用状态变化时触发 | (enabled: boolean) |
| analysis-start | 开始分析时触发 | - |
| analysis-end | 分析结束时触发 | (result: AnalysisResult[] \| null) |

#### Methods

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| callInDip | 调用事前事中分析 | (oeLs: any) |
| resetDip | 重置组件状态 | () |
| setEnabled | 设置是否启用分析 | (enabled: boolean) |
| isEnabled | 获取当前是否启用分析 | () => boolean |

### 3.3 数据结构

#### 分析结果类型

```typescript
interface AnalysisResult {
  jrId: string;           // 警告结果ID
  ruleName: string;       // 规则名称
  volaCont: string;       // 违规内容
  sevDeg: string;         // 严重程度（1-明确违规，2-高度可疑，3-轻度可疑）
  volaEvid: string;       // 违规依据
  [key: string]: any;     // 其他可能的字段
}
```

#### 反馈参数类型

```typescript
interface FeedbackParams {
  warnType: number;       // 警告类型
  warns: {
    warnRsltId: string;   // 警告结果ID
    dspoWay: number;      // 处理方式（1-继续执行，2-返回修改）
    dspoWayRea: string;   // 处理原因
  }[];
}
```

### 3.4 交互流程

#### 显示模式为switch或checkbox时

1. 组件渲染为switch或checkbox控件，用户可以选择是否启用分析
2. 当用户调用 `callInDip` 方法时：
   - 如果分析已启用，则执行分析流程
   - 如果分析未启用，则跳过分析，直接返回成功

#### 显示模式为hidden时

1. 组件不在页面上显示任何内容
2. 当用户调用 `callInDip` 方法时，始终执行分析流程

#### 分析流程

1. 组件显示加载状态，使用Props中的visitId和传入的医嘱列表调用 `preAnalysis` API
2. 获取分析结果后，显示弹窗并根据结果类型显示不同内容：
   - 如果有违规项，显示违规列表
   - 如果无违规项，显示成功信息
3. 当有违规项时，用户可以选择"继续执行"或"返回修改"
   - 选择"继续执行"时，弹出输入处理原因的表单
   - 选择"返回修改"时，直接提交反馈
4. 提交反馈后，根据结果显示成功或失败信息
5. 成功后关闭弹窗，触发相应事件

## 4. 界面设计

### 4.1 控制界面

根据displayMode属性的不同值，组件会呈现不同的控制界面：

#### switch模式

显示为一个开关组件，带有标签文本。用户可以通过切换开关来启用或禁用事前事中分析。

#### checkbox模式

显示为一个复选框组件，带有标签文本。用户可以通过勾选复选框来启用或禁用事前事中分析。

#### hidden模式

不显示任何控制界面，组件在页面上不可见。

### 4.2 分析结果弹窗

弹窗形式，包含以下部分：

- 标题：事前事中分析
- 内容区：
  - 加载中状态：显示加载动画和提示文字
  - 分析结果：显示违规列表或成功信息
- 底部按钮：
  - 有违规项时：显示"继续执行"和"返回修改"按钮
  - 无违规项时：显示"关闭"按钮

### 4.3 处理原因输入界面

弹窗形式，包含以下部分：

- 标题：输入处理原因
- 内容区：处理原因输入框
- 底部按钮：确认和关闭按钮

## 5. 扩展性设计

1. **自定义样式**：
   - 通过 Props 支持自定义弹窗宽度、标题等
   - 支持通过 CSS 变量覆盖默认样式

2. **插槽扩展**：
   - 提供 header、footer、empty 等插槽，支持自定义内容

3. **国际化支持**：
   - 支持通过 Props 传入自定义文本
   - 后续可考虑接入国际化框架

## 6. 性能优化

1. **按需加载**：
   - 组件内部的子组件采用按需加载
   - 减少初始加载时的资源消耗

2. **缓存优化**：
   - 对分析结果进行缓存，避免重复请求
   - 提供手动刷新机制

3. **渲染优化**：
   - 使用虚拟列表渲染大量结果项
   - 避免不必要的重渲染

## 7. 安全性考虑

1. **数据验证**：
   - 对输入参数进行严格验证
   - 防止注入攻击

2. **错误处理**：
   - 完善的错误捕获和处理机制
   - 友好的错误提示

3. **权限控制**：
   - 根据用户权限显示不同操作选项
   - 防止未授权操作

## 8. 测试策略

1. **单元测试**：
   - 测试各个组件的独立功能
   - 测试各种边界条件

2. **集成测试**：
   - 测试组件与医保接口的交互
   - 测试不同场景下的表现

3. **UI测试**：
   - 测试界面在不同设备和分辨率下的表现
   - 测试交互流程的完整性

## 9. 文档规划

1. **使用文档**：
   - 安装说明
   - API 参考
   - 示例代码

2. **开发文档**：
   - 组件结构说明
   - 扩展指南
   - 贡献指南

## 10. 后续迭代计划

1. **功能增强**：
   - 支持更多分析场景
   - 增加批量处理能力

2. **性能优化**：
   - 优化大数据量下的性能
   - 减少网络请求

3. **用户体验提升**：
   - 增加更丰富的视觉反馈
   - 优化交互流程
