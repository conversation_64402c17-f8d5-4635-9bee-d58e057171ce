<script setup lang="ts">
import { PharmacySelect } from '@mh-wm/pharmacy'
import { Card, Typography, Divider, Radio } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { bsnTypeUsage, importCode, packageJsonCode } from '../code/PharmacySelectCode'

const { Title } = Typography

// 选中的药房ID
const pharmacyId = ref<string>()

// 业务类型
const bsnType = ref(0)
</script>

<template>
  <Card title="根据业务类型过滤药房" class="mb-16px">
    <div mb-16px>
      <Title :level="4">业务类型</Title>
      <Radio.Group v-model:value="bsnType" button-style="solid">
        <Radio.Button :value="0">全部</Radio.Button>
        <Radio.Button :value="1">采购入库</Radio.Button>
        <Radio.Button :value="2">销售出库</Radio.Button>
        <Radio.Button :value="3">库存调拨</Radio.Button>
      </Radio.Group>
    </div>

    <div mb-16px>
      <Title :level="4">根据业务类型过滤药房</Title>
      <PharmacySelect v-model="pharmacyId" :bsnType="bsnType" w-200px />
      <div mt-8px>选中的药房ID: {{ pharmacyId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        当设置业务类型时，组件会根据业务类型自动过滤药房列表。例如，当bsnType=1时，只会显示支持采购入库的药房。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="bsnTypeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
