import { http } from '@idmy/core'

/**
 * 获取所有品种类型
 * @param params 查询参数
 * @returns 品种类型列表
 */
export function findAllArtCatApi(params: any): Promise<any> {
  return http.post('/hip-base/cattype/findStockEnabledAll', params, { appKey: 'hip' })
}

/**
 * 获取品种子类型
 * @param params 查询参数
 * @returns 品种子类型列表
 */
export function findAllArtSubTypeApi(params: any): Promise<any> {
  return http.post('/hip-base/artsubtype/findAll', params, { appKey: 'hip' })
}
