<script setup lang="ts">
 import { Maker } from '@mh-wm/maker'
import { Card, Typography, Divider, Button, message, Alert, Space } from 'ant-design-vue'
import { ref } from 'vue'

const { Title, Paragraph, Text } = Typography

// 组件引用
const makerRef = ref()

// 移除了仓库编码参数，组件内部不再需要外部传入

// 验证结果记录
const validationResults = ref([])

// 添加品种回调
const handleAddArt = (formData: any) => {
  console.log('添加品种成功，表单数据：', formData)

  const result = {
    time: new Date().toLocaleTimeString(),
    artName: formData.artData?.artName || '未知品种',
    status: 'success',
    message: '验证通过，品种添加成功'
  }

  validationResults.value.unshift(result)
  message.success(`品种 "${formData.artData?.artName}" 添加成功`)
}

// 清空验证结果
const clearResults = () => {
  validationResults.value = []
  message.success('验证结果已清空')
}

// 设置无效日期数据（用于测试验证）
const setInvalidDateData = () => {
  if (makerRef.value) {
    const mockData = {
      artData: {
        artId: 1001,
        artName: '阿莫西林胶囊',
        artSpec: '0.25g*24粒',
        producer: '哈药集团制药总厂',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 24,
        splittable: 1
      },
      originPlace: '中国',
      batchNo: '*********',
      dateManufactured: '20240301', // 生产日期
      expiry: '20240201',           // 有效期早于生产日期（无效）
      packPrice: 15.50,
      totalPacks: 10,
      totalCells: 5
    }

    makerRef.value.setFormData(mockData)
    message.warning('已设置无效日期数据，有效期早于生产日期，点击添加按钮测试验证')
  }
}

// 设置无效日期格式数据
const setInvalidFormatData = () => {
  if (makerRef.value) {
    const mockData = {
      artData: {
        artId: 1002,
        artName: '头孢克肟胶囊',
        artSpec: '0.1g*12粒',
        producer: '石药集团',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 12,
        splittable: 1
      },
      originPlace: '河北',
      batchNo: 'C20240201',
      dateManufactured: '2024-02-01', // 错误格式，应该是YYYYMMDD
      expiry: '2027-01-31',           // 错误格式，应该是YYYYMMDD
      packPrice: 28.80,
      totalPacks: 5,
      totalCells: 8
    }

    makerRef.value.setFormData(mockData)
    message.warning('已设置错误日期格式数据，点击添加按钮测试验证')
  }
}

// 设置空数量数据
const setEmptyQuantityData = () => {
  if (makerRef.value) {
    const mockData = {
      artData: {
        artId: 1003,
        artName: '胰岛素注射液',
        artSpec: '3ml:300IU',
        producer: '诺和诺德',
        packUnit: '支',
        cellUnit: '支',
        packCells: 1,
        splittable: 0
      },
      originPlace: '丹麦',
      batchNo: 'D20240301',
      dateManufactured: '20240301',
      expiry: '20270228',
      packPrice: 68.50,
      totalPacks: 0 // 数量为0，无效
    }

    makerRef.value.setFormData(mockData)
    message.warning('已设置空数量数据，点击添加按钮测试验证')
  }
}

// 设置有效数据
const setValidData = () => {
  if (makerRef.value) {
    const mockData = {
      artData: {
        artId: 1004,
        artName: '布洛芬缓释胶囊',
        artSpec: '0.3g*20粒',
        producer: '中美史克',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 20,
        splittable: 1
      },
      originPlace: '天津',
      batchNo: 'E20240401',
      dateManufactured: '20240401',
      expiry: '20270331',
      packPrice: 12.50,
      totalPacks: 15,
      totalCells: 10
    }

    makerRef.value.setFormData(mockData)
    message.success('已设置有效数据，点击添加按钮测试验证')
  }
}
</script>

<template>
  <Card title="表单验证 - 采购制单组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">表单验证功能</Title>
      <Paragraph>
        采购组件内置了完整的表单验证功能，包括必填字段验证、日期格式验证、数量验证等。
        以下演示了各种验证场景。
      </Paragraph>

      <!-- 验证规则说明 -->
      <Alert
        message="验证规则说明"
        type="info"
        show-icon
        style="margin-bottom: 16px;"
      >
        <template #description>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>必须先选择品种才能提交表单</li>
            <li>生产日期和有效期至必须填写，且格式为YYYYMMDD（8位数字）</li>
            <li>有效期至必须大于生产日期</li>
            <li>整包数量和拆零数量至少填写一个，且必须大于0</li>
            <li>如果品种不可拆零（splittable !== 1），则不会显示拆零数量字段</li>
          </ul>
        </template>
      </Alert>

      <!-- 采购制单录入组件 -->
      <div class="maker-form-section">
        <Title :level="5">采购制单录入组件</Title>
        <Maker
          ref="makerRef"
          @addArt="handleAddArt"
        />
      </div>

      <Divider />

      <!-- 验证测试按钮 -->
      <div class="validation-section">
        <Title :level="5">验证测试</Title>
        <Paragraph>
          点击下方按钮设置不同的测试数据，然后点击"添加"按钮测试验证功能：
        </Paragraph>

        <Space wrap style="margin-bottom: 16px;">
          <Button @click="setValidData" type="primary">
            设置有效数据
          </Button>
          <Button @click="setInvalidDateData" danger>
            设置无效日期（有效期早于生产日期）
          </Button>
          <Button @click="setInvalidFormatData" danger>
            设置错误日期格式
          </Button>
          <Button @click="setEmptyQuantityData" danger>
            设置空数量数据
          </Button>
        </Space>

        <!-- 验证结果显示 -->
        <div v-if="validationResults.length > 0">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <Title :level="5" style="margin: 0;">验证结果记录</Title>
            <Button size="small" @click="clearResults">清空记录</Button>
          </div>

          <div class="validation-results">
            <div
              v-for="(result, index) in validationResults"
              :key="index"
              class="validation-result-item"
              :class="{ 'success': result.status === 'success' }"
            >
              <div class="result-time">{{ result.time }}</div>
              <div class="result-content">
                <Text :type="result.status === 'success' ? 'success' : 'danger'">
                  {{ result.artName }}
                </Text>
                <span class="result-message">{{ result.message }}</span>
              </div>
            </div>
          </div>
        </div>

        <div v-else style="text-align: center; padding: 20px; color: #999; border: 1px dashed #d9d9d9; margin-top: 16px;">
          暂无验证记录，请设置测试数据并点击添加按钮
        </div>
      </div>

      <div class="mt-16px tip-text">
        <i class="tip-icon">i</i>
        组件会在提交时自动进行验证，验证失败时会显示相应的错误提示，并聚焦到错误字段。
        验证成功时会触发addArt事件并清空表单。
      </div>
    </div>
  </Card>
</template>

<style scoped>
.mb-16px {
  margin-bottom: 16px;
}

.mt-16px {
  margin-top: 16px;
}

.maker-form-section {
  margin-bottom: 24px;
}

.validation-section {
  margin-top: 24px;
}

.validation-results {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.validation-result-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background: #fff2f0;
}

.validation-result-item.success {
  background: #f6ffed;
}

.validation-result-item:last-child {
  border-bottom: none;
}

.result-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.result-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-message {
  color: #666;
}

.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  font-style: normal;
  margin-right: 8px;
  flex-shrink: 0;
}
</style>
