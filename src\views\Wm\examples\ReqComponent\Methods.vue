<script setup lang="ts">
import { ReqComponent } from '@mh-wm/req-component'
import { Card, Typography, Divider, Button, message, Space } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { methodsUsage, importCode, packageJsonCode } from '../code/ReqComponentCode'

const { Title, Paragraph, Text } = Typography

// 仓库编码
const deptCode = ref('000013')

// 组件引用
const reqComponentRef = ref()

// 获取选中的品种数据
const getSelectedData = () => {
  if (reqComponentRef.value) {
    const selectedData = reqComponentRef.value.getSelectedArtData()
    console.log('当前选中的品种数据:', selectedData)
    if (selectedData) {
      message.info(`当前选中品种: ${selectedData.artName}`)
    } else {
      message.warning('当前未选中任何品种')
    }
    return selectedData
  } else {
    message.error('组件引用不存在')
    return null
  }
}

// 清空表单数据
const clearFormData = () => {
  if (reqComponentRef.value) {
    reqComponentRef.value.clearFormData()
    message.success('表单数据已清空')
  } else {
    message.error('组件引用不存在')
  }
}
</script>

<template>
  <Card title="组件方法" class="mb-16px">
    <div mb-16px>
      <Title :level="4">组件方法调用示例</Title>
      <Text type="secondary">通过ref引用调用组件方法</Text>
      <Divider style="margin: 8px 0" />
      
      <div class="component-container">
        <ReqComponent
          ref="reqComponentRef"
          :deptCode="deptCode"
        />
        
        <div class="actions-container">
          <Space>
            <Button type="primary" @click="getSelectedData">获取选中品种数据</Button>
            <Button @click="clearFormData">清空表单数据</Button>
          </Space>
        </div>
        
        <div class="tip-container">
          <div class="tip-text">
            <i class="tip-icon">i</i>
            <div>
              <p><strong>组件暴露的方法：</strong></p>
              <ul>
                <li><code>getSelectedArtData()</code>: 获取当前选中的品种数据</li>
                <li><code>clearFormData()</code>: 清空表单数据</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="methodsUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.component-container {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.actions-container {
  margin-top: 16px;
  padding: 16px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

.tip-container {
  margin-top: 16px;
}

.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  padding: 12px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: #fff;
  text-align: center;
  line-height: 16px;
  font-style: normal;
  font-size: 12px;
  margin-right: 8px;
  flex-shrink: 0;
  margin-top: 2px;
}

.tip-text p {
  margin: 0 0 8px 0;
}

.tip-text ul {
  margin: 0;
  padding-left: 16px;
}

.tip-text code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}
</style>
