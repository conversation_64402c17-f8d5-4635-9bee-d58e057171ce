<script setup lang="ts">
import { DiseaseSelectorModal } from '@mh-hip/disease'
import { But<PERSON>, <PERSON>, Typography, Divider, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { packageJsonCode, modalUsage } from '../code/DiseaseCode'
import { ref } from 'vue'

const { Title } = Typography
const showModal = ref(false)
const diseaseId = ref()
const patientId = ref(1)

const importCode = `import { DiseaseSelectorModal } from '@mh-hip/disease'`
</script>
<template>
  <Card title="基础用法 - 病种选择弹窗" class="mb-16px">
    <Title :level="4">选择病种</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="24">
        <div>
          <Button @click="showModal = true">选择病种</Button>
          <DiseaseSelectorModal v-model:visible="showModal" v-model="diseaseId" :patientId="patientId" @select="(d)=>diseaseId=d.id" />
          <div mt-8px>选中值: {{ diseaseId }}</div>
        </div>
      </Col>
    </Row>
    <Divider />
    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="modalUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
