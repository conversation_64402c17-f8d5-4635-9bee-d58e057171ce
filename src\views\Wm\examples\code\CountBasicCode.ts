import { wrapCodeExample } from '@/utils/codeUtils'

// 创建盘点单示例代码
export const createUsage = wrapCodeExample(`<template>
  <WmCountCreate
    buttonText="新建盘点"
    buttonType="primary"
    :defaultDeptCode="deptCode"
    :onlyShowWithStock="true"
    @success="handleCreateSuccess"
    @cancel="handleCreateCancel"
  />
</template>

<script setup>
import { WmCountCreate } from '@mh-wm/count'
import { ref } from 'vue'

const deptCode = ref('1001')

const handleCreateSuccess = (countId) => {
  console.log('创建盘点单成功，盘点单ID:', countId)
}

const handleCreateCancel = () => {
  console.log('取消创建盘点单')
}
</script>`)

// 录入盘点结果示例代码
export const editUsage = wrapCodeExample(`<template>
  <WmCountEdit
    buttonText="录入盘点结果"
    buttonType="primary"
    @success="handleEditSuccess"
    @cancel="handleEditCancel"
    ref="countEditRef"
  />

  <Button @click="openEdit">打开录入</Button>
</template>

<script setup>
import { WmCountEdit } from '@mh-wm/count'
import '@mh-wm/count/index.css'  // 引入样式文件
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

const countEditRef = ref(null)

const openEdit = () => {
  countEditRef.value.open(123) // 传入盘点单ID
}

const handleEditSuccess = (countId) => {
  console.log('保存盘点结果成功，盘点单ID:', countId)
}

const handleEditCancel = () => {
  console.log('取消录入盘点结果')
}
</script>`)

// 导入代码
export const importCode = `import { WmCountCreate, WmCountEdit } from '@mh-wm/count'
import '@mh-wm/count/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/count": "^1.0.0"
  }
}`
