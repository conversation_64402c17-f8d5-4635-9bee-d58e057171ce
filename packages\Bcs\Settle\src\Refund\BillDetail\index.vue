<script lang="ts" setup>
import { add, Data, Dialog, Format, useLoading } from '@idmy/core'
import { getCash, listFullPaymentsByCashId, refundInjectKey } from '@mh-bcs/util'
import { Space } from 'ant-design-vue'
import Blue from './Blue.vue'
import New from './New.vue'
import Red from './Red.vue'

const refundCtx = inject<any>(refundInjectKey)

const { blueCashId, redCashId } = defineProps({
  blueCashId: { type: Number as PropType<number>, required: true },
  redCashId: { type: Number as PropType<number>, required: true },
})

const blueCash = ref<Data>({})
useLoading(async () => {
  blueCash.value = await getCash(blueCashId)
}, true)

const bluePayments = ref<Data[]>([])
useLoading(async () => {
  bluePayments.value = await listFullPaymentsByCashId(blueCashId)
}, true)

const blueBillAmount = ref<number>(0)
const blueBillDetails = ref<Data[]>([])
const blueLoad = (bills: Data[], billAmt: number) => {
  blueBillDetails.value = bills
  blueBillAmount.value = billAmt
}

const redAmt = ref(0)
const redBillDetails = ref<Data[]>([])
const onLoadRedBillDetails = (bills: Data[], amt: number) => {
  redBillDetails.value = bills
  redAmt.value = amt
}

const highlightedId = ref()
provide('highlightedId', highlightedId)

const activeKey = ref('1')

refundCtx.onFinish = async (cashId: number) => {
  await Dialog.info({
    title: '本次退费统计',
    xClosable: false,
    okText: '确定',
    content: h('div', {}, [
      h(Format, { valueClass: 'b', component: 'div', prefix: '退费之前：', type: 'Currency', value: blueBillAmount.value }),
      h(Space, {}, [
        h(Format, { valueClass: 'b', component: 'div', prefix: '退费总额：', type: 'Currency', value: redAmt.value }),
        h(Format, { valueClass: 'b', component: 'div', prefix: '医保：', type: 'Currency', value: refundCtx.paymentTotal.value.currentMi ?? 0 }),
        h(Format, { valueClass: 'b', component: 'div', prefix: '现金：', type: 'Currency', value: refundCtx.paymentTotal.value.currentCash ?? 0 }),
      ]),
      h(Format, { valueClass: 'b', component: 'div', prefix: '剩余金额：', type: 'Currency', value: add(blueBillAmount.value, redAmt.value) }),
    ]),
  })
  await Modal.ok(cashId)
}
</script>

<template>
  <div flex>
    <div class="f1">
      <Blue v-if="blueCash.cashId && bluePayments.length" v-model:activeKey="activeKey" :cash="blueCash" :payments="bluePayments" @load="blueLoad" />
    </div>
    <div class="w-16px tac" />
    <div class="f1">
      <Red v-if="blueCash.amount > 0" v-model:activeKey="activeKey" :blueAmount="blueBillAmount" :cashId="redCashId" @load="onLoadRedBillDetails" />
    </div>
    <template v-if="!cfg.setting?.partialRefundDisabled">
      <div class="w-16px tac" />
      <div class="f1">
        <New v-if="blueBillDetails.length && redBillDetails.length" v-model:activeKey="activeKey" :blues="blueBillDetails" :reds="redBillDetails" />
      </div>
    </template>
  </div>
</template>
<style lang="less" scoped>
:deep(.ant-collapse-content-box) {
  padding: 8px !important;
}

:deep(.ant-collapse-header) {
  padding: 4px 8px !important;
}
</style>
