<script setup lang="ts">
import { Tabs, Card } from 'ant-design-vue'
import { onMounted, nextTick, ref } from 'vue'
import hljs from 'highlight.js/lib/core'
import xml from 'highlight.js/lib/languages/xml'
import javascript from 'highlight.js/lib/languages/javascript'
import json from 'highlight.js/lib/languages/json'
import 'highlight.js/styles/atom-one-dark.css'

// 注册需要的语言
hljs.registerLanguage('xml', xml)
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('json', json)

// 定义props
const props = defineProps({
  // 使用示例代码
  usage: {
    type: String,
    required: true,
  },
  // 引入组件代码
  importCode: {
    type: String,
    default: '',
  },
  // package.json依赖配置代码
  packageJson: {
    type: String,
    default: '',
  },
  // 默认激活的tab
  defaultActiveKey: {
    type: String,
    default: 'usage',
  },
})

// 更新代码高亮
const updateHighlight = () => {
  nextTick(() => {
    document.querySelectorAll('pre code').forEach(block => {
      hljs.highlightElement(block as HTMLElement)
    })
  })
}

// 标签页切换时更新高亮
const onTabChange = () => {
  updateHighlight()
}

// 初始化时更新高亮
onMounted(() => {
  updateHighlight()
})
</script>

<template>
  <div class="code-demo">
    <Tabs :defaultActiveKey="defaultActiveKey" @change="onTabChange">
      <Tabs.TabPane key="usage" tab="使用示例">
        <Card>
          <pre><code class="language-html" v-html="usage"></code></pre>
        </Card>
      </Tabs.TabPane>

      <Tabs.TabPane v-if="importCode" key="import" tab="引入组件">
        <Card>
          <pre><code class="language-javascript" v-html="importCode"></code></pre>
        </Card>
      </Tabs.TabPane>

      <Tabs.TabPane v-if="packageJson" key="package" tab="package.json 依赖配置">
        <Card>
          <pre><code class="language-json" v-html="packageJson"></code></pre>
        </Card>
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
.code-demo {
  margin-top: 16px;
}

pre {
  margin: 0;
  padding: 16px;
  overflow: auto;
  font-size: 14px;
  line-height: 1.5;
  background-color: #282c34;
  border-radius: 6px;
}

code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}
</style>
