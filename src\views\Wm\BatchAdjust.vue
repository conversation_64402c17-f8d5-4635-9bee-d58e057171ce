<script setup lang="ts">
import { Card, Typography } from 'ant-design-vue'
import BatchAdjustExample from './examples/BatchAdjustExample.vue'

const { Title } = Typography
</script>

<template>
  <div class="batch-adjust-page">
    <Card title="BatchAdjust 批号调整组件" class="mb-16px">
      <BatchAdjustExample />
    </Card>
  </div>
</template>

<style scoped>
.batch-adjust-page {
  padding: 20px;
}
</style>
