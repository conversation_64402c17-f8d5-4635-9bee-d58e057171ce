# KeyMap 组件详细设计文档

## 1. 组件概述

KeyMap 是一个用于管理键盘快捷键的组件，它允许用户自定义页面上的快捷键，并将这些配置保存到本地存储中。该组件提供了一个交互式界面，使用户能够轻松地查看、修改和重置快捷键配置。

### 1.1 核心功能

- 为不同页面配置不同的快捷键
- 通过双击Shift键在右侧显示抽屉式界面修改快捷键
- 支持恢复默认快捷键配置
- 基于localStorage存储用户配置
- 支持键盘事件监听和处理

### 1.2 使用场景

- 需要提供快捷键功能的应用程序
- 需要允许用户自定义快捷键的系统
- 需要在不同页面使用不同快捷键配置的多页面应用
- 需要为不同用户提供个性化快捷键配置的系统

## 2. 详细需求

### 2.1 功能需求

1. **快捷键配置管理**
   - 支持定义默认的快捷键配置
   - 支持用户自定义快捷键
   - 支持恢复默认快捷键配置
   - 支持保存用户自定义的快捷键配置

2. **用户界面**
   - 提供一个右侧抽屉式界面用于显示和编辑快捷键配置
   - 通过双击Shift键触发显示抽屉
   - 显示功能描述和对应的快捷键
   - 提供修改快捷键的交互界面
   - 提供恢复默认配置的选项

3. **快捷键录入**
   - 支持录入单个按键（如F5）
   - 支持录入组合键（如Ctrl+S）
   - 在录入过程中显示实时反馈
   - 检测并提示快捷键冲突

4. **数据存储**
   - 使用localStorage存储用户配置
   - 支持按页面和用户ID区分存储的配置
   - 在配置更改时自动保存

5. **事件处理**
   - 提供快捷键配置更新的事件回调
   - 提供快捷键配置重置的事件回调

6. **API接口**
   - 提供打开和关闭配置对话框的方法
   - 提供获取当前快捷键配置的方法
   - 提供重置为默认配置的方法

### 2.2 非功能需求

1. **性能**
   - 组件应该轻量级，不影响页面加载性能
   - 快捷键事件处理应该高效，不影响用户体验

2. **兼容性**
   - 支持现代浏览器（Chrome, Firefox, Safari, Edge）
   - 兼容Vue 3框架
   - 兼容Ant Design Vue组件库

3. **可扩展性**
   - 组件设计应该允许未来添加更多功能
   - 代码结构应该清晰，便于维护和扩展

4. **可用性**
   - 界面应该直观易用
   - 提供清晰的用户反馈
   - 支持键盘操作

## 3. 组件设计

### 3.1 组件结构

```bash
packages/Base/KeyMap/
├── index.ts                # 导出组件和类型
├── package.json            # 包配置
├── README.md               # 使用文档
└── src/
    ├── index.ts            # 组件入口
    └── index.vue           # 组件实现
```

### 3.2 数据模型

```typescript
// 快捷键配置项
interface KeyMapItem {
  btnKey: string;    // 功能键名（唯一标识）
  btnDesc: string;   // 功能描述
  bindKey: string;   // 绑定的快捷键
}
```

### 3.3 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| pageKey | 页面标识，用于区分不同页面的快捷键配置 | string | - |
| functionKeys | 预定义的功能键配置 | KeyMapItem[] | [] |

### 3.4 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| update | 快捷键配置更新时触发 | (keyMap: KeyMapItem[]) => void |
| reset | 快捷键配置重置为默认时触发 | () => void |

### 3.5 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| openDrawer | 打开快捷键设置抽屉 | () => void |
| closeDrawer | 关闭快捷键设置抽屉 | () => void |
| getKeyMap | 获取当前快捷键配置 | () => KeyMapItem[] |
| resetToDefault | 重置为默认快捷键配置 | () => void |

### 3.6 存储结构

```javascript
// 获取当前用户ID
const userId = getCurrentUserId() // 从系统中获取当前登录用户ID

// localStorage存储键名格式（包含用户ID）
const storageKey = `mh_keymap_${pageKey}_${userId}`

// 存储的数据格式（JSON字符串）
const storedData = JSON.stringify(keyMapItems)
```

## 4. 实现细节

### 4.1 抽屉触发机制与界面

1. 组件监听键盘事件，检测Shift键的按下和释放
2. 当检测到用户在短时间内（如300ms内）连续按下两次Shift键时，触发抽屉显示
3. **只有当前显示页面的KeyMap组件会响应双击Shift事件**，即使同时打开了多个页面
4. 抽屉在右侧显示，包含快捷键配置列表
5. 列表展示每个功能键的btnKey、btnDesc和bindKey
6. bindKey字段可以点击进行修改，其他字段只读
7. 如果localStorage中已有配置，则显示localStorage中的bindKey值
8. 如果localStorage中没有配置，则显示传入的functionKeys中的bindKey值
9. 用户可以通过点击关闭按钮或按Esc键关闭抽屉

### 4.2 快捷键录入

1. 用户点击"修改"按钮开始录入
2. 组件监听键盘事件，捕获用户按下的按键
3. 构建按键组合字符串（如"Ctrl + S"）
4. 检查是否与其他快捷键冲突
5. 更新快捷键配置并保存

### 4.3 快捷键冲突检测

1. 当用户录入新的快捷键时，检查是否与现有快捷键冲突
2. 如果发现冲突，显示警告消息，不更新配置
3. 如果没有冲突，更新配置并保存

### 4.4 用户ID获取

1. 组件内部需要获取当前登录用户的ID，用于区分不同用户的配置
2. 使用系统提供的Auth工具获取当前用户ID，具体实现如下：

   ```javascript
   import { Currents } from '../../Util/Auth'

   // 获取当前用户ID
   const userId = Currents.id

   // 如果需要获取更多用户信息
   const user = await getUser(Currents.id)
   ```

3. 如果无法获取用户ID，可以使用默认值（如'default'）
4. 用户ID将与pageKey一起组成存储键名，确保不同用户有独立的配置

### 4.5 配置存储和加载

1. 组件挂载时，尝试从localStorage加载用户配置
2. 如果找到配置，解析并使用
3. 如果未找到配置或解析失败，将传入的functionKeys配置存入localStorage作为初始数据
4. 当用户修改bindKey时，自动保存更新后的配置到localStorage
5. 页面中的其他组件可以通过localStorage获取最新的按键配置

### 4.6 键盘事件处理

1. 在使用组件的页面中，添加键盘事件监听
2. 当键盘事件触发时，构建按键组合字符串
3. 查找匹配的快捷键配置
4. 如果找到匹配，执行相应的操作

### 4.6 多页面场景处理

1. 在多页面应用中，可能同时存在多个KeyMap组件实例
2. 每个KeyMap组件实例使用不同的pageKey，对应不同页面的快捷键配置
3. 当用户双击Shift键时，只有当前显示页面的KeyMap组件应该响应
4. 实现方式可以通过检测组件所在页面的可见性来判断
5. 可以使用document.visibilityState或Intersection Observer API检测页面可见性
6. 只有当页面可见时，才响应双击Shift事件并显示抽屉

## 5. 使用示例

### 5.1 基本用法

```vue
<template>
  <div>
    <Button @click="openKeyMapSettings">手动打开快捷键设置</Button>
    <p>提示：双击Shift键可以打开快捷键设置抽屉</p>
    <KeyMap
      ref="keyMapRef"
      pageKey="example-page"
      :functionKeys="functionKeys"
      @update="handleKeyMapUpdate"
      @reset="handleKeyMapReset"
    />
  </div>
</template>

<script setup>
import { KeyMap, KeyMapItem } from '@mh-base/keymap'
import { ref, onMounted } from 'vue'

// 组件引用
const keyMapRef = ref()

// 预定义的功能键配置
const functionKeys = [
  { btnKey: "SAVE", btnDesc: "保存", bindKey: "Ctrl + S" },
  { btnKey: "REFRESH", btnDesc: "刷新", bindKey: "F5" }
]

// 手动打开快捷键设置抽屉
const openKeyMapSettings = () => {
  keyMapRef.value.openDrawer()
}

// 快捷键配置更新回调
const handleKeyMapUpdate = (keyMap) => {
  console.log('快捷键配置已更新:', keyMap)

  // 此时可以根据更新后的配置执行其他操作
  // 例如更新UI提示或重新绑定事件处理函数
}

// 快捷键配置重置回调
const handleKeyMapReset = () => {
  console.log('快捷键配置已重置为默认')
}

// 组件挂载时初始化
onMounted(() => {
  // 获取当前快捷键配置
  const currentKeyMap = keyMapRef.value.getKeyMap()
  console.log('当前快捷键配置:', currentKeyMap)

  // 注意：首次加载时，如果localStorage中没有配置，
  // KeyMap组件会自动将传入的functionKeys存入localStorage
})
</script>
```

### 5.2 多页面场景

```vue
<script setup>
import { KeyMap, KeyMapItem } from '@mh-base/keymap'
import { ref, onMounted, onUnmounted } from 'vue'

// 组件引用
const keyMapRef = ref()

// 页面标识
const pageKey = 'track-code-page'

// 预定义的功能键配置
const functionKeys = [
  { btnKey: "OPEN_TRACK_CODE", btnDesc: "打开溯源码窗口", bindKey: "F5" },
  { btnKey: "SAVE_TRACK_CODE", btnDesc: "保存溯源码数据", bindKey: "Ctrl + S" }
]

// 当前快捷键配置
const currentKeyMap = ref([])

// 获取当前用户ID
import { Currents } from '../../Util/Auth'
const userId = Currents.id || 'default'

// 从localStorage获取配置
const getKeyMapFromStorage = () => {
  try {
    const storageKey = `mh_keymap_${pageKey}_${userId}`
    const storedData = localStorage.getItem(storageKey)

    if (storedData) {
      return JSON.parse(storedData)
    }
  } catch (error) {
    console.error('获取快捷键配置失败:', error)
  }

  // 如果没有找到配置或解析失败，返回预定义配置
  return functionKeys
}

// 处理键盘事件
const handleKeyDown = (event) => {
  // 如果是在输入框中，不处理快捷键
  if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
    return
  }

  // 构建按键组合字符串
  let keyCombo = ''
  if (event.ctrlKey) keyCombo += 'Ctrl + '
  if (event.altKey) keyCombo += 'Alt + '
  if (event.shiftKey) keyCombo += 'Shift + '
  if (event.metaKey) keyCombo += 'Meta + '

  if (event.key === ' ') {
    keyCombo += 'Space'
  } else if (event.key.length === 1) {
    keyCombo += event.key.toUpperCase()
  } else {
    keyCombo += event.key
  }

  // 查找匹配的快捷键
  const matchedItem = currentKeyMap.value.find(item => item.bindKey === keyCombo)

  if (matchedItem) {
    // 阻止默认行为
    event.preventDefault()

    // 根据功能键执行相应操作
    switch (matchedItem.btnKey) {
      case 'OPEN_TRACK_CODE':
        console.log('执行操作: 打开溯源码窗口')
        break
      case 'SAVE_TRACK_CODE':
        console.log('执行操作: 保存溯源码数据')
        break
    }
  }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
  // 获取当前快捷键配置（两种方式）
  // 方式1：直接从组件获取（推荐，会自动处理初始化和更新）
  currentKeyMap.value = keyMapRef.value.getKeyMap()

  // 方式2：直接从localStorage获取（适用于不使用组件引用的情况）
  // currentKeyMap.value = getKeyMapFromStorage()

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)
})

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
})
</script>
```

### 5.3 监听快捷键（不使用组件引用）

```vue
<template>
  <div>
    <!-- 不需要引用KeyMap组件，直接从localStorage获取配置 -->
    <p>当前页面支持以下快捷键：</p>
    <ul>
      <li v-for="item in keyMapItems" :key="item.btnKey">
        {{ item.btnDesc }}: <strong>{{ item.bindKey }}</strong>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 页面标识
const pageKey = 'track-code-page'

// 当前快捷键配置
const keyMapItems = ref([])

// 从localStorage获取配置
const getKeyMapFromStorage = () => {
  try {
    const storageKey = `mh_keymap_${pageKey}`
    const storedData = localStorage.getItem(storageKey)

    if (storedData) {
      return JSON.parse(storedData)
    }
  } catch (error) {
    console.error('获取快捷键配置失败:', error)
  }

  return []
}

// 处理键盘事件
const handleKeyDown = (event) => {
  // 如果是在输入框中，不处理快捷键
  if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
    return
  }

  // 构建按键组合字符串
  let keyCombo = ''
  if (event.ctrlKey) keyCombo += 'Ctrl + '
  if (event.altKey) keyCombo += 'Alt + '
  if (event.shiftKey) keyCombo += 'Shift + '
  if (event.metaKey) keyCombo += 'Meta + '

  if (event.key === ' ') {
    keyCombo += 'Space'
  } else if (event.key.length === 1) {
    keyCombo += event.key.toUpperCase()
  } else {
    keyCombo += event.key
  }

  // 查找匹配的快捷键
  const matchedItem = keyMapItems.value.find(item => item.bindKey === keyCombo)

  if (matchedItem) {
    // 阻止默认行为
    event.preventDefault()

    // 根据功能键执行相应操作
    switch (matchedItem.btnKey) {
      case 'OPEN_TRACK_CODE':
        console.log('执行操作: 打开溯源码窗口')
        break
      case 'SAVE_TRACK_CODE':
        console.log('执行操作: 保存溯源码数据')
        break
    }
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 直接从localStorage获取配置
  keyMapItems.value = getKeyMapFromStorage()

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyDown)

  // 监听storage事件，当其他页面更新配置时更新本页面的配置
  const handleStorageChange = (event) => {
    if (event.key === `mh_keymap_${pageKey}_${userId}`) {
      keyMapItems.value = JSON.parse(event.newValue || '[]')
    }
  }
  window.addEventListener('storage', handleStorageChange)
})

// 组件卸载时移除键盘事件监听
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('storage', handleStorageChange)
})
</script>
```

## 6. 未来扩展

1. **多语言支持**：添加国际化支持，使组件可以显示不同语言的界面
2. **主题定制**：支持自定义主题，适应不同的设计风格
3. **快捷键分类**：支持将快捷键分组，便于管理大量快捷键
4. **快捷键搜索**：添加搜索功能，方便用户查找特定快捷键
5. **快捷键导入/导出**：支持导入/导出快捷键配置，便于用户备份和迁移
6. **快捷键冲突解决**：提供更智能的冲突解决机制
7. **快捷键提示**：在界面上显示可用的快捷键提示
8. **快捷键禁用**：支持临时禁用某些快捷键
9. **快捷键组合**：支持定义快捷键序列（如先按A再按B）
10. **云端同步**：支持将快捷键配置同步到云端，实现跨设备同步
