<script setup lang="ts">
import { Table } from 'ant-design-vue'
import { computed, ref, onMounted, watchEffect } from 'vue'
import { INSU_TYPE, PSN_TYPE } from '@mh-mi/util'

interface Props {
  dataSource: Array<any>
}

const props = defineProps<Props>()
const emit = defineEmits(['selected'])

// 选中的行key
const selectedKeys = ref([])

// 处理后的数据源
const processedDataSource = computed(() => {
  return props.dataSource.map(item => ({
    ...item,
    key: `${item.insutype}_${item.psn_type}` // 使用险种和人员类别组合作为唯一key
  }))
})

// 表格选择配置
const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectedKeys.value,
    type: 'radio',
    onChange: (rowKeys: any, selectedRows: any[]) => {
      selectedKeys.value = rowKeys
      if (selectedRows.length > 0) {
        emit('selected', { value: selectedRows[0], selected: true })
      }
    }
  }
})

// 自定义行点击
const customRow = (record: any) => {
  return {
    onClick: () => {
      selectedKeys.value = [record.key]
      emit('selected', { value: record, selected: true })
    }
  }
}

// 初始化时设置默认选中第一行
onMounted(() => {
  try {
    if (processedDataSource.value.length > 0) {
      // 查找暂停参保日期为空的310险种
      let defaultRow = processedDataSource.value.find(item => 
        item.insutype === '310' && !item.paus_insu_date
      )
      
      // 如果没找到，查找暂停参保日期为空的390险种
      if (!defaultRow) {
        defaultRow = processedDataSource.value.find(item => 
          item.insutype === '390' && !item.paus_insu_date
        )
      }
      
      // 如果还没找到，使用第一行
      defaultRow = defaultRow || processedDataSource.value[0]
      
      // 设置选中状态
      selectedKeys.value = [defaultRow.key]
      emit('selected', { value: defaultRow, selected: true })
    }
  } catch (_e) { }
})

// 当数据源变化时，清空选中状态
watchEffect(() => {
  if (props.dataSource.length === 0) {
    selectedKeys.value = []
  }
})

const columns = computed(() => [
  { 
    title: '保种名称', 
    dataIndex: 'insutype', 
    key: 'insutype',
    width: 180,
    align: 'left',
    ellipsis: true,
    customRender: ({ text }: { text: string }) => INSU_TYPE[text] || text
  },
  { 
    title: '人员类别', 
    dataIndex: 'psn_type', 
    key: 'psn_type',
    width: 100,
    align: 'center',
    customRender: ({ text }: { text: string }) => PSN_TYPE[text] || text
  },
  { 
    title: '余额', 
    dataIndex: 'balc', 
    key: 'balc',
    width: 80,
    align: 'right'
  },
  { 
    title: '个人参保日期', 
    dataIndex: 'psn_insu_date', 
    key: 'psn_insu_date',
    width: 120,
    align: 'center'
  },
  { 
    title: '暂停参保日期', 
    dataIndex: 'paus_insu_date', 
    key: 'paus_insu_date',
    width: 120,
    align: 'center'
  },
  { 
    title: '公务员标志', 
    dataIndex: 'cvlserv_flag', 
    key: 'cvlserv_flag',
    width: 90,
    align: 'center',
    customRender: ({ text }: { text: string }) => text === '1' ? '是' : '否'
  },
  { 
    title: '单位名称', 
    dataIndex: 'emp_name', 
    key: 'emp_name',
    width: 180,
    align: 'left',
    ellipsis: true
  }
])
</script>

<template>
  <div class="insu-table-container">
    <div class="insu-table-card">
      <Table 
        :dataSource="processedDataSource" 
        :columns="columns"
        :pagination="false"
        size="small"
        :rowSelection="rowSelection"
        :custom-row="customRow"
        :scroll="{ x: 870 }"
      />
    </div>
  </div>
</template>

<style lang="less" scoped>
.insu-table-container {
  margin-bottom: 5px;

  .insu-table-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e8e8e8;
    overflow: hidden;
  }

  :deep(.ant-table-thead > tr > th) {
    background: #fafafa;
    font-weight: 500;
    padding: 8px !important;
    font-size: 14px;
  }

  :deep(.ant-table-tbody > tr > td) {
    padding: 8px !important;
    font-size: 14px;
  }

  :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
    background: #e6f7ff;
  }

  :deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
    background: #e6f7ff;
  }

  :deep(.ant-table-measure-row) {
    display: none !important;
    height: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    position: absolute !important;
  }

  // 修复表头和数据之间的空白
  :deep(.ant-table-container table) {
    border-collapse: collapse !important;
  }

  :deep(.ant-table-header) {
    margin-bottom: 0 !important;
    background: transparent !important;
    overflow-y: hidden !important;
  }

  :deep(.ant-table-header::-webkit-scrollbar) {
    display: none !important;
  }

  :deep(.ant-table-body) {
    overflow-y: auto !important;
  }

  // 针对 ant-design-vue 的特殊处理
  :deep(tr[aria-hidden="true"]) {
    display: none !important;
    height: 0 !important;
  }
}
</style> 