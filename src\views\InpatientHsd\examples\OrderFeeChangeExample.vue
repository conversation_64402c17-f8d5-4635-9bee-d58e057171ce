<script setup lang="ts">
import { AddArt, RefundOrderExecFee } from '@mh-inpatient-hsd/order-fee-change'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { addArtUsage, refundOrderExecFeeUsage, importCode, packageJsonCode } from './code/OrderFeeChangeCode'

const { Title, Paragraph } = Typography

// 引用
const addArtRef = ref()
const refundOrderExecFeeRef = ref()

// 模拟数据
const sectionId = 40
const orderId = "OE2025051500001" // 医嘱执行单号
const lineNo = 1 // 行号
const orderLineno = 1 // 医嘱行号

// 打开医嘱执行增加条目弹窗
const handleVisibleAddArt = () => {
  addArtRef.value.open(orderId, lineNo, orderLineno)
}

// 打开医嘱执行退费弹窗
const handleVisibleRefundOrderExecFee = () => {
  refundOrderExecFeeRef.value.open(orderId, lineNo)
}

// 提交回调
const handleSubmit = (data) => {
  message.success('操作成功')
  console.log('提交数据:', data)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>

<template>
  <Card title="医嘱执行费用变更 - 医嘱执行增加条目" class="mb-16px">
    <div mb-16px>
      <Title :level="4">医嘱执行增加条目</Title>
      <Button type="primary" @click="handleVisibleAddArt">打开医嘱执行增加条目弹窗</Button>
      <AddArt ref="addArtRef" :section-id="sectionId" @add="handleClose" />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="addArtUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <Card title="医嘱执行费用变更 - 医嘱执行退费" class="mb-16px">
    <div mb-16px>
      <Title :level="4">医嘱执行退费</Title>
      <Button type="primary" @click="handleVisibleRefundOrderExecFee">打开医嘱执行退费弹窗</Button>
      <RefundOrderExecFee ref="refundOrderExecFeeRef" @refunded="handleClose" />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="refundOrderExecFeeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
