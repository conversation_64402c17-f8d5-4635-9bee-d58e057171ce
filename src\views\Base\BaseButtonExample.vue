<script setup lang="ts">
import { ref } from 'vue'
import { Card, Typography, Divider, Space, message, Tabs, Alert } from 'ant-design-vue'
import { BaseButton } from '@mh-base/core'
import KeyMap from '@mh-base/keymap'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { basicUsage, hideKeyUsage, buttonTypesUsage, disabledUsage, visibilityUsage, loadingUsage, importCode, packageJsonCode } from './code/BaseButtonCode'
import { basicUsage as keyMapBasicUsage, hideKeyUsage as keyMapHideUsage, importCode as keyMapImportCode, packageJsonCode as keyMapPackageJsonCode } from './code/BaseButtonWithKeyMapCode'

const { Title, Paragraph } = Typography
const { TabPane } = Tabs

// 当前激活的tab
const activeKey = ref('basic')

// 最后触发的按钮
const lastTriggeredButton = ref('')

// KeyMap组件引用
const keyMapRef = ref()

// 预定义的功能键配置
const functionKeys = [
  { btnKey: 'SAVE_DATA', btnDesc: '保存数据', bindKey: 'Ctrl + S' },
  { btnKey: 'REFRESH_DATA', btnDesc: '刷新数据', bindKey: 'F5' },
  { btnKey: 'CANCEL_OPERATION', btnDesc: '取消操作', bindKey: 'Escape' },
]

// 快捷键配置更新回调
const handleKeyMapUpdate = (keyMap: any) => {
  console.log('快捷键配置已更新:', keyMap)
  message.success('快捷键配置已更新')
}

// 快捷键配置重置回调
const handleKeyMapReset = () => {
  console.log('快捷键配置已重置为默认')
  message.success('快捷键配置已重置为默认')
}

// 处理按钮点击
const handleButtonClick = (buttonName: string) => {
  lastTriggeredButton.value = buttonName
  message.success(`点击了${buttonName}按钮`)
}

// 处理保存按钮点击
const handleSave = () => {
  lastTriggeredButton.value = '保存'
  message.success('保存成功')
}

// 处理刷新按钮点击
const handleRefresh = () => {
  lastTriggeredButton.value = '刷新'
  message.success('刷新成功')
}

// 处理取消按钮点击
const handleCancel = () => {
  lastTriggeredButton.value = '取消'
  message.info('已取消操作')
}

// 处理打印按钮点击
const handlePrint = () => {
  lastTriggeredButton.value = '打印'
  message.success('正在打印...')
}

// API保存相关状态和方法
const loading = ref(false)
const handleApiSave = () => {
  if (loading.value) return

  loading.value = true
  lastTriggeredButton.value = '保存数据(API调用中)'
  message.loading('正在保存数据...')

  // 模拟API调用
  setTimeout(() => {
    loading.value = false
    lastTriggeredButton.value = '保存数据(API调用完成)'
    message.success('数据保存成功')
  }, 2000)
}

// 手动控制loading状态
const loadingManual = ref(false)
const handleManualLoading = () => {
  if (loadingManual.value) return

  loadingManual.value = true
  lastTriggeredButton.value = '手动控制(处理中)'
  message.loading('正在处理...')

  // 模拟API调用
  setTimeout(() => {
    loadingManual.value = false
    lastTriggeredButton.value = '手动控制(处理完成)'
    message.success('处理完成')
  }, 2000)
}
</script>

<template>
  <div class="base-button-example">
    <Card title="BaseButton 基础按钮组件" class="mb-16px">
      <Paragraph> BaseButton组件是对ant-design-vue的Button组件的扩展，增加了键盘快捷键功能。 您可以通过functionKey属性指定按钮的快捷键，当按下对应的快捷键时，会自动触发按钮的点击事件。 </Paragraph>

      <Alert v-if="lastTriggeredButton" type="success" show-icon style="margin: 16px 0">
        <template #message>
          <div>
            最近触发的按钮: <strong>{{ lastTriggeredButton }}</strong>
          </div>
        </template>
      </Alert>

      <Tabs v-model:activeKey="activeKey" class="mt-16px">
        <!-- 基础用法 -->
        <TabPane key="basic" tab="基础用法">
          <Paragraph>通过functionKey属性指定按钮的快捷键，支持单个按键（如F5）或组合按键（如Ctrl + S）。</Paragraph>

          <Space direction="vertical" style="width: 100%; margin-bottom: 16px">
            <div>
              <BaseButton type="primary" functionKey="F5" @click="() => handleButtonClick('刷新(F5)')">刷新</BaseButton>
              <span style="margin-left: 8px; color: #666; font-size: 13px">按F5键可触发此按钮</span>
            </div>

            <div>
              <BaseButton type="primary" functionKey="Ctrl + S" @click="handleSave">保存</BaseButton>
              <span style="margin-left: 8px; color: #666; font-size: 13px">按Ctrl+S组合键可触发此按钮</span>
            </div>

            <div>
              <BaseButton danger functionKey="Escape" @click="handleCancel">取消</BaseButton>
              <span style="margin-left: 8px; color: #666; font-size: 13px">按Escape键可触发此按钮</span>
            </div>
          </Space>

          <Divider />
          <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </TabPane>

        <!-- 隐藏快捷键显示 -->
        <TabPane key="hide" tab="隐藏快捷键显示">
          <Paragraph>通过showFunctionKey属性控制是否在按钮文本中显示快捷键信息。</Paragraph>

          <Space style="margin-bottom: 16px">
            <BaseButton type="primary" functionKey="F5" @click="handleRefresh">刷新(显示快捷键)</BaseButton>
            <BaseButton type="primary" functionKey="F5" :showFunctionKey="false" @click="handleRefresh">刷新(隐藏快捷键)</BaseButton>
          </Space>

          <Divider />
          <CodeDemoVue :usage="hideKeyUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </TabPane>

        <!-- 不同类型的按钮 -->
        <TabPane key="types" tab="按钮类型">
          <Paragraph>BaseButton组件支持ant-design-vue Button组件的所有属性。</Paragraph>

          <Space style="margin-bottom: 16px">
            <BaseButton type="primary" functionKey="P" @click="handlePrint">打印</BaseButton>
            <BaseButton type="default" functionKey="D" @click="() => handleButtonClick('默认')">默认</BaseButton>
            <BaseButton type="dashed" functionKey="A" @click="() => handleButtonClick('虚线')">虚线</BaseButton>
            <BaseButton type="link" functionKey="L" @click="() => handleButtonClick('链接')">链接</BaseButton>
            <BaseButton type="text" functionKey="T" @click="() => handleButtonClick('文本')">文本</BaseButton>
          </Space>

          <Divider />
          <CodeDemoVue :usage="buttonTypesUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </TabPane>

        <!-- 禁用状态 -->
        <TabPane key="disabled" tab="禁用状态">
          <Paragraph>当按钮处于禁用状态时，即使按下对应的快捷键也不会触发点击事件。</Paragraph>

          <Space style="margin-bottom: 16px">
            <BaseButton type="primary" functionKey="F5" disabled @click="() => handleButtonClick('禁用按钮')"> 禁用按钮(F5) </BaseButton>
            <span style="margin-left: 8px; color: #666; font-size: 13px">按F5键不会触发此按钮</span>
          </Space>

          <Divider />
          <CodeDemoVue :usage="disabledUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </TabPane>

        <!-- 可见性控制 -->
        <TabPane key="visibility" tab="可见性控制">
          <Paragraph> BaseButton组件会自动检测自身是否在视口中可见，只有在可见时才会监听键盘事件。 这可以避免在按钮不可见时仍然触发快捷键功能，提高性能和用户体验。 </Paragraph>

          <div style="margin-bottom: 16px">
            <div style="margin-bottom: 8px">
              <BaseButton type="primary" @click="() => (lastTriggeredButton = '')"> 清除测试结果 </BaseButton>
            </div>

            <div style="height: 200px; overflow: auto; border: 1px solid #eee; padding: 16px; margin-bottom: 8px">
              <div style="height: 300px; display: flex; align-items: center; justify-content: center">
                <p style="color: #999">向下滚动查看带有快捷键的按钮</p>
              </div>

              <BaseButton type="primary" functionKey="F6" @click="() => handleButtonClick('滚动可见按钮(F6)')"> 滚动可见按钮(F6) </BaseButton>

              <div style="margin-top: 8px; color: #666; font-size: 13px">只有当此按钮在视口中可见时，按F6键才会触发它</div>
            </div>
          </div>

          <Divider />
          <CodeDemoVue :usage="visibilityUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </TabPane>

        <!-- Loading状态 -->
        <TabPane key="loading" tab="Loading状态">
          <Paragraph>
            在实际应用中，点击按钮后通常需要调用API保存数据，此时按钮应该显示loading状态。 BaseButton组件支持loading属性，可以控制按钮的加载状态。在loading状态下，按下快捷键不会重复触发操作。
          </Paragraph>

          <div style="margin-bottom: 16px">
            <Space>
              <!-- 普通加载状态 -->
              <BaseButton type="primary" :loading="loading" functionKey="F8" @click="handleApiSave"> 保存数据 </BaseButton>

              <!-- 手动控制加载状态 -->
              <BaseButton type="primary" :loading="loadingManual" functionKey="F10" @click="handleManualLoading">
                {{ loadingManual ? '处理中...' : '手动控制' }}
              </BaseButton>
            </Space>

            <div style="margin-top: 8px; color: #666; font-size: 13px">点击按钮后会模拟API调用，2秒后自动恢复。在loading状态下，按下快捷键不会重复触发操作。</div>
          </div>

          <Divider />
          <CodeDemoVue :usage="loadingUsage" :importCode="importCode" :packageJson="packageJsonCode" />
        </TabPane>

        <!-- 与KeyMap集成 -->
        <TabPane key="keymap" tab="与KeyMap集成">
          <Paragraph> BaseButton组件可以与KeyMap组件集成，通过btnKey和pageKey属性从KeyMap组件获取快捷键配置。 用户可以通过双击Shift键打开KeyMap组件的抽屉，自定义快捷键配置。 </Paragraph>

          <!-- KeyMap组件 -->
          <KeyMap ref="keyMapRef" pageKey="button-example-page" :functionKeys="functionKeys" :editable="true" @update="handleKeyMapUpdate" @reset="handleKeyMapReset" />

          <Alert type="info" show-icon style="margin: 16px 0">
            <template #message>
              <div>提示: 双击Shift键可以打开快捷键设置抽屉，自定义按钮的快捷键</div>
            </template>
          </Alert>

          <div style="margin-bottom: 16px">
            <Space>
              <!-- 使用btnKey和pageKey从KeyMap获取快捷键 -->
              <BaseButton type="primary" btnKey="SAVE_DATA" pageKey="button-example-page" @click="handleSave"> 保存数据 </BaseButton>

              <!-- 使用btnKey和pageKey从KeyMap获取快捷键，同时提供默认快捷键 -->
              <BaseButton type="primary" btnKey="REFRESH_DATA" pageKey="button-example-page" @click="handleRefresh"> 刷新数据 </BaseButton>

              <!-- 使用btnKey和pageKey从KeyMap获取快捷键，隐藏快捷键显示 -->
              <BaseButton danger btnKey="CANCEL_OPERATION" pageKey="button-example-page" @click="handleCancel"> 取消操作 </BaseButton>
            </Space>

            <div style="margin-top: 8px; color: #666; font-size: 13px">这些按钮的快捷键可以通过双击Shift键打开的抽屉进行自定义。修改后的快捷键会自动应用到按钮上。</div>
          </div>

          <Divider />
          <CodeDemoVue :usage="keyMapBasicUsage" :importCode="keyMapImportCode" :packageJson="keyMapPackageJsonCode" />
        </TabPane>

        <!-- 隐藏快捷键显示（KeyMap版本） -->
        <TabPane key="keymap-hide" tab="隐藏快捷键显示（KeyMap版本）">
          <Paragraph> 与KeyMap集成时，也可以通过showFunctionKey属性控制是否在按钮文本中显示快捷键信息。 </Paragraph>

          <div style="margin-bottom: 16px">
            <Space>
              <!-- 显示快捷键 -->
              <BaseButton type="primary" btnKey="SAVE_DATA" pageKey="button-example-page" :showFunctionKey="false" @click="handleSave"> 保存数据 </BaseButton>

              <!-- 隐藏快捷键 -->
              <BaseButton type="primary" btnKey="REFRESH_DATA" pageKey="button-example-page" :showFunctionKey="false" @click="handleRefresh"> 刷新数据 </BaseButton>
              <BaseButton danger btnKey="CANCEL_OPERATION" pageKey="button-example-page" :showFunctionKey="false" @click="handleCancel"> 取消操作 </BaseButton>
            </Space>
          </div>

          <Divider />
          <CodeDemoVue :usage="keyMapHideUsage" :importCode="keyMapImportCode" :packageJson="keyMapPackageJsonCode" />
        </TabPane>
      </Tabs>
    </Card>
  </div>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
