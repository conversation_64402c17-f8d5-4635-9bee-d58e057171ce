<script setup lang="ts">
import { WholeBox } from '@mh-wm/whole-box'
import { Card, Typography, Divider, Button, message, Row, Col, Tag, Alert } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { tableIntegration } from '@/views/Wm/examples/code/WholeBoxCode'

const { Title, Paragraph, Text } = Typography

// WholeBox组件引用
const wholeBoxRef = ref()

// 药品示例数据
const medicineScenarios = [
  {
    id: 'medicine1',
    title: '阿莫西林胶囊',
    description: '常见抗生素，10粒/盒包装',
    data: {
      artName: '阿莫西林胶囊',
      artSpec: '0.25g',
      producer: '华北制药集团有限责任公司',
      packCells: 10,
      cellUnit: '粒',
      packUnit: '盒',
      deptTotalPacks: 15,
      deptTotalCells: 8,
      totalPacks: 5,
      totalCells: 3,
      batchNo: 'B20240101',
      expDate: '20251201'
    },
    scenario: '药品拆零',
    complexity: '简单'
  },
  {
    id: 'medicine2',
    title: '头孢克肟片',
    description: '抗生素片剂，6片/盒包装',
    data: {
      artName: '头孢克肟片',
      artSpec: '100mg',
      producer: '齐鲁制药有限公司',
      packCells: 6,
      cellUnit: '片',
      packUnit: '盒',
      deptTotalPacks: 8,
      deptTotalCells: 2,
      totalPacks: 3,
      totalCells: 4,
      batchNo: 'B20240102',
      expDate: '20251202'
    },
    scenario: '药品拆零',
    complexity: '简单'
  },
  {
    id: 'medicine3',
    title: '布洛芬缓释胶囊',
    description: '解热镇痛药，20粒/盒大包装',
    data: {
      artName: '布洛芬缓释胶囊',
      artSpec: '300mg',
      producer: '中美史克制药有限公司',
      packCells: 20,
      cellUnit: '粒',
      packUnit: '盒',
      deptTotalPacks: 12,
      deptTotalCells: 15,
      totalPacks: 5,
      totalCells: 18,
      batchNo: 'B20240103',
      expDate: '20251203'
    },
    scenario: '药品拆零',
    complexity: '中等'
  }
]

// 医疗器械示例数据
const deviceScenarios = [
  {
    id: 'device1',
    title: '一次性注射器',
    description: '5ml规格，100支/盒大包装',
    data: {
      artName: '一次性注射器',
      artSpec: '5ml',
      producer: '山东威高集团医用高分子制品股份有限公司',
      packCells: 100,
      cellUnit: '支',
      packUnit: '盒',
      deptTotalPacks: 3,
      deptTotalCells: 25,
      totalPacks: 1,
      totalCells: 50,
      batchNo: 'D20240101',
      expDate: '20261201'
    },
    scenario: '器械拆零',
    complexity: '中等'
  },
  {
    id: 'device2',
    title: '医用口罩',
    description: '一次性医用口罩，50只/盒',
    data: {
      artName: '医用口罩',
      artSpec: '一次性',
      producer: '3M中国有限公司',
      packCells: 50,
      cellUnit: '只',
      packUnit: '盒',
      deptTotalPacks: 10,
      deptTotalCells: 30,
      totalPacks: 4,
      totalCells: 20,
      batchNo: 'D20240102',
      expDate: '20261202'
    },
    scenario: '器械拆零',
    complexity: '简单'
  }
]

// 特殊包装示例数据
const specialScenarios = [
  {
    id: 'special1',
    title: '胰岛素笔芯',
    description: '特殊包装，5支/盒',
    data: {
      artName: '胰岛素笔芯',
      artSpec: '3ml',
      producer: '诺和诺德(中国)制药有限公司',
      packCells: 5,
      cellUnit: '支',
      packUnit: '盒',
      deptTotalPacks: 2,
      deptTotalCells: 3,
      totalPacks: 1,
      totalCells: 2,
      batchNo: 'S20240101',
      expDate: '20251201'
    },
    scenario: '特殊包装',
    complexity: '复杂'
  },
  {
    id: 'special2',
    title: '维生素C泡腾片',
    description: '筒装包装，12片/筒',
    data: {
      artName: '维生素C泡腾片',
      artSpec: '1g',
      producer: '拜耳医药保健有限公司',
      packCells: 12,
      cellUnit: '片',
      packUnit: '筒',
      deptTotalPacks: 6,
      deptTotalCells: 8,
      totalPacks: 2,
      totalCells: 10,
      batchNo: 'S20240102',
      expDate: '20251202'
    },
    scenario: '特殊包装',
    complexity: '中等'
  },
  {
    id: 'special3',
    title: '玻璃酸钠滴眼液',
    description: '单包装，1瓶/盒',
    data: {
      artName: '玻璃酸钠滴眼液',
      artSpec: '5ml',
      producer: '参天制药(中国)有限公司',
      packCells: 1,
      cellUnit: '瓶',
      packUnit: '盒',
      deptTotalPacks: 15,
      deptTotalCells: 0,
      totalPacks: 8,
      totalCells: 0,
      batchNo: 'S20240103',
      expDate: '20251203'
    },
    scenario: '特殊包装',
    complexity: '简单'
  }
]

// 边界情况示例数据
const edgeScenarios = [
  {
    id: 'edge1',
    title: '零库存测试',
    description: '测试零库存情况的处理',
    data: {
      artName: '测试药品(零库存)',
      artSpec: '100mg',
      producer: '测试制药公司',
      packCells: 10,
      cellUnit: '粒',
      packUnit: '盒',
      deptTotalPacks: 0,
      deptTotalCells: 0,
      totalPacks: 0,
      totalCells: 0,
      batchNo: 'E20240101',
      expDate: '20251201'
    },
    scenario: '边界测试',
    complexity: '复杂'
  },
  {
    id: 'edge2',
    title: '大包装规格',
    description: '测试大包装数量的处理',
    data: {
      artName: '大包装测试药品',
      artSpec: '500mg',
      producer: '测试制药公司',
      packCells: 500,
      cellUnit: '片',
      packUnit: '瓶',
      deptTotalPacks: 2,
      deptTotalCells: 350,
      totalPacks: 1,
      totalCells: 200,
      batchNo: 'E20240102',
      expDate: '20251202'
    },
    scenario: '边界测试',
    complexity: '复杂'
  }
]

// 获取复杂度颜色
const getComplexityColor = (complexity: string) => {
  switch (complexity) {
    case '简单': return 'green'
    case '中等': return 'orange'
    case '复杂': return 'red'
    default: return 'blue'
  }
}

// 获取场景颜色
const getScenarioColor = (scenario: string) => {
  switch (scenario) {
    case '药品拆零': return 'blue'
    case '器械拆零': return 'purple'
    case '特殊包装': return 'cyan'
    case '边界测试': return 'red'
    default: return 'default'
  }
}

// 处理拆零盒整操作
const handleSplitPack = (scenario: any) => {
  if (wholeBoxRef.value) {
    wholeBoxRef.value.handleSplitPack(scenario.data)
  }
}

// 批量测试
const runBatchTest = () => {
  message.info('开始批量测试...')
  
  const testScenarios = [
    ...medicineScenarios.slice(0, 2),
    ...deviceScenarios.slice(0, 1),
    ...specialScenarios.slice(0, 1)
  ]
  
  let index = 0
  const runNext = () => {
    if (index < testScenarios.length) {
      const scenario = testScenarios[index]
      message.info(`正在测试: ${scenario.title}`)
      handleSplitPack(scenario)
      index++
      setTimeout(runNext, 2000)
    } else {
      message.success('批量测试完成！')
    }
  }
  
  runNext()
}
</script>

<template>
  <Card title="多场景应用 - 拆零盒整组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">多场景应用演示</Title>
      <Paragraph>
        拆零盒整组件支持多种不同的业务场景，包括药品拆零、医疗器械拆零、特殊包装处理和边界情况测试。
        以下展示了各种真实业务场景下的组件使用方法。
      </Paragraph>

      <!-- 操作说明 -->
      <Alert
        message="操作说明"
        type="info"
        show-icon
        style="margin-bottom: 24px;"
      >
        <template #description>
          <ul style="margin: 8px 0; padding-left: 20px;">
            <li>点击各场景卡片中的"拆零盒整"按钮体验不同场景</li>
            <li>组件会根据不同的包装规格自动调整计算逻辑</li>
            <li>支持药品、医疗器械等不同类型商品的拆零操作</li>
            <li>包含边界情况测试，验证组件的健壮性</li>
          </ul>
        </template>
      </Alert>

      <!-- 药品拆零场景 -->
      <div class="scenario-section">
        <Title :level="5">🏥 药品拆零场景</Title>
        <Paragraph>常见的药品库存拆零盒整操作，支持不同包装规格的药品。</Paragraph>
        
        <Row :gutter="[16, 16]">
          <Col 
            v-for="scenario in medicineScenarios" 
            :key="scenario.id"
            :xs="24" :sm="12" :lg="8"
          >
            <Card size="small" class="scenario-card">
              <template #title>
                <div class="card-title">
                  {{ scenario.title }}
                  <div class="card-tags">
                    <Tag :color="getScenarioColor(scenario.scenario)">{{ scenario.scenario }}</Tag>
                    <Tag :color="getComplexityColor(scenario.complexity)">{{ scenario.complexity }}</Tag>
                  </div>
                </div>
              </template>
              
              <div class="card-content">
                <p class="description">{{ scenario.description }}</p>
                <div class="stock-info">
                  <p><strong>规格:</strong> {{ scenario.data.artSpec }}</p>
                  <p><strong>包装:</strong> {{ scenario.data.packCells }}{{ scenario.data.cellUnit }}/{{ scenario.data.packUnit }}</p>
                  <p><strong>仓库库存:</strong> {{ scenario.data.deptTotalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.deptTotalCells }}{{ scenario.data.cellUnit }}</p>
                  <p><strong>批次库存:</strong> {{ scenario.data.totalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.totalCells }}{{ scenario.data.cellUnit }}</p>
                </div>
              </div>
              
              <template #actions>
                <Button 
                  type="primary" 
                  size="small"
                  @click="handleSplitPack(scenario)"
                >
                  拆零盒整
                </Button>
              </template>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <!-- 医疗器械场景 -->
      <div class="scenario-section">
        <Title :level="5">🔬 医疗器械拆零场景</Title>
        <Paragraph>医疗器械的包装拆零操作，通常包装数量较大。</Paragraph>
        
        <Row :gutter="[16, 16]">
          <Col 
            v-for="scenario in deviceScenarios" 
            :key="scenario.id"
            :xs="24" :sm="12" :lg="8"
          >
            <Card size="small" class="scenario-card">
              <template #title>
                <div class="card-title">
                  {{ scenario.title }}
                  <div class="card-tags">
                    <Tag :color="getScenarioColor(scenario.scenario)">{{ scenario.scenario }}</Tag>
                    <Tag :color="getComplexityColor(scenario.complexity)">{{ scenario.complexity }}</Tag>
                  </div>
                </div>
              </template>
              
              <div class="card-content">
                <p class="description">{{ scenario.description }}</p>
                <div class="stock-info">
                  <p><strong>规格:</strong> {{ scenario.data.artSpec }}</p>
                  <p><strong>包装:</strong> {{ scenario.data.packCells }}{{ scenario.data.cellUnit }}/{{ scenario.data.packUnit }}</p>
                  <p><strong>仓库库存:</strong> {{ scenario.data.deptTotalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.deptTotalCells }}{{ scenario.data.cellUnit }}</p>
                  <p><strong>批次库存:</strong> {{ scenario.data.totalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.totalCells }}{{ scenario.data.cellUnit }}</p>
                </div>
              </div>
              
              <template #actions>
                <Button 
                  type="primary" 
                  size="small"
                  @click="handleSplitPack(scenario)"
                >
                  拆零盒整
                </Button>
              </template>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <!-- 特殊包装场景 -->
      <div class="scenario-section">
        <Title :level="5">📦 特殊包装场景</Title>
        <Paragraph>特殊包装规格的处理，包括小包装、筒装、单包装等。</Paragraph>
        
        <Row :gutter="[16, 16]">
          <Col 
            v-for="scenario in specialScenarios" 
            :key="scenario.id"
            :xs="24" :sm="12" :lg="8"
          >
            <Card size="small" class="scenario-card">
              <template #title>
                <div class="card-title">
                  {{ scenario.title }}
                  <div class="card-tags">
                    <Tag :color="getScenarioColor(scenario.scenario)">{{ scenario.scenario }}</Tag>
                    <Tag :color="getComplexityColor(scenario.complexity)">{{ scenario.complexity }}</Tag>
                  </div>
                </div>
              </template>
              
              <div class="card-content">
                <p class="description">{{ scenario.description }}</p>
                <div class="stock-info">
                  <p><strong>规格:</strong> {{ scenario.data.artSpec }}</p>
                  <p><strong>包装:</strong> {{ scenario.data.packCells }}{{ scenario.data.cellUnit }}/{{ scenario.data.packUnit }}</p>
                  <p><strong>仓库库存:</strong> {{ scenario.data.deptTotalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.deptTotalCells }}{{ scenario.data.cellUnit }}</p>
                  <p><strong>批次库存:</strong> {{ scenario.data.totalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.totalCells }}{{ scenario.data.cellUnit }}</p>
                </div>
              </div>
              
              <template #actions>
                <Button 
                  type="primary" 
                  size="small"
                  @click="handleSplitPack(scenario)"
                >
                  拆零盒整
                </Button>
              </template>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <!-- 边界情况测试 -->
      <div class="scenario-section">
        <Title :level="5">⚠️ 边界情况测试</Title>
        <Paragraph>测试各种边界情况和异常处理，验证组件的健壮性。</Paragraph>
        
        <Row :gutter="[16, 16]">
          <Col 
            v-for="scenario in edgeScenarios" 
            :key="scenario.id"
            :xs="24" :sm="12" :lg="8"
          >
            <Card size="small" class="scenario-card edge-case">
              <template #title>
                <div class="card-title">
                  {{ scenario.title }}
                  <div class="card-tags">
                    <Tag :color="getScenarioColor(scenario.scenario)">{{ scenario.scenario }}</Tag>
                    <Tag :color="getComplexityColor(scenario.complexity)">{{ scenario.complexity }}</Tag>
                  </div>
                </div>
              </template>
              
              <div class="card-content">
                <p class="description">{{ scenario.description }}</p>
                <div class="stock-info">
                  <p><strong>规格:</strong> {{ scenario.data.artSpec }}</p>
                  <p><strong>包装:</strong> {{ scenario.data.packCells }}{{ scenario.data.cellUnit }}/{{ scenario.data.packUnit }}</p>
                  <p><strong>仓库库存:</strong> {{ scenario.data.deptTotalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.deptTotalCells }}{{ scenario.data.cellUnit }}</p>
                  <p><strong>批次库存:</strong> {{ scenario.data.totalPacks }}{{ scenario.data.packUnit }}{{ scenario.data.totalCells }}{{ scenario.data.cellUnit }}</p>
                </div>
              </div>
              
              <template #actions>
                <Button 
                  type="primary" 
                  size="small"
                  @click="handleSplitPack(scenario)"
                >
                  测试拆零
                </Button>
              </template>
            </Card>
          </Col>
        </Row>
      </div>

      <Divider />

      <!-- 批量测试 -->
      <div class="batch-test-section">
        <Title :level="5">🚀 批量测试</Title>
        <Paragraph>
          一键测试多个场景，验证组件在不同情况下的表现。
        </Paragraph>
        
        <div class="batch-actions">
          <Button 
            type="primary" 
            size="large"
            @click="runBatchTest"
          >
            开始批量测试
          </Button>
          <Text type="secondary" style="margin-left: 16px;">
            将依次测试药品、器械、特殊包装等场景
          </Text>
        </div>
      </div>

      <Divider />

      <!-- 代码示例 -->
      <div class="code-section">
        <Title :level="5">代码示例</Title>
        
        <CodeDemoVue 
          title="表格集成示例"
          :code="tableIntegration"
          description="在数据表格中集成拆零盒整功能"
        />
      </div>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
  </Card>
</template>

<style scoped>
.scenario-section {
  margin-bottom: 32px;
}

.scenario-card {
  height: 100%;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.scenario-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.scenario-card.edge-case {
  border-color: #ff7875;
}

.scenario-card.edge-case:hover {
  border-color: #ff4d4f;
  box-shadow: 0 2px 8px rgba(255, 77, 79, 0.1);
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.card-tags {
  display: flex;
  gap: 4px;
}

.card-content {
  padding: 0;
}

.description {
  color: #666;
  font-size: 12px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.stock-info p {
  margin-bottom: 4px;
  font-size: 12px;
  line-height: 1.3;
}

.batch-test-section {
  text-align: center;
  padding: 24px;
  background-color: #f6f8fa;
  border-radius: 8px;
  margin-bottom: 24px;
}

.batch-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
}

.code-section {
  margin-top: 24px;
}

:deep(.ant-card-actions) {
  background-color: #fafafa;
}

:deep(.ant-card-actions > li) {
  margin: 8px 0;
}
</style>
