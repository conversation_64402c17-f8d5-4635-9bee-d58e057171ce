<script lang="ts" setup>
import { Data, Format } from '@idmy/core'
import { Descriptions, DescriptionsItem, Popover } from 'ant-design-vue'
import BillDetail from '../BillDetail/BillDetail.vue'

const { data } = defineProps({
  data: { type: Object as PropType<Data>, required: true },
})

const billIds = computed(() => [data.billId])
</script>
<template>
  <Popover :mouseEnterDelay="0.3" :overlayInnerStyle="{ width: '800px' }" placement="right">
    <template #content>
      <Descriptions bordered size="small">
        <DescriptionsItem label="患者信息">{{ data.name }}</DescriptionsItem>
        <DescriptionsItem label="划价类型">
          <Format :value="data.billType" params="BillType" type="Enum" />
        </DescriptionsItem>
        <DescriptionsItem label="开单医生">{{ data.clinicianName }}</DescriptionsItem>
        <DescriptionsItem label="开单科室">{{ data.deptName }}</DescriptionsItem>
        <DescriptionsItem label="医疗类型">{{ data.medTypeName }}</DescriptionsItem>
        <DescriptionsItem label="医疗险种">
          <Format :value="data.insuranceTypeId" params="InsuranceType" type="Dict" />
        </DescriptionsItem>
        <DescriptionsItem label="诊疗代号">{{ data.visitId }}</DescriptionsItem>
        <DescriptionsItem v-if="data.recipeNo" label="处方号">{{ data.recipeNo }}</DescriptionsItem>
        <DescriptionsItem label="开单日期">
          <Format :value="data.createdAt" type="Datetime" />
        </DescriptionsItem>
        <DescriptionsItem label="划价金额">
          <Format :value="data.amount" type="Currency" />
        </DescriptionsItem>
        <DescriptionsItem label="划价流水">{{ data.billId }}</DescriptionsItem>
        <DescriptionsItem v-if="data.blueBillId" label="原划价单">{{ data.blueBillId }}</DescriptionsItem>
        <DescriptionsItem v-if="data.blueCashId" label="原收费单">{{ data.blueCashId }}</DescriptionsItem>
        <DescriptionsItem label="摘要">{{ data.billAbstract }}</DescriptionsItem>
      </Descriptions>
      <BillDetail :billIds="billIds" :showHeader="false" class="mt-8px" />
    </template>
    <slot />
  </Popover>
</template>
