{"name": "@mh-hsd/base", "version": "1.0.1", "type": "module", "main": "umd/index.js", "module": "es/index.js", "types": "index.d.ts", "style": "index.css", "files": ["es", "umd", "src/*.d.ts", "src/**/*.d.ts", "index.d.ts", "index.css"], "scripts": {"publish:component": "cd ../../ && pnpm run publish:component Hsd/Base"}, "peerDependencies": {"@vueuse/core": "~13.0.0", "@ant-design/icons-vue": "~7.0.1", "ant-design-vue": "~4.2.6", "lodash-es": "~4.17.21", "vue": "~3.5.13", "vue-router": "~4.5.0"}, "dependencies": {"@mh-base/modal": "workspace:*"}, "publishConfig": {"access": "public"}}