<script setup lang="ts">
import { Select, Spin } from 'ant-design-vue'
import { Data, http } from '@idmy/core'
import { ref, watch, onMounted } from 'vue'

const props = defineProps({
  own: {
    type: Boolean,
    default: false,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
})

const modelValue = defineModel<any>()

// 组织机构列表
const orgList = ref<any[]>([])
const loading = ref(false)

// 获取所有组织机构列表
const fetchAllOrgs = async () => {
  loading.value = true
  try {
    const arr = await http.post<Data[]>('/hip-base/org/findAll', {}, { appKey: 'hip' })
    orgList.value = arr
      .filter(row => row.disabled !== 1)
      .map(row => ({
        code: row.orgCode,
        id: row.orgId,
        value: row.orgId,
        label: row.orgName,
        data: row,
      }))
  } catch (error) {
    console.error('获取所有组织机构失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取当前用户的组织机构列表
const fetchCurrentUserOrgs = async () => {
  loading.value = true
  try {
    const orgs = await http.post<Data[]>('/eam/user/org/list', {}, { appKey: 'eam' })
    orgList.value = orgs.map(org => ({
      code: org.orgCode,
      id: org.orgId,
      value: org.orgId,
      label: org.orgName,
      data: org,
    }))
  } catch (error) {
    console.error('获取当前用户组织机构失败:', error)
  } finally {
    loading.value = false
  }
}

// 根据own属性加载不同的组织机构列表
const loadOrgList = () => {
  if (props.own) {
    fetchCurrentUserOrgs()
  } else {
    fetchAllOrgs()
  }
}

// 初始加载
onMounted(() => {
  loadOrgList()
})

// 监听own属性变化，重新加载列表
watch(
  () => props.own,
  () => {
    loadOrgList()
  }
)
</script>

<template>
  <Spin :spinning="loading">
    <Select
      v-model:value="modelValue"
      :mode="props.multiple ? 'multiple' : undefined"
      :options="orgList"
      placeholder="请选择组织机构"
      :fieldNames="{ label: 'label', value: 'value' }"
      :showSearch="true"
      :filterOption="(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())"
      :allowClear="true"
      style="width: 100%"
    />
  </Spin>
</template>
