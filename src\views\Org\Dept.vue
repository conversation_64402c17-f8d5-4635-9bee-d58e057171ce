<script setup lang="ts">
import { OrgSelect, OrgDept } from '@mh-hip/org'
import { Switch, Divider } from 'ant-design-vue'
import { ref } from 'vue'

// 选中的组织机构ID
const orgId = ref<number>()

// 选中的部门ID
const deptId = ref<number>()

// 是否多选
const isMultiple = ref(false)
</script>

<template>
  <div p-16px>
    <div mb-16px>
      <h3>选择组织机构</h3>
      <OrgSelect v-model="orgId" w-200px />
      <div mt-8px>选中的组织机构ID: {{ orgId }}</div>
    </div>

    <Divider />

    <div flex items-center mb-16px>
      <span mr-8px>是否支持多选：</span>
      <Switch v-model:checked="isMultiple" />
      <span ml-8px>{{ isMultiple ? '是' : '否' }}</span>
    </div>

    <div mb-16px>
      <h3>选择部门</h3>
      <OrgDept v-model="deptId" :orgId="orgId" :multiple="isMultiple" w-200px />
      <div mt-8px>选中的部门ID: {{ deptId }}</div>
    </div>
  </div>
</template>
