<script setup lang="ts">
import { ref } from 'vue'
import DiseaseSelector from './DiseaseSelector.vue'
import { Modal, Button } from 'ant-design-vue'
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'

const props = defineProps({
  // 弹窗标题
  title: {
    type: String,
    default: '选择病种'
  },
  // 弹窗可见性
  visible: {
    type: Boolean,
    default: false
  },
  // 患者ID
  patientId: {
    type: Number,
    default: undefined
  },
  // 当前选择的病种
  modelValue: {
    type: [Number, String],
    default: undefined
  },
  // 弹窗宽度
  width: {
    type: [Number, String],
    default: 800
  },
  // 弹窗高度
  height: {
    type: [Number, String],
    default: 600
  }
})

// 向父组件发送事件
const emit = defineEmits(['update:visible', 'update:modelValue', 'select', 'cancel'])

// 选中的病种ID
const selectedDiseaseCode = ref(props.modelValue)
// 选中的病种数据
const selectedDiseaseData = ref<any>(null)

// 病种选择器组件引用
const diseaseSelectorRef = ref()

// 病种选择回调
const handleDiseaseSelect = (disease: any) => {
  selectedDiseaseCode.value = disease.diseaseCode
  selectedDiseaseData.value = disease
}

// 确认选择
const handleOk = () => {
  emit('update:modelValue', selectedDiseaseCode.value)
  emit('select', selectedDiseaseData.value)
  emit('update:visible', false)
}

// 取消选择
const handleCancel = () => {
  emit('update:visible', false)
  emit('cancel')
}

// 刷新数据
const refresh = () => {
  if (diseaseSelectorRef.value) {
    if (props.patientId) {
      diseaseSelectorRef.value.loadPatientDiseases()
    } else {
      diseaseSelectorRef.value.loadDiseaseCats()
    }
  }
}

// 暴露方法给父组件
defineExpose({
  refresh
})
</script>

<template>
  <Modal
    :title="title"
    :open="visible"
    :width="width"
    @cancel="handleCancel"
    :destroyOnClose="true"
  >
    <template #footer>
      <Button type="dashed" @click="handleCancel">
        <template #icon>
          <close-circle-outlined/>
        </template>
        关闭
      </Button>
      <Button type="primary" @click="handleOk">
        <template #icon>
          <check-circle-outlined/>
        </template>
        确认
      </Button>
    </template>
    <div :style="{ height: typeof height === 'number' ? `${height}px` : height }">
      <DiseaseSelector
        ref="diseaseSelectorRef"
        v-model="selectedDiseaseCode"
        :patientId="patientId"
        :isModal="true"
        @select="handleDiseaseSelect"
      />
    </div>
  </Modal>
</template>
