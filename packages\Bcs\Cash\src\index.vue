<script lang="ts" setup>
import { OpenFullRefund, OpenRefund, OpenUndo } from '@mh-bcs/settle'
import { Button } from 'ant-design-vue'
import CashDetail from './Detail/index.vue'
import CashPage from './Page.vue'

defineProps({
  showCond: { type: Boolean, default: true },
  showCount: { type: Boolean, default: true },
})

const cashRef = ref()
const onLoad = () => {
  cashRef.value?.onLoad()
}

const openCashDetail = (cashId: number) =>
  Modal.open({
    component: CashDetail,
    escClosable: true,
    maskClosable: true,
    position: 'right',
    props: { cashId, onLoad },
    title: `结算详情`,
    width: 4,
  })

const router = useRouter()
const toInvoice = (cashId: number) => router.replace('charging?tabKey=Invoice&S_EQ_t_invoice__Cash_ID=' + cashId)
</script>

<template>
  <CashPage ref="cashRef" :showCond="showCond" :showCount="showCount">
    <template #bodyCell="{ col, row }">
      <a v-if="col.dataIndex === 'cashId'" class="color-primary" @click="openCashDetail(row.cashId)">{{ row.cashId }}</a>
      <Button v-if="col.dataIndex === 'invoiceCount'" size="small" type="link" @click="toInvoice(row.cashId)">{{ row.invoiceCount }}</Button>
      <div flex v-if="col.dataIndex === 'op' && row.cashType === 'OUTPATIENT'">
        <template v-if="row.status === 'OK'">
          <OpenFullRefund :data="row" @ok="onLoad" />
        </template>
        <OpenRefund :data="row" @ok="onLoad" />
        <OpenUndo :data="row" @ok="onLoad" />
      </div>
    </template>
  </CashPage>
</template>
