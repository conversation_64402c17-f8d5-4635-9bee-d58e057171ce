<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

import DiseaseDictExample from './examples/DiseaseDictExample.vue'
import DiseaseSelectorExample from './examples/DiseaseSelectorExample.vue'
import DiseaseSelectorModalExample from './examples/DiseaseSelectorModalExample.vue'
import DiseaseUseSelectorExample from './examples/DiseaseUseSelectorExample.vue'

const { Title, Paragraph } = Typography
const activeKey = ref('dict')
</script>

<template>
  <Card title="Disease 组件" class="mb-16px">
    <Paragraph>病种相关组件，包括病种字典、病种选择器和病种选择弹窗。</Paragraph>
    <Tabs v-model:activeKey="activeKey" tabPosition="left">
      <Tabs.TabPane key="dict" tab="病种字典">
        <DiseaseDictExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="selector" tab="病种选择器">
        <DiseaseSelectorExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="modal" tab="病种选择弹窗">
        <DiseaseSelectorModalExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="useSelector" tab="推荐用法">
        <DiseaseUseSelectorExample />
      </Tabs.TabPane>
    </Tabs>
  </Card>
</template>

<style scoped>
:deep(.ant-tabs-left > .ant-tabs-content-holder) {
  border-left: 1px solid #f0f0f0;
  padding-left: 16px;
}
:deep(.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab) {
  padding: 8px 16px;
}
:deep(.ant-card-body) {
  padding: 24px;
}
</style> 