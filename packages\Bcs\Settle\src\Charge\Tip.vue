<script lang="ts" setup>
import { Format } from '@idmy/core'
import { ChargeContext, chargeInjectKey } from '@mh-bcs/util'

defineProps({
  totalAmount: { type: Number },
})
const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})
</script>

<template>
  <div v-if="Boolean(ctx.card?.readCard?.psn_name) && Boolean(ctx.visit?.patientName) && ctx.card?.readCard?.psn_name !== ctx.visit?.patientName" class="bgc-warn p-12px tac fs16">
    <div class="color-error">
      医保刷卡/刷脸姓名【<strong>{{ ctx.card?.readCard?.psn_name }}</strong
      >】和当前收费患者姓名【<strong>{{ ctx.visit?.patientName }}</strong
      >】不是同一个人！
    </div>
  </div>
  <div v-if="ctx.miTrans?.miTransId && totalAmount !== ctx.miTrans?.amount" class="bgc-error p-12px tac" text-16px>
    <Format :value="totalAmount ?? 0" prefix="结算总金额：" type="Currency" />
    和
    <Format :value="ctx.miTrans?.amount ?? 0" prefix="医保总金额：" type="Currency" />
    不一致！请检查医保是否需要重新结算。
  </div>
</template>
