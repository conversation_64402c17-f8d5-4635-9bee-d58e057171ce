<script setup lang="ts">
import { index as VisitInfo } from '@mh-inpatient-hsd/visit-info'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from './code/VisitInfoCode'

const { Title, Paragraph } = Typography

// 模拟数据
const visitId = 84503
const patientInfo = {
  visitId: 84503,
  patientId: 'P000123456',
  patientName: '张三',
  gender: '男',
  age: '45岁',
  bedNo: '1',
  sectionName: '内三科住院',
  admissionDate: '2025-03-14',
  diagnosisName: '高血压',
  doctorName: '李医生'
}
</script>

<template>
  <Card title="患者信息组件" class="mb-16px">
    <div mb-16px>
      <Title :level="4">患者信息组件</Title>
      <Paragraph>用于展示患者基本信息。</Paragraph>
      
      <VisitInfo :visit-id="visitId" :patient-info="patientInfo" />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
