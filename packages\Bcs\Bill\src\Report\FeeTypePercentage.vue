<script lang="ts">
import { listSql } from '@mh-base/core'

const columns = [
  { title: '费用类型', dataIndex: 'name', key: 'name' },
  { title: '费用', dataIndex: 'amount', key: 'amount', align: 'right' },
  { title: '百分比', dataIndex: 'percentage', key: 'percentage', align: 'right' },
]

function addPercentageField(data) {
  // 计算总金额
  const total = data.reduce((sum, item) => sum + Number(item.amount), 0)

  // 处理除零情况
  if (total === 0) return data.map(item => ({ ...item, percentage: 0 }))

  // 添加百分比字段
  return data.map(item => ({
    ...item,
    percentage: parseFloat(((item.amount / total) * 100).toFixed(2)) + '%',
  }))
}

const api = async (visitId: number, billDates?: [number, number]) => {
  if (!visitId) {
    return
  }
  let billDate = ``
  if (billDates?.[0] && billDates?.[1]) {
    billDate = `and b.bill_date >= ${billDates[0]} and b.bill_date <= ${billDates[1]}`
  }
  const data = await listSql(`
    select
      ft.Fee_Type_name as name,
      sum(bd.Amount) as amount
    from microhis_bcs.t_bill_detail bd
         inner join microhis_bcs.t_bill b on b.BSeqID = bd.BSeqID
         inner join hip_mdi.t_article a on bd.art_id = a.art_id
         inner join hip_mdi.t_fee_type ft on bd.fee_type_id = ft.fee_type_id
    where b.paid_status in (3, 4)
      and b.Visit_ID = ${visitId} ${billDate}
    group by bd.fee_type_id, ft.fee_type_name
  `)
  return data.length ? addPercentageField(data) : []
}
</script>
<script setup lang="ts">
import { add, Api, Data, Format } from '@idmy/core'
import { InputNumber, Popover, Tag, Tooltip, Table } from 'ant-design-vue'

defineProps({
  showHeader: { type: Boolean, default: true },
  bordered: { type: Boolean, default: true },
  visitId: { type: Number, required: true },
  billDates: { type: Array },
})
</script>
<template>
  <Api :load="() => api(visitId, billDates)" input-watch v-slot="{ output, loading }" type="Array" spin>
    <Table :bordered="bordered" :columns="columns" :dataSource="output" :loading="loading" :pagination="false" rowKey="name" :showHeader="showHeader" size="small">
      <template #bodyCell="{ column: col, record: row }">
        <Format v-if="col.dataIndex === 'amount'" :value="row.amount" type="Currency" />
      </template>
    </Table>
  </Api>
</template>
