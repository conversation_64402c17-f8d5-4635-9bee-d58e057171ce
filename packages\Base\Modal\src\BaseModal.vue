<template>
  <a-modal v-model:open="modalVisible" :width="full ? '100%' : width"
           :wrap-class-name="full ? 'base-modal full-base-modal' : 'base-modal'" :okButtonProps="okButtonProps" :cancelButtonProps="cancelButtonProps">
    <slot></slot>
    <template #title>
      <div v-if="hasHeader" ref="modalTitleRef" style="width: 100%; cursor: move">
        <a-space>
          <div style="font-size: 12px; color: #333">
            <DragOutlined />
          </div>
          <h3 style="margin-bottom: 0">{{ title }}</h3>
          <div v-if="isFull" class="full-icon text-18px" style="margin-left: 10px;" @click.stop="toggleFull">
            <FullscreenExitOutlined v-if="full" />
            <FullscreenOutlined v-else />
          </div>
        </a-space>
      </div>
    </template>
    <template v-if="hasFooter" #footer>
      <slot name="footer" />
    </template>
    <template #modalRender="{ originVNode }">
      <div :style="transformStyle">
        <component :is="originVNode" />
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">

import { ref, h, computed, CSSProperties, watch, watchEffect, useSlots } from 'vue';
import { useDraggable } from '@vueuse/core';

import * as antDesignVue from 'ant-design-vue'
import { FullscreenOutlined, FullscreenExitOutlined, CheckCircleOutlined, CloseCircleOutlined, DragOutlined } from '@ant-design/icons-vue'
const { Modal: AModal, Space: ASpace } = antDesignVue

const slots = useSlots()
const hasFooter = (Object.keys(slots).includes('footer'))

const modalVisible = defineModel('open', {
  type: Boolean,
  default: false
})

const { title, width } = defineProps({
  title: {
    type: String,
    default: '系统提示'
  },
  width: {
    type: String || Number,
    default: '520px'
  },
  // 需要显示头部
  hasHeader: {
    type: Boolean,
    default: true
  },
  // 全屏
  isFull: {
    type: Boolean,
    default: false
  }
})
const full = ref(false)

const toggleFull = () => {
  full.value = !full.value
}

const okButtonProps: any = {
  icon: h(CheckCircleOutlined)
}
const cancelButtonProps: any = {
  type: 'dashed',
  icon: h(CloseCircleOutlined)
}

const modalTitleRef = ref<HTMLElement>()
const { x, y, isDragging } = useDraggable(modalTitleRef);

const startX = ref<number>(0);
const startY = ref<number>(0);
const startedDrag = ref(false);
const transformX = ref(0);
const transformY = ref(0);
const preTransformX = ref(0);
const preTransformY = ref(0);
const dragRect = ref({ left: 0, right: 0, top: 0, bottom: 0 });
watch([x, y], () => {
  if (!startedDrag.value) {
    startX.value = x.value;
    startY.value = y.value;
    const bodyRect = document.body.getBoundingClientRect();
    const titleRect: any = modalTitleRef.value?.getBoundingClientRect();
    dragRect.value.right = bodyRect.width - titleRect.width;
    dragRect.value.bottom = bodyRect.height - titleRect.height;
    preTransformX.value = transformX.value;
    preTransformY.value = transformY.value;
  }
  startedDrag.value = true;
});
watch(isDragging, () => {
  if (!isDragging) {
    startedDrag.value = false;
  }
});

watchEffect(() => {
  if (startedDrag.value) {
    transformX.value =
      preTransformX.value +
      Math.min(Math.max(dragRect.value.left, x.value), dragRect.value.right) -
      startX.value;
    transformY.value =
      preTransformY.value +
      Math.min(Math.max(dragRect.value.top, y.value), dragRect.value.bottom) -
      startY.value;
  }
});
const transformStyle = computed<CSSProperties>(() => {
  return {
    transform: `translate(${transformX.value}px, ${transformY.value}px)`,
  };
})
</script>

<style lang="less" scoped>
.text-18px {
  font-size: 18px
}
</style>
