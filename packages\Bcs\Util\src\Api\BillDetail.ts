import { Data, http } from '@idmy/core'

export function findByMainIdApi(id: any) {
  return http.post('/api/bcs/billdetail/findByMainId', { mainId: id }, { appKey: 'bcs' })
}

export function findByMainIdsApi(mainIds: number[]) {
  return http.post('/api/bcs/billdetail/findByMainIds', { mainIds }, { appKey: 'bcs' })
}

export function pageDetailApi(params: any) {
  return http.post('/api/bcs/billdetail/page', params, { appKey: 'bcs' })
}

export function listFullBillDetailsByBillIds(billIds: number[]): Promise<Data[]> {
  return http.post('/api/bcs/BillDetail/listFullByBillIds', billIds, { appKey: 'bcs' })
}

export function listBillDetailsByBillIds(billIds: number[]): Promise<Data[]> {
  return http.post('/api/bcs/BillDetail/listByBillIds', billIds, { appKey: 'bcs' })
}

export async function updateSelfRatioByVisitIdAndArtId(body: Data[]) {
  if (!body.length) return
  return await http.post('/api/bcs/billdetail/updateSelfRatioByVisitIdAndArtId', body)
}

export function countFeeTypeAmountByCashId(cashId: number): Promise<Data[]> {
  return http.post('/api/bcs/BillDetail/countFeeTypeAmountByCashId/' + cashId, {}, { appKey: 'bcs' })
}

export function listMergedPaidByBlueCashId(cashId: number): Promise<Data[]> {
  return http.post(`/api/bcs/BillDetail/listMergedPaidByBlueCashId/${cashId}`, {}, { appKey: 'bcs' })
}

export function listMergedUnpaidByCashId(cashId: number): Promise<Data[]> {
  return http.post(`/api/bcs/BillDetail/listMergedUnpaidByCashId/${cashId}`, {}, { appKey: 'bcs' })
}
