<script lang="ts" setup>
import { add, Data, Format, round, subtract } from '@idmy/core'
import { ChargeContext, chargeInjectKey, PaymentType, usePay } from '@mh-bcs/util'
import { Button, Col, Input, InputNumber, Row } from 'ant-design-vue'

const { notTrans, first } = defineProps({
  first: { type: String as PropType<PaymentType>, default: 'CASH' },
  combine: { type: Boolean, default: false },
  notTrans: { type: Array as PropType<Data[]>, default: () => [] },
})

const ctx = inject<ChargeContext>(chargeInjectKey, <ChargeContext>{})

const realAmount = ref()
const balance = computed(() => subtract(realAmount.value ?? 0, ctx.payAmount ?? 0))
const findZero = computed(() => ((realAmount.value ?? 0) > Number(ctx.payAmount) ? balance.value : 0))
const suggestFindZero = computed(() => subtract(realAmount.value > 999999 ? 0 : realAmount.value ?? 0, round(ctx.payAmount ?? 0, 1)))

const { onPay, disabled } = usePay(ctx, notTrans)

const cashRef = ref()
const combineRef = ref()
const focus = () => nextTick(() => cashRef.value?.focus())

watch(
  () => ctx.unpaidAmount,
  () => focus()
)

onMounted(() => {
  nextTick(() => {
    focus()
  })
})
</script>
<template>
  <div class="f1">
    <div v-if="combine" flex flex-wrap justify-between>
      <div v-for="row in notTrans" :key="row.paymentType" flex items-center mb-8px>
        <span class="tar pr-4px" style="min-width: 60px">{{ row.paymentName }}</span>
        <InputNumber ref="combineRef" v-model:value="row.payAmount" :controls="false" :min="0" :precision="2" class="w-160px text-24px h-36px" />
      </div>
    </div>
    <div flex mt-16px>
      <div class="f1">
        <Row>
          <Col :span="12">
            <div flex items-center>
              <span w-60px>现金收费</span>
              <InputNumber v-model:value="ctx.payAmount" :controls="false" :disabled="ctx.isZero" :max="ctx.unpaidAmount" :min="0" :precision="2" class="f1 text-24px" h-36px />
            </div>
          </Col>
          <Col :span="1" />
          <Col :span="11">
            <Format
              :value="findZero > 999999 ? 0 : findZero"
              component="div"
              flex
              items-center
              prefix="现金找零"
              prefix-class="w-65px"
              prefix-component="div"
              type="Currency"
              value-class="text-24px"
            />
          </Col>
        </Row>
        <div class="h-8px"></div>
        <Row>
          <Col :span="12">
            <div flex items-center>
              <span class="w-60px">实收现金</span>
              <Input ref="cashRef" v-model:value.number="realAmount" allowClear class="f1 text-24px h-36px" />
            </div>
          </Col>
          <Col :span="1" />
          <Col :span="11">
            <Format
              :value="suggestFindZero > 0 ? suggestFindZero : 0"
              :value-class="findZero > 0 ? 'error font-bold text-24px' : 'text-24px'"
              component="div"
              flex
              items-center
              params="1"
              prefix="建议找零"
              prefix-class="w-65px"
              prefix-component="div"
              type="Currency"
            />
          </Col>
        </Row>
      </div>
      <Button :disabled="disabled || add(ctx.payAmount, ...notTrans.map(row => row.payAmount)) > ctx.unpaidAmount" :loading="ctx.paying" h-80px type="primary" w-64px @click="onPay()">支付 </Button>
    </div>
  </div>
</template>
