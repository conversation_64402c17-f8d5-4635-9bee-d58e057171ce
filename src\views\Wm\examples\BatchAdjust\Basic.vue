<script setup lang="ts">
import { BatchAdjust } from '@mh-wm/batch-adjust'
import { Card, Typography, Divider, Button } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from '../code/BatchAdjustCode'

const { Title } = Typography

// 批号调整组件引用
const batchAdjustRef = ref()

// 模拟数据
const mockDeptCode = 'DEPT001'
const mockArt = {
  artId: 1001,
  artName: '阿莫西林胶囊',
  artSpec: '0.25g*24粒',
  producer: '哈药集团制药总厂',
  packMaterial: '铝塑',
  packCells: 24,
  cellUnit: '粒',
  packUnit: '盒',
  rackNo: 'A-01-01'
}

// 打开批号调整弹窗
const openBatchAdjust = () => {
  batchAdjustRef.value.init(mockDeptCode, mockArt)
}

// 关闭回调
const handleClose = () => {
  console.log('批号调整完成')
}
</script>

<template>
  <Card title="基础用法 - 批号调整" class="mb-16px">
    <div mb-16px>
      <Title :level="4">批号调整组件</Title>
      <p>批号调整组件是一个弹窗组件，用于调整药品的批号信息。</p>
      <Button type="primary" @click="openBatchAdjust" mb-8px>打开批号调整弹窗</Button>
      <BatchAdjust ref="batchAdjustRef" @close="handleClose" />
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        点击按钮将打开批号调整弹窗，可以进行批号调整操作
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="basicUsage"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
