<script lang="ts" setup>
import type { Data } from '@idmy/core'
import { add, Format, Modal, subtract, useLoading } from '@idmy/core'
import { CashActionTypeEnum, CashTypeEnum, PaymentTypeEnum, toPaymentType } from '@mh-bcs/util'
import Cash from '~/components/cash-check/detail/Cash.vue'
import { countCheckOfCash } from '../services.ts'

interface CashRecord {
  paymentId: number
  paymentName: string
  plusQty: number
  minusQty: number
  plusAmt: number
  minusAmt: number
  insuranceTypeId?: number
}

const { cashTypeIds, checkId, miShowMode, endAt } = defineProps({
  cashType: { type: Number as PropType<CashTypeEnum>, required: true },
  cashTypeIds: { type: Array as PropType<CashTypeEnum[]>, required: true },
  checkId: { type: Number },
  endAt: { type: Object },
  miShowMode: { type: String, required: true },
  isShowRate: { type: Boolean },
})

const emit = defineEmits(['load'])

const cashLs = ref<CashRecord[]>([])

const [onLoad] = useLoading(async () => {
  const arr = await countCheckOfCash(cashTypeIds, checkId, endAt)
  if (miShowMode === 'a') {
    const map = new Map()
    arr.forEach((row: Data) => {
      row.paymentMode = row.paymentName
      const item = map.get(row.paymentId)
      if (item) {
        item.plusQty = add(item.plusQty, row.plusQty)
        item.minusQty = add(item.minusQty, row.minusQty)
        item.plusAmt = add(item.plusAmt, row.plusAmt)
        item.minusAmt = add(item.minusAmt, row.minusAmt)
      } else {
        if (row.paymentId === PaymentTypeEnum.MI_FUND) {
          row.paymentName = '医保'
        }
        map.set(row.paymentId, row)
      }
    })
    cashLs.value = Array.from(map.values())
  } else if (miShowMode === 'b') {
    const map = new Map()
    arr.forEach((row: Data) => {
      row.paymentMode = row.paymentName
      const key = row.paymentId === PaymentTypeEnum.MI_FUND ? row.paymentId + '' + row.insuranceTypeId : row.paymentId
      const item = map.get(key)
      if (item) {
        item.plusQty = add(item.plusQty, row.plusQty)
        item.minusQty = add(item.minusQty, row.minusQty)
        item.plusAmt = add(item.plusAmt, row.plusAmt)
        item.minusAmt = add(item.minusAmt, row.minusAmt)
      } else {
        if (row.paymentId === PaymentTypeEnum.MI_FUND) {
          row.paymentName = '医保'
        }
        map.set(key, row)
      }
    })
    cashLs.value = Array.from(map.values())
  } else if (miShowMode === 'aa') {
    const map = new Map()
    arr.forEach((row: Data) => {
      row.paymentMode = row.paymentName
      const key = row.paymentId === PaymentTypeEnum.MI_FUND ? row.paymentId : row.paymentId + '' + Boolean(row.insuranceTypeId)
      const item = map.get(key)
      if (item) {
        item.plusQty = add(item.plusQty, row.plusQty)
        item.minusQty = add(item.minusQty, row.minusQty)
        item.plusAmt = add(item.plusAmt, row.plusAmt)
        item.minusAmt = add(item.minusAmt, row.minusAmt)
      } else {
        if (row.insuranceTypeId && !row.paymentName.includes('医保')) {
          row.paymentName = '医保' + row.paymentName
        }
        map.set(key, row)
      }
    })
    cashLs.value = Array.from(map.values())
  } else if (miShowMode === 'bb') {
    arr.forEach((row: Data) => {
      row.paymentMode = row.paymentName
      if (row.insuranceTypeId && !row.paymentName.includes('医保')) {
        row.paymentName = '医保' + row.paymentName
      }
    })
    cashLs.value = arr
  }
}, true)

watch([() => miShowMode, () => cashTypeIds], () => onLoad())

const total = computed(() => {
  const tmp = {
    plusAmt: 0,
    minusAmt: 0,
    total: 0,
    prepaid: 0,
    cash: 0,
    mi: 0,
    hasPrepaid: false,
  }
  for (const row of cashLs.value) {
    tmp.plusAmt = add(tmp.plusAmt, row.plusAmt)
    tmp.minusAmt = add(tmp.minusAmt, -row.minusAmt)
    if (row.paymentId === PaymentTypeEnum.PREPAID) {
      tmp.prepaid = add(tmp.prepaid, add(row.plusAmt, row.minusAmt))
      tmp.hasPrepaid = true
    }
    if (row.paymentId === PaymentTypeEnum.CASH) {
      tmp.cash = add(tmp.cash, add(row.plusAmt, row.minusAmt))
    }
    if (row.paymentId === PaymentTypeEnum.MI_FUND) {
      tmp.mi = add(tmp.mi, add(row.plusAmt, row.minusAmt))
    }
  }
  tmp.total = subtract(tmp.plusAmt, tmp.minusAmt)

  return tmp
})

watch(
  () => total.value,
  tmp => {
    emit('load', subtract(tmp.total, tmp.prepaid), tmp.mi, tmp.cash)
  },
  { once: true }
)

const rows = computed(() =>
  cashLs.value
    .filter(row => row.paymentId !== PaymentTypeEnum.PREPAID)
    .map((row: CashRecord) => ({
      ...row,
      paymentType: toPaymentType(row.paymentId),
      ratio: add(row.plusAmt, row.minusAmt) / total.value.total,
    }))
)

const [openCash] = useLoading(async (row: Data, actionType?: CashActionTypeEnum) => {
  let title = row.paymentName
  if (actionType) {
    title += actionType === CashActionTypeEnum.Refunded ? '退款记录' : '收款记录'
  } else {
    title += '结算记录'
  }
  const props: any = { cashTypeIds, paymentId: row.paymentId, actionType, checkId, endAt }
  if (miShowMode == 'a') {
  } else if (miShowMode == 'aa') {
    if (row.insuranceTypeId) {
      props.insuranceTypeIdNotNull = true
    } else {
      props.paymentName = row.paymentMode
    }
  } else if (miShowMode === 'b') {
    props.insuranceTypeId = row.insuranceTypeId
  } else if (miShowMode === 'bb') {
    props.insuranceTypeId = row.insuranceTypeId
    if (!row.insuranceTypeId) {
      props.paymentName = row.paymentMode
    }
  }
  Modal.b.open({
    component: Cash,
    props,
    escClosable: true,
    maskClosable: true,
    title,
    position: 'right',
    width: 5,
  })
})
</script>

<template>
  <table v-if="rows.length > 0" class="print-table mt-8px">
    <tr>
      <th :colspan="isShowRate ? 9 : 8" class="tac">支付方式统计</th>
    </tr>
    <tr>
      <th>支付方式</th>
      <th v-if="!miShowMode.includes('a')">险种</th>
      <th>收费笔数</th>
      <th>退费笔数</th>
      <th>合计笔数</th>
      <th>收费金额</th>
      <th>退费金额</th>
      <th>实收金额</th>
      <th v-if="isShowRate">支付占比</th>
    </tr>
    <tr v-for="row in rows">
      <td>
        {{ row.paymentName }}
      </td>
      <td v-if="!miShowMode.includes('a')">
        {{ row.insuranceName }}
      </td>
      <td>
        <Format :value="row.plusQty" class="color-black" component="a" type="Integer" @click="openCash(row, CashActionTypeEnum.Normal)" />
      </td>
      <td>
        <Format :value="row.minusQty" class="color-black" component="a" type="Integer" @click="openCash(row, CashActionTypeEnum.Refunded)" />
      </td>
      <td>
        <Format :value="add(row.plusQty, row.minusQty)" class="color-black" component="a" type="Integer" @click="openCash(row)" />
      </td>
      <td>
        <Format :value="row.plusAmt ?? 0" type="Currency" />
      </td>
      <td>
        <Format :value="-row.minusAmt ?? 0" type="Currency" />
      </td>
      <td>
        <Format :value="subtract(row.plusAmt, -row.minusAmt)" type="Currency" />
      </td>
      <td v-if="isShowRate">
        <Format :value="isNaN(row.ratio) ? 0 : row.ratio" type="Percent" />
      </td>
    </tr>
    <tr>
      <td :colspan="isShowRate ? 9 : 8">
        <div flex justify-between>
          <strong>支付方式合计</strong>
          <div>
            <template v-if="cashType === CashTypeEnum.deposit">
              <Format :value="total.plusAmt" class="mr-16px primary" prefix="收费总额：" type="Currency" />
              <Format :value="total.minusAmt" class="mr-16px error" prefix="退费总额：" type="Currency" />
            </template>
            <template v-else>
              <Format :value="total.plusAmt" class="mr-16px" prefix="收费总额：" type="Currency" />
              <Format :value="total.minusAmt" class="mr-16px" prefix="退费总额：" type="Currency" />
              <Format v-if="total.hasPrepaid" :value="total.prepaid" class="mr-16px" prefix="预交金使用：" type="Currency" />
              <Format :value="subtract(total.total, total.prepaid)" prefix="实收总额：" type="Currency" value-class="primary b" />
            </template>
          </div>
        </div>
      </td>
    </tr>
  </table>
</template>
