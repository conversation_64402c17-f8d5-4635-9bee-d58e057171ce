<script setup lang="ts">
import { OrgSelect } from '@mh-hip/org'
import { Card, Typography, Divider, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from '../code/OrgSelectCode'

const { Title } = Typography

// 选择值
const orgValue = ref<any>()
</script>

<template>
  <Card title="基础用法 - 显示所有组织机构" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="24">
        <div>
          <div mb-8px>组织机构：</div>
          <OrgSelect v-model="orgValue" style="width: 100%" />
          <div mt-8px>选中值: {{ orgValue }}</div>
        </div>
      </Col>
    </Row>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
