<script lang="ts" setup>
import { Api, Data, Format } from '@idmy/core'
import { TransStatusHelp } from '@mh-bcs/help'
import { listFullPaymentsByCashId } from '@mh-bcs/util'
import { Table } from 'ant-design-vue'
import { PropType } from 'vue'

defineProps({
  cashId: { type: Number as PropType<number>, required: true },
})

const columns: Data[] = [
  { align: 'center', customRender: ({ index }: any) => index + 1, dataIndex: 'no', title: '#', width: 45 },
  { align: 'center', dataIndex: 'cashId', title: '结算流水', width: 100 },
  { align: 'center', dataIndex: 'status', title: '交易状态', width: 80 },
  { align: 'center', dataIndex: 'paymentType', title: '支付方式', width: 110 },
  { align: 'right', dataIndex: 'amount', title: '支付金额', width: 100 },
  { align: 'center', dataIndex: 'transId', title: '交易流水', width: 100 },
  { align: 'left', dataIndex: 'notes', minWidth: 130, title: '备注' },
]
</script>
<template>
  <Api v-slot="{ output: data }: Data" :load="() => listFullPaymentsByCashId(cashId)" first spin type="Array">
    <Table :columns="columns" :dataSource="data" class="mt-8px" size="small" bordered rowKey="lineNo" :pagination="false">
      <template #headerCell="{ column: col }">
        <TransStatusHelp v-if="col.dataIndex === 'status'" />
      </template>
      <template #bodyCell="{ column: col, record: row }">
        <template v-if="col.dataIndex === 'refundedAmount'">
          <Format v-if="row.amount > 0" :value="-row.refundedAmount" type="Currency" />
          <template v-else>-</template>
        </template>
        <template v-if="col.dataIndex === 'refundableAmount'">
          <Format v-if="row.refundableAmount >= 0" :value="row.refundableAmount" type="Currency" />
          <template v-else>-</template>
        </template>
        <Format v-if="col.dataIndex === 'amount'" :class="row.amount < 0 ? 'error' : ''" :value="row.amount" type="Currency" />
        <Format v-if="col.dataIndex === 'status'" :colour="false" :value="row.status" params="TransStatus" type="Enum" />
        <Format v-if="col.dataIndex === 'paymentType'" :value="row.paymentType" params="PaymentType" type="Enum" />
      </template>
    </Table>
  </Api>
</template>
