<script setup lang="ts">
import { OrgSelect, OrgDoctor } from '@mh-hip/org'
import { Card, Typography, Divider, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from '../code/OrgDoctorCode'

const { Title } = Typography

// 选中的组织机构ID
const orgId = ref<number>()

// 选中的医生ID
const doctorId = ref<number>()
</script>

<template>
  <Card title="基础用法 - 选择指定组织机构下的医生" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="12">
        <div>
          <div mb-8px>组织机构：</div>
          <OrgSelect v-model="orgId" style="width: 100%" />
          <div mt-8px>选中的组织机构ID: {{ orgId }}</div>
        </div>
      </Col>
      <Col :span="12">
        <div>
          <div mb-8px>医生：</div>
          <OrgDoctor v-model="doctorId" :orgId="orgId" style="width: 100%" />
          <div mt-8px>选中的医生ID: {{ doctorId }}</div>
        </div>
      </Col>
    </Row>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
