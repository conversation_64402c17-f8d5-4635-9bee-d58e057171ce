<script setup lang="ts">
import { Maker } from '@mh-wm/maker'
import { Card, Typography, Divider, Button, message, Space } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { methodsUsage, importCode, packageJsonCode, publishCommands, buildProcess } from '@/views/Wm/examples/code/MakerCode'

const { Title, Paragraph, Text } = Typography

// 组件引用
const makerRef = ref()

// 移除了仓库编码参数，组件内部不再需要外部传入

// 清空表单
const clearForm = () => {
  if (makerRef.value) {
    makerRef.value.clearFormData()
    message.success('表单已清空')
  } else {
    message.error('组件引用不存在')
  }
}

// 获取选中的品种数据
const getSelectedData = () => {
  if (makerRef.value) {
    const selectedData = makerRef.value.getSelectedArtData()
    console.log('当前选中的品种数据:', selectedData)
    if (selectedData) {
      message.success('已获取选中数据，请查看控制台')
    } else {
      message.warning('当前没有选中的品种')
    }
  } else {
    message.error('组件引用不存在')
  }
}

// 设置表单数据（用于编辑）
const setFormData = () => {
  if (makerRef.value) {
    const mockData = {
      artData: {
        artId: 1001,
        artName: '阿莫西林胶囊',
        artSpec: '0.25g*24粒',
        producer: '哈药集团制药总厂',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 24,
        splittable: 1
      },
      originPlace: '中国',
      batchNo: 'B20240101',
      dateManufactured: '20240101',
      expiry: '20261231',
      packPrice: 15.50,
      totalPacks: 10,
      totalCells: 5
    }

    makerRef.value.setFormData(mockData)
    message.success('表单数据已设置')
  } else {
    message.error('组件引用不存在')
  }
}

// 设置另一组测试数据
const setFormData2 = () => {
  if (makerRef.value) {
    const mockData = {
      artData: {
        artId: 1002,
        artName: '头孢克肟胶囊',
        artSpec: '0.1g*12粒',
        producer: '石药集团',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 12,
        splittable: 1
      },
      originPlace: '河北',
      batchNo: 'C20240201',
      dateManufactured: '20240201',
      expiry: '20270131',
      packPrice: 28.80,
      totalPacks: 5,
      totalCells: 8
    }

    makerRef.value.setFormData(mockData)
    message.success('表单数据已设置（头孢克肟）')
  } else {
    message.error('组件引用不存在')
  }
}

// 设置不可拆零的品种数据
const setNonSplittableData = () => {
  if (makerRef.value) {
    const mockData = {
      artData: {
        artId: 1003,
        artName: '胰岛素注射液',
        artSpec: '3ml:300IU',
        producer: '诺和诺德',
        packUnit: '支',
        cellUnit: '支',
        packCells: 1,
        splittable: 0
      },
      originPlace: '丹麦',
      batchNo: '*********',
      dateManufactured: '20240301',
      expiry: '20270228',
      packPrice: 68.50,
      totalPacks: 20
    }

    makerRef.value.setFormData(mockData)
    message.success('表单数据已设置（胰岛素，不可拆零）')
  } else {
    message.error('组件引用不存在')
  }
}

// 添加品种回调
const handleAddArt = (formData: any) => {
  console.log('添加品种成功，表单数据：', formData)
  message.success(`品种 "${formData.artData?.artName}" 添加成功`)
}
</script>

<template>
  <Card title="组件方法 - 采购制单组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">组件方法调用</Title>
      <Paragraph>
        采购组件提供了多个方法供外部调用，包括清空表单、获取选中数据、设置表单数据等。
        这些方法可以用于实现编辑功能、数据回填等场景。
      </Paragraph>

      <!-- 采购制单录入组件 -->
      <div class="maker-form-section">
        <Title :level="5">采购制单录入组件</Title>
        <Maker
          ref="makerRef"
          @addArt="handleAddArt"
        />
      </div>

      <Divider />

      <!-- 方法调用按钮 -->
      <div class="methods-section">
        <Title :level="5">方法调用</Title>
        <Space wrap>
          <Button @click="clearForm" type="default">
            清空表单
          </Button>
          <Button @click="getSelectedData" type="default">
            获取选中数据
          </Button>
          <Button @click="setFormData" type="primary">
            设置表单数据（阿莫西林）
          </Button>
          <Button @click="setFormData2" type="primary">
            设置表单数据（头孢克肟）
          </Button>
          <Button @click="setNonSplittableData" type="primary">
            设置不可拆零数据（胰岛素）
          </Button>
        </Space>

        <div style="margin-top: 16px;">
          <Title :level="5">方法说明：</Title>
          <ul style="margin-top: 8px; padding-left: 20px;">
            <li><Text code>clearFormData()</Text> - 清空表单数据，重置所有输入字段</li>
            <li><Text code>getSelectedArtData()</Text> - 获取当前选中的品种数据</li>
            <li><Text code>setFormData(data)</Text> - 设置表单数据，用于编辑时回填数据</li>
          </ul>
        </div>
      </div>

      <div class="mt-16px tip-text">
        <i class="tip-icon">i</i>
        点击上方按钮测试组件方法。"获取选中数据"会在控制台输出当前选中的品种信息。
        "设置表单数据"可以模拟编辑场景，将数据回填到表单中。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="methodsUsage"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>

  <!-- 打包发布指令 -->
  <Card title="打包与发布" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">采购制单组件打包指令</Title>
      <Paragraph>
        在开发完成后，需要打包组件以便发布和使用。以下是采购制单组件的打包和发布指令：
      </Paragraph>

      <div class="code-section">
        <Title :level="5">打包发布指令</Title>
        <pre class="code-block">{{ publishCommands }}</pre>
      </div>

      <div class="code-section">
        <Title :level="5">打包流程说明</Title>
        <pre class="code-block">{{ buildProcess }}</pre>
      </div>

      <div class="tips-section">
        <Title :level="5">使用说明</Title>
        <ul class="tips-list">
          <li><strong>正式版本：</strong>用于生产环境，版本号会自动递增</li>
          <li><strong>测试版本：</strong>用于测试环境，版本号带有beta标识</li>
          <li><strong>开发版本：</strong>用于开发环境，版本号带有alpha标识</li>
          <li><strong>安装组件：</strong>在其他项目中使用pnpm add命令安装组件</li>
        </ul>
      </div>

      <div class="warning-section">
        <Paragraph type="warning">
          <strong>注意：</strong>打包前请确保组件代码已经完成开发和测试，并且所有依赖项都已正确配置。
        </Paragraph>
      </div>
    </div>
  </Card>
</template>

<style scoped>
.mb-16px {
  margin-bottom: 16px;
}

.mt-16px {
  margin-top: 16px;
}

.maker-form-section {
  margin-bottom: 24px;
}

.methods-section {
  margin-top: 24px;
}

.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  font-style: normal;
  margin-right: 8px;
  flex-shrink: 0;
}

/* 打包指令相关样式 */
.code-section {
  margin-bottom: 24px;
}

.code-block {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.45;
  color: #24292e;
  overflow-x: auto;
  white-space: pre;
  margin: 0;
}

.tips-section {
  margin-bottom: 24px;
}

.tips-list {
  margin: 8px 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.tips-list strong {
  color: #1890ff;
}

.warning-section {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 12px 16px;
  margin-top: 16px;
}
</style>
