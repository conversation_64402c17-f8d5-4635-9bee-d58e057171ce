<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './PharmacySelect/Basic.vue'
import CurrentUserExample from './PharmacySelect/CurrentUser.vue'
import MultipleExample from './PharmacySelect/Multiple.vue'
import BsnTypeExample from './PharmacySelect/BsnType.vue'
import DispensingExample from './PharmacySelect/Dispensing.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>药房选择组件，支持选择所有药房或仅当前用户有权限的药房。</Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="currentUser" tab="当前用户">
        <CurrentUserExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="multiple" tab="多选模式">
        <MultipleExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="bsnType" tab="业务类型">
        <BsnTypeExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="dispensing" tab="处方发药">
        <DispensingExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
