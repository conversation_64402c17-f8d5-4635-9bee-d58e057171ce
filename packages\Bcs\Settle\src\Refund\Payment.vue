<script lang="ts" setup>
import { Data, Format } from '@idmy/core';
import { Table } from 'ant-design-vue';


defineProps({
  data: { type: Array as PropType<Data[]>, required: true },
})

const columns: Data[] = [
  { align: 'center', dataIndex: 'paymentMode', title: '支付方式', width: 100, ellipsis: true },
  { align: 'right', dataIndex: 'amount', title: '支付金额', width: 100 },
  { align: 'right', dataIndex: 'refundedAmount', title: '已退金额', width: 100 },
  { align: 'right', dataIndex: 'refundableAmount', title: '可退金额', width: 100 },
]
</script>
<template>
<Table
  :columns="columns"
  :dataSource="data"
  :headerHeight="25"
  :height="130"
  :maxHeight="130"
  :pagination="false"
  :rowHeight="25"
  :rowKey="row => `${row.billId}${row.lineNo}`"
  bordered
  class="mt-8px"
  size="small">
  <template #bodyCell="{column, record}">
    <Format v-if="column.dataIndex === 'amount'" :value="record.amount" type="Currency"/>
    <Format v-if="column.dataIndex === 'refundedAmount'" :value="-record.refundedAmount" type="Currency"/>
  </template>
</Table>
</template>
