import { DefaultLayout } from '@mh-base/core'

export default {
  path: '/InpatientHsd',
  meta: {
    title: '住院组件',
  },
  component: DefaultLayout,
  children: [
    {
      path: '/InpatientHsd/all',
      component: () => import('@v/InpatientHsd/ApplyOe.vue'),
      meta: {
        title: '医嘱核对',
      },
    },
    {
      path: '/InpatientHsd/components',
      component: () => import('@v/InpatientHsd/All.vue'),
      meta: {
        title: '住院组件示例',
      },
    },
    {
      path: '/InpatientHsd/OeFeeChange',
      component: () => import('@v/InpatientHsd/examples/OeFeeChangeExample.vue'),
      meta: {
        title: '医嘱费用变更',
      },
    },
    {
      path: '/InpatientHsd/OrderFeeChange',
      component: () => import('@v/InpatientHsd/examples/OrderFeeChangeExample.vue'),
      meta: {
        title: '医嘱执行费用变更',
      },
    },
    {
      path: '/InpatientHsd/SectionRouteConsumable',
      component: () => import('@v/InpatientHsd/examples/SectionRouteConsumableExample.vue'),
      meta: {
        title: '病区给药途径绑定',
      },
    },
    {
      path: '/InpatientHsd/SectionTrackCode',
      component: () => import('@v/InpatientHsd/examples/SectionTrackCodeExample.vue'),
      meta: {
        title: '病区绑定追溯码',
      },
    },
    {
      path: '/InpatientHsd/Selector',
      component: () => import('@v/InpatientHsd/examples/SelectorExample.vue'),
      meta: {
        title: '选择器',
      },
    },
    {
      path: '/InpatientHsd/VisitInfo',
      component: () => import('@v/InpatientHsd/examples/VisitInfoExample.vue'),
      meta: {
        title: '患者信息',
      },
    },
    {
      path: '/InpatientHsd/VisitForm',
      component: () => import('@v/InpatientHsd/examples/VisitFormExample.vue'),
      meta: {
        title: '患者信息表单',
      },
    },
    {
      path: '/InpatientHsd/WmBillDetail',
      component: () => import('@v/InpatientHsd/examples/WmBillDetailExample.vue'),
      meta: {
        title: '病区库存台账',
      },
    },
    {
      path: '/InpatientHsd/ComponentPublish',
      component: () => import('@v/InpatientHsd/examples/ComponentPublishGuide.vue'),
      meta: {
        title: '组件打包指令',
      },
    },
  ],
}
