<script setup lang="ts">
import { Medical<PERSON>ypeAll, MedicalTypeDict } from '@mh-hip/medical-type'

const select = ref()
const radio = ref()
const checkbox = reactive<any[]>([])
</script>

<template>
  <MedicalTypeDict v-model="select" type="Select" w-200px mr-8px />
  <MedicalTypeDict v-model="radio" type="Radio" w-200px mr-8px />
  <MedicalTypeDict v-model="checkbox" multiple type="Checkbox" w-400px />
  <div h-8px />
  <MedicalTypeAll />
</template>
