<template>
  <Menu v-model:selectedKeys="selectedKeys" :inlineIndent="16" class="windows-layout-menu" mode="vertical" @click="onMenuClick">
    <template v-for="menu in menus" :key="menu.path">
      <!-- 没有子菜单的情况 -->
      <MenuItem v-if="!menu.children?.length" :key="'item-' + menu.path">
        <template #icon>
          <component :is="menu.meta.icon" v-if="menu.meta?.icon" />
        </template>
        <span>{{ menu.meta?.title }}</span>
      </MenuItem>

      <!-- 有子菜单的情况 -->
      <SubMenu v-else :key="'sub-' + menu.path" :expandIcon="() => null" :trigger="['contextMenu']" mode="vertical">
        <template #title>
          <component :is="menu.meta.icon" v-if="menu.meta?.icon" />
          <span>{{ menu.meta?.title }}</span>
        </template>

        <!-- 二级菜单 -->
        <MenuItem v-for="subMenu in menu.children" :key="'item-' + subMenu.path">
          <template #icon>
            <component :is="subMenu.meta.icon" v-if="subMenu.meta?.icon" />
          </template>
          <span>{{ subMenu.meta?.title }}</span>
        </MenuItem>
      </SubMenu>
    </template>
  </Menu>
</template>

<script lang="ts" setup>
import { Menu, MenuItem, SubMenu } from 'ant-design-vue'
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// 选中的菜单项
const selectedKeys = computed(() => {
  // 获取当前路由的路径
  const currentPath = route.path
  // 返回带有 'item-' 前缀的路径数组
  return [`item-${currentPath}`]
})

// 递归过滤菜单
const filterMenus = (routes: any[]) => {
  return routes.filter(route => {
    // 检查基本条件
    if (!route.meta) return false
    if (route.meta.menu === false) return false

    // 如果有子路由，递归处理
    if (route.children?.length) {
      route.children = filterMenus(route.children)
      // 如果过滤后没有子菜单了，且当前路由没有组件，则不显示
      if (!route.children.length && !route.component) return false
    }

    return true
  })
}

// 获取路由菜单
const menus = computed(() => {
  return filterMenus(router.options.routes as any[])
})

// 菜单点击处理
const onMenuClick = (info: { key: string | number }) => {
  router.push(info.key.toString().replace(/(item-|sub-)/, ''))
}
</script>

<style lang="less" scoped>
.windows-layout-menu {
  border-right: none;
  user-select: none;
  border: 0 !important;

  :deep(.ant-menu-submenu) {
    width: 100%;
    margin: 8px 0;
    display: flex;
    justify-content: end;

    &-title {
      display: flex;
      height: auto;
      width: 34px;
      margin: 0 auto;
      padding: 12px 12px !important;
      writing-mode: vertical-lr;
      letter-spacing: 4px;
      align-items: center;
      justify-content: center;
      border: 1px solid #009b9b;

      &:hover {
        background: #009b9b;
      }
    }
  }

  :deep(.ant-menu-submenu-selected) {
    .ant-menu-submenu-title {
      background: #009b9b;
      color: #fff !important;
    }
  }

  :deep(.ant-menu-item) {
    display: flex;
    height: auto;
    width: 34px;
    margin: 0 auto;
    padding: 12px 12px !important;
    writing-mode: vertical-lr;
    letter-spacing: 4px;
    align-items: center;
    justify-content: center;
    border: 1px solid #009b9b;

    &:hover {
      background: #009b9b !important;
    }
  }

  :deep(.ant-menu-item-selected) {
    background: #009b9b;
    color: #fff;
  }
}
</style>
