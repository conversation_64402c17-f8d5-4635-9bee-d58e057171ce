import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { index as VisitForm } from '@mh-inpatient-hsd/visit-form'
import '@mh-inpatient-hsd/visit-form/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/visit-form": "^1.0.0",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleVisitForm">打开患者信息表单弹窗</Button>
  <VisitForm 
    ref="visitFormRef" 
    @submit="handleSubmit"
    @close="handleClose"
  />
</template>

<script setup>
import { index as VisitForm } from '@mh-inpatient-hsd/visit-form'
import '@mh-inpatient-hsd/visit-form/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const visitFormRef = ref()

// 模拟数据
const visitId = 84503

// 打开患者信息表单弹窗
const handleVisibleVisitForm = () => {
  visitFormRef.value.open(visitId)
}

// 提交回调
const handleSubmit = (data) => {
  message.success('提交成功')
  console.log('提交数据:', data)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>`)
