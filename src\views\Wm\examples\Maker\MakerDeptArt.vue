<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './MakerDeptArt/Basic.vue'
import MethodsExample from './MakerDeptArt/Methods.vue'
import PropsExample from './MakerDeptArt/PropsExample.vue'
import FullExample from './MakerDeptArt/FullExample.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>
      MakerDeptArt 品种选择组件，专门用于品种选择和数量输入的组件。该组件支持整包数量和拆零数量输入，具有完善的表单验证和键盘导航功能。支持回车键快速添加品种，自动清空表单以便连续录入，当品种信息为空时自动弹出机构商品设置窗口。
    </Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="methods" tab="组件方法">
        <MethodsExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="props" tab="Props & Events">
        <PropsExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="full" tab="完整示例">
        <FullExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
