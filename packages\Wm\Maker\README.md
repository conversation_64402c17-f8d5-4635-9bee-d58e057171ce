# 采购制单组件

采购制单组件，用于库房管理系统中快速录入制单信息。该组件包含两个主要组件：

- **Maker**: 完整的制单组件，提供完整的表单界面，支持品种选择、信息填写和数据验证
- **MakerDeptArt**: 简化的品种选择组件，用于快速品种选择和数量输入

## 特性

### Maker 组件特性
- 集成WmArticlePageByOrgSelect组件进行品种选择
- 支持缓存选中的品种数据
- 提供完整的表单验证功能
- 支持回车键顺序导航表单字段
- 自动填充品种相关信息（包装单位、拆零单位等）
- 支持日期格式验证（YYYYMMDD）
- 提供表单清空和重置功能
- 在添加品种成功后自动清空表单并聚焦回品种选择框
- 当品种信息为空时自动弹出机构商品设置窗口

### MakerDeptArt 组件特性
- ✅ 集成WmDeptArtSelect组件进行品种选择
- ✅ 支持部门信息传递（deptCode参数）
- ✅ 支持搜索类型配置（searchType参数）
- ✅ 简化的数量输入界面（整包数量和拆零数量）
- ✅ 智能表单验证（品种选择、数量验证）
- ✅ 支持品种数据缓存和返回
- ✅ Enter键快速导航功能
- ✅ 完整的表单清理功能
- ✅ 支持外部方法调用（获取数据、验证表单等）
- ✅ 响应式部门编码更新
- ✅ 优化的用户界面和样式
- ✅ 无外部API依赖，简化组件结构

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-inpatient-hsd/selector`: 提供WmArticlePageByOrgSelect和WmDeptArtSelect组件
- `@mh-wm/util`: 提供API接口（仅Maker组件使用）

## 打包与安装

### 打包组件

在开发完成后，需要打包组件以便发布和使用：

```bash
# 在项目根目录下执行打包命令
pnpm publish:component Wm/Maker
```

这个命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

### 安装组件

在其他项目中安装该组件：

```bash
# 使用npm安装
npm install @mh-wm/maker

# 或使用pnpm安装
pnpm add @mh-wm/maker
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/maker": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```javascript
// 引入完整的Maker组件
import { Maker } from '@mh-wm/maker'

// 引入简化的MakerDeptArt组件
import MakerDeptArt from '@mh-wm/maker/src/makerDeptArt.vue'
```

### Maker 组件基本用法

```vue
<template>
  <Maker
    @addArt="handleAddArt"
  />
</template>

<script setup>
import { Maker } from '@mh-wm/maker'

// 添加品种回调
const handleAddArt = (formData) => {
  console.log('添加品种成功，表单数据：', formData)
  // formData包含表单数据和选中的品种数据
}
</script>
```

### MakerDeptArt 组件基本用法

```vue
<template>
  <MakerDeptArt
    :deptCode="deptCode"
    :searchType="6"
    @addArt="handleAddArt"
    ref="makerDeptArtRef"
  />
</template>

<script setup>
import { ref } from 'vue'
import MakerDeptArt from '@mh-wm/maker/src/makerDeptArt.vue'

const deptCode = ref('001') // 部门编码
const makerDeptArtRef = ref() // 组件引用

// 处理添加品种事件
const handleAddArt = (artData) => {
  console.log('添加的品种数据:', artData)
  // 处理返回的数据...
}

// 清空表单
const clearForm = () => {
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.clearForm()
  }
}

// 获取表单数据
const getFormData = () => {
  if (makerDeptArtRef.value) {
    const data = makerDeptArtRef.value.getFormData()
    console.log('当前表单数据:', data)
  }
}

// 验证表单
const validateForm = () => {
  if (makerDeptArtRef.value) {
    const isValid = makerDeptArtRef.value.validateForm()
    console.log('验证结果:', isValid)
  }
}

// 聚焦到品种选择框
const focusArtSelect = () => {
  if (makerDeptArtRef.value) {
    makerDeptArtRef.value.focusArtSelect()
  }
}
</script>
```

## API

### Maker 组件 API

#### Props
组件不需要任何props参数，内部会自动处理所有逻辑。

#### Events
| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| addArt | 添加品种时触发 | (formData: object) => void |

#### Methods
| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| getSelectedArtData | 获取当前选中的品种数据 | - |
| clearFormData | 清空表单数据 | - |
| setFormData | 设置表单数据（用于编辑） | (data: object) => void |

### MakerDeptArt 组件 API

#### Props
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| deptCode | String | '' | 部门编码，传递给 WmDeptArtSelect 组件 |
| searchType | Number | 6 | 搜索类型：1-catType.in(201,301)，2-catType.eq(301)+stockReq=1，6-stockReq=1 药品+耗材 |

#### Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| addArt | artData: Object | 添加品种时触发，返回完整的表单数据 |

#### Methods
| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| clearFormData() | - | - | 清空表单数据 |
| clearForm() | - | - | clearFormData 的别名 |
| getFormData() | - | Object | 获取当前表单数据 |
| setFormData(data) | data: Object | - | 设置表单数据（用于编辑时回填） |
| validateForm() | - | Boolean | 验证表单数据 |
| focusArtSelect() | - | - | 聚焦到品种选择框 |
| onAddArt() | - | Object | 手动触发添加操作 |

### 表单数据结构

#### MakerDeptArt 组件返回数据结构

```javascript
{
  // 品种基本信息
  artId: 123,
  artCode: 'A001',
  artName: '阿莫西林胶囊',
  artSpec: '0.25g*24粒',
  producer: '某某制药厂',

  // 包装信息
  packUnit: '盒',
  cellUnit: '粒',
  packCells: 24,
  splittable: 1,

  // 数量信息
  totalPacks: 2,
  totalCells: 10,

  // 价格信息（来自品种选择组件）
  packPrice: 15.50,
  cellPrice: 0.65,

  // 库存信息（来自品种选择组件）
  stockPacksTotal: '100',
  stockCellsTotal: 2400,

  // 其他信息
  batchNo: '',
  expiry: '',
  originPlace: '',
  dateManufactured: '',

  // 完整的品种数据对象
  artData: { /* 选中的品种完整数据 */ }
}
```

#### Maker 组件表单数据结构

| 字段 | 说明 | 类型 | 是否必填 |
| --- | --- | --- | --- |
| artData | 选中的品种数据 | object | 是 |
| originPlace | 原产地 | string | 否 |
| batchNo | 生产批号 | string | 是 |
| dateManufactured | 生产日期（YYYYMMDD） | string | 是 |
| expiry | 有效期至（YYYYMMDD） | string | 是 |
| packPrice | 整包单价 | number | 是 |
| totalPacks | 整包数量 | number | 是（与totalCells至少填一个） |
| totalCells | 拆零数量（仅当splittable=1时显示） | number | 是（与totalPacks至少填一个） |
| packUnit | 包装单位 | string | 自动填充 |
| cellUnit | 拆零单位 | string | 自动填充 |
| splittable | 是否可拆零 | number | 自动填充 |

## 回车键导航顺序

组件支持通过回车键在表单字段之间导航，顺序如下：

1. 品种查询
2. 原产地
3. 生产批号
4. 生产日期
5. 有效期至
6. 整包单价
7. 整包数量
8. 拆零数量（如果可拆零）
9. 提交表单

## 注意事项

### Maker 组件注意事项
1. 生产日期和有效期至必须填写，且格式为YYYYMMDD
2. 整包数量和拆零数量至少填写一个
3. 必须先选择品种才能提交表单
4. 添加品种成功后会自动清空表单并聚焦回品种选择框
5. 如果品种不可拆零（splittable !== 1），则不会显示拆零数量字段
6. 当品种详细信息为空时，会自动弹出机构商品设置窗口

### MakerDeptArt 组件注意事项
1. **部门编码**: 确保传递正确的 deptCode，这会影响品种选择组件的数据查询
2. **搜索类型**: searchType 参数控制品种搜索范围，默认为6（药品+耗材）
3. **数据验证**: 组件会验证品种选择和数量输入，确保数据完整性
4. **数量输入**: 整包数量和拆零数量至少填写一个且必须大于0
5. **拆零逻辑**: 只有当品种可拆零（splittable=1）且包装数量大于1时才显示拆零数量输入框
6. **Enter键导航**: 支持Enter键在输入框间快速导航，提升录入效率
7. **自动清理**: 添加成功后自动清空表单并聚焦回品种选择框
8. **无API依赖**: 组件不依赖外部API，直接使用品种选择组件返回的数据
9. **价格信息**: 价格信息来源于品种选择组件返回的数据，如果品种数据中包含价格则自动填充
10. **样式优化**: 组件包含优化的CSS样式，提供良好的用户体验

## 开发和发布流程

### 开发流程

1. **开发组件**：在 `packages/Wm/Maker/src/` 目录下开发组件
2. **测试组件**：在示例页面中测试组件功能
3. **更新文档**：更新README.md文档和使用示例

### 发布流程

1. **打包组件**：开发完成后，执行打包命令
   ```bash
   pnpm publish:component Wm/Maker
   ```

2. **版本管理**：
   - 正式版本：`pnpm publish:component Wm/Maker`
   - 测试版本：`pnpm publish:test-component Wm/Maker`
   - 开发版本：`pnpm publish:dev-component Wm/Maker`

### 使用流程

1. **安装组件**：在项目中安装组件
   ```bash
   pnpm add @mh-wm/maker
   ```

2. **引入组件**：在Vue文件中引入并使用
   ```javascript
   import { Maker } from '@mh-wm/maker'
   ```

3. **配置依赖**：确保项目中已安装必要的依赖包
