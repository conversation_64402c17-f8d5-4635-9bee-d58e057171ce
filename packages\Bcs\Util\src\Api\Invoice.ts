import { http } from '@idmy/core'

export function createElectricInvoice(cashId: number) {
  return http.post('/api/bcs/Invoice/createElectricInvoice/' + cashId, {}, { appKey: 'bcs', ignoreError: true })
}

export function getInvoiceUrl(id: number) {
  return http.post('/api/bcs/Invoice/getInvoiceUrl/' + id, {}, { appKey: 'bcs' })
}

export function pageInvoice(params: any) {
  return http.post('/api/bcs/Invoice/pageInvoice', params, { appKey: 'bcs' })
}

export function getInvoiceByCashId(cashId: number) {
  return http.post('/api/bcs/Invoice/getByCashId/' + cashId, {}, { appKey: 'bcs' })
}

export function dropAndCreate(id: number) {
  return http.post('/api/bcs/Invoice/dropAndCreate', { id }, { appKey: 'bcs' })

}

export function printBefore(id: number) {
  return http.post('/api/bcs/invoice/printBefore', { id }, { appKey: 'bcs' })

}

export function printSuccess(invoice: any) {
  return http.post('/api/bcs/Invoice/printSuccess', invoice, { appKey: 'bcs' })
}

export function printFail(id: number) {
  return http.post('/api/bcs/Invoice/printFail', { id }, { appKey: 'bcs' })
}

export function updateStatus(id: number, printStatus: number) {
  return http.post('/api/bcs/Invoice/updateStatus', { id, printStatus }, { appKey: 'bcs' })

}

export function getInvoicePrintInfo(id: number) {
  return http.post('/api/bcs/Invoice/getInvoicePrintInfo', { id }, { appKey: 'bcs' })
}

export function sendElectricInvoice(params: any) {
  return http.post('/api/bcs/Invoice/sendElectricInvoice', params, { appKey: 'bcs' })
}

export function red(id: number, notes = '手动红冲') {
  return http.post('/api/bcs/Invoice/reverse', { id, notes }, { appKey: 'bcs' })
}
