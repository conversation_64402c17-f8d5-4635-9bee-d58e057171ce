<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './OrgDoctor/Basic.vue'
import CurrentUserExample from './OrgDoctor/CurrentUser.vue'
import MultipleExample from './OrgDoctor/Multiple.vue'
import ShowCodeExample from './OrgDoctor/ShowCode.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>医生选择组件，支持选择指定组织机构下的医生。</Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="currentUser" tab="当前用户">
        <CurrentUserExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="multiple" tab="多选模式">
        <MultipleExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="showCode" tab="医师编码">
        <ShowCodeExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
