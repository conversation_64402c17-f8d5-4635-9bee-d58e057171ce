<script setup lang="ts">
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { Card, Typography, Divider, Tabs } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { 
  scrollPaginationUsage, 
  buttonPaginationUsage, 
  manualPaginationUsage,
  importCode, 
  packageJsonCode 
} from '../code/ScmCustSelectPaginationCode'

const { Title, Paragraph } = Typography

// 单选值
const custId1 = ref<number>()
const custId2 = ref<number>()
const custId3 = ref<number>()

// 手动加载相关
const scmCustSelectRef = ref()
const loadingMore = ref(false)
const allDataLoaded = ref(false)

// 手动加载更多
const loadMore = async () => {
  if (loadingMore.value || allDataLoaded.value) return
  
  loadingMore.value = true
  try {
    // 调用组件的loadMore方法
    await scmCustSelectRef.value.loadMore()
    // 更新状态
    allDataLoaded.value = scmCustSelectRef.value.getAllDataLoaded()
  } finally {
    loadingMore.value = false
  }
}

// 当前激活的tab
const activeKey = ref('scroll')
</script>

<template>
  <Card title="分页加载" class="mb-16px">
    <div mb-16px>
      <Title :level="4">分页加载功能</Title>
      <Paragraph>
        供应商选择组件支持分页加载功能，可以通过滚动或按钮方式加载更多数据。
      </Paragraph>
      
      <Tabs v-model:activeKey="activeKey">
        <Tabs.TabPane key="scroll" tab="滚动加载">
          <div mb-16px>
            <Title :level="5">滚动加载</Title>
            <Paragraph>
              当滚动到下拉列表底部时，自动加载下一页数据。
            </Paragraph>
            <ScmCustSelect
              v-model="custId1"
              :pageSize="10"
              enablePagination
              paginationMode="scroll"
              :maxPages="5"
              style="width: 100%"
            />
            <div mt-8px>选中的供应商ID: {{ custId1 }}</div>
            <div mt-8px class="tip-text">
              <i class="tip-icon">i</i>
              滚动到下拉列表底部时，自动加载下一页数据
            </div>
            
            <Divider />
            <CodeDemoVue :usage="scrollPaginationUsage" :importCode="importCode" :packageJson="packageJsonCode" />
          </div>
        </Tabs.TabPane>
        
        <Tabs.TabPane key="button" tab="按钮加载">
          <div mb-16px>
            <Title :level="5">按钮加载</Title>
            <Paragraph>
              在下拉列表底部显示"加载更多"按钮，点击时加载下一页数据。
            </Paragraph>
            <ScmCustSelect
              v-model="custId2"
              :pageSize="10"
              enablePagination
              paginationMode="button"
              :maxPages="3"
              style="width: 100%"
            />
            <div mt-8px>选中的供应商ID: {{ custId2 }}</div>
            <div mt-8px class="tip-text">
              <i class="tip-icon">i</i>
              点击下拉列表底部的"加载更多"按钮加载下一页数据
            </div>
            
            <Divider />
            <CodeDemoVue :usage="buttonPaginationUsage" :importCode="importCode" :packageJson="packageJsonCode" />
          </div>
        </Tabs.TabPane>
        
        <Tabs.TabPane key="manual" tab="手动控制">
          <div mb-16px>
            <Title :level="5">手动控制加载</Title>
            <Paragraph>
              禁用自动分页加载，通过外部按钮手动控制加载更多数据。
            </Paragraph>
            <ScmCustSelect
              ref="scmCustSelectRef"
              v-model="custId3"
              :pageSize="10"
              :enablePagination="false"
              style="width: 100%"
            />
            <div mt-16px>
              <a-button @click="loadMore" :loading="loadingMore" :disabled="allDataLoaded">
                {{ loadingMore ? '加载中...' : '手动加载更多' }}
              </a-button>
              
              <span v-if="allDataLoaded" style="margin-left: 8px; color: #999">
                已加载全部数据
              </span>
            </div>
            <div mt-8px>选中的供应商ID: {{ custId3 }}</div>
            <div mt-8px class="tip-text">
              <i class="tip-icon">i</i>
              通过外部按钮手动控制加载更多数据
            </div>
            
            <Divider />
            <CodeDemoVue :usage="manualPaginationUsage" :importCode="importCode" :packageJson="packageJsonCode" />
          </div>
        </Tabs.TabPane>
      </Tabs>
    </div>
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
