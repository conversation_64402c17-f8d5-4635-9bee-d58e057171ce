<script setup lang="ts">
import { Currents } from '@mh-base/core'
import { createTemporaryOeApi, drugAccTempPackDeliverOeApi, SECTION_ACC_MODE_DRUG, SECTION_ACC_MODE_EXEC, sectionStoreLsApi } from '@mh-inpatient-hsd/util'
import { deptArtPageApi } from '@mh-wm/util'
import { Button, Checkbox, Col, Form, FormItem, InputNumber, message, Modal, Row, Select, SelectOption, Table } from 'ant-design-vue'
import ExecResultLog from './ExecResultLog.vue'

const props = defineProps({
  sectionId: {
    type: Number,
    default: null,
  },
  feedMethod: {
    type: Number,
    default: null,
  },
  oeTypeId: {
    type: Number,
    default: null,
  },
  medicinesAccountingMode: {
    type: Number,
    default: Number(SECTION_ACC_MODE_EXEC),
  },
  packDeliverAcc: {
    type: Boolean,
    default: false,
  },
})

const execResultLogRef = ref<InstanceType<typeof ExecResultLog>>()
const visible = ref(false)
const packDeliverLs = ref([])
const formModel = ref<any>({})
const storeLs = ref<any>([])
const selectedKeys = ref<any>([])
const emit = defineEmits(['submit'])
const submitLoading = ref<boolean>(false)
const packDeliverAccBool = ref<boolean>(false)

const columns: TableColumnType[] = [
  {
    title: '床号',
    dataIndex: 'bedNo',
    align: 'center',
    width: 70,
  },
  {
    title: '姓名',
    dataIndex: 'patientName',
    align: 'center',
    width: 80,
  },
  {
    title: '医嘱内容',
    dataIndex: 'oeText',
    ellipsis: true,
    align: 'left',
  },
  {
    title: '药房库存',
    dataIndex: 'wmStock',
    align: 'center',
    width: 100,
  },
  {
    title: '申请数量',
    dataIndex: 'total',
    align: 'center',
    width: 150,
  },
  {
    title: '已申领至',
    dataIndex: 'requestEnded',
    align: 'center',
    width: 150,
  },
]

const drugAccMode = computed(() => {
  return props.medicinesAccountingMode === Number(SECTION_ACC_MODE_DRUG)
})

const open = async (selectedRows: any) => {
  packDeliverLs.value = []
  selectedKeys.value = []
  packDeliverLs.value = selectedRows.filter(item => item.isPackDeliver === 1 && !item.originalNo)
  packDeliverAccBool.value = !!props.packDeliverAcc
  packDeliverLs.value.forEach((item: any) => {
    if (!item.packDeliverTotal) {
      item.packDeliverTotal = 1
    }
    if (!item.packUnitType) {
      item.packUnitType = 2
    }
    item.total = item.packDeliverTotal
    if (!item.requestEnded || item.requestEnded < item.todayInt) {
      selectedKeys.value.push(item.keyStr)
    }
  })
  packDeliverLs.value.sort((a, b) => {
    // 先根据 requestEnded 排序，null 或 undefined 视为 0
    const requestEndedA = a.requestEnded ?? 0
    const requestEndedB = b.requestEnded ?? 0
    if (requestEndedA !== requestEndedB) {
      return requestEndedA - requestEndedB
    }
    // 如果 requestEnded 相同，再根据 bedDisplayOrder 排序
    return a.bedDisplayOrder - b.bedDisplayOrder
  })
  formModel.value.storeCode = null
  await getSectionStoreLs()
  visible.value = true
}

const handleChangeStore = async () => {
  await getSectionWmStockLs()
}

const getSectionWmStockLs = async () => {
  const artIdLs = packDeliverLs.value.map((item: any) => {
    return item.artId
  })
  const stockParams = {
    orgId: Currents.tenantId,
    deptCode: formModel.value.storeCode,
    artIds: artIdLs,
    pageSize: 9999,
  }
  deptArtPageApi(stockParams).then((data: any) => {
    if (data) {
      packDeliverLs.value.forEach((item: any) => {
        if (data && data.list) {
          const existArt = data.list.find((art: any) => art.artId === item.artId)
          item.wmTotalPacks = existArt ? existArt.totalPacks : 0
          // item.wmTotalCells = existArt ? existArt.totalCells : 0
        }
      })
    }
  })
}

const getSectionStoreLs = async () => {
  formModel.value.storeCode = null
  sectionStoreLsApi({ sectionId: props.sectionId }).then((data: any) => {
    storeLs.value = data
    if (storeLs.value && storeLs.value.length > 0) {
      formModel.value.storeCode = storeLs.value[0].storeCode
      getSectionWmStockLs()
    }
  })
}

function handleCancel() {
  visible.value = false
}

const handleReq = async () => {
  let artReqLs = []
  const orderEntryLs = packDeliverLs.value.filter((item: any) => selectedKeys.value.includes(item.keyStr))
  orderEntryLs.forEach((item: any) => {
    const packTotal = item.packUnitType === 2 ? item.packDeliverTotal : 1
    let art = artReqLs.find((artReq: any) => item.artId === artReq.artId)
    if (!art) {
      art = {
        artId: item.artId,
        artName: item.artName,
        artSpec: item.artSpec,
        producer: item.producer,
        packTotal: packTotal,
        wmTotalPacks: item.wmTotalPacks ? item.wmTotalPacks : 0,
      }
      artReqLs.push(art)
    } else {
      art.packTotal += packTotal
    }
  })

  let notEnough = ''
  artReqLs.forEach((item: any) => {
    if (item.packTotal > item.wmTotalPacks) {
      notEnough += item.artName + ' ' + item.artSpec + ' ' + item.producer + ','
    }
  })
  if (notEnough) {
    const confirmContent = '以下药品仓储库存不足：' + notEnough + '是否继续提交申请?'
    Modal.confirm({
      title: '库存不足',
      content: confirmContent,
      cancelText: '取消',
      okText: '继续',
      onOk() {
        handleSubmit()
      },
    })
  } else {
    handleSubmit()
  }
}

const handleSubmit = () => {
  if (drugAccMode.value) {
    handleSubmitByDrugMode()
  } else {
    handleSubmitByNormalMode()
  }
}

const handleSubmitByDrugMode = () => {
  const orderEntryLs = packDeliverLs.value.filter((item: any) => selectedKeys.value.includes(item.keyStr))
  const groupOeIdLs = groupByVisitId(orderEntryLs)
  submitLoading.value = true
  execResultLogRef.value?.clearResults()

  const modal = Modal.info({
    title: '申请用药结果',
    width: 1000,
    style: 'top: 20px',
    okText: '确定',
    content: () => h(ExecResultLog, { ref: execResultLogRef }),
    okButtonProps: { disabled: true },
  })

  const executeNext = (index: number) => {
    if (index >= groupOeIdLs.length) {
      // 所有诊疗执行完毕
      modal.update({
        okButtonProps: { disabled: index < groupOeIdLs.length - 1 },
      })

      const eventResultLs = execResultLogRef.value?.getResults()
      if (eventResultLs.some(result => result.success)) {
        emit('submit')
      }
      if (eventResultLs.every(result => result.success)) {
        message.success('申请用药完成')
        setTimeout(() => {
          modal.destroy()
        }, 2000)
      } else {
        // 延迟执行scrollToTop
        setTimeout(() => {
          execResultLogRef.value?.scrollToTop()
        }, 100)
      }
      submitLoading.value = false
      visible.value = false
      return
    }

    const group = groupOeIdLs[index]
    const visitTitle = getVisitTitle(group.visitId)
    const params = {
      orderEntryLs: group.pdOeLs,
      storeCode: formModel.value.storeCode,
      sectionId: props.sectionId,
      feedMethod: props.feedMethod,
      oeTypeId: props.oeTypeId,
      packDeliverAcc: packDeliverAccBool.value,
    }

    drugAccTempPackDeliverOeApi(params)
      .then(() => {
        execResultLogRef.value?.addResult({ title: visitTitle, message: '', success: true })
      })
      .catch((err: any) => {
        execResultLogRef.value?.addResult({ title: visitTitle, message: err.message, success: false })
      })
      .finally(() => {
        // 执行下一个诊疗
        executeNext(index + 1)
      })
  }

  // 开始执行第一个诊疗
  executeNext(0)
}

const handleSubmitByNormalMode = () => {
  submitLoading.value = true
  const orderEntryLs = packDeliverLs.value.filter((item: any) => selectedKeys.value.includes(item.keyStr))
  const params = {
    orderEntryLs: orderEntryLs,
    storeCode: formModel.value.storeCode,
    sectionId: props.sectionId,
    feedMethod: props.feedMethod,
    oeTypeId: props.oeTypeId,
  }
  createTemporaryOeApi(params)
    .then(() => {
      message.success('申请用药完成')
      emit('submit')
      visible.value = false
    })
    .finally(() => {
      submitLoading.value = false
    })
}

const groupByVisitId = (orderEntryLs: any) => {
  return orderEntryLs.reduce((acc, { visitId, oeNo, total }) => {
    // 查找该 visitId 是否已经存在
    let existing = acc.find(item => item.visitId === visitId)
    const pdOe = {
      visitId: visitId,
      oeNo: oeNo,
      total: total,
    }
    if (existing) {
      // 如果存在，直接把 oeNo 加入对应的 pdOeLs 数组
      existing.pdOeLs.push(pdOe)
    } else {
      // 如果不存在，新增一个新对象
      acc.push({ visitId, pdOeLs: [pdOe] })
    }

    return acc
  }, [])
}

const getVisitTitle = (visitId: any) => {
  let title = ''
  if (visitId) {
    const visitOeLs = packDeliverLs.value.filter((item: any) => item.visitId === visitId)
    if (visitOeLs && visitOeLs.length > 0) {
      let bedMsg = visitOeLs[0].bedNo
      if (bedMsg && !bedMsg.endsWith('床')) {
        bedMsg = bedMsg + '床'
      }
      title = bedMsg + ' ' + visitOeLs[0].patientName
    }
  }
  return title
}

const disableReqBtn = computed(() => {
  if (packDeliverLs.value && packDeliverLs.value.length > 0) {
    const rows = packDeliverLs.value.filter((item: any) => item.total > 0)
    return rows.length === 0
  }
  return true
})

const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectedKeys.value,
    onChange: onSelectChange,
    selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT, Table.SELECTION_NONE],
    getCheckboxProps: (record: any) => ({
      disabled: record.bseqid != null,
    }),
  }
})

const onSelectChange = (selectedRowKeys: any) => {
  selectedKeys.value = selectedRowKeys
}

defineExpose({
  open,
})
</script>

<template>
  <Modal v-model:open="visible" title="申请用药" width="1200px" @cancel="handleCancel" :mask-closable="false">
    <template #footer>
      <Form :label-col="{ span: 7 }" :wrapper-col="{ span: 17 }">
        <Row class="aid-footer">
          <Col flex="auto"> </Col>
          <Col flex="250px" v-show="!packDeliverAccBool">
            <Form-item label="领药仓库" name="storeCode">
              <Select v-model:value="formModel.storeCode" placeholder="请选择领药仓库" style="width: 95%" @change="handleChangeStore" :allow-clear="true">
                <Select-option v-for="item in storeLs" :key="item.storeCode" :value="item.storeCode">
                  {{ item.storeName }}
                </Select-option>
              </Select>
            </Form-item>
          </Col>
          <Col flex="150px" v-if="props.packDeliverAcc">
            <Form-item label="领药计费" :label-col="{ span: 17 }">
              <Checkbox v-model:checked="packDeliverAccBool" />
            </Form-item>
          </Col>
          <Col flex="200px">
            <Button @click="handleReq" :loading="submitLoading" :disabled="disableReqBtn" type="primary"> 申请 </Button>
            <Button type="dashed" @click="handleCancel"> 关闭 </Button>
          </Col>
        </Row>
      </Form>
    </template>
    <div>
      <Table :rowKey="(record: any) => record.keyStr" :columns="columns" :has-surely="false" :pagination="false" :data-source="packDeliverLs" :row-selection="rowSelection" :scroll="{ y: 'calc(100vh - 240px)' }">
        <template #bodyCell="{ text, column, record, index }">
          <template v-if="column.dataIndex === 'oeText'">
            {{ text }}
            {{ record.freqCode }}
            <span v-if="record.isPackDeliver === 1 && record.totalPackPlanned > 0" m-l-3>累计{{ record.totalPackPlanned }}{{ record.packUnit }}</span>
          </template>
          <template v-if="column.dataIndex === 'total'">
            <Input-number
              v-model:value="record.total"
              placeholder="请录入用药数量"
              :min="1"
              :max="record.packDeliverTotal"
              :precision="0"
              style="width: 95%"
              :addon-after="!record.packUnitType || record.packUnitType === 2 ? record.packUnit : record.cellUnit"
            />
          </template>
          <template v-else-if="column.dataIndex === 'wmStock'">
            <span v-if="record.wmTotalPacks">{{ record.wmTotalPacks }}{{ record.packUnit }}</span>
            <span v-else-if="record.wmTotalCells">{{ record.wmTotalCells }}{{ record.cellUnit }}</span>
          </template>
        </template>
      </Table>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.aid-footer {
  text-align: left;
  .ant-form-item {
    margin-bottom: 0px;
  }
  button {
    padding: 0px 25px;
  }
}
</style>
