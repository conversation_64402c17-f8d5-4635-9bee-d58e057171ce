<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form'
import { findDeptStockLsByIdApi, changeBatchNoApi } from "@mh-wm/util"
import { Col, Form, FormItem, Modal, Row, InputNumber, Select, SelectOption, message } from 'ant-design-vue'

const visible = ref(false)
const emit = defineEmits(['close'])

const formRef = ref();
const formState = ref({})
const rules: Record<string, Rule[]> = {
  oldStockNo: [
    { required: true, message: '请选择原生产批号', trigger: 'change' }
  ],
  newStockNo: [
    { required: true, message: '请选择调整为批号', trigger: 'change' }
  ]
};
const artInfo = ref({})
async function init(deptCode: string, art: any) {
  artInfo.value = toValue(art)
  formState.value = {}
  formState.value.deptCode = deptCode
  formState.value.artId = artInfo.value.artId
  await findBatchNoList(deptCode, artInfo.value.artId)
  visible.value = true
}
const batchNoList = ref([])
const findBatchNoList = async (deptCode: string, artId: number) => {
  batchNoList.value = []
  batchNoList.value = await findDeptStockLsByIdApi({
    deptCode: deptCode,
    artId: artId
  })
}

function handleOk() {
  formRef.value?.validate().then(async () => {
    await changeBatchNoApi(formState.value)
    message.success('提交成功，请等待审核')
    emit('close')
    visible.value = false
  }).catch((info: any) => {
    console.log('Validate Failed:', info)
  })
}

function handleCancel() {
  visible.value = false
}

const changeBatchNo = () => {
  const batchNoItem = batchNoList.value.find((item: any) => item.stockNo === formState.value.oldStockNo)
  if (batchNoItem) {
    formState.value.oldTotalPacks = batchNoItem.totalPacks
    formState.value.oldTotalCells = batchNoItem.totalCells
  }
}

defineExpose({
  init
})
</script>

<template>
  <Modal v-model:open="visible" title="批号调整" :maskClosable="false" @ok="handleOk" @cancel="handleCancel">
    <Form
      ref="formRef"
      :model="formState"
      :rules="rules"
      :label-col="{ span: 6}"
      :wrapper-col="{ span: 18}"
    >
      <Row>
        <Col :span="12">
          <Form-item label="品名">
            {{ artInfo.artName }}
          </Form-item>
        </Col>
        <Col :span="12">
          <Form-item label="规格">
            {{ artInfo.artSpec }}
          </Form-item>
        </Col>
        <Col :span="12">
          <Form-item label="生产厂家">
            {{ artInfo.producer }}
          </Form-item>
        </Col>
        <Col :span="12">
          <Form-item label="包装材质">
            {{ artInfo.packMaterial }}
          </Form-item>
        </Col>
        <Col :span="12">
          <Form-item label="单位">
            {{ artInfo.packCells }} {{ artInfo.cellUnit }} / {{ artInfo.packUnit }}
          </Form-item>
        </Col>
        <Col :span="12">
          <Form-item label="货位编码">
            {{ artInfo.rackNo }}
          </Form-item>
        </Col>
        <Col :span="12">
          <Form-item label="原生产批号" name="oldStockNo">
            <Select v-model:value="formState.oldStockNo" placeholder="请选择原生产批号" @change="changeBatchNo">
              <Select-option v-for="item in batchNoList" :key="item.stockNo" :value="item.stockNo">{{ item.batchNo }}</Select-option>
            </Select>
          </Form-item>
        </Col>
        <Col :span="12">
          <Form-item label="调整为批号" name="newStockNo">
            <Select v-model:value="formState.newStockNo" placeholder="请选择调整为批号">
              <Select-option v-for="item in batchNoList" :key="item.stockNo" :value="item.stockNo">{{ item.batchNo }}</Select-option>
            </Select>
          </Form-item>
        </Col>
        <Col :span="12" v-if="formState.oldTotalPacks > 0">
          <Form-item label="整包数量">
            {{ formState.oldTotalPacks }}
          </Form-item>
        </Col>
        <Col :span="12" v-if="formState.oldTotalPacks > 0">
          <Form-item label="调整数量" name="totalPacks">
            <InputNumber v-model:value="formState.totalPacks" :min="0" :max="formState.oldTotalPacks"/>
          </Form-item>
        </Col>
        <Col :span="12" v-if="formState.oldTotalCells > 0">
          <Form-item label="拆零数量">
            {{ formState.oldTotalCells }}
          </Form-item>
        </Col>
        <Col :span="12" v-if="formState.oldTotalCells > 0">
          <Form-item label="拆零数量" name="totalCells">
            <InputNumber v-model:value="formState.totalCells" :min="0" :max="formState.oldTotalCells"/>
          </Form-item>
        </Col>
      </Row>
    </Form>
  </Modal>
</template>
