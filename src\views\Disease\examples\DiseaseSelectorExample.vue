<script setup lang="ts">
import { Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'
import BasicExample from './DiseaseSelector/Basic.vue'
const { Paragraph } = Typography
const activeKey = ref('basic')
</script>
<template>
  <div>
    <Paragraph>病种选择器组件，支持页面嵌入选择病种。</Paragraph>
    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template> 