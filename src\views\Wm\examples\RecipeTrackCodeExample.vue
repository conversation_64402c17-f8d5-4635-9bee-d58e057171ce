<script setup lang="ts">
import { ref, reactive } from 'vue'
import { RecipeTrackCode } from '@mh-wm/recipe-track-code'
// import '@mh-wm/recipe-track-code/index.css'
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Divider,
  Typography,
  message
} from 'ant-design-vue'

const { Title } = Typography

// 引用RecipeTrackCode组件
const recipeTrackCodeRef = ref()

// 表单数据
const formState = reactive({
  title: '处方号:CF202505200003 吴欢',
  wbSeqid: 242516,
  visitId: 87988,
  isView: false,
  onlyAddRecognizedTrackCode: false,
  enableOnlyAddRecognizedTrackCodeOption: false,
  checkTrackCodeComplete: false
})

// 表单引用
const formRef = ref()

// 打开处方扫码组件
const openRecipeTrackCode = () => {
  formRef.value.validate().then(() => {
    recipeTrackCodeRef.value.open(
      formState.title,
      formState.wbSeqid,
      formState.visitId,
      formState.isView
    )
  }).catch(error => {
    console.log('表单验证失败:', error)
    message.error('请填写必要的参数')
  })
}

// 处理成功事件
const handleSuccess = (data) => {
  console.log('处方扫码录入成功:', data)
  message.success('处方扫码录入成功')
}

// 处理取消事件
const handleCancel = () => {
  console.log('取消处方扫码录入')
}

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入标题' }],
  wbSeqid: [{ required: true, message: '请输入单据流水号' }],
  visitId: [{ required: true, message: '请输入就诊ID' }]
}

// 重置表单
const resetForm = () => {
  formRef.value.resetFields()
}

// 使用默认值
const useDefaultValues = () => {
  formState.title = '处方号:CF202505200003 吴欢'
  formState.wbSeqid = 242516
  formState.visitId = 87988
  formState.isView = false
  formState.onlyAddRecognizedTrackCode = false
  formState.enableOnlyAddRecognizedTrackCodeOption = true
  formState.checkTrackCodeComplete = false
}
</script>

<template>
  <div class="recipe-track-code-example">
    <Title :level="3">参数设置</Title>
    <Form
      :model="formState"
      :rules="rules"
      ref="formRef"
      layout="vertical"
      class="parameter-form"
    >
      <Form.Item label="标题" name="title">
        <Input v-model:value="formState.title" placeholder="请输入标题" />
      </Form.Item>

      <Form.Item label="单据流水号" name="wbSeqid">
        <InputNumber v-model:value="formState.wbSeqid" placeholder="请输入单据流水号" style="width: 100%" />
      </Form.Item>

      <Form.Item label="就诊ID" name="visitId">
        <InputNumber v-model:value="formState.visitId" placeholder="请输入就诊ID" style="width: 100%" />
      </Form.Item>

      <Form.Item label="是否只读模式" name="isView">
        <Switch v-model:checked="formState.isView" />
      </Form.Item>

      <Form.Item label="是否只添加识别追溯码" name="onlyAddRecognizedTrackCode">
        <Switch v-model:checked="formState.onlyAddRecognizedTrackCode" />
        <div class="form-item-description">设置为true时，只有能够匹配到药品的追溯码才会被添加</div>
      </Form.Item>

      <Form.Item label="是否启用'只添加识别追溯码'选项" name="enableOnlyAddRecognizedTrackCodeOption">
        <Switch v-model:checked="formState.enableOnlyAddRecognizedTrackCodeOption" />
        <div class="form-item-description">设置为true时，在追溯码输入框旁边显示"只添加识别追溯码"复选框</div>
      </Form.Item>

      <Form.Item label="是否检查追溯码完整性" name="checkTrackCodeComplete">
        <Switch v-model:checked="formState.checkTrackCodeComplete" />
        <div class="form-item-description">设置为true时，如果有未完成的药品，不允许提交，必须先扫描完所有追溯码</div>
      </Form.Item>

      <Form.Item>
        <Button type="primary" @click="openRecipeTrackCode" style="margin-right: 10px">打开处方扫码组件</Button>
        <Button @click="resetForm" style="margin-right: 10px">重置</Button>
        <Button type="dashed" @click="useDefaultValues">使用默认值</Button>
      </Form.Item>
    </Form>

    <Divider />

    <Title :level="3">JSON格式参数</Title>
    <pre class="json-display">{
  "title": "{{ formState.title }}",
  "wbSeqid": {{ formState.wbSeqid }},
  "visitId": {{ formState.visitId }},
  "isView": {{ formState.isView }},
  "onlyAddRecognizedTrackCode": {{ formState.onlyAddRecognizedTrackCode }},
  "enableOnlyAddRecognizedTrackCodeOption": {{ formState.enableOnlyAddRecognizedTrackCodeOption }},
  "checkTrackCodeComplete": {{ formState.checkTrackCodeComplete }}
}</pre>

    <Divider />

    <Title :level="3">打包与发布</Title>
    <div class="code-example">
      <pre>
// 在项目根目录下执行以下命令打包并发布组件
pnpm publish:component Wm/RecipeTrackCode

// 该命令会执行以下操作：
// 1. 编译组件源码
// 2. 生成类型声明文件
// 3. 打包CSS样式
// 4. 生成组件包到dist目录
// 5. 更新package.json中的版本号
// 6. 将组件发布到内部npm仓库
      </pre>
    </div>

    <Divider />

    <Title :level="3">使用方法</Title>
    <div class="code-example">
      <pre>
// 安装组件
npm install @mh-wm/recipe-track-code
// 或
pnpm add @mh-wm/recipe-track-code

// 引入组件和样式
import { RecipeTrackCode } from '@mh-wm/recipe-track-code'
import '@mh-wm/recipe-track-code/index.css'

// 在模板中使用
&lt;RecipeTrackCode
  ref="recipeTrackCodeRef"
  :onlyAddRecognizedTrackCode="false"
  :enableOnlyAddRecognizedTrackCodeOption="true"
  :checkTrackCodeComplete="false"
  @success="handleSuccess"
  @cancel="handleCancel"
/&gt;

// 在方法中调用
const openRecipeTrackCode = () => {
  recipeTrackCodeRef.value.open(
    '处方号:CF202505200003 吴欢',
    242516,
    87988,
    false
  )
}
      </pre>
    </div>

    <!-- 处方扫码组件 -->
    <RecipeTrackCode
      ref="recipeTrackCodeRef"
      :onlyAddRecognizedTrackCode="formState.onlyAddRecognizedTrackCode"
      :enableOnlyAddRecognizedTrackCodeOption="formState.enableOnlyAddRecognizedTrackCodeOption"
      :checkTrackCodeComplete="formState.checkTrackCodeComplete"
      @success="handleSuccess"
      @cancel="handleCancel"
    />
  </div>
</template>

<style scoped>
.recipe-track-code-example {
  padding: 20px;
}

.parameter-form {
  max-width: 600px;
}

.json-display {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  font-family: monospace;
}

.code-example {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 4px;
  overflow: auto;
}

.code-example pre {
  margin: 0;
  font-family: monospace;
}

.form-item-description {
  font-size: 12px;
  color: #888;
  margin-top: 4px;
}
</style>
