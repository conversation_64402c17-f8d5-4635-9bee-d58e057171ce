<script setup lang="ts">
import { Select, Spin } from 'ant-design-vue'
import { Data, http } from '@idmy/core'
import { ref, watch, onMounted } from 'vue'

const props = defineProps({
  orgId: {
    type: Number,
    default: undefined,
  },
  clinicianId: {
    type: Number,
    default: undefined,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  own: {
    type: Boolean,
    default: false,
  },
})

const modelValue = defineModel<any>()

// 部门列表
const deptList = ref<any[]>([])
const loading = ref(false)

// 获取组织机构下的部门列表
const fetchOrgDepts = async () => {
  loading.value = true
  try {
    let url = '/hip-base/orgdept/findAll'
    let params = {}

    // 如果own为true，则先获取自己的医师信息
    if (props.own) {
      try {
        // 调用clinicianInfo API获取自己的医师信息
        const clinicianParams = props.orgId ? { orgId: props.orgId } : {}
        const clinicianInfo = await http.post<any>('/microhis-hsd/clinician/clinicianInfo', clinicianParams, { appKey: 'hsd' })

        // 如果同时传入了clinicianId，打印警告
        if (props.clinicianId) {
          console.warn(`警告: 由于own为true，传入的clinicianId[${props.clinicianId}]已失效`)
        }

        // 获取到医师ID后，调用findDeptLsByClinician API
        if (clinicianInfo && clinicianInfo.clinicianId) {
          url = '/hip-base/clinician/findDeptLsByClinician'
          params = { clinicianId: clinicianInfo.clinicianId }
          // 如果有orgId，也传入
          if (props.orgId) {
            params = { ...params, orgId: props.orgId }
          }
        } else {
          // 如果没有获取到医师信息，则使用默认API
          params = props.orgId ? { orgId: props.orgId } : {}
        }
      } catch (error) {
        console.error('获取医师信息失败:', error)
        // 如果获取医师信息失败，则使用默认API
        params = props.orgId ? { orgId: props.orgId } : {}
      }
    }
    // 如果own为false且有clinicianId，则调用findDeptLsByClinician API
    else if (props.clinicianId) {
      url = '/hip-base/clinician/findDeptLsByClinician'
      params = { clinicianId: props.clinicianId }
      // 如果同时有orgId，也传入
      if (props.orgId) {
        params = { ...params, orgId: props.orgId }
      }
    }
    // 否则调用原来的API，如果有orgId则传入
    else {
      params = props.orgId ? { orgId: props.orgId } : {}
    }

    const arr = await http.post<Data[]>(url, params, { appKey: 'hip' })
    // 处理API返回的数据
    deptList.value = arr
      .filter(row => row.disabled !== 1)
      .map(row => ({
        code: row.deptCode,
        id: row.deptCode, // 使用deptCode作为部门ID
        value: row.deptCode, // 使用deptCode作为value
        label: `${row.deptName} (${row.deptCode})`, // 在显示内容中展示deptCode
        data: row,
      }))
  } catch (error) {
    console.error('获取部门列表失败:', error)
    deptList.value = []
  } finally {
    loading.value = false
  }
}

// 初始加载
onMounted(() => {
  fetchOrgDepts()
})

// 监听orgId属性变化，重新加载列表
watch(
  () => props.orgId,
  () => {
    fetchOrgDepts()
  }
)

// 监听clinicianId属性变化，重新加载列表
watch(
  () => props.clinicianId,
  () => {
    fetchOrgDepts()
  }
)

// 监听own属性变化，重新加载列表
watch(
  () => props.own,
  () => {
    fetchOrgDepts()
  }
)
</script>

<template>
  <Spin :spinning="loading">
    <Select
      v-model:value="modelValue"
      :mode="props.multiple ? 'multiple' : undefined"
      :options="deptList"
      placeholder="请选择部门"
      :fieldNames="{ label: 'label', value: 'value' }"
      :showSearch="true"
      :filterOption="(input, option) => (option?.label ?? '').toLowerCase().includes(input.toLowerCase())"
      :allowClear="true"
      style="width: 100%"
    />
  </Spin>
</template>
