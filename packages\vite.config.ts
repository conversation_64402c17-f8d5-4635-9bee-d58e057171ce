import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import autoImport from 'unplugin-auto-import/vite'
import { defineConfig } from 'vite'
import { lazyImport } from 'vite-plugin-lazy-import'


const currentComponent = process.env.COMPONENT
const entry = currentComponent ? resolve(__dirname, currentComponent, 'index.ts') : resolve(__dirname, 'index.ts')
const outDir = currentComponent ? resolve(__dirname, '../dist', currentComponent) : resolve(__dirname, '../dist')
const componentName = currentComponent?.split('/').pop() || ''


export default defineConfig(async (): Promise<any> => {
  const { default: unocss } = await import('unocss/vite')
  return {
    plugins: [
      vue({
        script: {
          defineModel: true,
        },
      }),
      unocss(),
      lazyImport({
        resolvers: [],
      }),
      autoImport({
        imports: [
          'vue',
          'vue-router',
          { '@idmy/core': ['fmt', 'cfg', 'sleep', 'useLoading', 'useCache', 'http', 'newError', 'Dialog', 'Modal', 'Message', 'dayjs', 'Data', 'back', 'to', 'currentRouter', 'currentRoute', 'emitter', 'clearObject'] },
          { 'lodash-es': ['isNil', 'isString', 'isArray', 'isNumber', 'isArray', 'cloneDeep', 'isEmpty'] },
        ],
        dts: 'auto-imports.d.ts',
        defaultExportByFilename: true,
        vueTemplate: true,
      }),
    ],
    build: {
      target: 'esnext',
      outDir,
      emptyOutDir: false,
      minify: false,
      cssCodeSplit: true,
      lib: {
        entry,
        name: componentName ? `MhBiz${componentName}` : 'MhBiz',
        fileName: format => `${format}/index.js`,
        formats: [process.env.FORMAT as 'es' | 'umd'],
      },
      rollupOptions: {
        external: [
          '@vueuse/core',
          'vue',
          'vue-router',
          'ant-design-vue',
          '@ant-design/icons-vue',
          '@surely-vue/table',
          'lodash-es',
          'dayjs',
          'axios',
          'crypto-js',
          '@idmy/core',
          '@idmy/antd',
          /^@mh-.*/,
        ],
        output: {
          globals: {},
        },
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
      },
    },
  }
})
