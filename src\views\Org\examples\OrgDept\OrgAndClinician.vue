<script setup lang="ts">
import { OrgSelect, OrgDoctor, OrgDept } from '@mh-hip/org'
import { Card, Typography, Divider, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { orgAndClinicianUsage, importCode, packageJsonCode } from '../code/OrgDeptCode'

const { Title } = Typography

// 选中的组织机构ID
const orgId = ref<number>()

// 选中的医生ID
const clinicianId = ref<number>()

// 同时指定组织机构ID和医生ID的部门选择
const bothDeptId = ref<number>()
</script>

<template>
  <Card title="同时指定组织机构ID和医生ID" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="12">
        <div>
          <div mb-8px>组织机构：</div>
          <OrgSelect v-model="orgId" style="width: 100%" />
          <div mt-8px>选中的组织机构ID: {{ orgId }}</div>
        </div>
      </Col>
      <Col :span="12">
        <div>
          <div mb-8px>医生：</div>
          <OrgDoctor v-model="clinicianId" :orgId="orgId" style="width: 100%" />
          <div mt-8px>选中的医生ID: {{ clinicianId }}</div>
        </div>
      </Col>
    </Row>

    <Divider />

    <div mb-16px>
      <Title :level="4">同时指定组织机构ID和医生ID</Title>
      <OrgDept v-model="bothDeptId" :orgId="orgId" :clinicianId="clinicianId" w-200px />
      <div mt-8px>选中的部门ID: {{ bothDeptId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        当同时指定orgId和clinicianId时，会调用findDeptLsByClinician API，并传入两个参数
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="orgAndClinicianUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
