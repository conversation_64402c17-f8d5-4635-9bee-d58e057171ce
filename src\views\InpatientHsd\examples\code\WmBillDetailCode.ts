import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { index as WmBillDetail } from '@mh-inpatient-hsd/wm-bill-detail'
import '@mh-inpatient-hsd/wm-bill-detail/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/wm-bill-detail": "^1.0.3",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleWmBillDetail">打开病区库存台账弹窗</Button>
  <WmBillDetail
    ref="wmBillDetailRef"
    @close="handleClose"
  />
</template>

<script setup>
import { index as WmBillDetail } from '@mh-inpatient-hsd/wm-bill-detail'
import '@mh-inpatient-hsd/wm-bill-detail/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const wmBillDetailRef = ref()

// 模拟数据
const sectionId = 40
const artId = 1044295
const startDate = '2025-03-01'
const endDate = '2025-03-31'

// 打开病区库存台账弹窗
const handleVisibleWmBillDetail = () => {
  wmBillDetailRef.value.open(sectionId, artId, startDate, endDate)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>`)
