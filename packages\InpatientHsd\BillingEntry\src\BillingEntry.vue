<script setup lang="ts">
import dayjs from 'dayjs'
import filters from './utils/filters'
import { Table, Button, Col, Form, FormItem, Modal, Row, InputNumber, DatePicker, Select, SelectOption, message } from 'ant-design-vue'
import type { TableColumnType } from 'ant-design-vue'
import BillingEntryItem from "./BillingEntryItem.vue"
import { BaseTag } from '@mh-base/core'
import { billingEntryOeLsApi, batchSaveBillingEntryApi } from '@mh-inpatient-hsd/util';
import { findDeptClinicianLsApi, getDictData } from '@mh-hip/util'

const props = defineProps({
  inOperating: {
    type: Number,
    default: null,
  },
  sectionId: {
    type: Number,
    default: null,
  },
  sectionName: {
    type: String,
    default: null
  },
  visitId: {
    type: Number,
    default: null,
  },
  freqCodeLs: {
    type: Array,
    default: [],
  },
  routeIdLs: {
    type: Array,
    default: [],
  },
  appliedCidLs: {
    type: Array,
    default: [],
  },
  genderId: {
    type: Number,
    default: null,
  },
  defaultFreqCode: {
    type: String,
    default: 'qd',
  }
})

const columns: TableColumnType[] = [
  {
    title: '状态',
    dataIndex: 'status',
    align: 'center',
    fixed: 'left',
    width: 50,
  },
  {
    title: '类别',
    dataIndex: 'oeTypeId',
    align: 'center',
    fixed: 'left',
    width: 50,
  },
  {
    title: '',
    dataIndex: 'oeCat',
    align: 'center',
    width: 50,
  },
  {
    title: '开始时间',
    dataIndex: 'oeTimeStarted',
    align: 'center',
    width: 100,
  },
  {
    title: '医嘱内容',
    dataIndex: 'oeText',
    ellipsis: true,
    width: 500,
    align: 'left'
  },
  {
    title: '频次',
    dataIndex: 'freqCodeStr',
    align: 'center',
    fixed: 'right',
    width: 100,
  },
  {
    title: '用法',
    dataIndex: 'routeName',
    align: 'center',
    fixed: 'right',
    width: 100,
  },
  {
    title: '单量',
    dataIndex: 'mealTotal',
    align: 'center',
    fixed: 'right',
    width: 100,
  },
  {
    title: '单位',
    dataIndex: 'mealUnitType',
    align: 'center',
    fixed: 'right',
    width: 120,
  },
  // {
  //   title: '医嘱号',
  //   dataIndex: 'oeNo',
  //   align: 'center',
  //   width: 60,
  // },
  {
    title: '操作',
    dataIndex: 'action',
    align: 'center',
    fixed: 'right',
    width: 80
  },
]

const emit = defineEmits(['oeChange'])
const billingEntryItemRef = ref<InstanceType<typeof BillingEntryItem>>()
const loading = ref<boolean>(false)
const billingLoading = ref<boolean>(false)
const dataSource = ref<any>([])
const displayOrder = ref(0)
const clinicianLs = ref<any>([])
const freqTypeLs = ref<any>([])
const routeTypeLs = ref<any>([])
const formModel = reactive<any>({})
const selectedKeys = ref<any>([])

const handleVisibleBillingEntry = () => {
  billingEntryItemRef.value?.open()
}

const cleanAll = () => {
  formModel.clinicianId = undefined
  formModel.freqCode = undefined
  formModel.routeId = undefined
  formModel.oeTimeStart = dayjs()
  clinicianLs.value = []
  freqTypeLs.value = []
  routeTypeLs.value = []
  selectedKeys.value = []
}

const getBillingEntryOeLs = () => {
  dataSource.value = []
  selectedKeys.value = []
  if (!props.visitId) {
    return
  }
  billingEntryOeLsApi({ sectionId: props.sectionId, visitId: props.visitId, inOperating: props.inOperating }).then((data: any) => {
    if (data) {
      dataSource.value = data
      dataSource.value.forEach((item: any) => {
        if (displayOrder.value < item.oeNo) {
          displayOrder.value = item.oeNo
        }
      })
    }
  })
}

const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectedKeys.value,
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE
    ],
  }
})

const onSelectChange = (keys: any, rows: any) => {
  selectedKeys.value = keys
}

const handleAddBillingEntry = (itemLs: any) => {
  if (itemLs) {
    let freqCode = undefined
    let routeId = itemLs[0].routeId ?  itemLs[0].routeId : undefined
    if (!routeId && props.routeIdLs.length > 0) {
      const notOraRouteIdLs = routeTypeLs.value.filter((item: any) => props.routeIdLs.includes(item.routeId) && item.feedMethod != 10).map(item => {return item.routeId})
      if (notOraRouteIdLs && notOraRouteIdLs.length > 0) {
        const idLs = props.routeIdLs.filter(item => notOraRouteIdLs.includes(item))
        if (notOraRouteIdLs && notOraRouteIdLs.length > 0) {
          routeId = idLs[0]
        }
      }
    }
    if (props.freqCodeLs.length === 1) {
      freqCode = props.freqCodeLs[0]
    } else {
      freqCode = props.defaultFreqCode
    }
    itemLs.forEach((item: any) => {
      const unitType = item.unitType
      let packUnit = item.packUnit
      let cellUnit = item.cellUnit
      if (unitType === 1 && !cellUnit) {
        cellUnit = '未知'
      } else if (unitType === 2 && !packUnit) {
        packUnit = '未知'
      }
      displayOrder.value = displayOrder.value + 1
      dataSource.value.push({
        artId: item.artId,
        artName: item.artName,
        artTypeId: item.artTypeId,
        packUnit: packUnit,
        cellUnit: cellUnit,
        mealTotal: item.initialTotal,
        unitType: unitType,
        isBillingEntry: 1,
        orderEntryStatus: 0,
        oeText: item.artName + ' ' + (item.artSpec ? item.artSpec : ''),
        routeId: routeId,
        freqCode: freqCode,
        displayOrder: displayOrder.value
      })
      selectedKeys.value.push(displayOrder.value)
    })
  }
}

async function getBaseData() {
  getDictData('mdiBasicData', { dataTypeLs: [ 'freqType', 'routeType' ] }).then((data: any) => {
    if (data) {
      freqTypeLs.value = data['freqType'].filter((item: any) => !['sos', 'prn'].includes(item.freqCode))
      // if (props.inOperating === 1) {
      //   freqTypeLs.value = [{ freqCode: 'st', freqName: 'st' }]
      // }
      const defaultRouteTypeLs = data['routeType']
      if (defaultRouteTypeLs && props.routeIdLs && props.routeIdLs.length > 0) {
        routeTypeLs.value = defaultRouteTypeLs.filter((item: any) => props.routeIdLs.includes(item.routeId))
      } else {
        routeTypeLs.value = defaultRouteTypeLs
      }
    }
  })
}

const getSectionClinician = async () => {
  clinicianLs.value = []
  findDeptClinicianLsApi({ sectionId: props.sectionId, 'S_EQ_t_clinician__Clinician_Type_ID': 1 }).then((data: any) => {
    if (data) {
      clinicianLs.value = data
      if (clinicianLs.value && props.appliedCidLs) {
        const applyClinicianLs = clinicianLs.value.filter((item: any) => props.appliedCidLs.includes(item.clinicianId))
        if (applyClinicianLs && applyClinicianLs.length > 0) {
          formModel.clinicianId = applyClinicianLs[0].clinicianId
        }
      }
    }
  })
}

function filterOption (input: any, option: any) {
  return option.text && option.text.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const init = async () => {
  cleanAll()
  await getBaseData()
  await getSectionClinician()
  getBillingEntryOeLs()
}

const handleBatchChangeFreqCode = async () => {
  if (!selectedKeys.value || selectedKeys.value.length === 0) {
    message.warning('请选择要批量修改的医嘱')
    formModel.freqCode = null
    return
  }
  const rows = dataSource.value.filter((item: any) => selectedKeys.value.includes(item.displayOrder))
  rows.forEach((item: any) => {
    item.freqCode = formModel.freqCode
  })
  formModel.freqCode = null
}

const handleBatchChangeRouteId = async () => {
  if (!selectedKeys.value || selectedKeys.value.length === 0) {
    message.warning('请选择要批量修改的医嘱')
    formModel.routeId = null
    return
  }
  const rows = dataSource.value.filter((item: any) => selectedKeys.value.includes(item.displayOrder))
  rows.forEach((item: any) => {
    item.routeId = formModel.routeId
  })
  formModel.routeId = null
}

const handleBatchDelBillingEntry = () => {
  if (!selectedKeys.value || selectedKeys.value.length === 0) {
    message.warning('请选择需要批量删除的计费医嘱')
    return
  }
  dataSource.value = dataSource.value.filter((item: any) => !selectedKeys.value.includes(item.displayOrder))
}

const handleDelete = async (record: any) => {
  dataSource.value = dataSource.value.filter((item: any) => item.displayOrder !== record.displayOrder)
}

const handleBatchSaveBillingEntry = () => {
  if (!dataSource.value || dataSource.value.length === 0) {
    Modal.confirm({
      title: '计费医嘱调整',
      content: '是否清空所有计费医嘱?',
      cancelText: '取消',
      okText: '清空',
      onOk() {
        batchSaveBillingEntry()
      }
    })
  } else {
    const noneArtIdRow = dataSource.value.filter((item: any) => !item.artId)
    // const noneRouteIdRow = dataSource.value.filter((item: any) => !item.routeId)
    const noneFreqCodeRow = dataSource.value.filter((item: any) => !item.freqCode)
    const noneMealTotalRow = dataSource.value.filter((item: any) => !item.oeNo && (!item.mealTotal || item.mealTotal <= 0))
    const noneUnitTypeRow = dataSource.value.filter((item: any) => !item.oeNo && !item.unitType)
    if (noneArtIdRow && noneArtIdRow.length > 0) {
      message.warning('请检查计费医嘱条目')
      return
    }
    if (noneFreqCodeRow && noneFreqCodeRow.length > 0) {
      message.warning('请检查计费医嘱频次')
      return
    }
    if (noneMealTotalRow && noneMealTotalRow.length > 0) {
      message.warning('请检查计费医嘱单次用量')
      return
    }
    if (noneUnitTypeRow && noneUnitTypeRow.length > 0) {
      message.warning('请检查计费医嘱单位类型')
      return
    }
    if (!formModel.oeTimeStart) {
      message.warning('请选择医嘱开始时间')
      return
    }
    batchSaveBillingEntry()
  }
}

const batchSaveBillingEntry = () => {
  billingLoading.value = true
  const params = {
    sectionId: props.sectionId,
    visitId: props.visitId,
    clinicianId: formModel.clinicianId,
    oeTimeStart: formModel.oeTimeStart.format('YYYY-MM-DD HH:mm'),
    oeLs: dataSource.value,
    inOperating: props.inOperating
  }
  batchSaveBillingEntryApi(params).then(() => {
      message.success('计费医嘱更新成功')
      getBillingEntryOeLs()
      emit('oeChange')
  }).finally(() => {
    billingLoading.value = false
  })
}

defineExpose({
  init
})

</script>

<template>
  <div>
    <Form :label-col="{ span: 8 }" w-full>
      <Row :gutter="10">
        <Col flex="200px">
          <Form-item label="立嘱医生" name="clinicianId" >
            <Select v-model:value="formModel.clinicianId" placeholder="请选择立嘱医生" style="width: 100%;" show-search :filter-option="filterOption" :allow-clear="false">
              <Select-option v-for="item in clinicianLs" :key="item.clinicianId" :value="item.clinicianId" :text="item.clinicianName">
                {{ item.clinicianName }}
              </Select-option>
            </Select>
          </Form-item>
        </Col>
        <Col flex="230px">
          <Form-item label="医嘱开始时间" name="oeTimeStart" :label-col="{ span: 10 }">
            <Date-picker class="flex" show-time v-model:value="formModel.oeTimeStart" format="MM-DD HH:mm" :allow-clear="false"/>
          </Form-item>
        </Col>
        <Col flex="120px">
          <Button type="primary" :loading="billingLoading" @click="handleBatchSaveBillingEntry">
            更新计费医嘱
          </Button>
        </Col>
        <Col flex="100px" p-t-1>
          <div class="op-btn">
            批量设置
          </div>
        </Col>
        <Col flex="150px">
          <Form-item label="频次" name="freqCode">
            <Select v-model:value="formModel.freqCode" style="width: 100%;" show-search :filter-option="filterOption" @change="handleBatchChangeFreqCode">
              <Select-option v-for="item in freqTypeLs" :key="item.freqCode" :value="item.freqCode" :text="item.freqCode">
                {{ item.freqCode }}
              </Select-option>
            </Select>
          </Form-item>
        </Col>
        <Col flex="180px">
          <Form-item label="用法" name="routeId">
            <Select v-model:value="formModel.routeId" style="width: 100%;" show-search :filter-option="filterOption" @change="handleBatchChangeRouteId">
              <Select-option v-for="item in routeTypeLs" :key="item.routeId" :value="item.routeId" :text="item.routeName">
                {{ item.routeName }}
              </Select-option>
            </Select>
          </Form-item>
        </Col>
        <Col flex="100px" p-t-1>
          <a class="op-btn" style="color: red" @click="handleBatchDelBillingEntry">
            批量删计费
          </a>
        </Col>
        <Col flex="auto">
          <div class="op-btn">
            <Button @click="handleVisibleBillingEntry">
              常用计费条目
            </Button>
          </div>
        </Col>
      </Row>
    </Form>
    <Table
      ref="table"
      :hasSurely="false"
      :row-key="(record: any) => record.displayOrder"
      :loading="loading"
      :columns="columns"
      :data-source="dataSource"
      :scroll="{ y: '600px' }"
      deepWatchDataSource
      :row-selection="rowSelection">
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.dataIndex === 'status'">
          <base-tag :status="record.orderEntryStatus" />
        </template>
        <template v-else-if="column.dataIndex === 'oeTypeId'">
          <span v-if="text === 1">临</span>
          <span v-else-if="text === 2">长</span>
          <span v-else-if="text === 3">备</span>
        </template>
        <template v-else-if="column.dataIndex === 'oeCat'">
          <base-tag :status="record.oeCat" type="oeCat"/>
        </template>
        <template v-else-if="column.dataIndex === 'oeTimeStarted'">
          <span>{{ filters.dateFormatMDHM(text) }}</span>
        </template>
        <template v-else-if="column.dataIndex === 'freqCodeStr'">
          <Select v-model:value="record.freqCode" style="width: 100%;" show-search :filter-option="filterOption" :allow-clear="false">
            <Select-option v-for="item in freqTypeLs" :key="item.freqCode" :value="item.freqCode" :text="item.freqCode">
              {{ item.freqCode }}
            </Select-option>
          </Select>
        </template>
        <template v-else-if="column.dataIndex === 'routeName'">
          <template v-if="record.oeNo">
            {{ record.routeName }}
          </template>
          <template v-else>
            <Select v-model:value="record.routeId" style="width: 100%;" show-search :filter-option="filterOption" :allow-clear="false">
              <Select-option v-for="item in routeTypeLs" :key="item.routeId" :value="item.routeId" :text="item.routeName">
                {{ item.routeName }}
              </Select-option>
            </Select>
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'mealTotal'">
          <template v-if="record.oeNo">
            {{ record.oeTypeId === 1 ? record.total : record.mealCells }}
          </template>
          <template v-else>
            <Input-number
              v-model:value="record.mealTotal"
              placeholder="请录入单次剂量"
              :min="1"
              :max="999"
              :precision="0"
              style="width: 95%;"/>
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'mealUnitType'">
          <template v-if="record.oeNo">
            {{ !record.unitType || record.unitType === 2 ? record.packUnit : record.cellUnit }}
          </template>
          <template v-else>
            <Select v-model:value="record.unitType" placeholder="请选择单位" style="width: 100%;">
              <Select-option v-if="record.packUnit" :value="2">{{ record.packUnit }}(包装)</Select-option>
              <Select-option v-if="record.cellUnit" :value="1">{{ record.cellUnit }}(拆零)</Select-option>
            </Select>
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'oeText'">
          <span v-if="record.groupMark === 1" class="b-line-s">&boxdl;</span>
          <span v-else-if="record.groupMark === 2" class="b-line-c">&boxv;</span>
          <span v-else-if="record.groupMark === 3" class="b-line-e">&boxul;</span>
          <div v-if="record.oeText" :class="{ 'b-line-txt': [1, 2, 3].includes(record.groupMark), 'row-warning': record.stFlag && (!record.stResult || record.stResult === 2) }">
            {{ text }}
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'action'">
          <a c-error @click="handleDelete(record)">
            删除
          </a>
        </template>
      </template>
    </Table>
    <billing-entry-item ref="billingEntryItemRef" :section-id="props.sectionId" :gender-id="props.genderId" @choose="handleAddBillingEntry"/>
  </div>
</template>

<style lang="less" scoped>
.op-btn {
  text-align: right;

  button {
    margin-right: 10px;
  }
}
</style>
