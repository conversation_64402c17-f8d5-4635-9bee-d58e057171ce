<script setup lang="ts">
import { WholeBox } from '@mh-wm/whole-box'
import { Card, Typography, Divider, Button, message, Space, Alert, Table } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, h } from 'vue'
import { methodsUsage, advancedConfig } from '@/views/Wm/examples/code/WholeBoxCode'

const { Title, Paragraph, Text } = Typography

// WholeBox组件引用
const wholeBoxRef = ref()

// 方法调用记录
const methodCalls = ref([])

// 当前组件状态
const componentState = ref({
  isModalOpen: false,
  currentRecord: null,
  validationStatus: null,
  lastOperation: null
})

// 模拟测试数据
const testRecord = {
  artName: '测试药品',
  artSpec: '100mg',
  producer: '测试制药公司',
  packCells: 10,
  cellUnit: '片',
  packUnit: '盒',
  deptTotalPacks: 5,
  deptTotalCells: 3,
  totalPacks: 2,
  totalCells: 8,
  batchNo: 'TEST001',
  expDate: '20251201'
}

// 方法调用记录表格列
const methodColumns = [
  {
    title: '调用时间',
    dataIndex: 'timestamp',
    key: 'timestamp',
    width: 150
  },
  {
    title: '方法名称',
    dataIndex: 'methodName',
    key: 'methodName',
    width: 150
  },
  {
    title: '参数',
    dataIndex: 'params',
    key: 'params',
    width: 200,
    customRender: ({ record }) => {
      return h('code', { style: 'font-size: 12px; background: #f6f8fa; padding: 2px 4px; border-radius: 3px;' }, 
        JSON.stringify(record.params)
      )
    }
  },
  {
    title: '返回值',
    dataIndex: 'result',
    key: 'result',
    width: 200,
    customRender: ({ record }) => {
      return h('code', { style: 'font-size: 12px; background: #f6f8fa; padding: 2px 4px; border-radius: 3px;' }, 
        JSON.stringify(record.result)
      )
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
    customRender: ({ record }) => {
      return h('span', { 
        style: { 
          color: record.status === 'success' ? '#52c41a' : '#ff4d4f',
          fontWeight: 'bold'
        } 
      }, record.status === 'success' ? '成功' : '失败')
    }
  }
]

// 记录方法调用
const recordMethodCall = (methodName: string, params: any, result: any, status: 'success' | 'error' = 'success') => {
  const record = {
    id: Date.now(),
    timestamp: new Date().toLocaleTimeString(),
    methodName,
    params,
    result,
    status
  }
  methodCalls.value.unshift(record)
  
  // 只保留最近20条记录
  if (methodCalls.value.length > 20) {
    methodCalls.value = methodCalls.value.slice(0, 20)
  }
}

// 1. 打开拆零盒整模态框
const openSplitPackModal = () => {
  try {
    const result = wholeBoxRef.value?.handleSplitPack(testRecord)
    componentState.value.isModalOpen = true
    componentState.value.currentRecord = testRecord
    
    recordMethodCall('handleSplitPack', testRecord, result)
    message.success('拆零盒整模态框已打开')
  } catch (error) {
    recordMethodCall('handleSplitPack', testRecord, error.message, 'error')
    message.error('打开模态框失败')
  }
}

// 2. 获取当前组件数据
const getCurrentData = () => {
  try {
    const result = wholeBoxRef.value?.getCurrentData?.() || '方法不存在'
    componentState.value.lastOperation = 'getCurrentData'
    
    recordMethodCall('getCurrentData', null, result)
    message.success('获取组件数据成功')
  } catch (error) {
    recordMethodCall('getCurrentData', null, error.message, 'error')
    message.error('获取组件数据失败')
  }
}

// 3. 验证表单数据
const validateForm = () => {
  try {
    const result = wholeBoxRef.value?.validateForm?.() || '方法不存在'
    componentState.value.validationStatus = result
    
    recordMethodCall('validateForm', null, result)
    message.success(`表单验证${result ? '通过' : '失败'}`)
  } catch (error) {
    recordMethodCall('validateForm', null, error.message, 'error')
    message.error('表单验证失败')
  }
}

// 4. 重置组件状态
const resetComponent = () => {
  try {
    const result = wholeBoxRef.value?.reset?.() || '方法不存在'
    componentState.value = {
      isModalOpen: false,
      currentRecord: null,
      validationStatus: null,
      lastOperation: 'reset'
    }
    
    recordMethodCall('reset', null, result)
    message.success('组件状态已重置')
  } catch (error) {
    recordMethodCall('reset', null, error.message, 'error')
    message.error('重置组件失败')
  }
}

// 5. 获取组件配置
const getConfig = () => {
  try {
    const result = wholeBoxRef.value?.getConfig?.() || '方法不存在'
    
    recordMethodCall('getConfig', null, result)
    message.success('获取组件配置成功')
  } catch (error) {
    recordMethodCall('getConfig', null, error.message, 'error')
    message.error('获取组件配置失败')
  }
}

// 6. 设置组件配置
const setConfig = () => {
  const config = {
    modalWidth: '800px',
    showValidationStatus: true,
    autoClose: false
  }
  
  try {
    const result = wholeBoxRef.value?.setConfig?.(config) || '方法不存在'
    
    recordMethodCall('setConfig', config, result)
    message.success('设置组件配置成功')
  } catch (error) {
    recordMethodCall('setConfig', config, error.message, 'error')
    message.error('设置组件配置失败')
  }
}

// 7. 获取验证状态
const getValidationStatus = () => {
  try {
    const result = wholeBoxRef.value?.getValidationStatus?.() || '方法不存在'
    
    recordMethodCall('getValidationStatus', null, result)
    message.success('获取验证状态成功')
  } catch (error) {
    recordMethodCall('getValidationStatus', null, error.message, 'error')
    message.error('获取验证状态失败')
  }
}

// 8. 关闭模态框
const closeModal = () => {
  try {
    const result = wholeBoxRef.value?.closeModal?.() || '方法不存在'
    componentState.value.isModalOpen = false
    
    recordMethodCall('closeModal', null, result)
    message.success('模态框已关闭')
  } catch (error) {
    recordMethodCall('closeModal', null, error.message, 'error')
    message.error('关闭模态框失败')
  }
}

// 清空调用记录
const clearMethodCalls = () => {
  methodCalls.value = []
  message.success('调用记录已清空')
}

// 导出调用记录
const exportMethodCalls = () => {
  if (methodCalls.value.length === 0) {
    message.warning('暂无调用记录可导出')
    return
  }
  
  const csvContent = [
    ['调用时间', '方法名称', '参数', '返回值', '状态'].join(','),
    ...methodCalls.value.map(record => [
      record.timestamp,
      record.methodName,
      JSON.stringify(record.params),
      JSON.stringify(record.result),
      record.status === 'success' ? '成功' : '失败'
    ].join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `WholeBox方法调用记录_${new Date().toISOString().slice(0, 10)}.csv`
  link.click()
  
  message.success('调用记录导出成功')
}

// 批量测试所有方法
const testAllMethods = () => {
  message.info('开始批量测试所有方法...')
  
  const methods = [
    { name: '打开模态框', fn: openSplitPackModal },
    { name: '获取数据', fn: getCurrentData },
    { name: '验证表单', fn: validateForm },
    { name: '获取配置', fn: getConfig },
    { name: '设置配置', fn: setConfig },
    { name: '获取验证状态', fn: getValidationStatus },
    { name: '重置组件', fn: resetComponent }
  ]
  
  let index = 0
  const runNext = () => {
    if (index < methods.length) {
      const method = methods[index]
      message.info(`正在测试: ${method.name}`)
      method.fn()
      index++
      setTimeout(runNext, 1000)
    } else {
      message.success('批量测试完成！')
    }
  }
  
  runNext()
}
</script>

<template>
  <Card title="组件方法 - 拆零盒整组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">组件方法调用</Title>
      <Paragraph>
        WholeBox组件提供了多个方法供外部调用，包括打开模态框、获取数据、验证表单、配置管理等。
        这些方法可以用于实现复杂的业务逻辑和用户交互。
      </Paragraph>

      <!-- 当前组件状态 -->
      <Alert
        message="当前组件状态"
        type="info"
        show-icon
        style="margin-bottom: 24px;"
      >
        <template #description>
          <div class="component-status">
            <p><strong>模态框状态：</strong>{{ componentState.isModalOpen ? '已打开' : '已关闭' }}</p>
            <p><strong>当前记录：</strong>{{ componentState.currentRecord ? componentState.currentRecord.artName : '无' }}</p>
            <p><strong>验证状态：</strong>{{ componentState.validationStatus !== null ? (componentState.validationStatus ? '通过' : '失败') : '未验证' }}</p>
            <p><strong>最后操作：</strong>{{ componentState.lastOperation || '无' }}</p>
          </div>
        </template>
      </Alert>

      <!-- 方法测试按钮 -->
      <div class="methods-section">
        <Title :level="5">方法测试</Title>
        <Paragraph>点击下方按钮测试各个组件方法，调用结果会显示在下方的记录表格中。</Paragraph>
        
        <div class="method-buttons">
          <div class="button-group">
            <Title :level="6">基础方法</Title>
            <Space wrap>
              <Button type="primary" @click="openSplitPackModal">
                打开拆零盒整
              </Button>
              <Button @click="getCurrentData">
                获取组件数据
              </Button>
              <Button @click="validateForm">
                验证表单
              </Button>
              <Button @click="closeModal">
                关闭模态框
              </Button>
            </Space>
          </div>
          
          <div class="button-group">
            <Title :level="6">配置方法</Title>
            <Space wrap>
              <Button @click="getConfig">
                获取配置
              </Button>
              <Button @click="setConfig">
                设置配置
              </Button>
              <Button @click="getValidationStatus">
                获取验证状态
              </Button>
              <Button @click="resetComponent">
                重置组件
              </Button>
            </Space>
          </div>
          
          <div class="button-group">
            <Title :level="6">批量测试</Title>
            <Space wrap>
              <Button type="primary" @click="testAllMethods">
                测试所有方法
              </Button>
              <Button @click="clearMethodCalls">
                清空记录
              </Button>
              <Button @click="exportMethodCalls">
                导出记录
              </Button>
            </Space>
          </div>
        </div>
      </div>

      <Divider />

      <!-- 方法调用记录 -->
      <div class="records-section">
        <Title :level="5">方法调用记录</Title>
        <Paragraph>
          以下表格显示了所有方法调用的详细记录，包括调用时间、参数、返回值等信息。
        </Paragraph>
        
        <Table
          :columns="methodColumns"
          :data-source="methodCalls"
          :pagination="{ pageSize: 10 }"
          size="small"
          :scroll="{ x: 800 }"
        >
          <template #emptyText>
            <div class="empty-records">
              <p>暂无方法调用记录</p>
              <p>点击上方按钮开始测试组件方法</p>
            </div>
          </template>
        </Table>
      </div>

      <Divider />

      <!-- 代码示例 -->
      <div class="code-section">
        <Title :level="5">代码示例</Title>
        
        <CodeDemoVue 
          title="组件方法调用"
          :code="methodsUsage"
          description="如何调用WholeBox组件的各种方法"
        />
        
        <CodeDemoVue 
          title="高级配置"
          :code="advancedConfig"
          description="组件的高级配置和自定义选项"
        />
      </div>
    </div>

    <!-- 使用拆零盒整组件 -->
    <WholeBox ref="wholeBoxRef" />
  </Card>
</template>

<style scoped>
.component-status p {
  margin-bottom: 4px;
  font-size: 14px;
}

.methods-section {
  margin-bottom: 32px;
}

.method-buttons {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.button-group {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background-color: #fafafa;
}

.button-group h6 {
  margin-bottom: 12px;
  color: #1890ff;
}

.records-section {
  margin-bottom: 32px;
}

.empty-records {
  text-align: center;
  color: #999;
  padding: 40px 20px;
}

.code-section {
  margin-top: 24px;
}

:deep(.ant-table) {
  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }
}

:deep(.ant-space-item) {
  margin-bottom: 8px;
}
</style>
