// 病区绑定追溯码组件的组合式函数

import { ref, reactive, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
// import { sectionFeeTrackCodeSummaryApi } from '@mh-inpatient-hsd/util'
import { mockSectionFeeTrackCodeSummaryApi } from './mock'
import type { 
  SectionFeeTrackCodeSummaryResponse, 
  ArtSummary, 
  DrugCard, 
  PatientCard,
  PharmacyTrackCodeSum
} from './types'

// 使用日期选择器
export function useDatePicker() {
  const selectedDate = ref(dayjs())
  
  const formatDate = computed(() => {
    return selectedDate.value.format('YYYYMMDD')
  })
  
  return {
    selectedDate,
    formatDate
  }
}

// 使用数据加载
export function useDataLoader(sectionId: number) {
  const loading = ref(false)
  const rawData = ref<SectionFeeTrackCodeSummaryResponse | null>(null)
  
  const loadData = async (bsnDate: string) => {
    loading.value = true
    try {
      // 在开发环境使用模拟数据
      const response = await mockSectionFeeTrackCodeSummaryApi({
        sectionId,
        bsnDate
      })
      // 生产环境使用真实API
      // const response = await sectionFeeTrackCodeSummaryApi({
      //   sectionId,
      //   bsnDate
      // })
      rawData.value = response
      return response
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
      throw error
    } finally {
      loading.value = false
    }
  }
  
  return {
    loading,
    rawData,
    loadData
  }
}

// 使用药品卡片数据处理
export function useDrugCards() {
  const drugCards = ref<DrugCard[]>([])
  
  // 处理原始数据转换为药品卡片
  const processDrugCards = (artSummaryList: ArtSummary[], pharmacyTrackCodeSums: PharmacyTrackCodeSum[]) => {
    const cards: DrugCard[] = []
    
    artSummaryList.forEach(art => {
      // 处理患者数据
      const patients: PatientCard[] = []
      
      art.visitSummaryList.forEach(visit => {
        // 计算患者需求量
        const needAmount = visit.feeDetailList.reduce((sum, fee) => sum + fee.total, 0)
        
        // 处理已绑定的追溯码
        const boundTrackCodes = visit.feeDetailList
          .filter(fee => fee.usedTrackCodeDetails && fee.usedTrackCodeDetails.length > 0)
          .flatMap(fee => fee.usedTrackCodeDetails!.map(used => ({
            trackCode: used.trackCode,
            cellsDispensed: used.usedAmount,
            cellsRemain: 0,
            isDisassembled: art.unitType === 1,
            isUsed: true // 已绑定的追溯码标记为已使用
          })))
        
        // 格式化床号
        const formattedBedNo = visit.bedNo.endsWith('床') ? visit.bedNo : `${visit.bedNo}床`
        
        patients.push({
          visitId: visit.visitId,
          patientName: visit.patientName,
          bedNo: formattedBedNo,
          needAmount,
          boundTrackCodes,
          isCompleted: boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0) >= needAmount
        })
      })
      
      // 获取药房预绑定的追溯码
      const pharmacyTrackCodes = pharmacyTrackCodeSums.filter(ptc => ptc.artId === art.artId)
      
      // 检查是否完成绑定
      const totalNeed = patients.reduce((sum, p) => sum + p.needAmount, 0)
      const totalBound = patients.reduce((sum, p) => 
        sum + p.boundTrackCodes.reduce((codeSum, code) => codeSum + code.cellsDispensed, 0), 0
      )
      
      cards.push({
        artId: art.artId,
        keyStr: art.keyStr,
        artName: art.artName,
        artSpec: art.artSpec,
        producer: art.producer,
        unitType: art.unitType,
        totalAmount: art.totalAmount,
        unitName: art.unitName,
        patients,
        isCompleted: totalBound >= totalNeed,
        pharmacyTrackCodes
      })
    })
    
    drugCards.value = cards
    return cards
  }
  
  return {
    drugCards,
    processDrugCards
  }
}

// 使用选中状态管理
export function useSelection() {
  const selectedDrugKey = ref<string>('')
  const selectedPatientId = ref<string>('')
  
  const selectDrug = (keyStr: string) => {
    selectedDrugKey.value = keyStr
    selectedPatientId.value = '' // 清空患者选择
  }
  
  const selectPatient = (visitId: string) => {
    selectedPatientId.value = visitId
  }
  
  return {
    selectedDrugKey,
    selectedPatientId,
    selectDrug,
    selectPatient
  }
}

// 使用追溯码录入
export function useTrackCodeInput() {
  const trackCodeInput = ref('')
  const trackCodeInputRef = ref()
  
  // 聚焦到输入框
  const focusInput = async () => {
    await nextTick()
    if (trackCodeInputRef.value) {
      trackCodeInputRef.value.focus()
    }
  }
  
  // 验证追溯码格式
  const validateTrackCode = (code: string): boolean => {
    if (!code || code.length < 19 || code.length > 27) {
      if (code.length === 13 && code.startsWith('69')) {
        message.warning('扫描到13位条形码，不是有效的追溯码，无法用于业务')
      } else if (code.length < 19) {
        message.warning('追溯码长度不足，有效追溯码应为19-27位')
      }
      return false
    }
    return true
  }
  
  // 清空输入框
  const clearInput = () => {
    trackCodeInput.value = ''
  }
  
  return {
    trackCodeInput,
    trackCodeInputRef,
    focusInput,
    validateTrackCode,
    clearInput
  }
}

// 使用一键分配功能
export function useAutoAllocation() {
  // 自动分配药房预绑定的追溯码
  const autoAllocatePharmacyTrackCodes = (drugCard: DrugCard) => {
    if (!drugCard.pharmacyTrackCodes || drugCard.pharmacyTrackCodes.length === 0) {
      message.warning('该药品没有药房预绑定的追溯码')
      return
    }

    // 获取未完成绑定的患者
    const uncompletedPatients = drugCard.patients.filter(p => !p.isCompleted)
    if (uncompletedPatients.length === 0) {
      message.info('所有患者已完成绑定')
      return
    }

    // 按需求量排序患者（需求量大的优先）
    uncompletedPatients.sort((a, b) => b.needAmount - a.needAmount)

    // 分配逻辑
    drugCard.pharmacyTrackCodes.forEach(pharmacyCode => {
      let remainingCells = pharmacyCode.curTotalCells

      for (const patient of uncompletedPatients) {
        if (remainingCells <= 0) break

        const currentBound = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
        const stillNeed = patient.needAmount - currentBound

        if (stillNeed > 0) {
          const allocateAmount = Math.min(stillNeed, remainingCells)

          // 添加追溯码到患者
          patient.boundTrackCodes.push({
            trackCode: pharmacyCode.trackCode,
            cellsDispensed: allocateAmount,
            cellsRemain: remainingCells - allocateAmount,
            isDisassembled: drugCard.unitType === 1
          })

          remainingCells -= allocateAmount

          // 更新患者完成状态
          const newBoundTotal = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
          patient.isCompleted = newBoundTotal >= patient.needAmount
        }
      }
    })

    // 更新药品完成状态
    const allPatientsCompleted = drugCard.patients.every(p => p.isCompleted)
    drugCard.isCompleted = allPatientsCompleted

    message.success('自动分配完成')
  }

  return {
    autoAllocatePharmacyTrackCodes
  }
}

// 使用追溯码绑定API
export function useTrackCodeBinding() {
  // 绑定追溯码到费用明细
  const bindTrackCodeToFeeDetail = async (params: {
    execSeqid: string
    lineNo: number
    trackCode: string
    isDisassembled: boolean
    totalCells: number
  }) => {
    try {
      // 这里调用实际的API
      // await trackCodeAddCodeApi(params)
      console.log('绑定追溯码:', params)
      return true
    } catch (error) {
      console.error('绑定追溯码失败:', error)
      message.error('绑定追溯码失败')
      return false
    }
  }

  // 解绑追溯码
  const unbindTrackCode = async (params: {
    execSeqid: string
    lineNo: number
    trackCode: string
  }) => {
    try {
      // 这里调用实际的API
      // await trackCodeDelCodeApi(params)
      console.log('解绑追溯码:', params)
      return true
    } catch (error) {
      console.error('解绑追溯码失败:', error)
      message.error('解绑追溯码失败')
      return false
    }
  }

  return {
    bindTrackCodeToFeeDetail,
    unbindTrackCode
  }
}

// 使用智能分配算法
export function useSmartAllocation() {
  // 智能分配追溯码到具体的费用明细
  const smartAllocateTrackCode = (
    drugCard: DrugCard,
    trackCode: string,
    isDisassembled: boolean,
    totalCells: number
  ) => {
    // 获取所有未完成的费用明细
    const uncompletedDetails: Array<{
      patient: PatientCard
      feeDetail: any
      remainingNeed: number
    }> = []

    drugCard.patients.forEach(patient => {
      if (!patient.isCompleted) {
        // 这里需要访问原始的费用明细数据
        // 暂时用模拟数据
        const currentBound = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
        const remainingNeed = patient.needAmount - currentBound

        if (remainingNeed > 0) {
          uncompletedDetails.push({
            patient,
            feeDetail: { execSeqid: '123', lineNo: 1 }, // 模拟数据
            remainingNeed
          })
        }
      }
    })

    if (uncompletedDetails.length === 0) {
      message.warning('所有患者已完成绑定')
      return []
    }

    // 按需求量排序（需求量大的优先）
    uncompletedDetails.sort((a, b) => b.remainingNeed - a.remainingNeed)

    // 分配算法
    const allocations: Array<{
      patient: PatientCard
      execSeqid: string
      lineNo: number
      allocatedAmount: number
    }> = []

    let remainingCells = totalCells

    for (const detail of uncompletedDetails) {
      if (remainingCells <= 0) break

      const allocateAmount = Math.min(detail.remainingNeed, remainingCells)

      if (allocateAmount > 0) {
        allocations.push({
          patient: detail.patient,
          execSeqid: detail.feeDetail.execSeqid,
          lineNo: detail.feeDetail.lineNo,
          allocatedAmount: allocateAmount
        })

        remainingCells -= allocateAmount
      }
    }

    return allocations
  }

  return {
    smartAllocateTrackCode
  }
}
