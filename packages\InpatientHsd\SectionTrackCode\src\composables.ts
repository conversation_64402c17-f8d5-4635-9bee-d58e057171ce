// 病区绑定追溯码组件的组合式函数

import { computed, nextTick, ref } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { sectionFeeTrackCodeSummaryApi } from '@mh-inpatient-hsd/util'
import { sectionOeFeeBindTrackCodeApi, sectionOeFeeUnbindTrackCodeApi } from '@mh-wm/util'
// import { mockSectionFeeTrackCodeSummaryApi } from './mock'
import { articleClearDisassembledApi, articleSetDisassembledApi } from '@mh-hip/util'
import type { ArtSummary, DrugCard, PatientCard, PharmacyTrackCodeSum, SectionFeeTrackCodeSummaryResponse } from './types'

// 使用日期选择器
export function useDatePicker() {
  const selectedDate = ref(dayjs())

  const formatDate = computed(() => {
    return selectedDate.value.format('YYYYMMDD')
  })

  return {
    selectedDate,
    formatDate
  }
}

// 使用数据加载
export function useDataLoader(sectionId: number) {
  const loading = ref(false)
  const rawData = ref<SectionFeeTrackCodeSummaryResponse | null>(null)

  const loadData = async (bsnDate: string) => {
    loading.value = true
    try {
      // 在开发环境使用模拟数据
      // const response = await mockSectionFeeTrackCodeSummaryApi({
      //   sectionId,
      //   bsnDate
      // })
      // 生产环境使用真实API
      const response = await sectionFeeTrackCodeSummaryApi({
        sectionId,
        bsnDate
      })
      rawData.value = response
      return response
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    rawData,
    loadData
  }
}

// 使用药品卡片数据处理
export function useDrugCards() {
  const drugCards = ref<DrugCard[]>([])

  // 处理原始数据转换为药品卡片
  const processDrugCards = (artSummaryList: ArtSummary[], pharmacyTrackCodeSums: PharmacyTrackCodeSum[]) => {
    const cards: DrugCard[] = []

    artSummaryList.forEach(art => {
      // 处理患者数据
      const patients: PatientCard[] = []

      art.visitSummaryList.forEach(visit => {
        // 计算患者需求量
        const needAmount = visit.feeDetailList.reduce((sum, fee) => sum + fee.total, 0)

        // 处理已绑定的追溯码
        const boundTrackCodes = visit.feeDetailList
          .filter(fee => fee.usedTrackCodeDetails && fee.usedTrackCodeDetails.length > 0)
          .flatMap(fee => fee.usedTrackCodeDetails!.map(used => ({
            trackCode: used.trackCode,
            cellsDispensed: used.usedAmount,
            cellsRemain: 0,
            isDisassembled: art.unitType === 1,
            isUsed: true // 已绑定的追溯码标记为已使用
          })))

        // 格式化床号
        const formattedBedNo = visit.bedNo ?
          (visit.bedNo.endsWith('床') ? visit.bedNo : `${visit.bedNo}床`) :
          '出区'

        // 初始化费用明细的boundTrackCodes
        const feeDetails = visit.feeDetailList.map(fee => ({
          ...fee,
          boundTrackCodes: [] as any[]
        }))

        patients.push({
          visitId: visit.visitId,
          patientName: visit.patientName,
          bedNo: formattedBedNo,
          needAmount,
          boundTrackCodes,
          isCompleted: boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0) >= needAmount,
          feeDetails // 保存费用明细信息
        })
      })

      // 获取药房预绑定的追溯码并保存原始数量
      const pharmacyTrackCodes = pharmacyTrackCodeSums.filter(ptc => ptc.artId === art.artId).map(code => ({
        ...code,
        originalTotalPacks: code.curTotalPacks, // 保存原始包装数
        originalTotalCells: code.curTotalCells  // 保存原始制剂数
      }))

      // 检查是否完成绑定
      const totalNeed = patients.reduce((sum, p) => sum + p.needAmount, 0)
      const totalBound = patients.reduce((sum, p) =>
        sum + p.boundTrackCodes.reduce((codeSum, code) => codeSum + code.cellsDispensed, 0), 0
      )

      cards.push({
        artId: art.artId,
        keyStr: art.keyStr,
        artName: art.artName,
        artSpec: art.artSpec,
        producer: art.producer,
        unitType: art.unitType,
        isDisassembled: art.isDisassembled,
        totalAmount: art.totalAmount,
        unitName: art.unitName,
        patients,
        isCompleted: totalBound >= totalNeed,
        pharmacyTrackCodes
      })
    })

    drugCards.value = cards
    return cards
  }

  return {
    drugCards,
    processDrugCards
  }
}

// 使用选中状态管理
export function useSelection() {
  const selectedDrugKey = ref<string>('')
  const selectedPatientId = ref<string>('')

  const selectDrug = (keyStr: string) => {
    selectedDrugKey.value = keyStr
    selectedPatientId.value = '' // 清空患者选择
  }

  const selectPatient = (visitId: string) => {
    selectedPatientId.value = visitId
  }

  return {
    selectedDrugKey,
    selectedPatientId,
    selectDrug,
    selectPatient
  }
}

// 使用追溯码录入
export function useTrackCodeInput() {
  const trackCodeInput = ref('')
  const trackCodeInputRef = ref()

  // 聚焦到输入框
  const focusInput = async () => {
    await nextTick()
    if (trackCodeInputRef.value) {
      trackCodeInputRef.value.focus()
    }
  }

  // 验证追溯码格式
  const validateTrackCode = (code: string): boolean => {
    if (!code || code.length < 19 || code.length > 27) {
      if (code.length === 13 && code.startsWith('69')) {
        message.warning('扫描到13位条形码，不是有效的追溯码，无法用于业务')
      } else if (code.length < 19) {
        message.warning('追溯码长度不足，有效追溯码应为19-27位')
      }
      return false
    }
    return true
  }

  // 清空输入框
  const clearInput = () => {
    trackCodeInput.value = ''
  }

  return {
    trackCodeInput,
    trackCodeInputRef,
    focusInput,
    validateTrackCode,
    clearInput
  }
}

// 使用一键分配功能
export function useAutoAllocation() {
  // 自动分配药房预绑定的追溯码（带API调用）
  const autoAllocatePharmacyTrackCodes = async (drugCard: DrugCard, sectionId: number, bindTrackCodeToFeeDetail: Function) => {
    if (!drugCard.pharmacyTrackCodes || drugCard.pharmacyTrackCodes.length === 0) {
      message.warning('该药品没有药房预绑定的追溯码')
      return
    }

    // 获取未完成绑定的患者
    const uncompletedPatients = drugCard.patients.filter(p => !p.isCompleted)
    if (uncompletedPatients.length === 0) {
      message.info('所有患者已完成绑定')
      return
    }

    // 按床号排序患者（床号小的优先）
    uncompletedPatients.sort((a, b) => {
      const bedNoA = parseInt(a.bedNo?.replace(/[^\d]/g, '') || '999')
      const bedNoB = parseInt(b.bedNo?.replace(/[^\d]/g, '') || '999')
      return bedNoA - bedNoB
    })

    try {
      let apiCalls: Promise<any>[] = []
      let allocatedPatients: string[] = []

      // 分配逻辑 - 更新为费用明细级别绑定
      for (const pharmacyCode of drugCard.pharmacyTrackCodes) {
        let remainingCells = drugCard.unitType === 2 ? pharmacyCode.curTotalPacks : pharmacyCode.curTotalCells

        for (const patient of uncompletedPatients) {
          if (remainingCells <= 0) break

          const currentBound = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
          const stillNeed = patient.needAmount - currentBound

          if (stillNeed > 0) {
            const allocateAmount = Math.min(stillNeed, remainingCells)

            // 绑定到费用明细级别
            const uncompletedFeeDetails = patient.feeDetails.filter(fee =>
              fee.artId === drugCard.artId &&
              fee.boundTrackCodes.reduce((sum, tc) => sum + (drugCard.unitType === 2 ? (tc.totalPacks || 0) : (tc.totalCells || 0)), 0) < fee.total
            )

            let remainingToAllocate = allocateAmount
            for (const feeDetail of uncompletedFeeDetails) {
              if (remainingToAllocate <= 0) break

              const currentFeeDetailBound = feeDetail.boundTrackCodes.reduce((sum, tc) =>
                sum + (drugCard.unitType === 2 ? (tc.totalPacks || 0) : (tc.totalCells || 0)), 0)
              const feeDetailNeed = feeDetail.total - currentFeeDetailBound

              if (feeDetailNeed > 0) {
                const feeDetailAllocate = Math.min(feeDetailNeed, remainingToAllocate)

                // 添加API调用到队列
                apiCalls.push(
                  bindTrackCodeToFeeDetail(
                    sectionId,
                    feeDetail,
                    pharmacyCode.trackCode,
                    feeDetailAllocate,
                    drugCard.unitType === 1
                  ).then(() => {
                    // API调用成功后更新本地数据
                    feeDetail.boundTrackCodes.push({
                      trackCode: pharmacyCode.trackCode,
                      totalPacks: drugCard.unitType === 2 ? feeDetailAllocate : undefined,
                      totalCells: drugCard.unitType === 1 ? feeDetailAllocate : undefined,
                      isDisassembled: drugCard.unitType === 1
                    })
                  })
                )

                remainingToAllocate -= feeDetailAllocate
              }
            }

            // 计算实际分配的数量
            const actualAllocated = allocateAmount - remainingToAllocate

            if (actualAllocated > 0) {
              // 同步更新患者显示的追溯码
              patient.boundTrackCodes.push({
                trackCode: pharmacyCode.trackCode,
                cellsDispensed: actualAllocated,
                cellsRemain: remainingCells - actualAllocated,
                isDisassembled: drugCard.unitType === 1
              })

              remainingCells -= actualAllocated

              // 更新患者完成状态
              const newBoundTotal = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
              patient.isCompleted = newBoundTotal >= patient.needAmount

              // 记录分配的患者
              if (!allocatedPatients.includes(`${patient.bedNo} ${patient.patientName}`)) {
                allocatedPatients.push(`${patient.bedNo} ${patient.patientName}`)
              }
            }
          }
        }

        // 更新药房追溯码的剩余数量
        if (drugCard.unitType === 2) {
          pharmacyCode.curTotalPacks = remainingCells
        } else {
          pharmacyCode.curTotalCells = remainingCells
        }
      }

      // 等待所有API调用完成
      await Promise.all(apiCalls)

      // 更新药品完成状态
      const allPatientsCompleted = drugCard.patients.every(p => p.isCompleted)
      drugCard.isCompleted = allPatientsCompleted

      if (allocatedPatients.length > 0) {
        message.success(`药房追溯码已分配给: ${allocatedPatients.join(', ')}`)
      } else {
        message.info('没有可分配的追溯码')
      }
    } catch (error) {
      console.error('一键分配失败:', error)
      message.error('一键分配失败，请重试')
    }
  }

  return {
    autoAllocatePharmacyTrackCodes
  }
}

// 使用追溯码绑定API
export function useTrackCodeBinding() {
  // 绑定追溯码到费用明细
  const bindTrackCodeToFeeDetail = async (params: {
    execSeqid: string
    lineNo: number
    trackCode: string
    isDisassembled: boolean
    totalCells: number
  }) => {
    try {
      // 这里调用实际的API
      // await trackCodeAddCodeApi(params)
      console.log('绑定追溯码:', params)
      return true
    } catch (error) {
      console.error('绑定追溯码失败:', error)
      message.error('绑定追溯码失败')
      return false
    }
  }

  // 解绑追溯码
  const unbindTrackCode = async (params: {
    execSeqid: string
    lineNo: number
    trackCode: string
  }) => {
    try {
      // 这里调用实际的API
      // await trackCodeDelCodeApi(params)
      console.log('解绑追溯码:', params)
      return true
    } catch (error) {
      console.error('解绑追溯码失败:', error)
      message.error('解绑追溯码失败')
      return false
    }
  }

  return {
    bindTrackCodeToFeeDetail,
    unbindTrackCode
  }
}

// 使用智能分配算法
export function useSmartAllocation() {
  // 智能分配追溯码到具体的费用明细
  const smartAllocateTrackCode = (
    drugCard: DrugCard,
    trackCode: string,
    isDisassembled: boolean,
    totalCells: number
  ) => {
    // 获取所有未完成的费用明细
    const uncompletedDetails: Array<{
      patient: PatientCard
      feeDetail: any
      remainingNeed: number
    }> = []

    drugCard.patients.forEach(patient => {
      if (!patient.isCompleted) {
        // 这里需要访问原始的费用明细数据
        // 暂时用模拟数据
        const currentBound = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
        const remainingNeed = patient.needAmount - currentBound

        if (remainingNeed > 0) {
          uncompletedDetails.push({
            patient,
            feeDetail: { execSeqid: '123', lineNo: 1 }, // 模拟数据
            remainingNeed
          })
        }
      }
    })

    if (uncompletedDetails.length === 0) {
      message.warning('所有患者已完成绑定')
      return []
    }

    // 按需求量排序（需求量大的优先）
    uncompletedDetails.sort((a, b) => b.remainingNeed - a.remainingNeed)

    // 分配算法
    const allocations: Array<{
      patient: PatientCard
      execSeqid: string
      lineNo: number
      allocatedAmount: number
    }> = []

    let remainingCells = totalCells

    for (const detail of uncompletedDetails) {
      if (remainingCells <= 0) break

      const allocateAmount = Math.min(detail.remainingNeed, remainingCells)

      if (allocateAmount > 0) {
        allocations.push({
          patient: detail.patient,
          execSeqid: detail.feeDetail.execSeqid,
          lineNo: detail.feeDetail.lineNo,
          allocatedAmount: allocateAmount
        })

        remainingCells -= allocateAmount
      }
    }

    return allocations
  }

  return {
    smartAllocateTrackCode
  }
}

// 使用API调用
export function useApiCalls() {
  // 调用真实的API获取数据
  const callSectionFeeTrackCodeSummaryApi = async (params: {
    sectionId: number
    bsnDate: string
  }) => {
    try {
      return await sectionFeeTrackCodeSummaryApi(params)
    } catch (error) {
      // console.error('API调用失败，使用模拟数据:', error)
      // // 如果真实API失败，使用模拟数据
      // return await mockSectionFeeTrackCodeSummaryApi(params)
    }
  }

  // 设置拆零上报
  const setDisassembled = async (artId: number) => {
    try {
      await articleSetDisassembledApi({ artId })
      return true
    } catch (error) {
      console.error('设置拆零上报失败:', error)
      message.error('设置拆零上报失败')
      return false
    }
  }

  // 取消拆零上报
  const clearDisassembled = async (artId: number) => {
    try {
      await articleClearDisassembledApi({ artId })
      return true
    } catch (error) {
      console.error('取消拆零上报失败:', error)
      message.error('取消拆零上报失败')
      return false
    }
  }

  // 绑定追溯码API调用
  const bindTrackCodeToFeeDetail = async (
    sectionId: number,
    feeDetail: any,
    trackCode: string,
    amount: number,
    isDisassembled: boolean
  ) => {
    const params: any = {
      sectionId,
      execSeqid: feeDetail.execSeqid,
      lineNo: feeDetail.lineNo,
      visitId: feeDetail.visitId,
      oeNo: feeDetail.oeNo,
      artId: feeDetail.artId,
      bseqid: feeDetail.bseqid,
      billLineno: feeDetail.billLineNo || 1,
      trackCode,
      isDisassembled
    }

    if (isDisassembled) {
      params.totalCells = amount
    } else {
      params.totalPacks = amount
    }

    try {
      const response = await sectionOeFeeBindTrackCodeApi(params)

      // 检查响应中的异常提醒
      if (response && typeof response === 'string' && response.trim()) {
        message.warning(`绑定提醒: ${response}`)
      }

      return { success: true, message: response }
    } catch (error) {
      console.error('绑定追溯码失败:', error)
      throw error
    }
  }

  // 解绑追溯码API调用
  const unbindTrackCodeFromFeeDetail = async (
    sectionId: number,
    feeDetail: any,
    trackCode: string,
    amount: number,
    isDisassembled: boolean
  ) => {
    const params: any = {
      sectionId,
      execSeqid: feeDetail.execSeqid,
      lineNo: feeDetail.lineNo,
      visitId: feeDetail.visitId,
      oeNo: feeDetail.oeNo,
      artId: feeDetail.artId,
      bseqid: feeDetail.bseqid,
      billLineno: feeDetail.billLineNo || 1,
      trackCode,
      isDisassembled
    }

    if (isDisassembled) {
      params.totalCells = amount
    } else {
      params.totalPacks = amount
    }

    try {
      const response = await sectionOeFeeUnbindTrackCodeApi(params)

      // 检查响应中的异常提醒
      if (response && typeof response === 'string' && response.trim()) {
        message.warning(`解绑提醒: ${response}`)
      }

      return { success: true, message: response }
    } catch (error) {
      console.error('解绑追溯码失败:', error)
      throw error
    }
  }

  return {
    callSectionFeeTrackCodeSummaryApi,
    setDisassembled,
    clearDisassembled,
    bindTrackCodeToFeeDetail,
    unbindTrackCodeFromFeeDetail
  }
}
