// 病区绑定追溯码组件的组合式函数

import { computed, nextTick, ref } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { sectionFeeTrackCodeSummaryApi } from '@mh-inpatient-hsd/util'
import { sectionOeFeeBindTrackCodeApi, sectionOeFeeUnbindTrackCodeApi } from '@mh-wm/util'
// import { mockSectionFeeTrackCodeSummaryApi } from './mock'
import { articleClearDisassembledApi, articleSetDisassembledApi } from '@mh-hip/util'
import type { ArtSummary, DrugCard, PatientCard, PharmacyTrackCodeSum, SectionFeeTrackCodeSummaryResponse } from './types'

// 请求队列管理器
class ApiRequestQueue {
  private bindQueue: Array<{
    params: any
    resolve: (value: any) => void
    reject: (reason: any) => void
    timestamp: number
  }> = []

  private unbindQueue: Array<{
    params: any
    resolve: (value: any) => void
    reject: (reason: any) => void
    timestamp: number
  }> = []

  private isProcessing = false
  private readonly BASE_BATCH_SIZE = 10 // 基础批次大小
  private readonly DELAY_BETWEEN_REQUESTS = 30 // 每个请求间隔30ms
  private readonly DELAY_BETWEEN_BATCHES = 100 // 每批间隔100ms
  private readonly MAX_CONCURRENT = 5 // 最大并发数

  // 动态计算批次大小
  private getBatchSize(totalRequests: number): number {
    if (totalRequests <= 10) {
      return totalRequests // 超小量：一次性处理
    } else if (totalRequests <= 50) {
      return 15 // 小量请求：较大批次，快速处理
    } else if (totalRequests <= 150) {
      return 20 // 中量请求：大批次
    } else {
      return 25 // 大量请求：最大批次，最高效率
    }
  }

  // 动态计算批次间延迟
  private getBatchDelay(totalRequests: number): number {
    if (totalRequests <= 10) {
      return 20 // 超小量：最小延迟
    } else if (totalRequests <= 50) {
      return 40 // 小量请求：小延迟
    } else if (totalRequests <= 150) {
      return 60 // 中量请求：适中延迟
    } else {
      return 80 // 大量请求：标准延迟
    }
  }

  // 动态计算并发数
  private getConcurrentSize(totalRequests: number): number {
    if (totalRequests <= 10) {
      return Math.min(totalRequests, 8) // 超小量：高并发
    } else if (totalRequests <= 50) {
      return 8 // 小量请求：高并发
    } else if (totalRequests <= 150) {
      return 6 // 中量请求：适中并发
    } else {
      return 5 // 大量请求：保守并发
    }
  }

  // 添加绑定请求到队列
  addBindRequest(params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.bindQueue.push({
        params,
        resolve,
        reject,
        timestamp: Date.now()
      })

      // 启动处理器
      this.processQueue()
    })
  }

  // 添加解绑请求到队列
  addUnbindRequest(params: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.unbindQueue.push({
        params,
        resolve,
        reject,
        timestamp: Date.now()
      })

      // 启动处理器
      this.processQueue()
    })
  }

  // 处理队列（优化版本）
  private async processQueue() {
    if (this.isProcessing) return

    this.isProcessing = true

    try {
      while (this.bindQueue.length > 0 || this.unbindQueue.length > 0) {
        // 计算总请求数，用于动态调整参数
        const totalRequests = this.bindQueue.length + this.unbindQueue.length
        const batchSize = this.getBatchSize(totalRequests)
        const batchDelay = this.getBatchDelay(totalRequests)
        const concurrentSize = this.getConcurrentSize(totalRequests)

        // 优先处理解绑请求（用户体验更重要）
        const unbindBatch = this.unbindQueue.splice(0, batchSize)
        const bindBatch = this.bindQueue.splice(0, batchSize)

        // 处理解绑批次
        if (unbindBatch.length > 0) {
          await this.processBatch(unbindBatch, 'unbind', concurrentSize)
        }

        // 处理绑定批次
        if (bindBatch.length > 0) {
          await this.processBatch(bindBatch, 'bind', concurrentSize)
        }

        // 动态批次间延迟
        if (this.bindQueue.length > 0 || this.unbindQueue.length > 0) {
          await this.delay(batchDelay)
        }
      }
    } finally {
      this.isProcessing = false
    }
  }

  // 处理单个批次（优化版本）
  private async processBatch(batch: any[], type: 'bind' | 'unbind', concurrentSize?: number) {
    // 使用动态并发数或默认值
    const maxConcurrent = concurrentSize || this.MAX_CONCURRENT
    // 直接按并发数分组，减少嵌套延迟
    const chunks = this.chunkArray(batch, maxConcurrent)

    for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
      const chunk = chunks[chunkIndex]

      // 并发处理当前组
      const promises = chunk.map(async (item, index) => {
        // 只在组内添加极小延迟，避免完全同时发送
        if (index > 0) {
          await this.delay(10) // 减少到10ms
        }

        try {
          let result: any
          if (type === 'bind') {
            result = await sectionOeFeeBindTrackCodeApi(item.params)
          } else {
            result = await sectionOeFeeUnbindTrackCodeApi(item.params)
          }
          item.resolve(result)
          return { success: true, result }
        } catch (error) {
          item.reject(error)
          return { success: false, error }
        }
      })

      // 等待当前组完成
      await Promise.allSettled(promises)

      // 组间延迟（只在非最后一组时添加）
      if (chunkIndex < chunks.length - 1) {
        await this.delay(5) // 减少组间延迟到5ms
      }
    }
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // 数组分块
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  // 获取队列状态
  getQueueStatus() {
    return {
      bindQueueLength: this.bindQueue.length,
      unbindQueueLength: this.unbindQueue.length,
      isProcessing: this.isProcessing,
      totalPending: this.bindQueue.length + this.unbindQueue.length
    }
  }

  // 清空队列（用于组件销毁时）
  clearQueue() {
    // 拒绝所有待处理的请求
    [...this.bindQueue, ...this.unbindQueue].forEach(item => {
      item.reject(new Error('队列已清空'))
    })

    this.bindQueue = []
    this.unbindQueue = []
  }
}

// 创建全局队列实例
const apiQueue = new ApiRequestQueue()

// 使用日期选择器
export function useDatePicker() {
  const selectedDate = ref(dayjs())

  const formatDate = computed(() => {
    return selectedDate.value.format('YYYYMMDD')
  })

  return {
    selectedDate,
    formatDate
  }
}

// 使用数据加载
export function useDataLoader(sectionId: number) {
  const loading = ref(false)
  const rawData = ref<SectionFeeTrackCodeSummaryResponse | null>(null)

  const loadData = async (bsnDate: string) => {
    loading.value = true
    try {
      // 在开发环境使用模拟数据
      // const response = await mockSectionFeeTrackCodeSummaryApi({
      //   sectionId,
      //   bsnDate
      // })
      // 生产环境使用真实API
      const response = await sectionFeeTrackCodeSummaryApi({
        sectionId,
        bsnDate
      })
      rawData.value = response
      return response
    } catch (error) {
      console.error('加载数据失败:', error)
      message.error('加载数据失败')
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    loading,
    rawData,
    loadData
  }
}

// 使用药品卡片数据处理
export function useDrugCards() {
  const drugCards = ref<DrugCard[]>([])

  // 处理原始数据转换为药品卡片
  const processDrugCards = (artSummaryList: ArtSummary[], pharmacyTrackCodeSums: PharmacyTrackCodeSum[]) => {
    const cards: DrugCard[] = []

    artSummaryList.forEach(art => {
      // 处理患者数据
      const patients: PatientCard[] = []

      art.visitSummaryList.forEach(visit => {
        // 计算患者需求量
        const needAmount = visit.feeDetailList.reduce((sum, fee) => sum + fee.total, 0)

        // 处理已绑定的追溯码（汇总相同追溯码）
        const trackCodeMap = new Map<string, any>()
        const feeDetailMap = new Map<string, any[]>() // 记录每个追溯码对应的费用明细

        visit.feeDetailList.forEach(fee => {
          if (fee.usedTrackCodeDetails && fee.usedTrackCodeDetails.length > 0) {
            fee.usedTrackCodeDetails.forEach(used => {
              const key = used.trackCode

              // 汇总相同追溯码的数量
              if (trackCodeMap.has(key)) {
                const existing = trackCodeMap.get(key)
                existing.cellsDispensed += used.usedAmount
                // 添加费用明细到映射
                feeDetailMap.get(key)!.push(fee)
              } else {
                trackCodeMap.set(key, {
                  trackCode: used.trackCode,
                  cellsDispensed: used.usedAmount,
                  cellsRemain: 0,
                  isDisassembled: art.unitType === 1,
                  isUsed: true // 已绑定的追溯码标记为已使用
                })
                // 初始化费用明细映射
                feeDetailMap.set(key, [fee])
              }
            })
          }
        })

        const boundTrackCodes = Array.from(trackCodeMap.values())

        // 为每个追溯码添加费用明细信息，用于解绑时使用
        boundTrackCodes.forEach(trackCode => {
          trackCode.feeDetails = feeDetailMap.get(trackCode.trackCode) || []
        })

        // 格式化床号
        const formattedBedNo = visit.bedNo ?
          (visit.bedNo.endsWith('床') ? visit.bedNo : `${visit.bedNo}床`) :
          '出区'

        // 初始化费用明细的boundTrackCodes
        const feeDetails = visit.feeDetailList.map(fee => ({
          ...fee,
          boundTrackCodes: [] as any[]
        }))

        patients.push({
          visitId: visit.visitId,
          patientName: visit.patientName,
          bedNo: formattedBedNo,
          needAmount,
          boundTrackCodes,
          isCompleted: boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0) >= needAmount,
          feeDetails // 保存费用明细信息
        })
      })

      // 获取药房预绑定的追溯码并保存原始数量
      const pharmacyTrackCodes = pharmacyTrackCodeSums.filter(ptc => ptc.artId === art.artId).map(code => ({
        ...code,
        originalTotalPacks: code.curTotalPacks, // 保存原始包装数
        originalTotalCells: code.curTotalCells  // 保存原始制剂数
      }))

      // 检查是否完成绑定
      const totalNeed = patients.reduce((sum, p) => sum + p.needAmount, 0)
      const totalBound = patients.reduce((sum, p) =>
        sum + p.boundTrackCodes.reduce((codeSum, code) => codeSum + code.cellsDispensed, 0), 0
      )

      cards.push({
        artId: art.artId,
        keyStr: art.keyStr,
        artName: art.artName,
        artSpec: art.artSpec,
        producer: art.producer,
        unitType: art.unitType,
        isDisassembled: art.isDisassembled,
        totalAmount: art.totalAmount,
        unitName: art.unitName,
        patients,
        isCompleted: totalBound >= totalNeed,
        pharmacyTrackCodes
      })
    })

    drugCards.value = cards
    return cards
  }

  return {
    drugCards,
    processDrugCards
  }
}

// 使用选中状态管理
export function useSelection() {
  const selectedDrugKey = ref<string>('')
  const selectedPatientId = ref<string>('')

  const selectDrug = (keyStr: string) => {
    selectedDrugKey.value = keyStr
    selectedPatientId.value = '' // 清空患者选择
  }

  const selectPatient = (visitId: string) => {
    selectedPatientId.value = visitId
  }

  return {
    selectedDrugKey,
    selectedPatientId,
    selectDrug,
    selectPatient
  }
}

// 使用追溯码录入
export function useTrackCodeInput() {
  const trackCodeInput = ref('')
  const trackCodeInputRef = ref()

  // 聚焦到输入框
  const focusInput = async () => {
    await nextTick()
    if (trackCodeInputRef.value) {
      trackCodeInputRef.value.focus()
    }
  }

  // 验证追溯码格式
  const validateTrackCode = (code: string): boolean => {
    if (!code || code.length < 19 || code.length > 27) {
      if (code.length === 13 && code.startsWith('69')) {
        message.warning('扫描到13位条形码，不是有效的追溯码，无法用于业务')
      } else if (code.length < 19) {
        message.warning('追溯码长度不足，有效追溯码应为19-27位')
      }
      return false
    }
    return true
  }

  // 清空输入框
  const clearInput = () => {
    trackCodeInput.value = ''
  }

  return {
    trackCodeInput,
    trackCodeInputRef,
    focusInput,
    validateTrackCode,
    clearInput
  }
}

// 使用一键分配功能
export function useAutoAllocation() {
  // 自动分配药房预绑定的追溯码（带API调用）
  const autoAllocatePharmacyTrackCodes = async (drugCard: DrugCard, sectionId: number, bindTrackCodeToFeeDetail: Function) => {
    if (!drugCard.pharmacyTrackCodes || drugCard.pharmacyTrackCodes.length === 0) {
      message.warning('该药品没有药房预绑定的追溯码')
      return
    }

    // 获取未完成绑定的患者
    const uncompletedPatients = drugCard.patients.filter(p => !p.isCompleted)
    if (uncompletedPatients.length === 0) {
      message.info('所有患者已完成绑定')
      return
    }

    // 按床号排序患者（床号小的优先）
    uncompletedPatients.sort((a, b) => {
      const bedNoA = parseInt(a.bedNo?.replace(/[^\d]/g, '') || '999')
      const bedNoB = parseInt(b.bedNo?.replace(/[^\d]/g, '') || '999')
      return bedNoA - bedNoB
    })

    try {
      let apiCalls: Promise<any>[] = []
      let allocatedPatients: string[] = []

      // 分配逻辑 - 更新为费用明细级别绑定
      for (const pharmacyCode of drugCard.pharmacyTrackCodes) {
        let remainingCells = drugCard.unitType === 2 ? pharmacyCode.curTotalPacks : pharmacyCode.curTotalCells

        for (const patient of uncompletedPatients) {
          if (remainingCells <= 0) break

          const currentBound = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
          const stillNeed = patient.needAmount - currentBound

          if (stillNeed > 0) {
            const allocateAmount = Math.min(stillNeed, remainingCells)

            // 绑定到费用明细级别
            const uncompletedFeeDetails = patient.feeDetails.filter(fee =>
              fee.artId === drugCard.artId &&
              fee.boundTrackCodes.reduce((sum, tc) => sum + (drugCard.unitType === 2 ? (tc.totalPacks || 0) : (tc.totalCells || 0)), 0) < fee.total
            )

            let remainingToAllocate = allocateAmount
            for (const feeDetail of uncompletedFeeDetails) {
              if (remainingToAllocate <= 0) break

              const currentFeeDetailBound = feeDetail.boundTrackCodes.reduce((sum, tc) =>
                sum + (drugCard.unitType === 2 ? (tc.totalPacks || 0) : (tc.totalCells || 0)), 0)
              const feeDetailNeed = feeDetail.total - currentFeeDetailBound

              if (feeDetailNeed > 0) {
                const feeDetailAllocate = Math.min(feeDetailNeed, remainingToAllocate)

                // 添加API调用到队列
                apiCalls.push(
                  bindTrackCodeToFeeDetail(
                    sectionId,
                    feeDetail,
                    pharmacyCode.trackCode,
                    feeDetailAllocate,
                    drugCard.unitType === 1
                  ).then(() => {
                    // API调用成功后更新本地数据
                    feeDetail.boundTrackCodes.push({
                      trackCode: pharmacyCode.trackCode,
                      totalPacks: drugCard.unitType === 2 ? feeDetailAllocate : undefined,
                      totalCells: drugCard.unitType === 1 ? feeDetailAllocate : undefined,
                      isDisassembled: drugCard.unitType === 1
                    })
                  })
                )

                remainingToAllocate -= feeDetailAllocate
              }
            }

            // 计算实际分配的数量
            const actualAllocated = allocateAmount - remainingToAllocate

            if (actualAllocated > 0) {
              // 同步更新患者显示的追溯码
              patient.boundTrackCodes.push({
                trackCode: pharmacyCode.trackCode,
                cellsDispensed: actualAllocated,
                cellsRemain: remainingCells - actualAllocated,
                isDisassembled: drugCard.unitType === 1
              })

              remainingCells -= actualAllocated

              // 更新患者完成状态
              const newBoundTotal = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
              patient.isCompleted = newBoundTotal >= patient.needAmount

              // 记录分配的患者
              if (!allocatedPatients.includes(`${patient.bedNo} ${patient.patientName}`)) {
                allocatedPatients.push(`${patient.bedNo} ${patient.patientName}`)
              }
            }
          }
        }

        // 更新药房追溯码的剩余数量
        if (drugCard.unitType === 2) {
          pharmacyCode.curTotalPacks = remainingCells
        } else {
          pharmacyCode.curTotalCells = remainingCells
        }
      }

      // 等待所有API调用完成
      await Promise.all(apiCalls)

      // 更新药品完成状态
      const allPatientsCompleted = drugCard.patients.every(p => p.isCompleted)
      drugCard.isCompleted = allPatientsCompleted

      if (allocatedPatients.length > 0) {
        message.success(`药房追溯码已分配给: ${allocatedPatients.join(', ')}`)
      } else {
        message.info('没有可分配的追溯码')
      }
    } catch (error) {
      console.error('一键分配失败:', error)
      message.error('一键分配失败，请重试')
    }
  }

  return {
    autoAllocatePharmacyTrackCodes
  }
}

// 使用追溯码绑定API
export function useTrackCodeBinding() {
  // 绑定追溯码到费用明细
  const bindTrackCodeToFeeDetail = async (params: {
    execSeqid: string
    lineNo: number
    trackCode: string
    isDisassembled: boolean
    totalCells: number
  }) => {
    try {
      // 这里调用实际的API
      // await trackCodeAddCodeApi(params)
      console.log('绑定追溯码:', params)
      return true
    } catch (error) {
      console.error('绑定追溯码失败:', error)
      message.error('绑定追溯码失败')
      return false
    }
  }

  // 解绑追溯码
  const unbindTrackCode = async (params: {
    execSeqid: string
    lineNo: number
    trackCode: string
  }) => {
    try {
      // 这里调用实际的API
      // await trackCodeDelCodeApi(params)
      console.log('解绑追溯码:', params)
      return true
    } catch (error) {
      console.error('解绑追溯码失败:', error)
      message.error('解绑追溯码失败')
      return false
    }
  }

  return {
    bindTrackCodeToFeeDetail,
    unbindTrackCode
  }
}

// 使用智能分配算法
export function useSmartAllocation() {
  // 智能分配追溯码到具体的费用明细
  const smartAllocateTrackCode = (
    drugCard: DrugCard,
    trackCode: string,
    isDisassembled: boolean,
    totalCells: number
  ) => {
    // 获取所有未完成的费用明细
    const uncompletedDetails: Array<{
      patient: PatientCard
      feeDetail: any
      remainingNeed: number
    }> = []

    drugCard.patients.forEach(patient => {
      if (!patient.isCompleted) {
        // 这里需要访问原始的费用明细数据
        // 暂时用模拟数据
        const currentBound = patient.boundTrackCodes.reduce((sum, code) => sum + code.cellsDispensed, 0)
        const remainingNeed = patient.needAmount - currentBound

        if (remainingNeed > 0) {
          uncompletedDetails.push({
            patient,
            feeDetail: { execSeqid: '123', lineNo: 1 }, // 模拟数据
            remainingNeed
          })
        }
      }
    })

    if (uncompletedDetails.length === 0) {
      message.warning('所有患者已完成绑定')
      return []
    }

    // 按需求量排序（需求量大的优先）
    uncompletedDetails.sort((a, b) => b.remainingNeed - a.remainingNeed)

    // 分配算法
    const allocations: Array<{
      patient: PatientCard
      execSeqid: string
      lineNo: number
      allocatedAmount: number
    }> = []

    let remainingCells = totalCells

    for (const detail of uncompletedDetails) {
      if (remainingCells <= 0) break

      const allocateAmount = Math.min(detail.remainingNeed, remainingCells)

      if (allocateAmount > 0) {
        allocations.push({
          patient: detail.patient,
          execSeqid: detail.feeDetail.execSeqid,
          lineNo: detail.feeDetail.lineNo,
          allocatedAmount: allocateAmount
        })

        remainingCells -= allocateAmount
      }
    }

    return allocations
  }

  return {
    smartAllocateTrackCode
  }
}

// 使用API调用
export function useApiCalls() {
  // 调用真实的API获取数据
  const callSectionFeeTrackCodeSummaryApi = async (params: {
    sectionId: number
    bsnDate: string
  }) => {
    try {
      return await sectionFeeTrackCodeSummaryApi(params)
    } catch (error) {
      // console.error('API调用失败，使用模拟数据:', error)
      // // 如果真实API失败，使用模拟数据
      // return await mockSectionFeeTrackCodeSummaryApi(params)
    }
  }

  // 设置拆零上报
  const setDisassembled = async (artId: number) => {
    try {
      await articleSetDisassembledApi({ artId })
      return true
    } catch (error) {
      console.error('设置拆零上报失败:', error)
      message.error('设置拆零上报失败')
      return false
    }
  }

  // 取消拆零上报
  const clearDisassembled = async (artId: number) => {
    try {
      await articleClearDisassembledApi({ artId })
      return true
    } catch (error) {
      console.error('取消拆零上报失败:', error)
      message.error('取消拆零上报失败')
      return false
    }
  }

  // 绑定追溯码API调用
  const bindTrackCodeToFeeDetail = async (
    sectionId: number,
    feeDetail: any,
    trackCode: string,
    amount: number,
    isDisassembled: boolean
  ) => {
    const params: any = {
      sectionId,
      execSeqid: feeDetail.execSeqid,
      lineNo: feeDetail.lineNo,
      visitId: feeDetail.visitId,
      oeNo: feeDetail.oeNo,
      artId: feeDetail.artId,
      bseqid: feeDetail.bseqid,
      trackCode,
      isDisassembled
    }

    // 只有当billLineNo存在时才添加到参数中
    if (feeDetail.billLineNo !== undefined && feeDetail.billLineNo !== null) {
      params.billLineno = feeDetail.billLineNo
    }

    if (isDisassembled) {
      params.totalCells = amount
    } else {
      params.totalPacks = amount
    }

    try {
      // 使用队列管理器处理请求
      const response = await apiQueue.addBindRequest(params)

      // 检查响应中的异常提醒
      if (response && typeof response === 'string' && response.trim()) {
        message.warning(`绑定提醒: ${response}`)
      }

      return { success: true, message: response }
    } catch (error) {
      console.error('绑定追溯码失败:', error)
      throw error
    }
  }

  // 解绑追溯码API调用
  const unbindTrackCodeFromFeeDetail = async (
    sectionId: number,
    feeDetail: any,
    trackCode: string,
    amount: number,
    isDisassembled: boolean
  ) => {
    const params: any = {
      sectionId,
      execSeqid: feeDetail.execSeqid,
      lineNo: feeDetail.lineNo,
      visitId: feeDetail.visitId,
      oeNo: feeDetail.oeNo,
      artId: feeDetail.artId,
      bseqid: feeDetail.bseqid,
      trackCode,
      isDisassembled
    }

    // 只有当billLineNo存在时才添加到参数中
    if (feeDetail.billLineNo !== undefined && feeDetail.billLineNo !== null) {
      params.billLineno = feeDetail.billLineNo
    }

    if (isDisassembled) {
      params.totalCells = amount
    } else {
      params.totalPacks = amount
    }

    try {
      // 使用队列管理器处理请求
      const response = await apiQueue.addUnbindRequest(params)

      // 检查响应中的异常提醒
      if (response && typeof response === 'string' && response.trim()) {
        message.warning(`解绑提醒: ${response}`)
      }

      return { success: true, message: response }
    } catch (error) {
      console.error('解绑追溯码失败:', error)
      throw error
    }
  }

  return {
    callSectionFeeTrackCodeSummaryApi,
    setDisassembled,
    clearDisassembled,
    bindTrackCodeToFeeDetail,
    unbindTrackCodeFromFeeDetail
  }
}

// 使用队列状态监控
export function useQueueMonitor() {
  const queueStatus = ref({
    bindQueueLength: 0,
    unbindQueueLength: 0,
    isProcessing: false,
    totalPending: 0
  })

  // 更新队列状态
  const updateQueueStatus = () => {
    queueStatus.value = apiQueue.getQueueStatus()
  }

  // 定时更新队列状态
  const startMonitoring = () => {
    const interval = setInterval(updateQueueStatus, 500)
    return () => clearInterval(interval)
  }

  // 清空队列
  const clearQueue = () => {
    apiQueue.clearQueue()
    updateQueueStatus()
  }

  return {
    queueStatus,
    updateQueueStatus,
    startMonitoring,
    clearQueue
  }
}
