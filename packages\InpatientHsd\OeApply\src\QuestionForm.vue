<script setup lang="ts">
import { Message } from '@idmy/core'
import { oeQuestionApi } from '@mh-inpatient-hsd/util'
import { Button, Col, Form, FormItem, Modal, Row, Textarea } from 'ant-design-vue'

const visible = ref(false)
const formModel = reactive<any>({})
const formRef = ref()
const visitId = ref<any>()
const oeNoLs = ref<any>()
const emit = defineEmits(['questioned'])
const questionLoading = ref<boolean>(false)
const open = (vId: any, noLs: any) => {
  visitId.value = vId
  oeNoLs.value = noLs
  formModel.questionNotes = undefined
  visible.value = true
}

function handleCancel() {
  visible.value = false
}

const handleQuestion = async () => {
  await formRef.value?.validate()
  questionLoading.value = true
  const params = {
    visitId: visitId.value,
    oeNoLs: oeNoLs.value,
    questionNotes: formModel.questionNotes,
  }
  oeQuestionApi(params)
    .then(() => {
      Message.success('医嘱质疑完成')
      emit('questioned')
      visible.value = false
    })
    .finally(() => {
      questionLoading.value = false
    })
}

defineExpose({
  open,
})
</script>

<template>
  <Modal v-model:open="visible" title="医嘱质疑" width="400px" @cancel="handleCancel" :mask-closable="false">
    <template #footer>
      <Button type="dashed" @click="handleCancel"> 关闭 </Button>
      <Button @click="handleQuestion" :loading="questionLoading" type="primary" danger> 质疑 </Button>
    </template>
    <div>
      <Form ref="formRef" :model="formModel" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <Row>
          <Col :span="24">
            <Form-item name="questionNotes" label="质疑说明" :rules="[{ required: true, message: '请录入质疑说明' }]">
              <Textarea v-model:value="formModel.questionNotes" allow-clear autocomplete="off" placeholder="请录入质疑说明" :auto-size="{ minRows: 2, maxRows: 5 }" max-length="60" showCount />
            </Form-item>
          </Col>
        </Row>
      </Form>
    </div>
  </Modal>
</template>
