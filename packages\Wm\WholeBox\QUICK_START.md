# WholeBox 组件快速开始指南

## 🚀 5分钟快速上手

### 第一步：安装组件

```bash
# 使用pnpm安装（推荐）
pnpm add @mh-wm/whole-box

# 使用npm安装
npm install @mh-wm/whole-box

# 使用yarn安装
yarn add @mh-wm/whole-box
```

### 第二步：导入组件

```javascript
// 方式1: 命名导入（推荐）
import { WholeBox } from '@mh-wm/whole-box'

// 方式2: 默认导入
import WholeBox from '@mh-wm/whole-box'

// 导入样式（如果需要）
import '@mh-wm/whole-box/index.css'
```

### 第三步：基础使用

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <a-button type="primary" @click="openSplitPack">
      拆零盒整
    </a-button>
    
    <!-- 拆零盒整组件 -->
    <WholeBox 
      ref="wholeBoxRef" 
      @split-pack-success="handleSuccess"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()

// 库存记录数据
const stockRecord = {
  artName: '阿莫西林胶囊',      // 药品名称
  artSpec: '0.25g',           // 规格
  packCells: 10,              // 包装规格：10粒/盒
  cellUnit: '粒',             // 拆零单位
  packUnit: '盒',             // 包装单位
  deptTotalPacks: 5,          // 仓库总库存整包数
  deptTotalCells: 3,          // 仓库总库存拆零数
  totalPacks: 2,              // 批次库存整包数
  totalCells: 8,              // 批次库存拆零数
}

// 打开拆零盒整模态框
const openSplitPack = () => {
  wholeBoxRef.value?.handleSplitPack(stockRecord)
}

// 操作成功回调
const handleSuccess = (data) => {
  console.log('拆零盒整成功:', data)
  // 这里可以刷新数据、显示提示等
}
</script>
```

## 📋 数据结构说明

### StockRecord 接口

```typescript
interface StockRecord {
  artName: string          // 药品名称（必填）
  artSpec: string          // 规格（必填）
  packCells: number        // 包装规格数量（必填）
  cellUnit: string         // 拆零单位（必填）
  packUnit: string         // 包装单位（必填）
  deptTotalPacks: number   // 仓库总库存整包数（必填）
  deptTotalCells: number   // 仓库总库存拆零数（必填）
  totalPacks: number       // 批次库存整包数（必填）
  totalCells: number       // 批次库存拆零数（必填）
  producer?: string        // 生产厂家（可选）
  batchNo?: string         // 批次号（可选）
  expDate?: string         // 有效期（可选）
}
```

## 🎯 常见使用场景

### 1. 在表格中使用

```vue
<template>
  <div>
    <a-table :columns="columns" :data-source="stockData">
      <template #action="{ record }">
        <a-button @click="handleSplitPack(record)">
          拆零盒整
        </a-button>
      </template>
    </a-table>
    
    <WholeBox ref="wholeBoxRef" @split-pack-success="refreshData" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'

const wholeBoxRef = ref()
const stockData = ref([
  // 你的库存数据
])

const handleSplitPack = (record) => {
  wholeBoxRef.value.handleSplitPack(record)
}

const refreshData = () => {
  // 刷新表格数据
}
</script>
```

### 2. TypeScript 使用

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'
import type { WholeBoxInstance, StockRecord } from '@mh-wm/whole-box'

const wholeBoxRef = ref<WholeBoxInstance>()

const handleSplitPack = (record: StockRecord) => {
  wholeBoxRef.value?.handleSplitPack(record)
}
</script>
```

### 3. 全局注册

```javascript
// main.ts
import { createApp } from 'vue'
import { WholeBox } from '@mh-wm/whole-box'
import '@mh-wm/whole-box/index.css'

const app = createApp(App)
app.component('WholeBox', WholeBox)
app.mount('#app')

// 使用时无需导入
// <template>
//   <WholeBox ref="wholeBoxRef" />
// </template>
```

## 🔧 API 参考

### 组件方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| handleSplitPack | `record: StockRecord` | 打开拆零盒整模态框 |

### 组件事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| split-pack-success | `data: any` | 拆零盒整操作成功时触发 |
| split-pack-error | `error: any` | 拆零盒整操作失败时触发 |

## ❓ 常见问题

### Q: 组件无法导入？
A: 检查依赖是否正确安装：
```bash
pnpm list @mh-wm/whole-box
```

### Q: 样式显示异常？
A: 确保导入了CSS文件：
```javascript
import '@mh-wm/whole-box/index.css'
```

### Q: TypeScript类型错误？
A: 确保正确导入类型：
```javascript
import type { WholeBoxInstance } from '@mh-wm/whole-box'
```

### Q: 组件方法调用失败？
A: 确保组件已挂载：
```javascript
const handleSplitPack = (record) => {
  if (!wholeBoxRef.value) {
    console.error('组件未挂载')
    return
  }
  wholeBoxRef.value.handleSplitPack(record)
}
```

## 📖 更多示例

访问组件仓库查看更多详细示例：

- **在线演示**: `/#/Wm/WholeBox`
- **组件集合**: `/#/Wm/All`
- **测试页面**: `/#/test/whole-box`

## 🆘 获取帮助

如果遇到问题，可以：

1. 查看完整文档：`README.md`
2. 查看构建文档：`BUILD.md`
3. 查看部署指南：`DEPLOYMENT.md`
4. 联系开发团队

---

**祝你使用愉快！** 🎉
