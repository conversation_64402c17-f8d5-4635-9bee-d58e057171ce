# 目录介绍
- Base： 公共全局组件（和具体业务无关，和公司有关。如页面布局，菜单，登录、忘记密码等）
- Hip： 之前的 hip-base 业务通用组件
- Hsd： hsd 业务通用组件
- Mi： 医保业务通用组件
- Wm： 仓库业务通用组件
-
# 注意事项
- 非公共业务组件，请勿使用。
- http 接口，TS类型和其他乱七八糟的，全部放到当前项目的*-util模块中

# 参考 packages/Hip/FeeCat

# 自动配置 tsconfig.json paths
运行 `scripts/generate-tsconfig-paths.js` tsconfig.json paths 会自动添加对应的配置

# 发布
```tip
重新发布打包 dist 目录不会自动删除
```
## 发布单个组件
```shell
pnpm publish:component Hip/FeeCat        # @mh-hip/fee-cat@1.0.0
pnpm publish:dev-component Hip/FeeCat    # @mh-hip/fee-cat@1.0.0-dev
pnpm publish:test-component Hip/FeeCat   # @mh-hip/fee-cat@1.0.0-test
```
## 发布部分组件
```shell
pnpm publish:component Hip #发布Hip目录下全部组件
```

## 发布全部组件
```shell
pnpm publish:all-components
```

