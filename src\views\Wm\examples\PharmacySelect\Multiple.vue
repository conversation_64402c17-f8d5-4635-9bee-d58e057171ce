<script setup lang="ts">
import { PharmacySelect } from '@mh-wm/pharmacy'
import { Card, Typography, Divider } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { multipleUsage, importCode, packageJsonCode } from '../code/PharmacySelectCode'

const { Title } = Typography

// 多选药房ID
const pharmacyIds = ref<string[]>([])
</script>

<template>
  <Card title="多选模式" class="mb-16px">
    <div mb-16px>
      <Title :level="4">多选模式</Title>
      <PharmacySelect v-model="pharmacyIds" multiple w-200px />
      <div mt-8px>选中的药房ID: {{ pharmacyIds }}</div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="multipleUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 样式可以根据需要添加 */
</style>
