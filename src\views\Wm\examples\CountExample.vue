<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import CreateExample from './Count/Create.vue'
import EditExample from './Count/Edit.vue'

const { Title, Paragraph } = Typography
</script>

<template>
  <div>
    <Card class="mb-16px">
      <Title :level="3">盘点组件</Title>
      <Paragraph>仓库盘点组件，用于创建盘点单和录入盘点结果。支持多种方式添加盘点品种，支持分页管理盘点品种，使用tab页切换不同页码。</Paragraph>
    </Card>

    <Tabs>
      <Tabs.TabPane key="create" tab="创建盘点单">
        <CreateExample />
      </Tabs.TabPane>
      <Tabs.TabPane key="edit" tab="录入盘点结果">
        <EditExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>
