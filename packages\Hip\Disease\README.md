# @mh-hip/disease

病种选择组件

## 组件列表

- DiseaseSelector: 病种选择组件
- DiseaseSelectorModal: 病种选择弹窗组件
- DiseaseDict: 病种字典组件

## 使用示例

### 1. 病种字典组件

```vue
<template>
  <DiseaseDict v-model="diseaseId" />
</template>

<script setup>
import { DiseaseDict } from '@mh-hip/disease'
import { ref } from 'vue'

const diseaseId = ref()
</script>
```

### 2. 病种选择页面嵌入组件

```vue
<template>
  <DiseaseSelector 
    v-model="diseaseId" 
    :patientId="patientId" 
    @select="handleDiseaseSelect" 
  />
</template>

<script setup>
import { DiseaseSelector } from '@mh-hip/disease'
import { ref } from 'vue'

const diseaseId = ref()
const patientId = ref(1) // 患者ID

const handleDiseaseSelect = (disease) => {
  console.log('选择的病种:', disease)
}
</script>
```

### 3. 病种选择弹窗组件

```vue
<template>
  <Button @click="showModal = true">选择病种</Button>
  
  <DiseaseSelectorModal
    v-model:visible="showModal"
    v-model="diseaseId"
    :patientId="patientId"
    @select="handleDiseaseSelect"
  />
</template>

<script setup>
import { DiseaseSelectorModal } from '@mh-hip/disease'
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

const showModal = ref(false)
const diseaseId = ref()
const patientId = ref(1) // 患者ID

const handleDiseaseSelect = (disease) => {
  console.log('选择的病种:', disease)
}
</script>
```

### 4. 使用useDiseaseSelector函数（推荐方式）

根据传入的参数自动选择合适的病种选择方式：

```vue
<template>
  <!-- 使用弹窗方式 -->
  <template v-if="diseaseConfig.component === DiseaseSelectorModal">
    <Button @click="showModal = true">选择病种</Button>
    <component 
      :is="diseaseConfig.component" 
      v-model:visible="showModal"
      v-model="diseaseId"
      v-bind="diseaseConfig.props"
      @select="handleDiseaseSelect"
    />
  </template>
  
  <!-- 使用页面嵌入或字典方式 -->
  <template v-else>
    <component 
      :is="diseaseConfig.component" 
      v-model="diseaseId"
      v-bind="diseaseConfig.props"
      @select="handleDiseaseSelect"
    />
  </template>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Button } from 'ant-design-vue'
import { useDiseaseSelector, DiseaseSelectorModal } from '@mh-hip/disease'

const diseaseId = ref()
const showModal = ref(false)
const patientId = ref(1) // 患者ID

// 根据需要选择模式：'modal', 'selector', 'dict'
const diseaseConfig = useDiseaseSelector({
  mode: 'modal', // 使用弹窗模式
  patientId: patientId.value,
  title: '选择患者病种'
})

const handleDiseaseSelect = (disease) => {
  console.log('选择的病种:', disease)
}
</script>
```

## 动态切换模式示例

```vue
<template>
  <div>
    <Radio.Group v-model:value="mode" @change="changeMode">
      <Radio.Button value="dict">字典模式</Radio.Button>
      <Radio.Button value="selector">选择器模式</Radio.Button>
      <Radio.Button value="modal">弹窗模式</Radio.Button>
    </Radio.Group>
    
    <div style="margin-top: 16px">
      <!-- 使用弹窗方式 -->
      <template v-if="diseaseConfig.component === DiseaseSelectorModal">
        <Button @click="showModal = true">打开病种选择弹窗</Button>
        <component 
          :is="diseaseConfig.component" 
          v-model:visible="showModal"
          v-model="diseaseId"
          v-bind="diseaseConfig.props"
          @select="handleDiseaseSelect"
        />
      </template>
      
      <!-- 使用页面嵌入或字典方式 -->
      <template v-else>
        <component 
          :is="diseaseConfig.component" 
          v-model="diseaseId"
          v-bind="diseaseConfig.props"
          @select="handleDiseaseSelect"
        />
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Button, Radio } from 'ant-design-vue'
import { useDiseaseSelector, DiseaseSelectorModal } from '@mh-hip/disease'

const diseaseId = ref()
const showModal = ref(false)
const patientId = ref(1)
const mode = ref('dict')

// 计算属性，根据模式返回相应的配置
const diseaseConfig = computed(() => {
  return useDiseaseSelector({
    mode: mode.value,
    patientId: patientId.value,
    title: '选择病种'
  })
})

const changeMode = () => {
  console.log(`切换到${mode.value}模式`)
}

const handleDiseaseSelect = (disease) => {
  console.log('选择的病种:', disease)
}
</script>
``` 