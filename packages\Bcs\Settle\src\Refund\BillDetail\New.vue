<script lang="ts" setup>
import { add, Data, Format } from '@idmy/core'
import { Collapse, CollapsePanel } from 'ant-design-vue'
import Detail from './Detail.vue'

const props = defineProps({
  blues: { type: Array as PropType<Data[]>, required: true },
  reds: { type: Array as PropType<Data[]>, required: true },
})

const merges = computed(() => props.blues.concat(props.reds))

const totalAmount = computed(() => merges.value.reduce((a: number, b: Data) => add(a, b.amount), 0))

const activeKey = defineModel('activeKey')
</script>
<template>
  <Collapse v-model:activeKey="activeKey" accordion bordered>
    <CollapsePanel key="1" header="剩余">
      <template #extra>
        <Format :value="totalAmount" prefix="总金额：" type="Currency" value-class="b primary" />
      </template>
      <Detail :data="merges" :height="160" :merge="true" />
    </CollapsePanel>
  </Collapse>
</template>
