<script setup lang="ts">
import { TrackCode } from '@mh-wm/track-code'
import { Card, Typography, Divider, Input, Space } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { basicUsage, importCode, packageJsonCode } from '../code/TrackCodeCode'

const { Title, Paragraph } = Typography

// 溯源码ID
const wbSeqid = ref<string>('12345')

// 成功回调
const handleSuccess = (data: any) => {
  console.log('溯源码录入成功', data)
}
</script>

<template>
  <Card title="基础用法 - 按钮触发" class="mb-16px">
    <div mb-16px>
      <Title :level="4">溯源码录入组件</Title>
      <Paragraph>溯源码录入组件是一个弹窗组件，用于录入和管理溯源码信息。</Paragraph>

      <Space direction="vertical" style="width: 100%">
        <div>
          <span style="margin-right: 8px">溯源码ID:</span>
          <Input v-model:value="wbSeqid" style="width: 200px" placeholder="请输入溯源码ID" />
        </div>

        <div>
          <TrackCode :wbSeqid="wbSeqid" @success="handleSuccess" />
          <span style="margin-left: 8px; color: #666; font-size: 13px"> 点击按钮或按 F5 键将打开溯源码录入弹窗 </span>
        </div>
      </Space>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
/* 可以添加自定义样式 */
</style>
