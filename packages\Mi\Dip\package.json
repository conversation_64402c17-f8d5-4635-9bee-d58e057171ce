{"name": "@mh-mi/dip", "version": "1.0.11", "type": "module", "main": "umd/index.js", "module": "es/index.js", "types": "index.d.ts", "style": "index.css", "files": ["es", "umd", "src/*.d.ts", "src/**/*.d.ts", "index.d.ts", "index.css"], "scripts": {"publish:build": "cd ../../ && pnpm run publish:component Mi/Dip --no-publish", "publish:component": "cd ../../ && pnpm run publish:component Mi/Dip"}, "dependencies": {"@mh-base/core": "workspace:*", "@mh-mi/util": "workspace:*"}, "peerDependencies": {"@ant-design/icons-vue": "~7.0.1", "@idmy/antd": "~0.0.120", "@idmy/core": "~1.0.143", "ant-design-vue": "~4.2.6", "lodash-es": "~4.17.21", "vue": "~3.5.13"}, "publishConfig": {"access": "public"}}