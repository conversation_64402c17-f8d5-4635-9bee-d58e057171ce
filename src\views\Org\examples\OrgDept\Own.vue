<script setup lang="ts">
import { OrgSelect, OrgDoctor, OrgDept } from '@mh-hip/org'
import { Card, Typography, Divider, Switch, Row, Col } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { ownUsage, importCode, packageJsonCode } from '../code/OrgDeptCode'

const { Title } = Typography

// 选中的组织机构ID
const orgId = ref<number>()

// 选中的医生ID
const clinicianId = ref<number>()

// 当前用户的部门选择
const ownDeptId = ref<number>()

// 是否只显示当前用户的部门
const isOwn = ref(false)
</script>

<template>
  <Card title="显示当前用户的部门" class="mb-16px">
    <Title :level="4">选择条件</Title>
    <Row :gutter="[16, 16]" mb-16px>
      <Col :span="12">
        <div>
          <div mb-8px>组织机构：</div>
          <OrgSelect v-model="orgId" style="width: 100%" />
          <div mt-8px>选中的组织机构ID: {{ orgId }}</div>
        </div>
      </Col>
      <Col :span="12">
        <div>
          <div mb-8px>医生：</div>
          <OrgDoctor v-model="clinicianId" :orgId="orgId" style="width: 100%" />
          <div mt-8px>选中的医生ID: {{ clinicianId }}</div>
        </div>
      </Col>
    </Row>

    <Divider />

    <div flex items-center mb-16px>
      <span mr-8px>是否只显示当前用户的部门：</span>
      <Switch v-model:checked="isOwn" />
      <span ml-8px>{{ isOwn ? '是' : '否' }}</span>
      <span ml-16px class="tip-text">
        <i class="tip-icon">i</i>
        当开启此选项时，会先获取当前用户的医师信息，然后查询该医师对应的部门
      </span>
    </div>

    <div mb-16px>
      <Title :level="4">显示当前用户的部门</Title>
      <OrgDept v-model="ownDeptId" :own="isOwn" :orgId="orgId" :clinicianId="clinicianId" w-200px />
      <div mt-8px>选中的部门ID: {{ ownDeptId }}</div>
      <div mt-8px class="tip-text">
        <i class="tip-icon">i</i>
        当own为true时，会先获取当前用户的医师信息，然后查询该医师对应的部门。如果同时传入了clinicianId，该值会被忽略。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="ownUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #1890ff;
  color: white;
  font-size: 12px;
  font-style: normal;
  margin-right: 6px;
}
</style>
