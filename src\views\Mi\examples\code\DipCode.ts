import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法 - 隐藏模式
export const basicUsage = wrapCodeExample(`<template>
  <dip
    ref="dipRef"
    :trig-scen="config.trigScen"
    @success="onSuccess"
    @error="onError"
  />

  <!-- 触发按钮 -->
  <Button type="primary" @click="handlePreAnalyze" style="margin-right: 8px">执行事前分析</Button>
  <Button type="primary" @click="handleInAnalyze">执行事中分析</Button>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Button, message } from 'ant-design-vue'
import { Dip } from '@mh-mi/dip'

const dipRef = ref()

// 配置数据
const config = reactive({
  visitId: 125338,
  trigScen: 3, // 住院医嘱签名
  oeList: [2], // 医嘱ID列表，可以为空数组
  cashId: 0 // 收费ID，可以为0或undefined，可选参数
})

// 调用事前分析
const handlePreAnalyze = async () => {
  try {
    const result = await dipRef.value.callPreDip(config.visitId, config.oeList, config.cashId)
    console.log('事前分析结果:', result)
  } catch (error) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事中分析
const handleInAnalyze = async () => {
  try {
    const result = await dipRef.value.callInDip(config.visitId, config.oeList, config.cashId)
    console.log('事中分析结果:', result)
  } catch (error) {
    message.error('分析失败: ' + error.message)
  }
}

// 成功回调
const onSuccess = (result) => {
  if (result.action === 'continue') {
    message.success('用户选择继续执行，原因: ' + result.reason)
  } else {
    message.info('分析成功: ' + result.message)
  }
}

// 错误回调
const onError = (result) => {
  if (result.action === 'modify') {
    message.warning('用户选择返回修改')
  } else {
    message.error('分析失败: ' + result.message)
  }
}
</script>`)

// 开关模式
export const switchModeUsage = wrapCodeExample(`<template>
  <dip
    ref="dipRef"
    :trig-scen="config.trigScen"

    label="启用事前事中分析"
    storage-key="dip_switch"
    @change="onChange"
    @success="onSuccess"
    @error="onError"
  />

  <!-- 触发按钮 -->
  <Button type="primary" @click="handlePreAnalyze" style="margin-right: 8px" ml-16px>执行事前分析</Button>
  <Button type="primary" @click="handleInAnalyze">执行事中分析</Button>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Button, message } from 'ant-design-vue'
import { Dip } from '@mh-mi/dip'

const dipRef = ref()

// 配置数据
const config = reactive({
  visitId: 125338,
  trigScen: 1, // 门诊处方签名
  oeList: [2], // 医嘱ID列表，可以为空数组
  cashId: 0 // 收费ID，可以为0或undefined，可选参数
})

// 调用事前分析
const handlePreAnalyze = async () => {
  try {
    const result = await dipRef.value.callPreDip(config.visitId, config.oeList, config.cashId)
    console.log('事前分析结果:', result)
  } catch (error) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事中分析
const handleInAnalyze = async () => {
  try {
    const result = await dipRef.value.callInDip(config.visitId, config.oeList, config.cashId)
    console.log('事中分析结果:', result)
  } catch (error) {
    message.error('分析失败: ' + error.message)
  }
}

// 状态变化回调
const onChange = (enabled) => {
  message.info('事前事中分析状态: ' + (enabled ? '启用' : '禁用'))
}

// 成功回调
const onSuccess = (result) => {
  if (result.action === 'continue') {
    message.success('用户选择继续执行，原因: ' + result.reason)
  } else {
    message.info('分析成功: ' + result.message)
  }
}

// 错误回调
const onError = (result) => {
  if (result.action === 'modify') {
    message.warning('用户选择返回修改')
  } else {
    message.error('分析失败: ' + result.message)
  }
}
</script>`)

// 复选框模式
export const checkboxModeUsage = wrapCodeExample(`<template>
  <dip
    ref="dipRef"
    :trig-scen="config.trigScen"

    label="启用事前事中分析"
    storage-key="dip_checkbox"
    @change="onChange"
    @success="onSuccess"
    @error="onError"
  />

  <!-- 触发按钮 -->
  <Button type="primary" @click="handlePreAnalyze" style="margin-right: 8px" ml-16px>执行事前分析</Button>
  <Button type="primary" @click="handleInAnalyze">执行事中分析</Button>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Button, message } from 'ant-design-vue'
import { Dip } from '@mh-mi/dip'

const dipRef = ref()

// 配置数据
const config = reactive({
  visitId: 125338,
  trigScen: 2, // 门诊预结算
  oeList: [2], // 医嘱ID列表，可以为空数组
  cashId: 0 // 收费ID，可以为0或undefined，可选参数
})

// 调用事前分析
const handlePreAnalyze = async () => {
  try {
    const result = await dipRef.value.callPreDip(config.visitId, config.oeList, config.cashId)
    console.log('事前分析结果:', result)
  } catch (error) {
    message.error('分析失败: ' + error.message)
  }
}

// 调用事中分析
const handleInAnalyze = async () => {
  try {
    const result = await dipRef.value.callInDip(config.visitId, config.oeList, config.cashId)
    console.log('事中分析结果:', result)
  } catch (error) {
    message.error('分析失败: ' + error.message)
  }
}

// 状态变化回调
const onChange = (enabled) => {
  message.info('事前事中分析状态: ' + (enabled ? '启用' : '禁用'))
}

// 成功回调
const onSuccess = (result) => {
  if (result.action === 'continue') {
    message.success('用户选择继续执行，原因: ' + result.reason)
  } else {
    message.info('分析成功: ' + result.message)
  }
}

// 错误回调
const onError = (result) => {
  if (result.action === 'modify') {
    message.warning('用户选择返回修改')
  } else {
    message.error('分析失败: ' + result.message)
  }
}
</script>`)

// 引入组件
export const importCode = `import { Dip } from '@mh-mi/dip'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-mi/dip": "^1.0.11",
    "@mh-mi/util": "^1.0.23"
  }
}`
