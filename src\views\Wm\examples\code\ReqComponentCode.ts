import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 品种申请表单 -->
  <ReqComponent
    :deptCode="deptCode"
    @addArt="handleAddArt"
  />

  <!-- 显示已添加的品种列表 -->
  <div v-if="addedItems.length > 0" class="added-items-container">
    <h3>已添加品种列表</h3>
    <a-table :dataSource="addedItems" :columns="columns" rowKey="id" />
  </div>
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref } from 'vue'
import { message } from 'ant-design-vue'

// 仓库编码
const deptCode = ref('000013')

// 已添加的品种列表
const addedItems = ref([])

// 表格列定义
const columns = [
  { title: '品种名称', dataIndex: 'artName', key: 'artName' },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec' },
  { title: '生产批号', dataIndex: 'batchNo', key: 'batchNo' },
  { title: '生产日期', dataIndex: 'dateManufactured', key: 'dateManufactured' },
  { title: '有效期至', dataIndex: 'expiry', key: 'expiry' },
  { title: '整包数量', dataIndex: 'totalPacks', key: 'totalPacks' },
  { title: '包装单位', dataIndex: 'packUnit', key: 'packUnit' }
]

// 添加品种回调
const handleAddArt = (formData) => {
  // 添加唯一ID
  const newItem = {
    id: Date.now(),
    ...formData,
    artName: formData.artData.artName,
    artSpec: formData.artData.artSpec
  }

  // 添加到列表
  addedItems.value.push(newItem)

  // 提示添加成功
  message.success(\`成功添加品种: \${newItem.artName}\`)
}
</script>`)

// 在表单中使用
export const formUsage = wrapCodeExample(`<template>
  <a-form :model="formState" layout="vertical">
    <a-form-item label="申请单号" name="reqNo">
      <a-input v-model:value="formState.reqNo" placeholder="系统自动生成" disabled />
    </a-form-item>

    <a-form-item label="申请科室" name="deptCode">
      <a-select v-model:value="formState.deptCode" @change="handleDeptChange">
        <a-select-option v-for="dept in deptList" :key="dept.deptCode" :value="dept.deptCode">
          {{ dept.deptName }}
        </a-select-option>
      </a-select>
    </a-form-item>

    <a-form-item label="品种信息" name="items" :rules="[{ validator: validateItems }]">
      <ReqComponent
        ref="reqComponentRef"
        :deptCode="formState.deptCode"
        @addArt="handleAddArt"
      />

      <!-- 已添加品种列表 -->
      <div v-if="formState.items.length > 0" class="items-table">
        <a-table
          :dataSource="formState.items"
          :columns="columns"
          rowKey="id"
          :pagination="false"
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <a-button type="link" danger @click="removeItem(record.id)">删除</a-button>
            </template>
          </template>
        </a-table>
      </div>
    </a-form-item>

    <a-form-item>
      <a-button type="primary" @click="handleSubmit">提交申请</a-button>
      <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

// 组件引用
const reqComponentRef = ref()

// 表单数据
const formState = reactive({
  reqNo: 'REQ' + Date.now(),
  deptCode: '000013',
  deptName: '西药库',
  items: [],
  remark: ''
})

// 验证品种列表
const validateItems = (rule, value) => {
  if (formState.items.length === 0) {
    return Promise.reject('请至少添加一个品种')
  }
  return Promise.resolve()
}

// 添加品种回调
const handleAddArt = (formData) => {
  // 添加到列表
  formState.items.push({
    id: Date.now(),
    ...formData,
    artName: formData.artData.artName,
    artSpec: formData.artData.artSpec
  })

  message.success(\`成功添加品种: \${formData.artData.artName}\`)
}
</script>`)

// 组件方法
export const methodsUsage = wrapCodeExample(`<template>
  <ReqComponent
    ref="reqComponentRef"
    :deptCode="deptCode"
    @addArt="handleAddArt"
  />

  <div class="actions-container">
    <a-button type="primary" @click="getSelectedData">获取选中品种数据</a-button>
    <a-button @click="clearFormData">清空表单数据</a-button>
    <a-button @click="setFormData">设置表单数据</a-button>
  </div>
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref } from 'vue'
import { message } from 'ant-design-vue'

// 仓库编码
const deptCode = ref('000013')

// 组件引用
const reqComponentRef = ref()

// 模拟的品种数据
const mockData = {
  artData: {
    artId: 12345,
    artName: '阿莫西林胶囊',
    artSpec: '0.25g*24粒/盒',
    producer: '石药集团',
    packUnit: '盒',
    cellUnit: '粒',
    splittable: 1,
    packCells: 24
  },
  originPlace: '河北',
  batchNo: 'AMX20240101',
  dateManufactured: '20240101',
  expiry: '20261201',
  packPrice: 15.50,
  totalPacks: 10,
  totalCells: 5
}

// 获取选中的品种数据
const getSelectedData = () => {
  if (reqComponentRef.value) {
    const selectedData = reqComponentRef.value.getSelectedArtData()
    console.log('当前选中的品种数据:', selectedData)
    if (selectedData) {
      message.info(\`当前选中品种: \${selectedData.artName}\`)
    } else {
      message.warning('当前未选中任何品种')
    }
  }
}

// 清空表单数据
const clearFormData = () => {
  if (reqComponentRef.value) {
    reqComponentRef.value.clearFormData()
    message.success('表单数据已清空')
  }
}

// 设置表单数据（回填数据）
const setFormData = () => {
  if (reqComponentRef.value) {
    reqComponentRef.value.setFormData(mockData)
    message.success('表单数据已设置')
  }
}

// 添加品种回调
const handleAddArt = (formData) => {
  console.log('添加品种成功，表单数据：', formData)
  message.success(\`成功添加品种: \${formData.artData.artName}\`)
}
</script>`)

// 表单验证
export const validationUsage = wrapCodeExample(`<template>
  <ReqComponent
    :deptCode="deptCode"
    @addArt="handleAddArt"
  />
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref } from 'vue'
import { message } from 'ant-design-vue'

// 仓库编码
const deptCode = ref('000013')

// 添加品种回调
const handleAddArt = (formData) => {
  // 提示添加成功
  message.success(\`成功添加品种: \${formData.artData.artName}\`)
  console.log('添加品种成功，表单数据：', formData)
}
</script>

<!--
组件内置了以下验证规则：
1. 生产日期和有效期至必须填写，且格式为YYYYMMDD
2. 整包数量和拆零数量至少填写一个
3. 必须先选择品种才能提交表单
4. 如果品种不可拆零（splittable !== 1），则不会显示拆零数量字段
-->`)

// 引入组件
export const importCode = `import { ReqComponent } from '@mh-wm/req-component'`

// package.json依赖配置
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/req-component": "^1.0.0",
    "@mh-inpatient-hsd/selector": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}`

// 打包发布指令
export const publishCommands = `# 在项目根目录下执行以下命令打包并发布组件

# 正式版本
pnpm publish:component Wm/ReqComponent

# 测试版本
pnpm publish:test-component Wm/ReqComponent

# 开发版本
pnpm publish:dev-component Wm/ReqComponent

# 安装组件
pnpm add @mh-wm/req-component`

// 打包流程说明
export const buildProcess = `打包命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库`

// 编辑功能示例
export const editFunctionUsage = wrapCodeExample(`<template>
  <div>
    <!-- 制单录入组件 -->
    <ReqComponent
      ref="reqComponentRef"
      :deptCode="deptCode"
      @addArt="handleAddArt"
    />

    <!-- 已添加品种列表 -->
    <div class="added-items-section">
      <h4>已添加品种列表</h4>
      <a-table
        :dataSource="addedItems"
        :columns="columns"
        rowKey="id"
        :scroll="{ x: 1400 }"
      />
    </div>
  </div>
</template>

<script setup>
import { ReqComponent } from '@mh-wm/req-component'
import { ref, h } from 'vue'
import { message, Button, Space, Input, InputNumber } from 'ant-design-vue'

// 仓库编码
const deptCode = ref('000013')

// ReqComponent组件引用
const reqComponentRef = ref()

// 已添加的品种列表
const addedItems = ref([])

// 当前正在表单中编辑的记录ID
const formEditingId = ref('')

// 编辑状态管理
const editingKey = ref('')

// 判断是否在编辑状态
const isEditing = (record) => record.id === editingKey.value

// 表格列定义
const columns = [
  { title: '序号', dataIndex: 'id', key: 'id', width: 80 },
  { title: '品种名称', dataIndex: 'artName', key: 'artName', width: 200 },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec', width: 150 },
  {
    title: '生产批号',
    dataIndex: 'batchNo',
    key: 'batchNo',
    width: 120,
    customRender: ({ text, record }) => {
      if (isEditing(record)) {
        return h(Input, {
          value: text,
          onChange: (e) => {
            record.batchNo = e.target.value
          }
        })
      }
      return text
    }
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ text, record }) => {
      if (isEditing(record)) {
        return h(InputNumber, {
          value: text,
          min: 0,
          precision: 0,
          style: { width: '80px' },
          onChange: (value) => {
            record.totalPacks = value
          }
        })
      }
      return text
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    customRender: ({ record }) => {
      const editable = isEditing(record)
      if (editable) {
        return h(Space, { size: 'small' }, [
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => saveItem(record)
          }, { default: () => '保存' }),
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => cancelEdit()
          }, { default: () => '取消' })
        ])
      } else {
        return h(Space, { size: 'small' }, [
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => editItem(record)
          }, { default: () => '编辑' }),
          h(Button, {
            type: 'link',
            size: 'small',
            onClick: () => loadToForm(record)
          }, { default: () => '回填表单' }),
          h(Button, {
            type: 'link',
            size: 'small',
            danger: true,
            onClick: () => removeItem(record.id)
          }, { default: () => '删除' })
        ])
      }
    }
  }
]

// 添加品种回调
const handleAddArt = (formData) => {
  // 检查是否是更新操作
  if (formEditingId.value) {
    // 更新现有记录
    const index = addedItems.value.findIndex(item => item.id === formEditingId.value)
    if (index !== -1) {
      const updatedItem = {
        ...addedItems.value[index],
        ...formData,
        artName: formData.artData.artName,
        artSpec: formData.artData.artSpec
      }
      addedItems.value[index] = updatedItem
      message.success(\`成功更新品种: \${updatedItem.artName}\`)
      formEditingId.value = ''
      return
    }
  }

  // 添加新记录
  const newItem = {
    id: Date.now(),
    ...formData,
    artName: formData.artData.artName,
    artSpec: formData.artData.artSpec
  }

  addedItems.value.push(newItem)
  message.success(\`成功添加品种: \${newItem.artName}\`)
}

// 回填数据到表单
const loadToForm = (record) => {
  if (reqComponentRef.value && typeof reqComponentRef.value.setFormData === 'function') {
    reqComponentRef.value.setFormData(record)
    formEditingId.value = record.id
    message.info('已将数据加载到表单中，修改后点击"添加"按钮将更新此记录')
  }
}

// 编辑品种（行内编辑）
const editItem = (record) => {
  record.originalData = { ...record }
  editingKey.value = record.id
}

// 保存编辑
const saveItem = (record) => {
  // 数据验证
  if (!record.batchNo) {
    message.error('生产批号不能为空')
    return
  }

  if (!record.totalPacks || record.totalPacks <= 0) {
    message.error('整包数量必须大于0')
    return
  }

  delete record.originalData
  editingKey.value = ''
  message.success('保存成功')
}

// 取消编辑
const cancelEdit = () => {
  const editingRecord = addedItems.value.find(item => item.id === editingKey.value)
  if (editingRecord && editingRecord.originalData) {
    Object.assign(editingRecord, editingRecord.originalData)
    delete editingRecord.originalData
  }
  editingKey.value = ''
  message.info('已取消编辑')
}

// 删除品种
const removeItem = (id) => {
  const index = addedItems.value.findIndex(item => item.id === id)
  if (index !== -1) {
    const removedItem = addedItems.value.splice(index, 1)[0]
    message.success(\`已删除品种: \${removedItem.artName}\`)
  }
}
</script>

<style scoped>
.added-items-section {
  margin-top: 24px;
}
</style>`)
