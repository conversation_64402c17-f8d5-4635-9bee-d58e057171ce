<script lang="ts" setup>
import { ModalButton } from '@idmy/antd'
import { Message, Modal, useLoading } from '@idmy/core'
import { Alert, InputNumber } from 'ant-design-vue'
import { random } from 'lodash-es'

const val = ref(random(100000, 999999))

const input = ref()
const [onOk, loading] = useLoading(async () => {
  if (input.value === val.value) {
    await Modal.b.ok()
  } else {
    Message.error('输入错误，请重新输入')
  }
})
</script>

<template>
  <Alert mb-8px message="警告：该操作比较危险，请了解清楚后再操作！" type="warning" />
  <Alert mb-8px message="为防止意外，确认继续操作请输入以下验证码！" type="warning" />
  <Alert :message="`验证码：${val}`" mb-8px type="error" />
  <InputNumber v-model:value="input" mb-8px placeholder="请输入验证码" w-100% />
  <ModalButton :loading="loading" @ok="onOk" />
</template>
<style lang="less" scoped></style>
