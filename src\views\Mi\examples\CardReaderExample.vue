<script setup lang="ts">
import { Card, Typography, Tabs } from 'ant-design-vue'
import { ref } from 'vue'

// 导入各个示例组件
import BasicExample from './CardReader/Basic.vue'
import FullParamsExample from './CardReader/FullParams.vue'

const { Title, Paragraph } = Typography

// 当前激活的tab
const activeKey = ref('basic')
</script>

<template>
  <div>
    <Paragraph>医保读卡组件，支持身份证、社保卡等读卡操作，并可以获取医保相关信息。</Paragraph>

    <Tabs v-model:activeKey="activeKey">
      <Tabs.TabPane key="basic" tab="基础用法">
        <BasicExample />
      </Tabs.TabPane>

      <Tabs.TabPane key="fullParams" tab="完整参数">
        <FullParamsExample />
      </Tabs.TabPane>
    </Tabs>
  </div>
</template>

<style scoped>
/* 全局样式可以放在这里 */
</style>
