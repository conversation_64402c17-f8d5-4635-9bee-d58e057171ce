<script setup lang="ts">
import { ScmCustSelect } from '@mh-wm/scm-cust'
import { Card, Typography, Divider, Switch, Row, Col, Alert } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { orgPartnerNoPaginationUsage, importCode, packageJsonCode } from '../code/ScmCustSelectCode'

const { Title, Text } = Typography

// 机构供应商选择值
const orgCustId = ref<number>()
const orgCustIds = ref<number[]>([])

// 是否使用机构供应商API
const useOrgPartner = ref<boolean>(true)

// 是否启用分页
const enablePagination = ref<boolean>(false)

// 搜索类型
const searchType = ref<string>('all')

// 显示字段
const showField = ref<string>('custName')
</script>

<template>
  <Card title="机构供应商 - 非分页模式" class="mb-16px">
    <div mb-16px>
      <Alert
        message="非分页模式说明"
        description="当 enablePagination=false 时，组件会一次性加载所有机构供应商数据，然后在前端进行过滤。这种模式适合数据量不大的场景，可以提供更好的用户体验。"
        type="info"
        show-icon
        class="mb-16px"
      />

      <Title :level="4">配置选项</Title>
      <Row :gutter="16" class="mb-16px">
        <Col :span="6">
          <div mb-8px>
            <Text strong>使用机构供应商API：</Text>
            <br>
            <Switch v-model:checked="useOrgPartner" />
            <span ml-8px>{{ useOrgPartner ? '是' : '否' }}</span>
          </div>
        </Col>
        <Col :span="6">
          <div mb-8px>
            <Text strong>启用分页：</Text>
            <br>
            <Switch v-model:checked="enablePagination" />
            <span ml-8px>{{ enablePagination ? '是' : '否' }}</span>
          </div>
        </Col>
        <Col :span="6">
          <div mb-8px>
            <Text strong>搜索类型：</Text>
            <br>
            <select v-model="searchType" style="width: 120px; padding: 4px;">
              <option value="all">全部字段</option>
              <option value="name">仅名称</option>
              <option value="code">仅编码</option>
              <option value="wubi">仅五笔</option>
              <option value="pinyin">仅拼音</option>
            </select>
          </div>
        </Col>
        <Col :span="6">
          <div mb-8px>
            <Text strong>显示字段：</Text>
            <br>
            <select v-model="showField" style="width: 120px; padding: 4px;">
              <option value="custName">名称优先</option>
              <option value="custCode">编码优先</option>
            </select>
          </div>
        </Col>
      </Row>

      <Title :level="4">单选示例</Title>
      <div mb-16px>
        <ScmCustSelect
          v-model="orgCustId"
          :orgPartner="useOrgPartner"
          :enablePagination="enablePagination"
          :searchType="searchType"
          :showField="showField"
          style="width: 100%"
          placeholder="请输入机构供应商名称/编码/五笔/拼音"
        />
        <div mt-8px>
          <Text type="secondary">选中的供应商ID: {{ orgCustId || '未选择' }}</Text>
        </div>
      </div>

      <Title :level="4">多选示例</Title>
      <div mb-16px>
        <ScmCustSelect
          v-model="orgCustIds"
          :orgPartner="useOrgPartner"
          :enablePagination="enablePagination"
          :searchType="searchType"
          :showField="showField"
          multiple
          style="width: 100%"
          placeholder="请选择多个机构供应商"
        />
        <div mt-8px>
          <Text type="secondary">选中的供应商IDs: {{ orgCustIds.length > 0 ? orgCustIds.join(', ') : '未选择' }}</Text>
        </div>
      </div>

      <div mt-16px>
        <Title :level="5">功能特点：</Title>
        <ul class="feature-list">
          <li><strong>一次性加载：</strong>组件会调用 <code>orgPartnerFindAllApi</code> 一次性加载所有机构供应商数据</li>
          <li><strong>前端过滤：</strong>根据用户输入在前端进行实时过滤，响应速度更快</li>
          <li><strong>四字段搜索：</strong>支持根据 <code>custName</code>、<code>custCode</code>、<code>qsCode1</code>（五笔）、<code>qsCode2</code>（拼音）进行搜索</li>
          <li><strong>大小写不敏感：</strong>搜索时会自动转换为大写进行比较，支持拼音首字母和拼音大写匹配</li>
          <li><strong>搜索类型：</strong>可以指定只搜索特定字段，或搜索所有字段</li>
          <li><strong>显示格式：</strong>可以选择名称优先或编码优先的显示格式</li>
        </ul>
      </div>

      <div mt-16px>
        <Title :level="5">API数据结构：</Title>
        <div class="api-info">
          <Text code>orgPartnerFindAllApi</Text> 返回的数据包含以下关键属性：
          <ul class="api-fields">
            <li><code>custId</code>: 供应商ID（用作选项值）</li>
            <li><code>custName</code>: 供应商名称</li>
            <li><code>custCode</code>: 供应商编码</li>
            <li><code>qsCode1</code>: 拼音首字母和五笔码（大写）</li>
            <li><code>qsCode2</code>: 拼音码（大写）</li>
          </ul>
        </div>
      </div>

      <div mt-16px>
        <Title :level="5">使用示例：</Title>
        <div class="example-block">
          <pre><code>// 基础用法 - 机构供应商非分页模式
&lt;ScmCustSelect
  v-model="orgCustId"
  :orgPartner="true"
  :enablePagination="false"
  style="width: 300px"
  placeholder="请输入机构供应商"
/&gt;

// 指定搜索类型 - 只搜索名称
&lt;ScmCustSelect
  v-model="orgCustId"
  :orgPartner="true"
  :enablePagination="false"
  searchType="name"
  style="width: 300px"
/&gt;

// 指定搜索类型 - 只搜索拼音
&lt;ScmCustSelect
  v-model="orgCustId"
  :orgPartner="true"
  :enablePagination="false"
  searchType="pinyin"
  style="width: 300px"
/&gt;

// 多选模式
&lt;ScmCustSelect
  v-model="orgCustIds"
  :orgPartner="true"
  :enablePagination="false"
  multiple
  style="width: 100%"
/&gt;

// 显示编码优先
&lt;ScmCustSelect
  v-model="orgCustId"
  :orgPartner="true"
  :enablePagination="false"
  showField="custCode"
  style="width: 300px"
/&gt;</code></pre>
        </div>
      </div>

      <div mt-16px>
        <Alert
          message="性能提示"
          description="非分页模式适合机构供应商数据量较少的场景（建议少于1000条）。如果数据量很大，建议使用分页模式以获得更好的性能。"
          type="warning"
          show-icon
        />
      </div>
    </div>

    <Divider />

    <CodeDemoVue :code="orgPartnerNoPaginationUsage" title="机构供应商非分页示例" />
    <CodeDemoVue :code="importCode" title="引入组件" language="javascript" />
    <CodeDemoVue :code="packageJsonCode" title="package.json依赖配置" language="json" />
  </Card>
</template>

<style scoped>
.feature-list {
  margin-left: 20px;
  line-height: 1.8;
}

.feature-list li {
  margin-bottom: 8px;
}

.feature-list code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  color: #d56161;
}

.api-info {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
}

.api-fields {
  margin-left: 20px;
  margin-top: 8px;
}

.api-fields li {
  margin-bottom: 4px;
}

.api-fields code {
  background-color: #e9ecef;
  padding: 2px 4px;
  border-radius: 3px;
  color: #495057;
  font-weight: 500;
}

.example-block {
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 16px;
  margin-top: 8px;
  overflow: auto;
}

.example-block pre {
  margin: 0;
}

.example-block code {
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
}
</style>
