import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { PharmacySelect } from '@mh-wm/pharmacy'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-wm/pharmacy": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}`

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 显示所有药房 -->
  <PharmacySelect
    v-model="pharmacyId"
    style="width: 200px"
  />
</template>

<script setup>
import { PharmacySelect } from '@mh-wm/pharmacy'
import { ref } from 'vue'

// 药房ID
const pharmacyId = ref()
</script>`)

// 当前用户药房
export const currentUserUsage = wrapCodeExample(`<template>
  <!-- 仅显示当前用户有权限的药房 -->
  <PharmacySelect
    v-model="userPharmacyId"
    :own="true"
    style="width: 200px"
  />
</template>

<script setup>
import { PharmacySelect } from '@mh-wm/pharmacy'
import { ref } from 'vue'

// 当前用户的药房ID
const userPharmacyId = ref()
</script>`)

// 多选模式
export const multipleUsage = wrapCodeExample(`<template>
  <!-- 多选模式 -->
  <PharmacySelect
    v-model="pharmacyIds"
    multiple
    style="width: 200px"
  />
</template>

<script setup>
import { PharmacySelect } from '@mh-wm/pharmacy'
import { ref } from 'vue'

// 多选药房ID
const pharmacyIds = ref([])
</script>`)

// 业务类型过滤
export const bsnTypeUsage = wrapCodeExample(`<template>
  <!-- 根据业务类型过滤 -->
  <PharmacySelect
    v-model="purchasePharmacyId"
    :bsnType="1"
    style="width: 200px"
  />
</template>

<script setup>
import { PharmacySelect } from '@mh-wm/pharmacy'
import { ref } from 'vue'

// 采购入库药房ID
const purchasePharmacyId = ref()
</script>`)

// 处方发药药房
export const dispensingUsage = wrapCodeExample(`<template>
  <!-- 用于处方发药的药房 -->
  <PharmacySelect
    v-model="dispensingPharmacyId"
    :forRecipeDispensing="true"
    style="width: 200px"
  />
</template>

<script setup>
import { PharmacySelect } from '@mh-wm/pharmacy'
import { ref } from 'vue'

// 处方发药药房ID
const dispensingPharmacyId = ref()
</script>`)
