export const theme = { token: { colorPrimary: '#009B9B' } }
// 医嘱状态
export const oeStatus = [{
    status: 0,
    color: '#EA741E',
    label: '新'
}, {
    status: 1,
    color: '#009B9B',
    label: '核'
}, {
    status: 5,
    color: '#899595',
    label: '废'
}, {
    status: 2,
    color: '#1983BF',
    label: '执'
}, {
    status: 3,
    color: '#747474',
    label: '待停'
}, {
    status: 4,
    color: '#BDC1C2',
    label: '停'
}, {
    status: 6,
    color: '#ff4d4f',
    label: '疑'
}]
// 医嘱类型
export const oeCat = [{
    status: 1,
    color: '#009B9B',
    label: '药'
}, {
    status: 2,
    color: '#009B9B',
    label: '治'
}, {
    status: 3,
    color: '#009B9B',
    label: '护'
}, {
    status: 4,
    color: '#009B9B',
    label: '验'
}, {
    status: 5,
    color: '#009B9B',
    label: '查'
}, {
    status: 6,
    color: '#009B9B',
    label: '手'
}, {
    status: 7,
    color: '#009B9B',
    label: '膳'
}, {
    status: 8,
    color: '#747474',
    label: '文'
}, {
    status: 9,
    color: '#009B9B',
    label: '其'
}, {
    status: -1,
    color: '#64a6e1',
    label: '费'
}]
export const sample = [{
    status: 1,
    color: '#64a6e1',
    label: '篮管'
}, {
    status: 2,
    color: '#0d0d0d',
    label: '黑管'
}, {
    status: 3,
    color: '#fc2435',
    label: '红管'
}, {
    status: 4,
    color: '#ffa04d',
    label: '黄管'
}, {
    status: 5,
    color: '#007130',
    label: '绿管'
}, {
    status: 6,
    color: '#a491f3',
    label: '紫管'
}, {
    status: 7,
    color: '#c5c5c5',
    label: '灰管'
}, {
    status: 8,
    color: '#009B9B',
    label: '其他'
}]