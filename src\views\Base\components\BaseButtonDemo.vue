<script setup lang="ts">
import { ref } from 'vue'
import { Typography, Divider, Space, message, Alert } from 'ant-design-vue'
import { BaseButton } from '@mh-base/core'

const { Title, Paragraph } = Typography

// 最后触发的按钮
const lastTriggeredButton = ref('')

// 处理按钮点击
const handleButtonClick = (buttonName: string) => {
  lastTriggeredButton.value = buttonName
  message.success(`点击了${buttonName}按钮`)
}

// 处理保存按钮点击
const handleSave = () => {
  lastTriggeredButton.value = '保存'
  message.success('保存成功')
}

// 这个函数在完整示例中使用，这里保留以保持一致性

// 处理取消按钮点击
const handleCancel = () => {
  lastTriggeredButton.value = '取消'
  message.info('已取消操作')
}

// 处理打印按钮点击
const handlePrint = () => {
  lastTriggeredButton.value = '打印'
  message.success('正在打印...')
}
</script>

<template>
  <div class="base-button-demo">
    <Title :level="4">BaseButton 基础按钮组件</Title>
    <Paragraph> BaseButton组件是对ant-design-vue的Button组件的扩展，增加了键盘快捷键功能。 您可以通过functionKey属性指定按钮的快捷键，当按下对应的快捷键时，会自动触发按钮的点击事件。 </Paragraph>

    <Alert v-if="lastTriggeredButton" type="success" show-icon style="margin-bottom: 16px">
      <template #message>
        <div>
          最近触发的按钮: <strong>{{ lastTriggeredButton }}</strong>
        </div>
      </template>
    </Alert>

    <Divider />

    <Title :level="5">基础用法</Title>
    <Paragraph> 通过functionKey属性指定按钮的快捷键，支持单个按键（如F5）或组合按键（如Ctrl + S）。 </Paragraph>

    <Space direction="vertical" style="width: 100%">
      <div>
        <BaseButton type="primary" functionKey="F5" @click="() => handleButtonClick('刷新(F5)')"> 刷新 </BaseButton>
        <span style="margin-left: 8px; color: #666; font-size: 13px">按F5键可触发此按钮</span>
      </div>

      <div>
        <BaseButton type="primary" functionKey="Ctrl + S" @click="handleSave"> 保存 </BaseButton>
        <span style="margin-left: 8px; color: #666; font-size: 13px">按Ctrl+S组合键可触发此按钮</span>
      </div>

      <div>
        <BaseButton danger functionKey="Escape" @click="handleCancel"> 取消 </BaseButton>
        <span style="margin-left: 8px; color: #666; font-size: 13px">按Escape键可触发此按钮</span>
      </div>
    </Space>

    <Divider />

    <Title :level="5">不同类型的按钮</Title>
    <Paragraph>
      BaseButton组件支持ant-design-vue Button组件的所有属性。
      <a href="#/Base/Button" style="margin-left: 8px">查看完整示例 →</a>
    </Paragraph>

    <Space>
      <BaseButton type="primary" functionKey="P" @click="handlePrint">打印</BaseButton>
      <BaseButton type="default" functionKey="D" @click="() => handleButtonClick('默认')">默认</BaseButton>
      <BaseButton type="dashed" functionKey="A" @click="() => handleButtonClick('虚线')">虚线</BaseButton>
      <BaseButton type="link" functionKey="L" @click="() => handleButtonClick('链接')">链接</BaseButton>
      <BaseButton type="text" functionKey="T" @click="() => handleButtonClick('文本')">文本</BaseButton>
    </Space>
  </div>
</template>

<style scoped>
.base-button-demo {
  margin-top: 20px;
}
</style>
