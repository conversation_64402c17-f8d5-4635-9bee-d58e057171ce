import { addEnum, EnumProps } from '@idmy/core'

export type PaymentType = 'CASH' | 'PREPAID' | 'CHECK' | 'VOUCHER' | 'MI_FUND' | 'MI_FAMILY' | 'BI' | 'EPP' | 'POS' | 'OTHER' | 'MI_ACCOUNT' | 'CREDIT' | 'ALIPAY' | 'WECHAT'

export enum PaymentTypeEnum {
  CASH = 11,
  DEPOSIT = 12,
  CHECK = 13,
  VOUCHER = 14,
  MI_FUND = 21,
  MI_FAMILY = 22,
  BI = 23,
  EPP = 24,
  POS = 25,
  OTHER = 26,
  MI_ACCOUNT = 27,
  CREDIT = 28,
  ALIPAY = 29,
  WECHAT = 30,
}

export function toPaymentType(value: number): PaymentType {
  return PaymentTypeEnum[value] as PaymentType
}

export const PaymentTypes: EnumProps[] = [
  { title: '现金', value: PaymentTypeEnum.CASH, key: 'CASH' },
  { title: '押金', value: PaymentTypeEnum.DEPOSIT, key: 'DEPOSIT' },
  { title: '医保统筹', value: PaymentTypeEnum.MI_FUND, key: 'MI_FUND' },
  { title: '电子支付', value: PaymentTypeEnum.EPP, key: 'EPP' },
  { title: 'POS刷卡', value: PaymentTypeEnum.POS, key: 'POS' },
  { title: '其他移动支付', value: PaymentTypeEnum.OTHER, key: 'OTHER' },
  { title: '医保个账', value: PaymentTypeEnum.MI_ACCOUNT, key: 'MI_ACCOUNT' },
  { title: '挂账', value: PaymentTypeEnum.CREDIT, key: 'CREDIT' },
  { title: '支付宝', value: PaymentTypeEnum.ALIPAY, key: 'ALIPAY' },
  { title: '微信', value: PaymentTypeEnum.WECHAT, key: 'WECHAT' },
]

addEnum('PaymentType', () => PaymentTypes)

export const SUPPORT_BACKTRACK_PAYMENT_TYPES = ['POS', 'EPP', 'OTHER']
