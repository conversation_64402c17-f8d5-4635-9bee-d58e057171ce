<script setup lang="ts">
import { AddArt as OeAddArt, ChangeTotal, RefundOe } from '@mh-inpatient-hsd/oe-fee-change'
import { Card, Typography, Divider, Button, message } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { oeAddArtUsage, changeTotalUsage, refundOeUsage, importCode, packageJsonCode } from './code/OeFeeChangeCode'

const { Title, Paragraph } = Typography

// 引用
const oeAddArtRef = ref()
const changeTotalRef = ref()
const refundOeRef = ref()

// 模拟数据
const visitId = 84503
const oeNo = 15
const sectionId = 40
const execSeqid = "OE2025051500001" // 医嘱执行流水号
const bsnDate = "20250515" // 业务日期

// 模拟医嘱数量变更记录
const changeTotalRecord = {
  execSeqid: execSeqid,
  lineNo: 1,
  artId: 1044295,
  artName: "盐酸氨溴索口服溶液",
  artSpec: "100ml:300mg/瓶",
  producer: "江苏汉晨药业有限公司",
  unit: "瓶",
  price: 35.8,
  total: 1,
  amount: 35.8,
  stockReq: 1
}

// 打开医嘱增加条目弹窗
const handleVisibleOeAddArt = () => {
  oeAddArtRef.value.open(execSeqid, 1)
}

// 打开医嘱数量变更弹窗
const handleVisibleChangeTotal = () => {
  changeTotalRef.value.open(changeTotalRecord)
}

// 打开医嘱退费弹窗
const handleVisibleRefundOe = () => {
  refundOeRef.value.open(execSeqid, 1, bsnDate)
}

// 提交回调
const handleSubmit = (data) => {
  message.success('操作成功')
  console.log('提交数据:', data)
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>

<template>
  <Card title="医嘱费用变更 - 医嘱增加条目" class="mb-16px">
    <div mb-16px>
      <Title :level="4">医嘱增加条目</Title>
      <Button type="primary" @click="handleVisibleOeAddArt">打开医嘱增加条目弹窗</Button>
      <OeAddArt ref="oeAddArtRef" :section-id="sectionId" @page="handleClose" />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="oeAddArtUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <Card title="医嘱费用变更 - 医嘱数量变更" class="mb-16px">
    <div mb-16px>
      <Title :level="4">医嘱数量变更</Title>
      <Button type="primary" @click="handleVisibleChangeTotal">打开医嘱数量变更弹窗</Button>
      <ChangeTotal ref="changeTotalRef" :section-id="sectionId" @page="handleClose" />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="changeTotalUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>

  <Card title="医嘱费用变更 - 医嘱退费" class="mb-16px">
    <div mb-16px>
      <Title :level="4">医嘱退费</Title>
      <Button type="primary" @click="handleVisibleRefundOe">打开医嘱退费弹窗</Button>
      <RefundOe ref="refundOeRef" :section-id="sectionId" @page="handleClose" />
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="refundOeUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>
