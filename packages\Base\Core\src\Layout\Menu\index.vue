<template>
  <Menu v-model:openKeys="openKeys" v-model:selectedKeys="selectedKeys" :inlineCollapsed="collapsed" class="layout-menu" mode="inline" @click="onMenuClick">
    <template v-for="menu in menus" :key="menu.path">
      <!-- 没有子菜单的情况 -->
      <MenuItem v-if="!menu.children?.length" :key="menu.path">
        <component :is="menu.meta.icon" v-if="menu.meta?.icon" />
        <span>{{ menu.meta?.title }}</span>
      </MenuItem>

      <!-- 有子菜单的情况 -->
      <SubMenu v-else :key="'sub-' + menu.path">
        <template #title>
          <component :is="menu.meta.icon" v-if="menu.meta?.icon" />
          <span>{{ menu.meta?.title }}</span>
        </template>

        <!-- 二级菜单 -->
        <template v-for="subMenu in menu.children" :key="subMenu.path">
          <!-- 二级菜单项 -->
          <MenuItem v-if="!subMenu.children?.length" :key="subMenu.path">
            <component :is="subMenu.meta.icon" v-if="subMenu.meta?.icon" />
            <span>{{ subMenu.meta?.title }}</span>
          </MenuItem>

          <!-- 三级菜单 -->
          <SubMenu v-else :key="'sub-' + subMenu.path">
            <template #title>
              <component :is="subMenu.meta.icon" v-if="subMenu.meta?.icon" />
              <span>{{ subMenu.meta?.title }}</span>
            </template>

            <!-- 三级菜单项 -->
            <MenuItem v-for="item in subMenu.children" :key="item.path">
              <component :is="item.meta.icon" v-if="item.meta?.icon" />
              <span>{{ item.meta?.title }}</span>
            </MenuItem>
          </SubMenu>
        </template>
      </SubMenu>
    </template>
  </Menu>
</template>

<script lang="ts" setup>
import { Menu, MenuItem, SubMenu } from 'ant-design-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

defineProps<{
  collapsed?: boolean
}>()

const route = useRoute()
const router = useRouter()

// 选中的菜单项
const selectedKeys = computed(() => [route.path])

// 展开的子菜单
const openKeys = ref<string[]>([])

// 初始化展开的菜单
const initOpenKeys = () => {
  const path = route.path
  const pathSegments = path.split('/').filter(Boolean)

  // 构建父级路径数组
  const parentPaths: string[] = []
  let currentPath = ''

  pathSegments.forEach(segment => {
    currentPath += `/${segment}`
    if (currentPath !== path) {
      parentPaths.push(`sub-${currentPath}`)
    }
  })

  openKeys.value = parentPaths
}

// 监听路由变化，更新展开的菜单
watch(
  () => route.path,
  () => {
    initOpenKeys()
  }
)

// 页面加载时初始化展开的菜单
onMounted(() => {
  initOpenKeys()
})

// 递归过滤菜单
const filterMenus = (routes: any[]) => {
  return routes.filter(route => {
    // 检查基本条件
    if (!route.meta) return false
    if (route.meta.menu === false) return false

    // 如果有子路由，递归处理
    if (route.children?.length) {
      route.children = filterMenus(route.children)
      // 如果过滤后没有子菜单了，且当前路由没有组件，则不显示
      if (!route.children.length && !route.component) return false
    }

    return true
  })
}

// 获取路由菜单
const menus = computed(() => {
  return filterMenus(router.options.routes as any[])
})

// 菜单点击处理
const onMenuClick = (info: { key: string | number }) => {
  router.push(info.key.toString())
}
</script>

<style lang="less" scoped>
.layout-menu {
  overflow-y: auto;
}
</style>
