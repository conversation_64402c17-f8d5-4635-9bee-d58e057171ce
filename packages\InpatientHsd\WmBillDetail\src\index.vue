<script setup lang="ts">
import dayjs from "dayjs";
import filters from "./utils/filters.ts"
import {FileExcelOutlined} from '@ant-design/icons-vue'
import type {TableColumnType, PaginationProps} from 'ant-design-vue'
import { Table, Button, Col, Form, FormItem, Modal, Row, Input, Space, RangePicker, Select, SelectOption } from 'ant-design-vue'
import { SectionArtSelect } from '@mh-inpatient-hsd/selector'
import { findWmBillDetailPageApi, wmBillDetailSumByArtIdApi, expWmBillDetailApi } from "@mh-inpatient-hsd/util"

const props = defineProps({
  sectionId: {
    type: Number,
    default: null
  },
  sectionName: {
    type: String,
    default: null
  }
})

const sectionArtSelectRef = ref<InstanceType<typeof SectionArtSelect>>()
const columns: TableColumnType[] = [
  {
    title: '制单时间',
    dataIndex: 'timeCreated',
    align: 'center',
    width: 100
  },
  {
    title: '业务类型',
    dataIndex: 'bsnType',
    align: 'center',
    width: 90
  },
  {
    title: '单据类型',
    dataIndex: 'wmbillTypeId',
    align: 'center',
    width: 90
  },
  {
    title: '单据流水',
    dataIndex: 'wbSeqid',
    align: 'center',
    width: 180
  },
  {
    title: '患者',
    dataIndex: 'patient',
    align: 'center',
    width: 90
  },
  {
    title: '条目编号',
    dataIndex: 'artId',
    align: 'center',
    width: 90
  },
  {
    title: '条目',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '批号',
    dataIndex: 'batchNo',
    align: 'center',
    width: 100
  },
  {
    title: '制单人',
    dataIndex: 'creatorName',
    align: 'center',
    width: 90
  },
  {
    title: '数量',
    dataIndex: 'total',
    align: 'right',
    width: 90
  },
  {
    title: '单价',
    dataIndex: 'price',
    align: 'right',
    width: 90
  },
  {
    title: '金额',
    dataIndex: 'amount',
    align: 'right',
    width: 90
  },
]

const visible = ref(false);
const formModel = ref<any>({})
const loading = ref<boolean>(false)
const stConfirmLoading = ref<boolean>(false)
const searchFormModel = reactive<any>({})
const dataSource = ref<any>([])
const expLoading = ref<boolean>(false)
const sumMap = ref<any>({})

const open = () => {
  onReset()
  handleSearch()
  visible.value = true
}

async function onReset() {
  // 清空所有参数重新请求
  dataSource.value = []
  searchFormModel.applyTimeRange = [dayjs().add(-6, 'd'), dayjs()]
  searchFormModel.patientName = null
  searchFormModel.creator = null
  searchFormModel.billTypeId = null
  searchFormModel.artId = null
  nextTick(() => {
    sectionArtSelectRef.value?.init()
  })
  await handleSearch()
}

function handleArtSelect (art: any) {
  searchFormModel.artId = undefined
  if (art) {
    searchFormModel.artId = art.artId
  }
}

const pagination = reactive<PaginationProps>({
  pageSize: 50,
  pageSizeOptions: ['50', '200', '500', '1000'],
  current: 1,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: total => `共：${total} 条`,
  onChange(current, pageSize) {
    pagination.pageSize = pageSize
    pagination.current = current
    queryPage()
  },
})

async function handleSearch() {
  pagination.current = 1
  await queryPage()
}

async function queryPage() {
  if (loading.value)
    return
  loading.value = true
  try {
    searchFormModel.absByType = true
    searchFormModel.sectionId = props.sectionId
    searchFormModel.billStatus = 5
    searchFormModel.startDate = undefined
    searchFormModel.endDate = undefined
    if (searchFormModel.applyTimeRange && searchFormModel.applyTimeRange.length === 2) {
      searchFormModel.startDate = searchFormModel.applyTimeRange[0].format('YYYY-MM-DD')
      searchFormModel.endDate = searchFormModel.applyTimeRange[1].format('YYYY-MM-DD')
    }
    sumByArtId()
    const { list, total } = await findWmBillDetailPageApi({
      ...searchFormModel,
      sidx: 't_wm_bill_detail.WB_SeqID',
      order: 'asc',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    })
    dataSource.value = list ?? []
    pagination.total = total ?? 0
  } catch (e) {
    console.log(e)
  } finally {
    loading.value = false
  }
}

async function sumByArtId() {
  sumMap.value = {}
  if (searchFormModel.artId) {
    wmBillDetailSumByArtIdApi(searchFormModel).then((data: any) => {
      if (data) {
        sumMap.value = data
      }
    })
  }
}

function handleCancel() {
  visible.value = false
}

const rangePresets = ref([
  { label: '最近2日', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近3日', value: [dayjs().add(-2, 'd'), dayjs()] },
  { label: '最近4日', value: [dayjs().add(-3, 'd'), dayjs()] },
  { label: '最近5日', value: [dayjs().add(-4, 'd'), dayjs()] },
  { label: '最近6日', value: [dayjs().add(-5, 'd'), dayjs()] },
  { label: '最近7日', value: [dayjs().add(-6, 'd'), dayjs()] }
])

async function handleExpWmBillDetail() {
  expLoading.value = true
  expWmBillDetailApi(searchFormModel).then(() => {
  }).finally(() => {
    setTimeout(() => {
      expLoading.value = false
    }, 3000)
  })
}

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" title="库存台账" width="1580px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <span style="text-align: right;" v-if="sumMap && sumMap.artId" m-r-10>
        <span m-r-5>包装制剂数: {{ sumMap.packCells }}</span>
        <span>余</span>
        <span v-if="sumMap.totalPacks">{{ sumMap.totalPacks }}{{ sumMap.packUnit }}</span>
        <span v-if="sumMap.totalCells">{{ sumMap.totalCells }}{{ sumMap.cellUnit }}</span>
        <span v-if="!sumMap.totalPacks && !sumMap.totalCells">0</span>
        <span m-l-5>￥{{ sumMap.amount }}</span>
      </span>
      <Button type="dashed" @click="handleCancel">
        关闭
      </Button>
    </template>
    <div class="content-req">
      <Form :label-col="{ span: 4 }" :model="searchFormModel" w-full>
        <Row>
          <Col flex="300px">
            <Form-item label="条目" :label-col="{ span: 3 }">
              <section-art-select ref="sectionArtSelectRef" :section-id="props.sectionId" @selected="handleArtSelect"/>
            </Form-item>
          </Col>
          <Col flex="310px">
            <Form-item label="申请日期" name="timeAppliedRange" :label-col="{ style: { width: '80px' } }" :wrapper-col="{ style: { width: '230px' } }">
              <Range-picker :presets="rangePresets" v-model:value="searchFormModel.applyTimeRange" :allow-clear="true" @change="handleSearch"/>
            </Form-item>
          </Col>
          <Col flex="220px">
            <Form-item label="业务类型" name="billTypeId" :label-col="{ span: 8 }">
              <Select v-model:value="searchFormModel.billTypeId" placeholder="请选择业务类型" style="width: 100%;" allow-clear>
                <Select-option :value="11">入库</Select-option>
                <Select-option :value="12">入库冲红</Select-option>
                <Select-option :value="121">退回药房</Select-option>
                <Select-option :value="122">消耗出库</Select-option>
                <Select-option :value="21">出库</Select-option>
                <Select-option :value="22">出库冲红</Select-option>
              </Select>
            </Form-item>
          </Col>
          <Col flex="180px">
            <Form-item label="患者" name="patientName" :label-col="{ span: 8 }">
              <Input v-model:value="searchFormModel.patientName" placeholder="患者姓名检索"/>
            </Form-item>
          </Col>
          <Col flex="200px">
            <Form-item label="制单人" name="creator" :label-col="{ span: 8 }">
              <Input v-model:value="searchFormModel.creator" placeholder="制单人姓名检索"/>
            </Form-item>
          </Col>
          <Col flex="160px">
            <Space flex w-full m-l-5>
              <Button :loading="loading" type="primary" @click="handleSearch">
                查询
              </Button>
              <Button :loading="loading" @click="onReset">
                重置
              </Button>
            </Space>
          </Col>
          <Col flex="auto">
            <div style="text-align: right;">
              <Button
                @click="handleExpWmBillDetail"
                :loading="expLoading"
                type="primary"
                :disabled="!dataSource || dataSource.length === 0"
                m-r-5>
                <template #icon>
                  <FileExcelOutlined/>
                </template>
                导出
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
      <Table
        :rowKey="(record: any) => record.keyStr"
        :loading="loading"
        :columns="columns"
        :data-source="dataSource"
        :has-page="true"
        :pagination="pagination"
        :scroll="{ y: 'calc(100vh - 300px)' }">
        <template #bodyCell="{ text, column, record }">
          <template v-if="column.dataIndex === 'artDesc'">
            <div>
              <span style="font-weight: bold;">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
            </div>
            <div v-if="record.producer">{{ record.producer }}</div>
          </template>
          <template v-if="column.dataIndex === 'patient'">
            <div v-if="record.bedNo">{{ record.bedNo }}床</div>
            <div v-if="record.patientName">{{ record.patientName }}</div>
          </template>
          <template v-if="column.dataIndex === 'bsnType'">
            <span v-if="text == 1">申领入库</span>
            <span v-else-if="text == 2">销售出库</span>
            <span v-else-if="text == 3">库存损益</span>
          </template>
          <template v-if="column.dataIndex === 'wmbillTypeId'">
            <span v-if="text == 11">入库</span>
            <span v-else-if="text == 12">入库冲红</span>
            <span v-else-if="text == 121">退回药房</span>
            <span v-else-if="text == 122">消耗出库</span>
            <span v-else-if="text == 21">出库</span>
            <span v-else-if="text == 22">出库冲红</span>
          </template>
          <template v-if="column.dataIndex === 'price'">
            <div v-if="record.totalPacks">{{ record.packPrice }}/{{ record.packUnit }}</div>
            <div v-if="record.totalCells">{{ record.cellPrice }}/{{ record.cellUnit }}</div>
          </template>
          <template v-if="column.dataIndex === 'total'">
            <div v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</div>
            <div v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</div>
          </template>
          <template v-if="['applyTime','applyTime'].includes(column.dataIndex)">
            {{ filters.dateFormatMDHM(text) }}
          </template>
          <template v-if="['timeCreated'].includes(column.dataIndex)">
            {{ filters.dateFormatMDHM(text) }}
          </template>
        </template>
      </Table>
    </div>
  </Modal>
</template>
