import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <!-- 基础用法 - 带快捷键的按钮 -->
  <BaseButton type="primary" functionKey="F5" @click="handleRefresh">
    刷新
  </BaseButton>

  <!-- 组合键示例 -->
  <BaseButton type="primary" functionKey="Ctrl + S" @click="handleSave">
    保存
  </BaseButton>
</template>

<script setup>
import { BaseButton } from '@mh-base/core'
import { message } from 'ant-design-vue'

// 处理刷新按钮点击
const handleRefresh = () => {
  message.success('刷新成功')
}

// 处理保存按钮点击
const handleSave = () => {
  message.success('保存成功')
}
</script>`)

// 隐藏快捷键显示
export const hideKeyUsage = wrapCodeExample(`<template>
  <!-- 显示快捷键 -->
  <BaseButton type="primary" functionKey="F5" @click="handleRefresh">
    刷新
  </BaseButton>

  <!-- 隐藏快捷键 -->
  <BaseButton
    type="primary"
    functionKey="F5"
    :showFunctionKey="false"
    @click="handleRefresh"
  >
    刷新
  </BaseButton>
</template>

<script setup>
import { BaseButton } from '@mh-base/core'
import { message } from 'ant-design-vue'

const handleRefresh = () => {
  message.success('刷新成功')
}
</script>`)

// 不同类型的按钮
export const buttonTypesUsage = wrapCodeExample(`<template>
  <Space>
    <BaseButton type="primary" functionKey="P" @click="handlePrint">
      打印
    </BaseButton>

    <BaseButton type="default" functionKey="D" @click="handleDefault">
      默认
    </BaseButton>

    <BaseButton type="dashed" functionKey="A" @click="handleDashed">
      虚线
    </BaseButton>

    <BaseButton type="link" functionKey="L" @click="handleLink">
      链接
    </BaseButton>

    <BaseButton type="text" functionKey="T" @click="handleText">
      文本
    </BaseButton>
  </Space>
</template>

<script setup>
import { BaseButton } from '@mh-base/core'
import { Space, message } from 'ant-design-vue'

const handlePrint = () => {
  message.success('正在打印...')
}

const handleDefault = () => {
  message.info('点击了默认按钮')
}

const handleDashed = () => {
  message.info('点击了虚线按钮')
}

const handleLink = () => {
  message.info('点击了链接按钮')
}

const handleText = () => {
  message.info('点击了文本按钮')
}
</script>`)

// 禁用状态
export const disabledUsage = wrapCodeExample(`<template>
  <!-- 禁用状态的按钮 -->
  <BaseButton
    type="primary"
    functionKey="F5"
    disabled
    @click="handleClick"
  >
    禁用按钮
  </BaseButton>
</template>

<script setup>
import { BaseButton } from '@mh-base/core'
import { message } from 'ant-design-vue'

const handleClick = () => {
  // 按钮被禁用，此函数不会被调用
  message.success('按钮被点击')
}
</script>`)

// 可见性控制
export const visibilityUsage = wrapCodeExample(`<template>
  <!-- 在长滚动页面中的按钮 -->
  <div style="height: 300px; overflow: auto;">
    <div style="height: 500px;">
      <!-- 这个按钮可能不在视口中 -->
      <BaseButton
        type="primary"
        functionKey="F6"
        @click="handleClick"
      >
        滚动可见按钮(F6)
      </BaseButton>
    </div>
  </div>
</template>

<script setup>
import { BaseButton } from '@mh-base/core'
import { message } from 'ant-design-vue'

const handleClick = () => {
  // 只有当按钮在视口中可见时，按F6键才会调用此函数
  message.success('按钮被点击')
}
</script>`)

// 导入代码
export const importCode = `import { BaseButton } from '@mh-base/core'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-base/core": "^1.0.22"
  }
}`

// Loading状态
export const loadingUsage = wrapCodeExample(`<template>
  <div>
    <Space>
      <!-- 普通加载状态 -->
      <BaseButton type="primary" :loading="loading" functionKey="F8" @click="handleSave">
        保存数据
      </BaseButton>

      <!-- 带图标的加载状态 -->
      <BaseButton
        type="primary"
        :loading="loadingWithIcon"
        functionKey="F9"
        @click="handleSubmit"
      >
        提交表单
      </BaseButton>

      <!-- 手动控制加载状态 -->
      <BaseButton
        type="primary"
        :loading="loadingManual"
        functionKey="F10"
        @click="handleManualLoading"
      >
        {{ loadingManual ? '处理中...' : '手动控制' }}
      </BaseButton>
    </Space>

    <div style="margin-top: 16px">
      <p>说明：点击按钮后会模拟API调用，2秒后自动恢复。在loading状态下，按下快捷键不会重复触发操作。</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { BaseButton } from '@mh-base/core'
import { Space, message } from 'ant-design-vue'

// 普通加载状态
const loading = ref(false)
const handleSave = () => {
  if (loading.value) return

  loading.value = true
  message.loading('正在保存数据...')

  // 模拟API调用
  setTimeout(() => {
    loading.value = false
    message.success('数据保存成功')
  }, 2000)
}

// 带图标的加载状态
const loadingWithIcon = ref(false)
const handleSubmit = () => {
  if (loadingWithIcon.value) return

  loadingWithIcon.value = true
  message.loading('正在提交表单...')

  // 模拟API调用
  setTimeout(() => {
    loadingWithIcon.value = false
    message.success('表单提交成功')
  }, 2000)
}

// 手动控制加载状态
const loadingManual = ref(false)
const handleManualLoading = () => {
  if (loadingManual.value) return

  loadingManual.value = true
  message.loading('正在处理...')

  // 模拟API调用
  setTimeout(() => {
    loadingManual.value = false
    message.success('处理完成')
  }, 2000)
}
</script>`)
