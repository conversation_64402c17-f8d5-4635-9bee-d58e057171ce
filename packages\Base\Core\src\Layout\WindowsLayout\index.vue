<template>
  <Layout class="windows-layout-container">
    <!-- 顶部导航 -->
    <LayoutHeader class="windows-layout-header">
      <div class="logo">
        <h1>{{ appConfig.app.title }}</h1>
      </div>
      <OrgSelect />
      <div class="header-right">
        <HeaderRight />
      </div>
    </LayoutHeader>
    <Layout class="windows-layout-main">
      <LayoutSider :width="50" class="windows-layout-sider">
        <WindowsMenu />
      </LayoutSider>
      <Layout class="windows-layout-content-wrapper">
        <LayoutContent class="windows-layout-content">
          <router-view />
        </LayoutContent>
      </Layout>
    </Layout>
  </Layout>
</template>

<script lang="ts" setup>
import { appConfig } from '@idmy/core'
import { Layout, LayoutContent, LayoutHeader, LayoutSider } from 'ant-design-vue'
import HeaderRight from '../HeaderRight/index.vue'
import OrgSelect from '../Org/OrgSelect.vue'
import WindowsMenu from '../WindowsMenu/index.vue'
</script>

<style lang="less" scoped>
.windows-layout-container {
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
}

.windows-layout-header {
  background: var(--primary-color) !important;
  height: 44px !important;
  padding: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .logo {
    display: flex;
    align-items: center;
    padding-left: 24px;

    h1 {
      margin: 0;
      color: #fff;
      font-weight: 600;
      font-size: 18px;
    }
  }

  .header-right {
    flex: 1;
    height: 100%;
    display: flex;
    flex-flow: row-reverse;
  }
}

.windows-layout-main {
  height: calc(100vh - 44px);
}

.windows-layout-sider {
  background: #fff;
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.windows-layout-content-wrapper {
  padding: 0;
  background: #f0f2f5;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.windows-layout-content {
  flex: 1;
  margin: 16px;
  background: #fff;
  padding: 16px;
  border-radius: 2px;
  overflow-y: auto;
  overflow-x: hidden;
  border: solid 1px #e8e8e8;
}
</style>
