<script setup lang="ts">
import { Api, Data } from '@idmy/core'
import { allMedicalTypes } from '@mh-hip/util'
import { Table } from 'ant-design-vue'

const columns: Data[] = [
  {
    align: 'center',
    customRender: ({ index }: Data) => index + 1,
    dataIndex: 'no',
    fixed: 'left',
    title: '#',
    width: 45,
  },
  {
    align: 'center',
    dataIndex: 'medTypeId',
    title: '医疗类别代号',
  },
  {
    align: 'center',
    dataIndex: 'medTypeCode',
    title: '医疗类别编码',
  },
  {
    align: 'center',
    dataIndex: 'medTypeName',
    title: '医疗类别名称',
  },
]
</script>

<template>
  <Api :load="() => allMedicalTypes()" v-slot="{ output, loading }" type="Array">
    <Table rowKey="medTypeId" :columns="columns" :dataSource="output as any[]" :loading="loading" :pagination="false">
      <template #bodyCell="{ column: col, record: row }"> </template>
    </Table>
  </Api>
</template>
