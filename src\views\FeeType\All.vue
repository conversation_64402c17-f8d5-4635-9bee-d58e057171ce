<script setup lang="ts">
import { FeeTypeAll, FeeTypeDict } from '@mh-hip/fee-type'
import { StockReqCat } from '@mh-hip/art-cat'
import { ArtSubTypeDict } from '@mh-hip/art-sub-type'

const select = ref()
const radio = ref()
const checkbox = reactive<any[]>([])
const cat = ref()
const subType = ref()
</script>

<template>
  <FeeTypeDict v-model="select" type="Select" w-200px mr-8px />
  <FeeTypeDict v-model="radio" type="Radio" w-200px mr-8px />
  <FeeTypeDict v-model="checkbox" multiple type="Checkbox" w-400px />
  <StockReqCat v-model="cat" type="Select" w-150px ml-8px />
  <ArtSubTypeDict v-model="subType" type="Select" w-150px ml-8px />
  <div h-8px />
  <FeeTypeAll />
</template>
