# RecipeTrackCode 处方扫码组件

处方扫码组件，用于录入和管理处方药品的追溯码信息。

## 特性

- 支持通过扫码枪或高拍仪录入追溯码
- 支持追溯码有效性验证
- 支持拆零药品的追溯码录入
- 支持采集结果状态显示
- 基于ant-design-vue的组件实现

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-wm/util`: 工具库，提供API调用

## 打包与安装

### 打包组件

在开发完成后，需要打包组件以便发布和使用：

```bash
# 在项目根目录下执行打包命令
pnpm publish:component Wm/RecipeTrackCode
```

这个命令会执行以下操作：
1. 编译组件源码
2. 生成类型声明文件
3. 打包CSS样式
4. 生成组件包到dist目录
5. 更新package.json中的版本号
6. 将组件发布到内部npm仓库

### 安装组件

在其他项目中安装该组件：

```bash
# 使用npm安装
npm install @mh-wm/recipe-track-code

# 或使用pnpm安装
pnpm add @mh-wm/recipe-track-code
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/recipe-track-code": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { RecipeTrackCode } from '@mh-wm/recipe-track-code'
import '@mh-wm/recipe-track-code/index.css'
```

### 基本用法 - 按钮触发

```vue
<template>
  <!-- 基础用法 - 显示按钮 -->
  <RecipeTrackCode
    :recipeId="recipeId"
    @success="handleSuccess"
  />
</template>

<script setup>
import { RecipeTrackCode } from '@mh-wm/recipe-track-code'
import '@mh-wm/recipe-track-code/index.css'
import { ref } from 'vue'

// 处方ID
const recipeId = ref('12345')

// 成功回调
const handleSuccess = (data) => {
  console.log('处方扫码录入成功', data)
}
</script>
```

### 通过js方法调用

```vue
<template>
  <!-- 不显示按钮，通过js方法调用 -->
  <RecipeTrackCode
    ref="recipeTrackCodeRef"
    :showButton="false"
    @success="handleSuccess"
  />

  <!-- 自定义按钮 -->
  <Button @click="openRecipeTrackCode">打开处方扫码录入</Button>
</template>

<script setup>
import { RecipeTrackCode } from '@mh-wm/recipe-track-code'
import '@mh-wm/recipe-track-code/index.css'
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

// 组件引用
const recipeTrackCodeRef = ref()

// 处方ID
const recipeId = ref('12345')

// 打开处方扫码录入窗口
const openRecipeTrackCode = () => {
  recipeTrackCodeRef.value.open(recipeId.value)
}

// 成功回调
const handleSuccess = (data) => {
  console.log('处方扫码录入成功', data)
}
</script>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| buttonText | 按钮文本，为空时自动生成 | string | '' |
| buttonType | 按钮类型 | 'primary' \| 'ghost' \| 'dashed' \| 'link' \| 'text' \| 'default' | 'default' |
| buttonSize | 按钮大小 | 'large' \| 'middle' \| 'small' | 'middle' |
| modalWidth | 对话框宽度 | number \| string | 1200 |
| showButton | 是否显示触发按钮 | boolean | true |
| recipeId | 处方ID | number \| string | '' |
| hotkey | 绑定的键盘快捷键 | string | 'F6' |
| enableAutoClosePrompt | 是否启用自动关闭提示 | boolean | true |
| onlyAddRecognizedTrackCode | 是否只添加识别追溯码 | boolean | false |
| enableOnlyAddRecognizedTrackCodeOption | 是否启用"只添加识别追溯码"复选框 | boolean | false |
| checkTrackCodeComplete | 是否检查追溯码完整性，为true时未完成不允许提交 | boolean | false |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 处方扫码录入成功时触发 | (data: any) => void |
| cancel | 取消处方扫码录入时触发 | () => void |

## 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开处方扫码录入窗口 | (recipeId?: number \| string) => void |
| close | 关闭处方扫码录入窗口 | () => void |

## 核心概念

### 拆零系数

拆零系数（packCells）记录整包与拆零数量的转换系数。例如药品规格为1盒里装5支，packCells=5，表示1盒药（整包totalPacks）= 5支（拆零数totalCells）。

### 追溯码

每盒药都有一个唯一的追溯码，用于药品追溯管理。药品追溯码的长度通常在19位到27位之间，小于19位的码（如69开头的13位条形码）不是有效的追溯码，组件会给出警告提示，不能用于业务绑定。

市面上常见的是20位溯源码，其中前7位是药品标识，能标识一个药品的唯一性，与系统中的artId（条目编号）能够对应。未来数据健全后，组件将支持校验artId和追溯码前7位的对应关系，确保追溯码与药品匹配。

追溯码前7位是药品的唯一标志，一种药需要发5盒的话，这个药扫进来的五个码前7位应该是相同的。

### 追溯码拆零标志

口服药一般按整盒发药，注射药有1盒多支的情况，多支拆零数共用1盒的一个溯源码，但会记录拆零标志位。

### 追溯码扫描

在摆药时扫描一个或多个溯源码对应一条医嘱的需求量。住院的注射药按拆零数执行，可能出现多支注射药扫同一个码的情况，也可能出现一个患者医嘱的用药需求跨多个追溯码的情况。

药品数量分为整包数量(totalPacks)和拆零数量(totalCells)两种计量方式：
- 整包数量：以药品包装单位计量，如"盒"、"瓶"等
- 拆零数量：以药品最小单位计量，如"片"、"支"等

扫描追溯码时，系统会根据药品的整包数量和拆零数量进行响应。例如，如果一种药品需要发5片，而一盒有10片，则可能出现以下情况：
1. 使用一个追溯码，设置拆零数量为5
2. 如果上一盒只剩3片，则需要使用两个追溯码，一个设置拆零数量为3，另一个设置拆零数量为2

### 外部设备

追溯码的扫描通过外部设备（扫码枪或高拍仪）进行，识别后将文本传到组件里。扫码枪扫一个码将文本写入并触发回车事件，高拍仪可能一个追溯码对应一个回车事件，在文本文档查看的效果就是5个追溯码5行。

## 开发与使用流程

### 开发流程

1. **组件开发**：在`packages/Wm/RecipeTrackCode/src`目录下开发组件
   ```
   packages/
   └── Wm/
       └── RecipeTrackCode/
           ├── src/
           │   ├── index.vue      # 主组件
           │   └── ...            # 其他子组件或工具
           ├── index.ts           # 组件入口文件
           ├── package.json       # 组件包配置
           └── README.md          # 组件文档
   ```

2. **本地测试**：在`src/views/wm/examples`目录下创建示例页面进行测试
   ```vue
   <!-- src/views/wm/examples/RecipeTrackCodeExample.vue -->
   <template>
     <div>
       <h1>处方扫码组件示例</h1>
       <RecipeTrackCode
         ref="recipeTrackCodeRef"
         :modalWidth="1400"
         @success="handleSuccess"
         @cancel="handleCancel"
       />
       <Button type="primary" @click="openTrackCode">打开处方扫码</Button>
     </div>
   </template>

   <script setup>
   import { ref } from 'vue'
   import { Button, message } from 'ant-design-vue'
   import RecipeTrackCode from '@/packages/Wm/RecipeTrackCode/src/index.vue'

   const recipeTrackCodeRef = ref()

   const openTrackCode = () => {
     // 示例参数
     const title = '处方追溯码采集'
     const wbSeqid = 123456
     const visitId = 789012
     const isView = false

     recipeTrackCodeRef.value.open(title, wbSeqid, visitId, isView)
   }

   const handleSuccess = (visitId) => {
     message.success(`处方扫码成功，visitId: ${visitId}`)
   }

   const handleCancel = () => {
     message.info('取消处方扫码')
   }
   </script>
   ```

3. **打包组件**：开发完成后，执行打包命令
   ```bash
   pnpm publish:component Wm/RecipeTrackCode
   ```

### 使用流程

1. **安装组件**：在项目中安装组件
   ```bash
   pnpm add @mh-wm/recipe-track-code
   ```

2. **引入组件**：在Vue文件中引入组件
   ```vue
   <template>
     <div>
       <RecipeTrackCode
         ref="recipeTrackCodeRef"
         :modalWidth="1400"
         @success="handleSuccess"
         @cancel="handleCancel"
       />
       <Button type="primary" @click="openTrackCode">打开处方扫码</Button>
     </div>
   </template>

   <script setup>
   import { ref } from 'vue'
   import { Button, message } from 'ant-design-vue'
   import { RecipeTrackCode } from '@mh-wm/recipe-track-code'
   import '@mh-wm/recipe-track-code/index.css'

   const recipeTrackCodeRef = ref()

   const openTrackCode = () => {
     // 实际业务参数
     const title = '处方追溯码采集'
     const wbSeqid = 123456 // 实际业务中的单据ID
     const visitId = 789012 // 实际业务中的就诊ID
     const isView = false   // 是否为查看模式

     recipeTrackCodeRef.value.open(title, wbSeqid, visitId, isView)
   }

   const handleSuccess = (visitId) => {
     message.success(`处方扫码成功，visitId: ${visitId}`)
     // 在这里处理成功后的业务逻辑
   }

   const handleCancel = () => {
     message.info('取消处方扫码')
     // 在这里处理取消后的业务逻辑
   }
   </script>
   ```

## 注意事项

1. 组件设计考虑了对高拍仪数据的接收处理，支持一个追溯码一个回车事件的模式。
2. **追溯码长度验证**：组件会验证扫描的追溯码长度，只有19位到27位之间的码才被视为有效追溯码。如果扫描到小于19位的码（如69开头的13位条形码），组件会给出警告提示，不允许用于业务绑定。
3. **追溯码与药品匹配**：目前市面上常见的20位溯源码，其前7位是药品标识，能标识药品的唯一性。未来数据健全后，组件将支持校验artId和追溯码前7位的对应关系，确保扫描的追溯码与实际药品匹配。
4. 追溯码前7位是药品的唯一标志，一种药需要发5盒的话这个药扫进来的五个码前7位应该是相同的。
5. 左侧表格用于处方下的药品展示，包含以下信息：
   - 药品基本信息（品名、规格、厂家等）
   - 整包数量(totalPacks)：以药品包装单位计量的数量
   - 拆零数量(totalCells)：以药品最小单位计量的数量
   - 已采整包数和已采拆零数
   - 采集结果状态：未采集、采集中、已完成、异常（用带颜色图标区分，异常用橙色突出）
6. 右侧表格是展示当前单据下所有追溯码详情，通过计算属性根据左侧选中的药品lineNo进行过滤显示。
7. 拆零药品处理：
   - 只有当药品的拆零数量(totalCells)大于0时，才显示拆零选项和拆零数量输入框
   - 拆零数量默认为药品的totalCells值，但用户可以修改
   - 如果一种药品需要多个追溯码（如一盒不够），可以分别设置不同的拆零数量
8. 每次进入页面就自动聚焦到追溯码输入框。所有事件（包括改变拆零标志后、点击左侧表格行或者触发一种药扫码完毕扫下一种药）成功/失败后也自动聚焦这个录入框，让用户不需要点鼠标就能连续扫码录入。
9. **高拍仪优化**：由于高拍仪传入文本和回车事件的速度很快，组件将`onAddArt`和`onDelArt`做成异步事件，不影响追溯码的录入。扫描的追溯码和API返回的数据都存储在一个数组中进行管理。
10. **智能药品切换**：当满足以下条件时，组件会自动开始扫描下一种药品：
    - 当前药品的整包数量和拆零数量都已满足
    - 扫描的追溯码没有被其他药品绑定
    - 一种药品需要扫描多盒时，每个追溯码的前7位都相同（此时左侧行会显示绿色的"已完成"状态）
11. **异常处理**：当一种药品需要扫描多盒但扫描出了多种不同前7位的追溯码时，组件会在右侧表格中通过颜色标识给出提醒：
    - 同一药品只有一种前七位时，显示为绿色
    - 出现第二种前七位时，显示为橙色
    - 出现第三种及以上前七位时，显示为红色
    - 异常情况只做提醒，不会阻止提交
12. **智能识别控制**：组件支持"只添加识别追溯码"功能，可以通过props控制：
    - `onlyAddRecognizedTrackCode`：是否只添加能够识别的追溯码（默认为false）
    - `enableOnlyAddRecognizedTrackCodeOption`：是否启用"只添加识别追溯码"复选框（默认为false）
    - 当启用此功能时，如果扫描的追溯码前7位无法匹配到任何药品，则不会绑定到当前选中行
13. **API调用优化**：组件减少了对API的调用，特别是在扫码或删除追溯码后，只有在API报错时才会重新查询表格数据。
14. **智能药品匹配**：组件使用追溯码前7位与药品的品种唯一码(prodNoLs)进行匹配：
    - 当追溯码前7位与某一种药品的prodNoLs匹配时，自动将追溯码绑定到该药品
    - 如果匹配到多种药品，则将追溯码绑定到当前选中的药品
    - 如果只匹配到一种药品，即使当前选中的是其他药品，也会将追溯码绑定到匹配的药品
15. **全部清除功能**：组件提供全部清除按钮，可以一键清除所有已扫描的追溯码
16. **自动完成提示**：当所有需要追溯码的药品都满足要求且没有异常情况（如不同前缀的追溯码）时，组件会弹出提示，倒计时5秒后自动提交。可以通过`enableAutoClosePrompt`参数控制是否启用此功能（默认为false）：
    - 当`enableAutoClosePrompt=true`时，会显示自动关闭提示
    - 当`enableAutoClosePrompt=false`时，不会显示自动关闭提示，只会显示普通的成功消息
    - 适用于防止因药品被错误设置为无追溯码而导致页面被提早关闭的情况
17. **视觉反馈**：绑定或删除追溯码时，已采整包/已采拆零数量会有动画效果，全部完成时数字变为绿色
18. **无追溯码管理**：组件提供"设置为无追溯码"和"设置为有追溯码"按钮，可以根据药品的`noTrackCode`属性动态显示：
    - 当`noTrackCode=false`时，显示"设置为无追溯码"按钮
    - 当`noTrackCode=true`时，显示"设置为有追溯码"按钮
    - 点击按钮后直接更新本地数据，无需刷新左侧列表
    - 当药品设置为无追溯码时，左侧表格的采集结果列会显示"无追溯码"状态
    - 设置为无追溯码的药品不参与追溯码全部绑定成功的判断，即使没有绑定追溯码也视为已完成
19. **拆零上报管理**：组件提供"拆零上报"和"取消拆零上报"按钮，可以根据药品的`artIsDisassembled`属性动态显示：
    - 当`artIsDisassembled=false`时，显示"拆零上报"按钮
    - 当`artIsDisassembled=true`时，显示"取消拆零上报"按钮
    - 点击按钮后直接更新本地数据，无需刷新左侧列表
    - 设置为拆零上报的药品，即使整包数量（待采集数量）`totalPacks`大于0，也会显示拆零相关UI元素
    - 拆零上报的药品只需要绑定一个追溯码并设置拆零数量，即可满足采集要求
    - 适用于历史数据中大输液等一箱一码的情况，例如`packCells=50`但被错误设置成1的情况
    - 拆零上报按钮组只在满足以下条件时显示：`totalPacks > 0`且`packCells`未赋值或`packCells = 1`
    - 当`totalPacks>0`、`packCells=1`且设置了拆零上报时，会自动将`totalPacks`的值赋给拆零数量，方便一箱一码的情况
    - 点击"拆零上报"按钮后，会自动勾选拆零复选框并设置拆零数量，无需手动操作
    - 设置为拆零上报的药品，即使没有拆零数量，也会在"已采拆零"列显示采集数量
    - 智能拆零数量计算：选中条目时自动计算剩余拆零数量，例如需求5支，已扫1支，下次自动带出剩余4支
    - 拆零上报完成判断：当`totalPacks>0`、`packCells=1`时，要求拆零数合计等于`totalPacks`才算完成，采集结果列会显示进度
    - 拆零上报智能计算：当`totalPacks>0`、`packCells=1`且设置了拆零上报时，会自动计算剩余数量（`totalPacks - collectedCells`）
    - 已采数量显示优化：当已采整包或已采拆零数量为0时，不显示数字，使界面更加简洁
    - 焦点优化：选中行或设置拆零上报后，焦点自动设置到追溯码输入框，方便用户直接扫码
    - 选中行优化：多次点击同一行不会取消选中状态，只有点击其他行才会切换选中状态，避免误操作
20. **追溯码完整性检查**：组件提供`checkTrackCodeComplete`参数控制提交时的校验行为：
    - 当`checkTrackCodeComplete=false`（默认值）时，如果有未完成的药品，会弹出确认对话框让用户选择是否继续提交
    - 当`checkTrackCodeComplete=true`时，如果有未完成的药品，会直接显示警告信息，不允许提交，用户必须先扫描完所有追溯码才能提交
    - 适用于严格要求所有药品都必须绑定追溯码的业务场景

## 版本历史

### v1.0.0 (初始版本)
- 基础功能实现：追溯码扫描、验证、绑定
- 支持拆零药品管理
- 支持追溯码长度验证(19-27位)
- 左侧表格显示药品信息和采集状态
- 右侧表格显示追溯码详情

### v1.1.0
- 新增智能药品匹配功能：使用追溯码前7位与药品的品种唯一码(prodNoLs)进行匹配
- 新增全部清除功能：一键清除所有已扫描的追溯码
- 新增自动完成提示：所有药品都满足要求且没有异常情况时，弹出提示，倒计时5秒后自动提交
- 新增视觉反馈：绑定或删除追溯码时，数量变化有动画效果
- 优化追溯码前缀异常处理：使用不同颜色标识不同前缀数量
- 优化API调用：减少不必要的API请求
- 优化高拍仪支持：异步处理追溯码，支持快速连续扫码

### v1.2.0 (当前版本)
- 新增"只添加识别追溯码"功能：可以选择只绑定能够匹配上前七位的追溯码
- 新增无追溯码管理：提供"设置为无追溯码"和"设置为有追溯码"按钮，根据药品状态动态显示
- 新增拆零上报管理：提供"拆零上报"和"取消拆零上报"按钮，根据药品状态动态显示
- 优化UI交互：使用Switch替代Checkbox，增加间距和样式
- 优化异常处理：只有在全部绑定上且没有异常情况时才弹出自动关闭提示
- 优化状态显示：当药品设置为无追溯码时，左侧表格的采集结果列会显示"无追溯码"状态
- 优化拆零上报逻辑：设置为拆零上报的药品只需要绑定一个追溯码并设置拆零数量，适用于大输液等一箱一码的情况
- 优化拆零上报按钮显示：只在`totalPacks > 0`且`packCells`未赋值或`packCells = 1`时显示拆零上报按钮
- 优化拆零数量自动赋值：当`totalPacks>0`、`packCells=1`且设置了拆零上报时，自动将`totalPacks`的值赋给拆零数量
- 优化拆零上报交互：点击"拆零上报"按钮后，自动勾选拆零复选框并设置拆零数量，无需手动操作
- 优化拆零上报显示：设置为拆零上报的药品，在"已采拆零"列显示采集数量，即使没有拆零数量
- 优化拆零数量智能计算：选中条目时自动计算剩余拆零数量，提高连续扫码效率
- 优化拆零上报完成判断：当`totalPacks>0`、`packCells=1`时，要求拆零数合计等于`totalPacks`才算完成
- 优化拆零上报智能计算：当`totalPacks>0`、`packCells=1`且设置了拆零上报时，会自动计算剩余数量
- 优化界面显示：当已采整包或已采拆零数量为0时，不显示数字，使界面更加简洁
- 优化用户体验：选中行或设置拆零上报后，焦点自动设置到追溯码输入框，方便用户直接扫码
- 优化交互体验：多次点击同一行不会取消选中状态，只有点击其他行才会切换选中状态，避免误操作
- 新增自动关闭控制参数：通过`enableAutoClosePrompt`参数控制是否显示自动关闭提示，防止因药品被错误设置为无追溯码而导致页面被提早关闭
- 修复"拆零上报"按钮显示逻辑：当选中未设置拆零上报的药品时，正确显示"拆零上报"按钮，而不是"取消拆零上报"按钮
- 修复拆零上报模式下扫码自动切换逻辑：当药品被标记为拆零上报时，扫码后正确判断是否完成采集并自动跳转到下一行，避免同一种药品绑定多个拆零码
- 优化追溯码输入框：禁用浏览器的自动完成功能，避免显示历史追溯码记录，提高数据安全性
- 移除分页功能：将两个表格的分页设置为false，API调用从分页接口改为列表接口，提高组件性能和用户体验
