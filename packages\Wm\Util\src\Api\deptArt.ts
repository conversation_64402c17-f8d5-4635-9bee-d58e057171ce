import { http } from '@idmy/core'

/**
 * 科室物品分页查询
 * @param params 查询参数
 * @returns 科室物品列表
 */
export function deptArtPageApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptart/page', params, { appKey: 'wm' })
}

/**
 * 科室物品列表查询
 * @param params 查询参数
 * @returns 科室物品列表
 */
export function deptArtLsApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptart/findList', params, { appKey: 'wm' })
}

/**
 * 科室损溢品种库存分页查询
 * @param params 查询参数
 * @returns 科室物品列表
 */
export function deptStockPageApi(params: any): Promise<any> {
  return http.post('/clinics_wm/deptstock/page', params, { appKey: 'wm' })
}

/**
 * 制单品种分页查询
 * @param params 查询参数
 * @returns 物品列表
 */
export function articlePageByOrgApi(params: any): Promise<any> {
  return http.post('/hip-base/article/pageByOrg', params, { appKey: 'hip' })
}

/**
 * 机构商品查询
 * @param params 查询参数
 * @returns 物品列表
 */
export function orgArtInfoApi(params: any): Promise<any> {
  return http.post('/clinics_wm/orgart/info', params, { appKey: 'wm' })
}

/**
 * 保存orgArt信息
 * @param params orgArt实体
 * @returns
 */
export function orgArtSaveApi(params: any): Promise<any> {
  return http.post('/clinics_wm/orgart/save', params, { appKey: 'wm' })
}


/**
 * 取库存零售价计算方式
 * @param params orgArt实体
 * @returns
 */
export function orgCustMapInfoApi(params: any): Promise<any> {
  return http.post('/clinics_wm/orgcustmap/info', params, { appKey: 'wm' })
}
