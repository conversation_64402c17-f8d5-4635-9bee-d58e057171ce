import { Data } from '@idmy/core'
import { getSql } from '../Sql'
import { Currents } from '../Util/Auth'

export async function getPrintTemplate(code: string): Promise<Data> {
  const sql = `select
                 b.title,
                 b.width,
                 b.height,
                 b.page_width pageWidth,
                 b.page_height pageHeight,
                 b.int_Orient intOrient,
                 b.temp_items tempItems
               from hip_mdi.t_print_template_type a,
                    hip_mdi.t_print_template b,
                    hip_mdi.t_org_print_template c
               where a.template_type_id = b.template_type_id
                 and a.template_type_id = c.Template_Type_Id
                 and c.template_id = b.template_id
                 and a.template_type_code = '${code}'
                 and c.Org_ID = ${Currents.tenantId}
                 and b.disabled = 0
  `
  return getSql(sql)
}
