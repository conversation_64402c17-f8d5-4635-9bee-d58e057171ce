import { wrapCodeExample } from '@/utils/codeUtils'

// 导入代码
export const importCode = `import { index as SectionRouteConsumable } from '@mh-inpatient-hsd/section-route-consumable'
import '@mh-inpatient-hsd/section-route-consumable/index.css'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-inpatient-hsd/section-route-consumable": "^1.0.0",
    "@mh-inpatient-hsd/util": "^1.0.0"
  }
}`

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <Button type="primary" @click="handleVisibleSectionRouteConsumable">打开病区给药途径绑定弹窗</Button>
  <SectionRouteConsumable 
    ref="sectionRouteConsumableRef" 
    :section-id="sectionId" 
    :section-name="sectionName" 
    @close="handleClose"
  />
</template>

<script setup>
import { index as SectionRouteConsumable } from '@mh-inpatient-hsd/section-route-consumable'
import '@mh-inpatient-hsd/section-route-consumable/index.css'
import { Button, message } from 'ant-design-vue'
import { ref } from 'vue'

const sectionRouteConsumableRef = ref()

// 模拟数据
const sectionId = 40
const sectionName = '内三科住院'

// 打开病区给药途径绑定弹窗
const handleVisibleSectionRouteConsumable = () => {
  sectionRouteConsumableRef.value.open()
}

// 关闭回调
const handleClose = () => {
  message.success('关闭成功')
}
</script>`)
