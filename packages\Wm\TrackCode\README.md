# TrackCode 溯源码组件

溯源码录入组件，用于录入和管理溯源码信息。

## 特性

- 支持通过js方法调用，传入wbSeqid，弹出窗口
- 支持在页面中显示"溯源码(F5)"按钮，点击后打开弹窗
- 支持通过按键盘快捷键（默认F5）打开弹窗
- 支持自定义按钮文本和快捷键
- 基于ant-design-vue的Modal组件实现

## 依赖

- `ant-design-vue`: UI组件库
- `@mh-wm/util`: 工具库，提供API调用

## 安装

```bash
# 安装组件
pnpm publish:component Wm/TrackCode
```

### package.json依赖配置

在项目的package.json中添加以下依赖：

```json
{
  "dependencies": {
    "@mh-wm/track-code": "^1.0.0",
    "@mh-wm/util": "^1.0.0"
  }
}
```

## 使用方法

### 引入组件

```javascript
import { TrackCode } from '@mh-wm/track-code'
```

### 基本用法 - 按钮触发

```vue
<template>
  <!-- 基础用法 - 显示按钮 -->
  <TrackCode :wbSeqid="wbSeqid" @success="handleSuccess" />
</template>

<script setup>
import { TrackCode } from '@mh-wm/track-code'
import { ref } from 'vue'

// 溯源码ID
const wbSeqid = ref('12345')

// 成功回调
const handleSuccess = (data) => {
  console.log('溯源码录入成功', data)
}
</script>
```

### 通过js方法调用

```vue
<template>
  <!-- 不显示按钮，通过js方法调用 -->
  <TrackCode ref="trackCodeRef" :showButton="false" @success="handleSuccess" />

  <!-- 自定义按钮 -->
  <Button @click="openTrackCode">打开溯源码录入</Button>
</template>

<script setup>
import { TrackCode } from '@mh-wm/track-code'
import { Button } from 'ant-design-vue'
import { ref } from 'vue'

// 组件引用
const trackCodeRef = ref()

// 溯源码ID
const wbSeqid = ref('12345')

// 打开溯源码录入窗口
const openTrackCode = () => {
  trackCodeRef.value.open(wbSeqid.value)
}

// 成功回调
const handleSuccess = (data) => {
  console.log('溯源码录入成功', data)
}
</script>
```

## 组件属性

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| buttonText | 按钮文本，为空时自动生成 | string | '' |
| buttonType | 按钮类型 | 'primary' \| 'ghost' \| 'dashed' \| 'link' \| 'text' \| 'default' | 'default' |
| buttonSize | 按钮大小 | 'large' \| 'middle' \| 'small' | 'middle' |
| modalWidth | 对话框宽度 | number \| string | 1200 |
| showButton | 是否显示触发按钮 | boolean | true |
| wbSeqid | 溯源码ID | number \| string | '' |
| hotkey | 绑定的键盘快捷键 | string | 'F5' |

## 组件事件

| 事件名 | 说明 | 回调参数 |
| --- | --- | --- |
| success | 溯源码录入成功时触发 | (data: any) => void |
| cancel | 取消溯源码录入时触发 | () => void |

## 组件方法

| 方法名 | 说明 | 参数 |
| --- | --- | --- |
| open | 打开溯源码录入窗口 | (wbSeqid?: number \| string) => void |
| close | 关闭溯源码录入窗口 | () => void |
