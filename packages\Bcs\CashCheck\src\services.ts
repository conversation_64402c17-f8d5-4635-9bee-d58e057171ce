import { Data } from '@idmy/core'
import { CashActionTypeEnum } from '@mh-bcs/util'





export async function undo(checkId: number) {
  return await http.post('/api/bcs/cashcheck/undo', { checkId })

}

export async function getLastCashCheck(userId: number) {
  return await http.post('/api/bcs/cashcheck/getLast', { userId })

}

export async function pageCashCheck(params: Data) {
  params = { ...params, ...params.params }
  params.sidx = 't_cash_check.Time_Checked'
  params.order = 'desc'
  return await http.post('/api/bcs/cashcheck/page', params)
}

export async function findGroupPaymentByCheckId(checkId: number) {
  return await http.post('/api/bcs/cashcheckpayment/findGroupPaymentByCheckId', { checkId })

}

export async function findPaidByFeeTypeId(cashTypeIds: number[], feeTypeId: number, actionType: CashActionTypeEnum, checkId: number | undefined, endAt: any) {
  return await http.post('/api/bcs/billdetail/findPaidByFeeTypeId', { cashTypeIds, feeTypeId, actionType, checkId, endAt })

}

export async function countCheckOfCash(cashTypeIds: number[], checkId: number | undefined, endAt: any) {
  return await http.post('/api/bcs/report/countCheckOfCash', { cashTypeIds, checkId, endAt })

}

export async function countCheckOfFeeType(cashTypeIds: number[], checkId: number | undefined, endAt: any) {
  return await http.post('/api/bcs/report/countCheckOfFeeType', { cashTypeIds, checkId, endAt })

}
