<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue'
import { Table, Button, Col, Badge, FormItem, Modal, Row, Select, SelectOption, message } from 'ant-design-vue'
import { ArtSelect } from '@mh-inpatient-hsd/selector'
import { findBeTemplateLsBySectionIdApi, findBeTemplateItemPageApi } from "@mh-hip/util"
import { sectionStockAndWmStockApi } from "@mh-inpatient-hsd/util"

const props = defineProps({
  sectionId: {
    type: Number,
    default: null
  },
  sectionName: {
    type: String,
    default: null
  },
  genderId: {
    type: Number,
    default: null
  }
})

const columns: TableColumnType[] = [
  {
    title: '条目编号',
    dataIndex: 'artId',
    align: 'center',
    width: 90
  },
  {
    title: '模板',
    dataIndex: 'templateName',
    align: 'center',
    width: 90
  },
  {
    title: '条目',
    dataIndex: 'artDesc',
    align: 'left',
    ellipsis: true
  },
  {
    title: '默认数量',
    dataIndex: 'initialTotal',
    align: 'center',
    width: 90
  },
  {
    title: '默认选择',
    dataIndex: 'defaultSelectedBool',
    align: 'center',
    width: 80
  },
  {
    title: '病区库存',
    dataIndex: 'sectionStock',
    align: 'center',
    width: 100
  },
  {
    title: '药房库存',
    dataIndex: 'wmStock',
    align: 'center',
    width: 100
  },
  {
    title: '单位类型',
    dataIndex: 'unitType',
    align: 'center',
    width: 100
  },
]

const artSelectRef = ref<InstanceType<typeof ArtSelect>>()
const emit = defineEmits(['choose'])
const visible = ref(false)
const title = ref()
const dataSource = ref<any>([])
const selectedKeys = ref<any>([])
const selectedRows = ref<any>([])
const templateLs = ref<any>([])
const searchFormModel = reactive<any>({})

const pagination = reactive<PaginationProps>({
  pageSize: 100,
  pageSizeOptions: ['100', '200', '500', '1000'],
  current: 1,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: total => `共：${total} 条`,
  onChange(current, pageSize) {
    pagination.pageSize = pageSize
    pagination.current = current
    queryPage()
  },
})

const open = () => {
  title.value = (props.sectionName ? props.sectionName : '') + ' 病区常用计费条目'
  searchFormModel.templateId = undefined
  nextTick(() => {
    clearAll()
  })
  getTemplateLs()
  handleSearch()
  visible.value = true
}

const getTemplateLs = () => {
  findBeTemplateLsBySectionIdApi({ sectionId: props.sectionId, genderId: props.genderId }).then((data: any) => {
    if (data) {
      templateLs.value = data
    }
  })
}

const handleSearch = () => {
  pagination.current = 1
  queryPage()
}

async function queryPage() {
  if (!props.sectionId || !searchFormModel.templateId) {
    return
  }
  try {
    selectedKeys.value = []
    selectedRows.value = []
    const { list, total } = await findBeTemplateItemPageApi({
      sectionId: props.sectionId,
      genderId: props.genderId,
      templateId: searchFormModel.templateId,
      sidx: 't_be_template_item.Template_ID, ifnull(t_be_template_item.Default_Selected, 0) desc, t_be_template_item.Display_Order',
      order: 'asc',
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
    })
    dataSource.value = list ?? []
    pagination.total = total ?? 0
    dataSource.value.forEach((item: any) => {
      if (item.defaultSelected === 1) {
        selectedKeys.value.push(item.keyStr)
        selectedRows.value.push(item)
      }
    })
    const artIdLs = dataSource.value.map((item: any) => item.artId)
    const stockParams = {
      sectionId: props.sectionId,
      artIdLs: artIdLs
    }
    sectionStockAndWmStockApi(stockParams).then((data: any) => {
      if (data) {
        dataSource.value.forEach((item: any) => {
          const existArt = data.find((art: any) => art.artId === item.artId)
          item.totalPacks = existArt ? existArt.totalPacks : 0
          item.totalCells = existArt ? existArt.totalCells : 0
          item.wmTotalPacks = existArt ? existArt.wmTotalPacks : 0
          item.wmTotalCells = existArt ? existArt.wmTotalCells : 0
        })
      }
    })
  } catch (e) {
    console.log(e)
  }
}

const clearAll = () => {
  selectedKeys.value = []
  selectedRows.value = []
  dataSource.value = []
  templateLs.value = []
  searchFormModel.templateId = undefined
  artSelectRef.value?.init()
}

function handleCancel() {
  visible.value = false
}

const customRow = (record) => {
  return {
    onClick: () => {
      if (selectedKeys.value.includes(record.keyStr)) {
        selectedKeys.value = selectedKeys.value.filter(item => item !== record.keyStr)
        selectedRows.value = selectedRows.value.filter(item => item.keyStr !== record.keyStr)
      } else {
        selectedKeys.value.push(record.keyStr)
        selectedRows.value.push(record)
      }
    },
    onDblclick: () => {
      selectedKeys.value = [record.keyStr]
      selectedRows.value = [record]
      handleChoose()
    }
  }
}

const rowSelection = computed(() => {
  return {
    selectedRowKeys: selectedKeys.value,
    onChange: onSelectChange,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE
    ],
  }
})

const onSelectChange = (keys: any, rows: any) => {
  selectedKeys.value = keys
  selectedRows.value = rows
}

const handleChoose = async () => {
  if (!selectedRows.value || selectedRows.value.length === 0) {
    message.warning('请选择条目')
    return
  }
  emit('choose', selectedRows.value)
  handleCancel()
}

function filterOption (input: any, option: any) {
  return (
    option.text.toLowerCase().indexOf(input.toLowerCase()) >= 0
  )
}

defineExpose({
  open
})
</script>

<template>
  <Modal v-model:open="visible" :title="title" width="1100px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Button type="dashed" @click="handleCancel">
        关闭
      </Button>
      <Button @click="handleChoose" type="primary">
        选中
      </Button>
    </template>
    <Row>
      <Col flex="300px">
        <Form-item label="模板" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          <Select
            show-search
            placeholder="请选择给药途径"
            style="width: 100%"
            :filter-option="filterOption"
            v-model:value="searchFormModel.templateId"
            @change="handleSearch"
            :allow-clear="true">
            <Select-option v-for="item in templateLs" :key="item.templateId" :value="item.templateId" :text="item.templateName">
              {{ item.templateName }}
            </Select-option>
          </Select>
        </Form-item>
      </Col>
    </Row>
    <Table
      :rowKey="(record: any) => record.keyStr"
      :columns="columns"
      :data-source="dataSource"
      :has-surely="false"
      :has-page="true"
      :pagination="pagination"
      :custom-row="customRow"
      :scroll="{ y: 'calc(100vh - 200px)'}"
      :row-selection="rowSelection">
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.dataIndex === 'artDesc'">
          <div>
            <span class="art-name">{{ record.artName }}</span><span>{{ record.artSpec }}</span>
          </div>
          <div v-if="record.producer">{{ record.producer }}</div>
        </template>
        <template v-else-if="column.dataIndex === 'defaultSelectedBool'">
          <Badge v-if="record.defaultSelected === 1" status="success" />
        </template>
        <template v-else-if="column.dataIndex === 'unitType'">
          <span v-if="record.unitType === 1">{{ record.cellUnit }}(拆零)</span>
          <span v-else-if="record.unitType === 2">{{ record.packUnit }}(包装)</span>
        </template>
        <template v-else-if="column.dataIndex === 'sectionStock'">
          <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span>
          <span v-else-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>
        </template>
        <template v-else-if="column.dataIndex === 'wmStock'">
          <span v-if="record.wmTotalPacks">{{ record.wmTotalPacks }}{{ record.packUnit }}</span>
          <span v-else-if="record.wmTotalCells">{{ record.wmTotalCells }}{{ record.cellUnit }}</span>
        </template>
      </template>
    </Table>
  </Modal>
</template>

<style lang="less" scoped>
.art-name {
  font-weight: bold;
}
</style>
