const fs = require('fs')
const path = require('path')

// 将驼峰命名转换为短横线命名
function pascalToKebab(pascal) {
  return pascal.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()
}

// 读取目录并生成路径配置
function generatePaths() {
  const packages = path.join(__dirname, '../packages')
  const dirs = fs.readdirSync(packages).filter(file => fs.statSync(path.join(packages, file)).isDirectory())

  const paths = {
    '@/*': ['./src/*'],
    '@c/*': ['./src/components/*'],
    '@v/*': ['./src/views/*'],
  }

  // 为每个目录生成对应的路径配置
  dirs.forEach(dir => {
    const kebabName = pascalToKebab(dir)
    const dirPath = path.join(packages, dir)

    // 检查是否包含子目录
    const hasSubDirs = fs.readdirSync(dirPath).some(file => fs.statSync(path.join(dirPath, file)).isDirectory())

    if (hasSubDirs && dir !== 'dist') {
      // 如果是包含子目录的一级目录（除了Base目录），遍历其子目录
      const subDirs = fs.readdirSync(dirPath).filter(file => fs.statSync(path.join(dirPath, file)).isDirectory())
      subDirs.forEach(subDir => {
        const subKebabName = pascalToKebab(subDir)
        paths[`@mh-${kebabName.toLowerCase()}/${subKebabName}`] = [`./packages/${dir}/${subDir}`]
      })
    } else {
      paths[`@mh/${kebabName}`] = [`./packages/${dir}`]
    }
  })

  return paths
}

// 更新 tsconfig.json
function updateTsConfig() {
  const tsConfigPath = path.join(__dirname, '../tsconfig.json')
  const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'))

  tsConfig.compilerOptions.paths = generatePaths()
  console.info(tsConfig.compilerOptions.paths)

  fs.writeFileSync(tsConfigPath, JSON.stringify(tsConfig, null, 2), 'utf8')
}

updateTsConfig()
