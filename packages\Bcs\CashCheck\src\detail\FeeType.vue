<script lang="ts" setup>
import { Data, Format, Modal, useLoading } from '@idmy/core'
import { CashTypeEnum, countCheckOfFeeType } from '@mh-bcs/util'
import { add } from 'lodash-es'
import BillDetail from '~/components/cash-check/detail/BillDetail.vue'

const { cashTypeIds, checkId } = defineProps({
  cashTypeIds: { type: Array as PropType<CashTypeEnum[]>, required: true },
  checkId: { type: Number },
})

function openBillDetail(title: string, feeTypeId: number) {
  Modal.d.open({
    component: BillDetail,
    title,
    position: 'right',
    maskClosable: true,
    width: 3,
    props: {
      feeTypeId,
      checkId,
      cashTypeIds,
    },
  })
}

const feeTypes = reactive<Data[][]>([]) // 使用二维数组来存储每行的数据

const totalAmt = ref(0)
const [onLoad] = useLoading(async (col = 4) => {
  feeTypes.splice(0)
  totalAmt.value = 0
  const arr = await countCheckOfFeeType(cashTypeIds, checkId)

  let currentRow = [] // 用于存储当前行的数据

  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    totalAmt.value = add(item.amount, totalAmt.value)
    currentRow.push(item)
    if (currentRow.length === col) {
      feeTypes.push(currentRow)
      currentRow = []
    }
  }
  if (currentRow.length > 0) {
    while (currentRow.length < col) {
      currentRow.push(null)
    }
    feeTypes.push(currentRow)
  }
}, true)

watch(
  () => cashTypeIds,
  () => onLoad()
)
</script>

<template>
  <template v-if="feeTypes.length">
    <table class="print-table" style="margin-top: -1px">
      <tr>
        <th class="tac" colspan="8">费用类别统计</th>
      </tr>
      <tr v-for="row in feeTypes">
        <template v-for="item in row">
          <td style="width: 12.5%">
            {{ item?.feeTypeName }}
          </td>
          <td style="width: 12.5%">
            <Format :value="item?.amount" class="color-black" component="a" type="Currency" @click="openBillDetail(item?.feeTypeName, item?.feeTypeId)" />
          </td>
        </template>
      </tr>
      <tr>
        <td colspan="8">
          <div class="ml-8px mr-8px" flex justify-between>
            <strong>费用类别合计</strong>
            <Format :value="totalAmt" type="Currency" value-class="b" />
          </div>
        </td>
      </tr>
    </table>
  </template>
</template>
