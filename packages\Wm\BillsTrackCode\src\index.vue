<!--多单据追溯码扫描组件-->
<script setup lang="ts">
import { reactive, ref, computed, nextTick, h } from 'vue'
import type { Rule } from 'ant-design-vue/es/form'

import {
  Button,
  Modal,
  Form,
  Input,
  InputNumber,
  Checkbox,
  Row,
  Col,
  Table,
  Space,
  message,
  Switch
} from 'ant-design-vue'

// 导入API，使用原始API名称
import {
  wmBillDetailListApi,
  trackCodeListByWbSeqIdsApi,
  trackCodeAddCodeApi as addCodeApi,
  trackCodeDelCodeApi as delCodeApi,
  trackCodeDelCodeByWbSeqIdApi as delAllCodeApi,
  trackCodeAllConfirmByWbSeqIdApi
} from '@mh-wm/util'
import {
  articleSetNoTrackCodeApi,
  articleClearNoTrackCodeApi,
  articleSetDisassembledApi,
  articleClearDisassembledApi
} from '@mh-hip/util'

const props = defineProps({
  // 对话框宽度
  modalWidth: {
    type: [Number, String],
    default: 1400,
  },
  // 是否只添加识别追溯码（默认为false，即未识别的追溯码也会绑定到当前选中行）
  onlyAddRecognizedTrackCode: {
    type: Boolean,
    default: false,
  },
  // 是否启用"只添加识别追溯码"复选框（默认为false，即不显示复选框）
  enableOnlyAddRecognizedTrackCodeOption: {
    type: Boolean,
    default: false,
  },
  // 是否启用自动关闭提示（默认为true，即显示自动关闭提示）
  enableAutoClosePrompt: {
    type: Boolean,
    default: false,
  },
  // 是否为查看模式
  isView: {
    type: Boolean,
    default: false,
  },
  // 是否校验追溯码为20位纯数字（默认为true）
  checkTrackCode20Num: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['success', 'cancel'])
const modalVisible = ref(false)

// 添加缺失的ref引用
const titleRef = ref('多单据追溯码采集')
const trackCodeRef = ref()
const isDisassembledRef = ref()
const totalCellsRef = ref()
const btnOkRef = ref()
const isViewRef = computed(() => props.isView)

// 库存变化类型(101-调拨入库,102-调拨出库,103-报溢入库,104-报损出库,105-销毁出库,106-其他入库,107-其他出库,108-采购入库,109-采购退出,110-赠品入库,111-赠品退出)
const inventoryChangeTypeLs = reactive([
  {
    label: '调拨入库',
    value: 101
  }, {
    label: '调拨出库',
    value: 102
  }, {
    label: '报溢入库',
    value: 103
  }, {
    label: '报损出库',
    value: 104
  }, {
    label: '销毁出库',
    value: 105
  }, {
    label: '其他入库',
    value: 106
  }, {
    label: '其他出库',
    value: 107
  }, {
    label: '采购入库',
    value: 108
  }, {
    label: '采购退出',
    value: 109
  }, {
    label: '赠品入库',
    value: 110
  }, {
    label: '赠品退出',
    value: 111
  }
])

const formRef = ref()
const formState = reactive({
  wbSeqIdLs: [] as number[], // 多个单据ID列表
  title: '多单据追溯码采集',
  inventoryChangeType: undefined
})

const rules: Record<string, Rule[]> = {
  bsnType: [
    { required: true, message: '请选择业务类型', trigger: 'change' }
  ],
  custId: [
    { required: true, message: '请选择往来单位', trigger: 'change' }
  ],
  custName: [
    { required: true, message: '请选择往来单位', trigger: 'change' }
  ],
  bsnDate: [
    { required: true, message: '请选择业务日期', trigger: 'change' }
  ]
}

interface tableModel {
  loading?: boolean,
  columns: any[],
  dataSource: any[],
  allTrackCodes?: any[], // 存储所有追溯码数据
  selectedRowKeys?: any[],
  rowSelection?: any,
  pagination: any,
  loadDataSource?: () => Promise<void>,
  selectRow?: (record: any) => void,
  customRow?: (record: any) => any
}

const currentAggregatedKey = ref() // 当前选中的汇总键
const currentArtInfo = reactive({
  artName: '',
  artSpec: '',
  producer: '',
  noTrackCode: false,
  artIsDisassembled: false, // 拆零上报
  trackCodePrefix: '', // 追溯码前缀（前7位）
  scannedPrefixes: new Set(), // 已扫描的追溯码前缀集合
  totalPacks: 0, // 整包数量
  totalCells: 0, // 拆零数量
  collectedPacks: 0, // 已采整包数
  collectedCells: 0, // 已采拆零数
  packCells: 0, // 每包拆零数量
  sourceDetails: [] as any[] // 原始明细列表
})

// 追溯码明细表格
const trackCodeDetailTableModel = reactive<tableModel>({
  loading: false,
  columns: [{
    title: '药品追溯码',
    dataIndex: 'trackCode',
    width: 180,
    resizable: true,
    customRender: ({ text, record }) => {
      // 获取追溯码前7位
      const prefix = text.substring(0, 7)

      // 获取当前药品的所有前缀
      const drugPrefixes = new Set()
      trackCodeDetailTableModel.allTrackCodes?.forEach((item: any) => {
        if (item.aggregatedKey === record.aggregatedKey) {
          drugPrefixes.add(item.trackCode.substring(0, 7))
        }
      })

      // 根据前缀数量确定颜色
      let color = '#52c41a' // 绿色（默认，只有一种前缀）
      if (drugPrefixes.size === 2) {
        color = '#faad14' // 橙色（两种前缀）
      } else if (drugPrefixes.size > 2) {
        color = '#f5222d' // 红色（三种及以上前缀）
      }

      // 创建带颜色的前缀和普通的后缀
      return h('span', {}, [
        h('span', { style: { color, fontWeight: 'bold' } }, prefix),
        h('span', {}, text.substring(7))
      ])
    }
  }, {
    title: '拆零',
    dataIndex: 'isDisassembled',
    width: 60,
    align: 'center'
  }, {
    title: '拆零数量',
    dataIndex: 'totalCells',
    width: 100,
    align: 'center',
    customRender: ({ text }) => {
      // 拆零数量为0不显示
      return text > 0 ? text : ''
    }
  // }, {
  //   title: '单据流水号',
  //   dataIndex: 'wbSeqid',
  //   width: 120,
  //   align: 'right'
  // }, {
  //   title: '行号',
  //   dataIndex: 'lineNo',
  //   width: 80,
  //   align: 'right'
  }, {
    title: '操作',
    dataIndex: 'action',
    width: 60,
    fixed: 'right',
    align: 'center'
  }],
  dataSource: [],
  allTrackCodes: [], // 存储所有追溯码数据
  loadDataSource: async () => {
    trackCodeDetailTableModel.loading = true
    try {
      const response = await trackCodeListByWbSeqIdsApi({
        wbSeqids: formState.wbSeqIdLs,
        validatorUsed: true
      })
      // 存储所有追溯码数据，并添加汇总键
      trackCodeDetailTableModel.allTrackCodes = (response as any[] || []).map((item: any) => ({
        ...item,
        aggregatedKey: generateAggregatedKey(item)
      }))
      // 通过计算属性过滤当前药品的追溯码
      updateFilteredTrackCodes()
      trackCodeDetailTableModel.selectedRowKeys = []
      trackCodeDetailTableModel.loading = false

      // 更新追溯码缓存
      updateTrackCodeCache()
    } catch (err) {
      console.log(err)
      trackCodeDetailTableModel.loading = false
    }
  },
  pagination: false
})

// 生成汇总键的函数
const generateAggregatedKey = (item: any) => {
  const isDisassembledType = (item.totalCells > 0) || (item.totalPacks > 0 && item.artIsDisassembled)
  const type = isDisassembledType ? 'cells' : 'packs'
  return `${item.artId}_${type}`
}

// 单据明细汇总后的数据
const aggregatedBillDetails = computed(() => {
  // 依赖更新触发器，确保响应式更新
  updateTrigger.value

  const aggregationMap = new Map()

  // 遍历所有原始明细进行汇总
  billDetailTableModel.dataSource.forEach((detail: any) => {
    const aggregatedKey = generateAggregatedKey(detail)

    if (aggregationMap.has(aggregatedKey)) {
      const existing = aggregationMap.get(aggregatedKey)
      // 累加数量
      existing.aggregatedTotalPacks += detail.totalPacks || 0
      existing.aggregatedTotalCells += detail.totalCells || 0
      existing.collectedPacks += detail.collectedPacks || 0
      existing.collectedCells += detail.collectedCells || 0
      // 添加到原始明细列表
      existing.sourceDetails.push(detail)
    } else {
      // 创建新的汇总项
      const isDisassembledType = (detail.totalCells > 0) || (detail.totalPacks > 0 && detail.artIsDisassembled)
      aggregationMap.set(aggregatedKey, {
        ...detail,
        aggregatedKey,
        aggregatedTotalPacks: detail.totalPacks || 0,
        aggregatedTotalCells: detail.totalCells || 0,
        sourceDetails: [detail],
        isDisassembledType
      })
    }
  })

  // 计算每个汇总项的追溯码采集数量
  if (trackCodeDetailTableModel.allTrackCodes && trackCodeDetailTableModel.allTrackCodes.length > 0) {
    // 重置所有汇总项的采集数量
    Array.from(aggregationMap.values()).forEach((drug: any) => {
      drug.collectedPacks = 0
      drug.collectedCells = 0
      drug.collectedCount = 0
      drug.hasMultiplePrefixes = false
    })

    // 根据追溯码更新汇总项的采集数量
    const drugPrefixes: Record<string, Set<string>> = {}

    trackCodeDetailTableModel.allTrackCodes.forEach((item: any) => {
      const aggregatedKey = item.aggregatedKey
      const drug = Array.from(aggregationMap.values()).find((d: any) => d.aggregatedKey === aggregatedKey)

      if (drug) {
        // 更新整包数和拆零数
        if (item.isDisassembled) {
          // 拆零模式
          drug.collectedCells += (item.totalCells || 0)
        } else {
          // 整包模式
          drug.collectedPacks += 1
        }

        // 兼容旧版本，保留collectedCount字段
        drug.collectedCount += 1

        // 记录汇总项的追溯码前缀
        const prefix = item.trackCode.substring(0, 7)
        if (!drugPrefixes[aggregatedKey]) {
          drugPrefixes[aggregatedKey] = new Set()
        }
        drugPrefixes[aggregatedKey].add(prefix)
      }
    })

    // 检测前缀冲突：同一前缀被绑定给不同药品
    const prefixToAggregatedKeys: Record<string, Set<string>> = {}

    // 收集所有前缀对应的药品
    trackCodeDetailTableModel.allTrackCodes.forEach((item: any) => {
      const prefix = item.trackCode.substring(0, 7)
      if (!prefixToAggregatedKeys[prefix]) {
        prefixToAggregatedKeys[prefix] = new Set()
      }
      prefixToAggregatedKeys[prefix].add(item.aggregatedKey)
    })

    // 检查每个药品是否有前缀冲突
    Array.from(aggregationMap.values()).forEach((drug: any) => {
      drug.hasMultiplePrefixes = false
      drug.conflictInfo = null

      // 获取该药品使用的所有前缀
      const drugTrackCodes = trackCodeDetailTableModel.allTrackCodes.filter(
        (item: any) => item.aggregatedKey === drug.aggregatedKey
      )

      const conflictPrefixes: string[] = []
      const conflictDrugs: string[] = []

      drugTrackCodes.forEach((trackCode: any) => {
        const prefix = trackCode.trackCode.substring(0, 7)
        const aggregatedKeysWithSamePrefix = prefixToAggregatedKeys[prefix]

        if (aggregatedKeysWithSamePrefix && aggregatedKeysWithSamePrefix.size > 1) {
          // 该前缀被多个药品使用
          conflictPrefixes.push(prefix)

          // 找到使用相同前缀的其他药品
          aggregatedKeysWithSamePrefix.forEach(otherKey => {
            if (otherKey !== drug.aggregatedKey) {
              const otherDrug = Array.from(aggregationMap.values()).find((d: any) => d.aggregatedKey === otherKey)
              if (otherDrug && !conflictDrugs.includes(otherDrug.artName)) {
                conflictDrugs.push(otherDrug.artName)
              }
            }
          })
        }
      })

      if (conflictPrefixes.length > 0) {
        drug.hasMultiplePrefixes = true
        drug.conflictInfo = {
          prefixes: [...new Set(conflictPrefixes)],
          drugs: conflictDrugs
        }
      }
    })
  }

  // 转换为数组并排序
  const result = Array.from(aggregationMap.values()).sort((a, b) => {
    // 计算拆零数（包含被标记为拆零上报的药品）
    const getTotalCellsForSort = (item: any) => {
      let totalCells = item.aggregatedTotalCells || 0
      // 如果被标记为拆零上报，将整包数按包装规格转换为拆零数
      if (item.artIsDisassembled && item.aggregatedTotalPacks > 0 && item.packCells > 0) {
        totalCells += item.aggregatedTotalPacks * item.packCells
      }
      return totalCells
    }

    const aTotalCells = getTotalCellsForSort(a)
    const bTotalCells = getTotalCellsForSort(b)

    // 按拆零数从大到小排序
    if (aTotalCells !== bTotalCells) {
      return bTotalCells - aTotalCells
    }

    // 按整包数从大到小排序
    if (a.aggregatedTotalPacks !== b.aggregatedTotalPacks) {
      return (b.aggregatedTotalPacks || 0) - (a.aggregatedTotalPacks || 0)
    }

    // 按已采整包数从大到小排序
    if (a.aggregatedCollectedPacks !== b.aggregatedCollectedPacks) {
      return (b.aggregatedCollectedPacks || 0) - (a.aggregatedCollectedPacks || 0)
    }

    // 按已采拆零数从大到小排序
    if (a.aggregatedCollectedCells !== b.aggregatedCollectedCells) {
      return (b.aggregatedCollectedCells || 0) - (a.aggregatedCollectedCells || 0)
    }

    // 按品名|规格|厂家排序
    const aName = `${a.artName || ''}|${a.artSpec || ''}|${a.producer || ''}`
    const bName = `${b.artName || ''}|${b.artSpec || ''}|${b.producer || ''}`
    if (aName !== bName) {
      return aName.localeCompare(bName)
    }

    return 0
  })

  return result
})

// 过滤后的药品列表
const filteredBillDetails = computed(() => {
  const filterText = drugFilterText.value.trim().toUpperCase()
  if (!filterText) {
    return aggregatedBillDetails.value
  }

  return aggregatedBillDetails.value.filter((item: any) => {
    // 药品名称匹配
    const artNameMatch = item.artName && item.artName.toUpperCase().includes(filterText)
    // qsCode1匹配
    const qsCode1Match = item.qsCode1 && item.qsCode1.toUpperCase().includes(filterText)
    // qsCode2匹配
    const qsCode2Match = item.qsCode2 && item.qsCode2.toUpperCase().includes(filterText)

    return artNameMatch || qsCode1Match || qsCode2Match
  })
})

// 单据明细表格（显示汇总后的数据）
const billDetailTableModel = reactive<tableModel>({
  loading: false,
  columns: [{
    title: '品名|规格|厂家',
    dataIndex: 'artName',
    width: 300,
    fixed: 'left',
    resizable: true,
    sorter: (a: any, b: any) => {
      // 使用qsCode1进行排序，如果没有qsCode1则使用artName
      const aValue = a.qsCode1 || a.artName || ''
      const bValue = b.qsCode1 || b.artName || ''
      return aValue.localeCompare(bValue)
    }
  }, {
    title: '整包数',
    dataIndex: 'aggregatedTotalPacks',
    width: 70,
    fixed: 'left',
    align: 'right',
    sorter: (a: any, b: any) => (a.aggregatedTotalPacks || 0) - (b.aggregatedTotalPacks || 0),
    customRender: ({ text }) => {
      // 整包数为0或不存在时不显示
      return text > 0 ? text : ''
    }
  }, {
    title: '拆零数',
    dataIndex: 'aggregatedTotalCells',
    width: 70,
    fixed: 'left',
    align: 'right',
    defaultSortOrder: 'descend',
    sorter: (a: any, b: any) => {
      // 计算拆零数（包含被标记为拆零上报的药品）
      const getTotalCellsForSort = (item: any) => {
        let totalCells = item.aggregatedTotalCells || 0
        // 如果被标记为拆零上报，将整包数按包装规格转换为拆零数
        if (item.artIsDisassembled && item.aggregatedTotalPacks > 0 && item.packCells > 0) {
          totalCells += item.aggregatedTotalPacks * item.packCells
        }
        return totalCells
      }
      return getTotalCellsForSort(a) - getTotalCellsForSort(b)
    },
    customRender: ({ text }) => {
      // 拆零数为0或不存在时不显示
      return text > 0 ? text : ''
    }
  }, {
    title: '已采整包',
    dataIndex: 'collectedPacks',
    width: 80,
    fixed: 'left',
    align: 'right',
    sorter: (a: any, b: any) => (a.collectedPacks || 0) - (b.collectedPacks || 0),
    customRender: ({ text, record }) => {
      // 整包数为0或不存在时不显示已采整包
      if (record.aggregatedTotalPacks <= 0) return ''

      // 已采数量为0时不显示
      if (!text || text <= 0) return ''

      // 计算是否已完成采集
      const isPacksComplete = record.aggregatedTotalPacks > 0 ? (record.collectedPacks >= record.aggregatedTotalPacks) : true

      // 创建带动画效果的数字
      return h('span', {
        class: 'collected-count',
        style: {
          color: isPacksComplete ? '#52c41a' : 'inherit',
          animation: record.animatePacks ? 'count-change 0.5s' : 'none'
        }
      }, text)
    }
  }, {
    title: '已采拆零',
    dataIndex: 'collectedCells',
    width: 80,
    fixed: 'left',
    align: 'right',
    sorter: (a: any, b: any) => (a.collectedCells || 0) - (b.collectedCells || 0),
    customRender: ({ text, record }) => {
      // 拆零数为0且没有设置拆零上报时不显示已采拆零
      if (record.aggregatedTotalCells <= 0 && !record.artIsDisassembled) return ''

      // 已采数量为0时不显示
      if (!text || text <= 0) return ''

      // 计算是否已完成采集
      let isCellsComplete = false

      // 如果设置了拆零上报标志，只要有拆零采集就算完成
      if (record.artIsDisassembled) {
        isCellsComplete = record.collectedCells > 0
      } else {
        // 否则按正常逻辑检查
        isCellsComplete = record.aggregatedTotalCells > 0 ? (record.collectedCells >= record.aggregatedTotalCells) : true
      }

      // 创建带动画效果的数字
      return h('span', {
        class: 'collected-count',
        style: {
          color: isCellsComplete ? '#52c41a' : 'inherit',
          animation: record.animateCells ? 'count-change 0.5s' : 'none'
        }
      }, text)
    }
  }, {
    title: '采集结果',
    dataIndex: 'collectStatus',
    width: 80,
    fixed: 'left',
    align: 'center',
    customRender: ({ record }) => {
      // 如果设置为无追溯码，直接显示无追溯码状态
      if (record.noTrackCode) {
        return h('span', { style: { color: '#722ed1', fontWeight: 'bold' } }, '无追溯码')
      }

      // 如果设置了拆零上报标志
      if (record.artIsDisassembled) {
        // 如果是拆零上报且totalPacks>0，则要求拆零数合计等于totalPacks
        if (record.aggregatedTotalPacks > 0 && record.packCells === 1) {
          if (record.collectedCells >= record.aggregatedTotalPacks) {
            return h('span', { style: { color: '#52c41a' } }, '✓ 拆零上报')
          } else if (record.collectedCells > 0) {
            return h('span', { style: { color: '#1890ff' } }, `拆零上报中(${record.collectedCells}/${record.aggregatedTotalPacks})`)
          } else {
            return h('span', { style: { color: '#1890ff' } }, '待拆零上报')
          }
        } else {
          // 否则只要有拆零数量即可
          if (record.collectedCells > 0) {
            return h('span', { style: { color: '#52c41a' } }, '✓ 拆零上报')
          } else {
            return h('span', { style: { color: '#1890ff' } }, '待拆零上报')
          }
        }
      }

      // 计算是否已完成采集
      const isPacksComplete = record.aggregatedTotalPacks > 0 ? (record.collectedPacks >= record.aggregatedTotalPacks) : true
      const isCellsComplete = record.aggregatedTotalCells > 0 ? (record.collectedCells >= record.aggregatedTotalCells) : true
      const isComplete = isPacksComplete && isCellsComplete

      // 计算是否有采集
      const hasCollected = (record.collectedPacks > 0 || record.collectedCells > 0)

      // 未采集
      if (!hasCollected) {
        return h('span', { style: { color: '#999' } }, '未采集')
      }

      // 采集完且正常
      if (isComplete && !record.hasMultiplePrefixes) {
        return h('span', { style: { color: '#52c41a' } }, '✓ 已完成')
      }

      // 采集中
      if (!isComplete && !record.hasMultiplePrefixes) {
        return h('span', { style: { color: '#1890ff' } }, '采集中')
      }

      // 采集异常（超量或有多个前缀）
      const isOverCollected = (record.aggregatedTotalPacks > 0 && record.collectedPacks > record.aggregatedTotalPacks) ||
                             (record.aggregatedTotalCells > 0 && record.collectedCells > record.aggregatedTotalCells)

      if (isOverCollected || record.hasMultiplePrefixes) {
        // 创建带提示的异常状态
        let tooltipTitle = ''
        if (record.hasMultiplePrefixes && record.conflictInfo) {
          // 使用新的冲突信息
          const { prefixes, drugs } = record.conflictInfo
          const prefixText = prefixes.join('、')
          const drugText = drugs.join('、')
          tooltipTitle = `药品标识码异常：前缀${prefixText}与${drugText}雷同`
        } else if (record.hasMultiplePrefixes) {
          tooltipTitle = '该药品采集了多种不同前缀的追溯码，请检查'
        } else {
          tooltipTitle = '采集数量超过了应采数量，请检查'
        }

        return h(
          'Tooltip',
          {
            title: tooltipTitle
          },
          [h('span', { style: { color: '#faad14', cursor: 'pointer' } }, '⚠ 异常')]
        )
      }

      return null
    }
  }, {
    title: '生产批号',
    dataIndex: 'batchNo',
    width: 110,
    resizable: true
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 100,
    resizable: true,
    align: 'center'
  }, {
    title: '单位',
    dataIndex: 'packCells',
    width: 90,
    resizable: true,
    align: 'right'
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 80,
    resizable: true
  }],
  dataSource: [],
  loadDataSource: async () => {
    billDetailTableModel.loading = true
    try {
      // 调用API获取所有单据的明细
      const response = await wmBillDetailListApi({
        wbSeqids: formState.wbSeqIdLs, // 传递数组参数
        setProdNo: true,
      })

      // 确保每个明细都有wbSeqid属性
      const allDetails = (response || []).map((detail: any) => ({
        ...detail,
        // 如果API返回的数据中没有wbSeqid，需要根据业务逻辑补充
        // 这里假设API已经返回了正确的wbSeqid
      }))

      billDetailTableModel.dataSource = allDetails
      billDetailTableModel.selectedRowKeys = []

      // 自动选择第一个非无追溯码的汇总项
      const aggregated = aggregatedBillDetails.value
      if (aggregated.length > 0) {
        const firstValidItem = aggregated.find(p => !p.noTrackCode)
        if (firstValidItem) {
          billDetailTableModel.selectRow(firstValidItem)
        }
      }
      billDetailTableModel.loading = false
    } catch (err) {
      console.log(err)
      billDetailTableModel.loading = false
    }
  },
  selectedRowKeys: [],
  rowSelection: computed(() => {
    return {
      type: 'radio',
      selectedRowKeys: billDetailTableModel.selectedRowKeys,
      onChange: (selectedRowKeys: any) => {
        billDetailTableModel.selectedRowKeys = selectedRowKeys
      },
    }
  }),
  selectRow: (record) => {
    // 重置追溯码前缀信息
    currentArtInfo.trackCodePrefix = ''
    currentArtInfo.scannedPrefixes.clear()

    // 更新当前药品信息
    const {
      artName, artSpec, producer, noTrackCode,
      artIsDisassembled = false,
      aggregatedTotalPacks, aggregatedTotalCells,
      collectedPacks, collectedCells, packCells,
      sourceDetails, aggregatedKey
    } = record

    // 设置当前汇总键
    currentAggregatedKey.value = aggregatedKey

    // 明确设置每个属性
    currentArtInfo.artName = artName
    currentArtInfo.artSpec = artSpec
    currentArtInfo.producer = producer
    currentArtInfo.noTrackCode = noTrackCode
    currentArtInfo.artIsDisassembled = artIsDisassembled
    currentArtInfo.totalPacks = aggregatedTotalPacks
    currentArtInfo.totalCells = aggregatedTotalCells
    currentArtInfo.collectedPacks = collectedPacks
    currentArtInfo.collectedCells = collectedCells
    currentArtInfo.packCells = packCells
    currentArtInfo.sourceDetails = sourceDetails

    // 添加日志，帮助调试
    console.log('选中汇总行数据:', {
      aggregatedKey,
      artName,
      artIsDisassembled,
      sourceDetails: sourceDetails.length,
      currentArtInfo: { ...currentArtInfo }
    })

    // 更新选中行
    const selectedRowKeys = [...billDetailTableModel.selectedRowKeys]
    if (!selectedRowKeys.includes(aggregatedKey)) {
      selectedRowKeys.splice(0, selectedRowKeys.length)
      selectedRowKeys.push(aggregatedKey)
      billDetailTableModel.selectedRowKeys = selectedRowKeys
    }

    // 使用计算属性过滤追溯码
    updateFilteredTrackCodes()

    // 处理拆零情况
    searchFormModel.totalCells = 0
    if (aggregatedTotalCells > 0 || artIsDisassembled) {
      searchFormModel.isDisassembled = true

      // 特殊情况：totalPacks>0，packCells=1且设置了拆零上报时，使用totalPacks减去已采集数量
      if (artIsDisassembled && aggregatedTotalPacks > 0 && packCells === 1) {
        const remainingPacks = aggregatedTotalPacks - (collectedCells || 0)
        let defaultCells = remainingPacks > 0 ? remainingPacks : aggregatedTotalPacks

        // 对于拆零类药品，如果packCells>1，默认最大数值是packCells而不是需求量
        // 需求量不到packCells就显示需求量，需求量超出packCells显示packCells
        if (packCells > 1) {
          defaultCells = Math.min(defaultCells, packCells)
        }

        searchFormModel.totalCells = defaultCells
      }
      // 如果有拆零数量，则计算剩余的拆零数量
      else if (aggregatedTotalCells > 0) {
        const remainingCells = aggregatedTotalCells - (collectedCells || 0)
        let defaultCells = remainingCells > 0 ? remainingCells : aggregatedTotalCells

        // 对于拆零类药品，如果packCells>1，默认最大数值是packCells而不是需求量
        // 需求量不到packCells就显示需求量，需求量超出packCells显示packCells
        if (packCells > 1) {
          defaultCells = Math.min(defaultCells, packCells)
        }

        searchFormModel.totalCells = defaultCells
      } else {
        // 如果packCells>1，默认使用packCells，否则使用1
        searchFormModel.totalCells = packCells > 1 ? packCells : 1
      }

      nextTick(() => {
        trackCodeRef.value?.focus()
      })
    } else {
      searchFormModel.isDisassembled = false
      trackCodeRef.value?.focus()
    }
  },
  customRow: (record) => {
    return {
      onClick: () => {
        billDetailTableModel.selectRow(record)
      },
    }
  },
  pagination: false
})



// 计算是否有任何追溯码被采集
const hasAnyTrackCodes = computed(() => {
  return trackCodeDetailTableModel.allTrackCodes && trackCodeDetailTableModel.allTrackCodes.length > 0
})

// 更新过滤后的追溯码数据
const updateFilteredTrackCodes = () => {
  if (currentAggregatedKey.value) {
    // 过滤当前汇总键的追溯码
    if (trackCodeDetailTableModel.allTrackCodes && trackCodeDetailTableModel.allTrackCodes.length > 0) {
      const filteredCodes = trackCodeDetailTableModel.allTrackCodes.filter(
        (item: any) => item.aggregatedKey === currentAggregatedKey.value
      )

      // 排序：已使用的追溯码优先显示在最上方
      trackCodeDetailTableModel.dataSource = filteredCodes.sort((a: any, b: any) => {
        // 已使用的排在前面
        if (a.used && !b.used) return -1
        if (!a.used && b.used) return 1

        // 如果使用状态相同，按追溯码字符串排序
        return a.trackCode.localeCompare(b.trackCode)
      })

      // 计算已采集的拆零数量总和
      let collectedCellsSum = 0
      trackCodeDetailTableModel.dataSource.forEach((item: any) => {
        if (item.isDisassembled) {
          collectedCellsSum += (item.totalCells || 0)
        }
      })

      // 更新汇总数据的已采集拆零数量
      const aggregated = aggregatedBillDetails.value
      const currentAggregated = aggregated.find((item: any) => item.aggregatedKey === currentAggregatedKey.value)
      if (currentAggregated) {
        currentAggregated.collectedCells = collectedCellsSum
      }
    } else {
      trackCodeDetailTableModel.dataSource = []
    }
  } else {
    trackCodeDetailTableModel.dataSource = []
  }
}

// 更新追溯码缓存
const updateTrackCodeCache = () => {
  // 清空缓存
  trackCodeCache.clear()

  // 将所有追溯码添加到缓存
  if (trackCodeDetailTableModel.allTrackCodes) {
    trackCodeDetailTableModel.allTrackCodes.forEach((item: any) => {
      trackCodeCache.add(item.trackCode, item.aggregatedKey, item.artName)
    })
  }
}

// 更新药品的采集结果和已采数量
const updateCollectedCounts = () => {
  // 触发响应式更新，让aggregatedBillDetails重新计算
  updateTrigger.value++
}

// 更新拆零数量输入框（智能计算剩余数量）
const updateTotalCellsInput = () => {
  if (!currentAggregatedKey.value) return

  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)

  if (!currentArt) return

  // 只有在拆零模式下才更新拆零数量
  if (searchFormModel.isDisassembled) {
    // 特殊情况：totalPacks>0，packCells=1且设置了拆零上报时，使用totalPacks减去已采集数量
    if (currentArt.artIsDisassembled && currentArt.aggregatedTotalPacks > 0 && currentArt.packCells === 1) {
      const remainingPacks = currentArt.aggregatedTotalPacks - (currentArt.collectedCells || 0)
      let defaultCells = remainingPacks > 0 ? remainingPacks : 1

      // 对于拆零类药品，如果packCells>1，默认最大数值是packCells而不是需求量
      // 需求量不到packCells就显示需求量，需求量超出packCells显示packCells
      if (currentArt.packCells > 1) {
        defaultCells = Math.min(defaultCells, currentArt.packCells)
      }

      searchFormModel.totalCells = defaultCells
    }
    // 如果有拆零数量，则计算剩余的拆零数量
    else if (currentArt.aggregatedTotalCells > 0) {
      const remainingCells = currentArt.aggregatedTotalCells - (currentArt.collectedCells || 0)
      let defaultCells = remainingCells > 0 ? remainingCells : 1

      // 对于拆零类药品，如果packCells>1，默认最大数值是packCells而不是需求量
      // 需求量不到packCells就显示需求量，需求量超出packCells显示packCells
      if (currentArt.packCells > 1) {
        defaultCells = Math.min(defaultCells, currentArt.packCells)
      }

      searchFormModel.totalCells = defaultCells
    } else {
      // 如果packCells>1，默认使用packCells，否则使用1
      searchFormModel.totalCells = currentArt.packCells > 1 ? currentArt.packCells : 1
    }
  }
}

// 追溯码缓存，用于存储所有已扫描的追溯码
const trackCodeCache = {
  // 存储格式: { [trackCode]: { aggregatedKey, artName } }
  cache: {} as Record<string, { aggregatedKey: string, artName: string }>,

  // 添加追溯码
  add(trackCode: string, aggregatedKey: string, artName: string) {
    this.cache[trackCode] = { aggregatedKey, artName }
  },

  // 删除追溯码
  remove(trackCode: string) {
    delete this.cache[trackCode]
  },

  // 获取所有追溯码
  getAll() {
    return this.cache
  },

  // 检查追溯码是否存在
  has(trackCode: string) {
    return !!this.cache[trackCode]
  },

  // 清空缓存
  clear() {
    this.cache = {}
  }
}

// 追溯码处理队列
const trackCodeQueue = reactive({
  queue: [] as string[],
  processing: false,

  // 添加追溯码到队列
  add(trackCode: string) {
    this.queue.push(trackCode)
    this.processQueue()
  },

  // 处理队列
  async processQueue() {
    if (this.processing || this.queue.length === 0) return

    this.processing = true
    const trackCode = this.queue.shift()

    try {
      await processTrackCode(trackCode as string)
    } catch (error) {
      console.log('处理追溯码出错:', error)
    } finally {
      this.processing = false
      this.processQueue() // 处理下一个
    }
  }
})

const init = async (wbSeqIdLs: number[]) => {
  titleRef.value = '多单据追溯码采集'
  currentAggregatedKey.value = null

  // 重置currentArtInfo
  currentArtInfo.artName = ''
  currentArtInfo.artSpec = ''
  currentArtInfo.producer = ''
  currentArtInfo.noTrackCode = false
  currentArtInfo.artIsDisassembled = false
  currentArtInfo.trackCodePrefix = ''
  currentArtInfo.scannedPrefixes.clear()
  currentArtInfo.totalPacks = 0
  currentArtInfo.totalCells = 0
  currentArtInfo.collectedPacks = 0
  currentArtInfo.collectedCells = 0
  currentArtInfo.sourceDetails = []

  // 清空追溯码缓存
  trackCodeCache.clear()

  // 重置searchFormModel
  searchFormModel.isDisassembled = false
  searchFormModel.totalCells = 0
  searchFormModel.trackCode = ''
  searchFormModel.onlyAddRecognized = props.onlyAddRecognizedTrackCode

  // 初始化formState
  formState.wbSeqIdLs = wbSeqIdLs

  // 注意：多单据模式不需要调用infoApi

  billDetailTableModel.dataSource = []
  trackCodeDetailTableModel.dataSource = []
  await billDetailTableModel.loadDataSource()
  await trackCodeDetailTableModel.loadDataSource()

  // 更新追溯码缓存
  updateTrackCodeCache()

  // 更新药品的采集结果和已采数量
  updateCollectedCounts()

  // 显示对话框
  modalVisible.value = true

  // 自动聚焦到追溯码输入框
  nextTick(() => {
    trackCodeRef.value?.focus()
  })
}

const searchFormModel = reactive({
  isDisassembled: false,
  totalCells: 0,
  trackCode: '',
  onlyAddRecognized: false // 是否只添加识别追溯码
})

// 药品过滤状态
const drugFilterText = ref('')

// 响应式更新触发器
const updateTrigger = ref(0)

// 处理追溯码的核心函数
const processTrackCode = async (trackCode: string) => {
  if (billDetailTableModel.selectedRowKeys.length === 0) {
    message.error('请选择要采集的药品')
    return
  }

  // 验证追溯码格式
  if (props.checkTrackCode20Num) {
    // 20位纯数字校验
    if (trackCode.length !== 20) {
      message.warning('追溯码应为20位数字')
      return
    }
    if (!/^\d{20}$/.test(trackCode)) {
      message.warning('追溯码应为20位纯数字')
      return
    }
  } else {
    // 原有的19-27位校验逻辑
    if (trackCode.length < 19 || trackCode.length > 27) {
      message.warning('追溯码长度应在19-27位之间，请检查')
      return
    }
  }

  // 提取追溯码前7位
  const prefix = trackCode.substring(0, 7)

  // 检查追溯码是否已经扫描过（当前汇总项）
  const isTrackCodeAlreadyScanned = trackCodeDetailTableModel.dataSource.some(
    item => item.trackCode === trackCode
  )

  if (isTrackCodeAlreadyScanned) {
    message.warning(`追溯码 ${trackCode} 已经扫描过，请勿重复扫描`)
    return
  }

  // 获取所有已扫描的追溯码（全局缓存）
  const allScannedTrackCodes = trackCodeCache.getAll()

  // 检查追溯码是否已被其他汇总项使用
  if (allScannedTrackCodes[trackCode] && allScannedTrackCodes[trackCode].aggregatedKey !== currentAggregatedKey.value) {
    const aggregated = aggregatedBillDetails.value
    const usedDrug = aggregated.find(
      item => item.aggregatedKey === allScannedTrackCodes[trackCode].aggregatedKey
    )

    if (usedDrug) {
      message.warning(`追溯码 ${trackCode} 已被药品"${usedDrug.artName}"使用，请检查`)
      return
    }
  }

  // 获取当前汇总项信息
  const aggregated = aggregatedBillDetails.value

  // 智能匹配药品
  const matchedDrugs = findMatchingDrugs(prefix)

  // 如果只匹配到一种药品，自动选择该药品
  if (matchedDrugs.length === 1) {
    // 如果匹配的药品不是当前选中的药品，自动切换
    if (matchedDrugs[0].aggregatedKey !== currentAggregatedKey.value) {
      // 选择匹配的药品
      billDetailTableModel.selectRow(matchedDrugs[0])
      console.log(`追溯码前7位 ${prefix} 匹配到药品 "${matchedDrugs[0].artName}"，已自动选择`)
    }
  } else if (matchedDrugs.length > 1) {
    // 如果匹配到多种药品，给出详细的雷同信息
    const drugNames = matchedDrugs.map(drug => drug.artName).join('、')
    message.warning(`药品标识码（前七位）${prefix} 与以下药品雷同：${drugNames}`)

    // 标记所有匹配的药品为有多前缀问题
    matchedDrugs.forEach(drug => {
      drug.hasMultiplePrefixes = true
    })

    // 检查当前选中的药品是否在匹配列表中
    const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
    if (currentArt) {
      // 标记当前药品为有多前缀问题
      currentArt.hasMultiplePrefixes = true
    }
  } else if (matchedDrugs.length === 0) {
    // 如果没有匹配到任何药品
    if (searchFormModel.onlyAddRecognized) {
      // 设置了只添加识别追溯码，则不绑定
      message.warning(`追溯码前7位 ${prefix} 未匹配到任何药品，已跳过`)
      return
    }
    // 如果没有勾选"只添加识别追溯码"，则允许录入，不给出提醒
  }

  // 获取当前汇总项信息（可能已经被上面的代码更新）
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) {
    message.error('未找到当前选中的药品')
    return
  }

  // 拆零数量提醒和packCells处理
  if (searchFormModel.isDisassembled && currentArt.packCells > 1) {
    // 如果当前拆零数量超过packCells，给出提醒
    if (searchFormModel.totalCells > currentArt.packCells) {
      const confirmed = await new Promise((resolve) => {
        Modal.confirm({
          title: '拆零数量提醒',
          content: `当前药品包装规格为${currentArt.packCells}支/盒，建议单次绑定不超过${currentArt.packCells}支。当前设置为${searchFormModel.totalCells}支，是否继续？`,
          okText: '继续绑定',
          cancelText: '调整数量',
          onOk: () => resolve(true),
          onCancel: () => resolve(false)
        })
      })

      if (!confirmed) {
        // 用户选择调整数量，自动设置为packCells
        searchFormModel.totalCells = currentArt.packCells
        return
      }
    }
  }

  // 如果是第一次扫描该药品的追溯码，记录前缀
  if (!currentArtInfo.trackCodePrefix && !currentArtInfo.scannedPrefixes.size) {
    currentArtInfo.trackCodePrefix = prefix
    currentArtInfo.scannedPrefixes.add(prefix)
  }
  // 检查追溯码前7位与药品匹配
  else if (currentArtInfo.trackCodePrefix && prefix !== currentArtInfo.trackCodePrefix) {
    // 记录不同的前缀
    currentArtInfo.scannedPrefixes.add(prefix)

    // 根据前缀数量确定提示级别
    if (currentArtInfo.scannedPrefixes.size === 2) {
      message.warning(`检测到第二种追溯码前缀(${prefix})，与之前的前缀(${currentArtInfo.trackCodePrefix})不同，请注意检查`)
    } else if (currentArtInfo.scannedPrefixes.size > 2) {
      message.error(`检测到多种追溯码前缀，当前前缀(${prefix})，请特别注意检查`)
    }

    // 不阻止继续扫描，只做提醒
  }

  // 智能分配追溯码到具体明细
  const allocateResult = allocateTrackCodeToDetails(currentArt, searchFormModel.isDisassembled, searchFormModel.totalCells)
  if (!allocateResult.success) {
    message.error(allocateResult.message)
    return
  }

  // 添加追溯码
  try {
    // 根据分配结果调用API
    for (const allocation of allocateResult.allocations) {
      await addCodeApi({
        wbSeqid: allocation.wbSeqid,
        lineNo: allocation.lineNo,
        trackCode: trackCode,
        isDisassembled: searchFormModel.isDisassembled ? 1 : 0,
        totalCells: allocation.allocatedCells
      })
    }

    // 添加动画效果到当前药品
    if (searchFormModel.isDisassembled) {
      // 添加拆零动画效果
      currentArt.animateCells = true
      setTimeout(() => {
        currentArt.animateCells = false
      }, 500)
    } else {
      // 添加整包动画效果
      currentArt.animatePacks = true
      setTimeout(() => {
        currentArt.animatePacks = false
      }, 500)
    }

    // 添加到追溯码缓存
    trackCodeCache.add(trackCode, currentAggregatedKey.value, currentArt.artName)

    // 为每个分配结果创建追溯码记录
    for (const allocation of allocateResult.allocations) {
      const newTrackCode = {
        wbSeqid: allocation.wbSeqid,
        lineNo: allocation.lineNo,
        trackCode: trackCode,
        isDisassembled: searchFormModel.isDisassembled ? 1 : 0,
        totalCells: searchFormModel.isDisassembled ? allocation.allocatedCells : 0,
        artName: currentArt.artName,
        artSpec: currentArt.artSpec,
        producer: currentArt.producer,
        aggregatedKey: currentAggregatedKey.value
      }

      // 添加到追溯码列表
      if (trackCodeDetailTableModel.allTrackCodes) {
        trackCodeDetailTableModel.allTrackCodes.push(newTrackCode)
      } else {
        trackCodeDetailTableModel.allTrackCodes = [newTrackCode]
      }
    }

    // 更新过滤后的追溯码数据
    updateFilteredTrackCodes()

    // 重新计算所有药品的采集数量和状态
    updateCollectedCounts()

    // 更新当前药品的拆零数量输入框（智能计算剩余数量）
    updateTotalCellsInput()

    // 强制更新左侧表格显示（触发响应式更新）
    nextTick(() => {
      // 触发左侧表格的重新渲染，确保动画效果和数据更新
      const aggregated = aggregatedBillDetails.value
      // 通过修改数组引用来触发响应式更新
      aggregated.splice(0, 0)
    })

    // 检查是否需要自动切换到下一种药品
    checkAndSwitchToNextDrug()

    // 清空追溯码输入框
    searchFormModel.trackCode = ''

    // 重新聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    console.log('追溯码添加成功:', trackCode)
  } catch (error) {
    console.error('添加追溯码失败:', error)
    message.error('添加追溯码失败，请重试')
  }
}

// 智能分配追溯码到具体明细
const allocateTrackCodeToDetails = (currentArt: any, isDisassembled: boolean, totalCells: number) => {
  if (!currentArt || !currentArt.sourceDetails || currentArt.sourceDetails.length === 0) {
    return {
      success: false,
      message: '未找到原始明细数据',
      allocations: []
    }
  }

  const allocations: Array<{
    wbSeqid: number
    lineNo: number
    allocatedCells: number
    detail: any
  }> = []

  if (!isDisassembled) {
    // 整包模式：选择第一个有整包数量的明细
    const packDetail = currentArt.sourceDetails.find((detail: any) => (detail.totalPacks || 0) > 0)
    if (!packDetail) {
      return {
        success: false,
        message: '未找到可绑定整包的明细',
        allocations: []
      }
    }

    allocations.push({
      wbSeqid: packDetail.wbSeqid,
      lineNo: packDetail.lineNo,
      allocatedCells: 0, // 整包模式不需要拆零数
      detail: packDetail
    })
  } else {
    // 拆零模式：智能分配到各个明细
    let remainingCells = totalCells

    // 获取每个明细的已采集数量
    const detailCollectedMap = new Map()
    if (trackCodeDetailTableModel.allTrackCodes) {
      trackCodeDetailTableModel.allTrackCodes.forEach((trackCode: any) => {
        if (trackCode.aggregatedKey === currentArt.aggregatedKey && trackCode.isDisassembled) {
          const key = `${trackCode.wbSeqid}_${trackCode.lineNo}`
          const current = detailCollectedMap.get(key) || 0
          detailCollectedMap.set(key, current + (trackCode.totalCells || 0))
        }
      })
    }

    // 按优先级排序明细：优先分配给有拆零数量或被标记为拆零上报的明细
    const sortedDetails = currentArt.sourceDetails
      .filter((detail: any) => {
        // 有拆零数量的明细
        if ((detail.totalCells || 0) > 0) return true

        // 被标记为拆零上报且有整包数量的明细（如totalPacks=1, artIsDisassembled=1）
        if (detail.artIsDisassembled && (detail.totalPacks || 0) > 0) return true

        return false
      })
      .sort((a: any, b: any) => {
        const aKey = `${a.wbSeqid}_${a.lineNo}`
        const bKey = `${b.wbSeqid}_${b.lineNo}`
        const aCollected = detailCollectedMap.get(aKey) || 0
        const bCollected = detailCollectedMap.get(bKey) || 0

        // 计算剩余需求量：对于拆零上报的整包药品，需求量等于整包数量
        let aRemaining: number, bRemaining: number

        if (a.artIsDisassembled && (a.totalPacks || 0) > 0 && (a.totalCells || 0) === 0) {
          // 拆零上报的整包药品：需求量 = 整包数量 - 已采集拆零数量
          aRemaining = (a.totalPacks || 0) - aCollected
        } else {
          // 普通拆零药品：需求量 = 拆零数量 - 已采集拆零数量
          aRemaining = (a.totalCells || 0) - aCollected
        }

        if (b.artIsDisassembled && (b.totalPacks || 0) > 0 && (b.totalCells || 0) === 0) {
          // 拆零上报的整包药品：需求量 = 整包数量 - 已采集拆零数量
          bRemaining = (b.totalPacks || 0) - bCollected
        } else {
          // 普通拆零药品：需求量 = 拆零数量 - 已采集拆零数量
          bRemaining = (b.totalCells || 0) - bCollected
        }

        // 优先分配给剩余数量多的明细
        return bRemaining - aRemaining
      })

    if (sortedDetails.length === 0) {
      return {
        success: false,
        message: '未找到可绑定拆零的明细',
        allocations: []
      }
    }

    // 智能分配
    for (const detail of sortedDetails) {
      if (remainingCells <= 0) break

      const detailKey = `${detail.wbSeqid}_${detail.lineNo}`
      const collected = detailCollectedMap.get(detailKey) || 0

      // 计算需求量：对于拆零上报的整包药品，需求量等于整包数量
      let needed: number
      if (detail.artIsDisassembled && (detail.totalPacks || 0) > 0 && (detail.totalCells || 0) === 0) {
        // 拆零上报的整包药品：需求量 = 整包数量 - 已采集拆零数量
        needed = (detail.totalPacks || 0) - collected
      } else {
        // 普通拆零药品：需求量 = 拆零数量 - 已采集拆零数量
        needed = (detail.totalCells || 0) - collected
      }

      if (needed > 0) {
        const allocatedToThis = Math.min(remainingCells, needed)

        allocations.push({
          wbSeqid: detail.wbSeqid,
          lineNo: detail.lineNo,
          allocatedCells: allocatedToThis,
          detail: detail
        })

        remainingCells -= allocatedToThis
      }
    }

    // 如果还有剩余数量，分配给第一个明细（超量分配）
    if (remainingCells > 0 && allocations.length > 0) {
      allocations[0].allocatedCells += remainingCells
    }
  }

  return {
    success: true,
    message: '',
    allocations: allocations
  }
}

// 查找匹配的药品
const findMatchingDrugs = (prefix: string) => {
  const aggregated = aggregatedBillDetails.value
  return aggregated.filter((drug: any) => {
    // 检查药品的品种唯一码列表是否包含该前缀
    if (!drug.prodNoLs || !Array.isArray(drug.prodNoLs)) return false

    // 将prodNoLs中的数字转换为字符串进行比较
    return drug.prodNoLs.some((prodNo: any) => String(prodNo) === prefix)
  })
}

// 检查并切换到下一种药品
const checkAndSwitchToNextDrug = () => {
  // 使用排序后的数组（即表格当前显示的数组）
  const sortedAggregated = filteredBillDetails.value
  const currentArt = sortedAggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)

  if (!currentArt) return

  // 检查当前药品是否已完成采集
  let isCurrentComplete = false

  if (currentArt.artIsDisassembled) {
    // 拆零上报模式
    if (currentArt.aggregatedTotalPacks > 0 && currentArt.packCells === 1) {
      isCurrentComplete = currentArt.collectedCells >= currentArt.aggregatedTotalPacks
    } else {
      isCurrentComplete = currentArt.collectedCells > 0
    }
  } else {
    // 正常模式
    const isPacksComplete = currentArt.aggregatedTotalPacks > 0 ? (currentArt.collectedPacks >= currentArt.aggregatedTotalPacks) : true
    const isCellsComplete = currentArt.aggregatedTotalCells > 0 ? (currentArt.collectedCells >= currentArt.aggregatedTotalCells) : true
    isCurrentComplete = isPacksComplete && isCellsComplete
  }

  if (isCurrentComplete) {
    // 找到当前药品在排序后数组中的位置
    const currentIndex = sortedAggregated.findIndex(item => item.aggregatedKey === currentAggregatedKey.value)

    if (currentIndex === -1) return

    // 从当前位置的下一个开始查找未完成的药品
    let nextDrug = null
    for (let i = currentIndex + 1; i < sortedAggregated.length; i++) {
      const drug = sortedAggregated[i]

      if (drug.noTrackCode) continue // 跳过无追溯码的药品

      // 检查是否已完成
      let isDrugComplete = false
      if (drug.artIsDisassembled) {
        if (drug.aggregatedTotalPacks > 0 && drug.packCells === 1) {
          isDrugComplete = drug.collectedCells >= drug.aggregatedTotalPacks
        } else {
          isDrugComplete = drug.collectedCells > 0
        }
      } else {
        const isPacksComplete = drug.aggregatedTotalPacks > 0 ? (drug.collectedPacks >= drug.aggregatedTotalPacks) : true
        const isCellsComplete = drug.aggregatedTotalCells > 0 ? (drug.collectedCells >= drug.aggregatedTotalCells) : true
        isDrugComplete = isPacksComplete && isCellsComplete
      }

      if (!isDrugComplete) {
        nextDrug = drug
        break
      }
    }

    if (nextDrug) {
      console.log(`当前药品已完成，自动切换到下一种药品: ${nextDrug.artName}`)
      billDetailTableModel.selectRow(nextDrug)
    } else {
      console.log('所有药品已完成采集')
      // 可以在这里添加全部完成的提示逻辑
    }
  }
}

// 删除追溯码
const onDelArt = async (record: any) => {
  // 检查追溯码是否已被使用
  if (record.used) {
    message.warning('该追溯码已被使用，不允许删除')
    return
  }

  try {
    await delCodeApi({
      wbSeqid: record.wbSeqid,
      lineNo: record.lineNo,
      trackCode: record.trackCode
    })

    // 从追溯码缓存中删除
    trackCodeCache.remove(record.trackCode)

    // 从追溯码列表中删除
    if (trackCodeDetailTableModel.allTrackCodes) {
      const index = trackCodeDetailTableModel.allTrackCodes.findIndex(
        (item: any) => item.trackCode === record.trackCode
      )
      if (index > -1) {
        trackCodeDetailTableModel.allTrackCodes.splice(index, 1)
      }
    }

    // 更新过滤后的追溯码数据
    updateFilteredTrackCodes()

    // 重新计算所有药品的采集数量和状态
    updateCollectedCounts()

    // 更新当前药品的拆零数量输入框（智能计算剩余数量）
    updateTotalCellsInput()

    // 添加动画效果到当前药品
    const aggregated = aggregatedBillDetails.value
    const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
    if (currentArt) {
      if (record.isDisassembled) {
        // 添加拆零动画效果
        currentArt.animateCells = true
        setTimeout(() => {
          currentArt.animateCells = false
        }, 500)
      } else {
        // 添加整包动画效果
        currentArt.animatePacks = true
        setTimeout(() => {
          currentArt.animatePacks = false
        }, 500)
      }
    }

    // 强制更新左侧表格显示（触发响应式更新）
    nextTick(() => {
      // 触发左侧表格的重新渲染，确保动画效果和数据更新
      const aggregated = aggregatedBillDetails.value
      // 通过修改数组引用来触发响应式更新
      aggregated.splice(0, 0)
    })

    // 重新聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    console.log('追溯码删除成功:', record.trackCode)
  } catch (error) {
    console.error('删除追溯码失败:', error)
    message.error('删除追溯码失败，请重试')
  }
}

// 全部清除追溯码
const onDelAllArt = async () => {
  try {
    await delAllCodeApi({
      wbSeqids: formState.wbSeqIdLs
    })

    // 清空追溯码缓存
    trackCodeCache.clear()

    // 清空追溯码列表
    trackCodeDetailTableModel.allTrackCodes = []
    trackCodeDetailTableModel.dataSource = []

    // 重置所有汇总项的采集数量
    const aggregated = aggregatedBillDetails.value
    aggregated.forEach((drug: any) => {
      drug.collectedPacks = 0
      drug.collectedCells = 0
      drug.collectedCount = 0
      drug.hasMultiplePrefixes = false
    })

    // 重新聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    message.success('已清除所有追溯码')
    console.log('全部清除追溯码成功')
  } catch (error) {
    console.error('全部清除追溯码失败:', error)
    message.error('全部清除追溯码失败，请重试')
  }
}

// 设置无追溯码
const onSetNoTrackCode = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 使用新的基于artId的API，避免循环调用
    await articleSetNoTrackCodeApi({
      artId: currentArt.artId
    })

    // 更新本地数据
    currentArt.noTrackCode = true
    currentArtInfo.noTrackCode = true

    message.success('已设置为无追溯码')
  } catch (error) {
    console.error('设置无追溯码失败:', error)
    message.error('设置无追溯码失败，请重试')
  }
}

// 清除无追溯码
const onClearNoTrackCode = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 使用新的基于artId的API，避免循环调用
    await articleClearNoTrackCodeApi({
      artId: currentArt.artId
    })

    // 更新本地数据
    currentArt.noTrackCode = false
    currentArtInfo.noTrackCode = false

    message.success('已设置为有追溯码')
  } catch (error) {
    console.error('清除无追溯码失败:', error)
    message.error('清除无追溯码失败，请重试')
  }
}

// 设置拆零上报
const onSetDisassembled = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 使用新的基于artId的API，避免循环调用
    await articleSetDisassembledApi({
      artId: currentArt.artId
    })

    // 更新本地数据
    currentArt.artIsDisassembled = true
    currentArtInfo.artIsDisassembled = true

    // 自动勾选拆零复选框并设置拆零数量
    searchFormModel.isDisassembled = true
    if (currentArt.aggregatedTotalPacks > 0 && currentArt.packCells === 1) {
      searchFormModel.totalCells = currentArt.aggregatedTotalPacks
    } else {
      searchFormModel.totalCells = 1
    }

    // 聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })

    message.success('已设置为拆零上报')
  } catch (error) {
    console.error('设置拆零上报失败:', error)
    message.error('设置拆零上报失败，请重试')
  }
}

// 清除拆零上报
const onClearDisassembled = async () => {
  const aggregated = aggregatedBillDetails.value
  const currentArt = aggregated.find(item => item.aggregatedKey === currentAggregatedKey.value)
  if (!currentArt) return

  try {
    // 使用新的基于artId的API，避免循环调用
    await articleClearDisassembledApi({
      artId: currentArt.artId
    })

    // 更新本地数据
    currentArt.artIsDisassembled = false
    currentArtInfo.artIsDisassembled = false

    message.success('已取消拆零上报')
  } catch (error) {
    console.error('清除拆零上报失败:', error)
    message.error('清除拆零上报失败，请重试')
  }
}

// 添加追溯码的主要方法
const onAddArt = async () => {
  if (billDetailTableModel.selectedRowKeys.length === 0) {
    message.error('请选择要采集的药品')
    return
  }

  // 获取并清空输入框
  const inputText = searchFormModel.trackCode.trim()
  console.log('输入的追溯码:', inputText)
  searchFormModel.trackCode = ''
  trackCodeRef.value?.focus()

  // 检查是否是多行文本（多个追溯码）
  if (inputText.includes('\n') || inputText.includes('\r')) {
    // 按行分割，处理每一行（处理不同的换行符）
    const trackCodes = inputText.split(/[\r\n]+/).filter(code => code.trim() !== '')
    console.log('检测到多行追溯码:', trackCodes)

    // 将每个追溯码添加到处理队列
    for (const code of trackCodes) {
      trackCodeQueue.add(code.trim())
    }

    // message.info(`已添加${trackCodes.length}个追溯码到处理队列`)
  } else {
    // 单个追溯码，直接添加到队列
    trackCodeQueue.add(inputText)
  }
}

// 处理粘贴事件
const handlePaste = (e: ClipboardEvent) => {
  // 阻止默认粘贴行为
  e.preventDefault()

  // 获取剪贴板数据
  const clipboardData = e.clipboardData
  if (!clipboardData) return

  // 获取文本
  const pastedText = clipboardData.getData('text')
  console.log('粘贴的文本:', pastedText)
  if (!pastedText) return

  // 检查是否包含换行符
  if (pastedText.includes('\n') || pastedText.includes('\r')) {
    // 多行文本，直接处理
    const trackCodes = pastedText.split(/[\r\n]+/).filter(code => code.trim() !== '')
    console.log('检测到多行追溯码:', trackCodes)

    // 将每个追溯码添加到处理队列
    for (const code of trackCodes) {
      trackCodeQueue.add(code.trim())
    }

    // message.info(`已添加${trackCodes.length}个追溯码到处理队列`)
  } else {
    // 单行文本，设置到输入框
    searchFormModel.trackCode = pastedText.trim()
  }
}

const changeIsDisassembled = (e: any) => {
  console.log(e)
  console.log(searchFormModel.isDisassembled)

  // 如果选中拆零，聚焦到拆零数量输入框
  if (searchFormModel.isDisassembled) {
    nextTick(() => {
      totalCellsRef.value?.focus()
    })
  } else {
    // 如果取消拆零，聚焦到追溯码输入框
    nextTick(() => {
      trackCodeRef.value?.focus()
    })
  }
}

// 处理"只添加识别追溯码"复选框的变化
const changeOnlyAddRecognized = (checkedValue: boolean) => {
  console.log('只添加识别追溯码:', checkedValue)
  searchFormModel.onlyAddRecognized = checkedValue
  // 自动聚焦到追溯码输入框
  nextTick(() => {
    trackCodeRef.value?.focus()
  })
}

// 处理追溯码输入
const onTrackCodeInput = (e: any) => {
  const value = e.target.value
  if (value.includes('\n') || value.includes('\r')) {
    // 处理回车事件（扫码枪或高拍仪）
    const trackCode = value.replace(/[\r\n]/g, '').trim()
    if (trackCode) {
      trackCodeQueue.add(trackCode)
    }
    searchFormModel.trackCode = ''
  }
}

// 处理追溯码按键事件
const onTrackCodeKeydown = (e: any) => {
  if (e.key === 'Enter') {
    e.preventDefault()
    const trackCode = searchFormModel.trackCode.trim()
    if (trackCode) {
      trackCodeQueue.add(trackCode)
    }
  }
}

// 确定按钮
const onOk = async () => {
  try {
    // 这里可以添加提交逻辑
    emit('success', formState.wbSeqIdLs)
    modalVisible.value = false
  } catch (error) {
    console.error('提交失败:', error)
    message.error('提交失败，请重试')
  }
}

// 取消按钮
const onCancel = () => {
  emit('cancel')
  modalVisible.value = false
}

// 暴露给父组件的方法
const open = (wbSeqIdLs?: number[]) => {
  if (wbSeqIdLs && wbSeqIdLs.length > 0) {
    init(wbSeqIdLs)
  } else {
    message.error('请提供有效的单据ID列表')
  }
}

const close = () => {
  modalVisible.value = false
}

defineExpose({
  open,
  close
})
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    :title="titleRef"
    :width="props.modalWidth"
    :maskClosable="false"
    :keyboard="false"
    :style="{ top: '20px', maxHeight: 'calc(100vh - 40px)' }"
    @cancel="onCancel"
  >
    <template #footer>
      <Space>
        <Button @click="onCancel">取消</Button>
        <Button type="primary" @click="onOk" ref="btnOkRef">确定</Button>
      </Space>
    </template>

    <div class="bills-track-code-container">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <Row :gutter="1">
          <!-- 左侧：汇总后的药品列表 -->
          <Col :span="16">
            <div class="table-section">
              <div class="table-header">
                <h4>药品汇总</h4>
                <div class="table-header-right">
                  <Input
                    v-model:value="drugFilterText"
                    placeholder="搜索药品名称、助记码..."
                    style="width: 200px; margin-right: 8px;"
                    allowClear
                  />
                  <span class="table-count">共 {{ filteredBillDetails.length }} 项</span>
                </div>
              </div>
              <Table
                :columns="billDetailTableModel.columns"
                :dataSource="filteredBillDetails"
                :loading="billDetailTableModel.loading"
                :pagination="billDetailTableModel.pagination"
                :rowSelection="billDetailTableModel.rowSelection"
                :customRow="billDetailTableModel.customRow"
                :rowKey="(record) => record.aggregatedKey"
                size="small"
                :scroll="{ y: 'calc(100vh - 400px)' }"
                :sortDirections="['descend', 'ascend']"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'artName'">
                  {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}
                </template>
                <template v-if="column.dataIndex === 'packCells'">
                  {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
                </template>
              </template>
            </Table>
            </div>
          </Col>

          <!-- 右侧：追溯码明细 -->
          <Col :span="8">
            <div class="table-section">
              <div class="table-header">
                <h4>追溯码明细</h4>
                <span class="table-count">共 {{ trackCodeDetailTableModel.dataSource.length }} 条</span>
              </div>
              <Table
                :columns="trackCodeDetailTableModel.columns"
                :dataSource="trackCodeDetailTableModel.dataSource"
                :loading="trackCodeDetailTableModel.loading"
                :pagination="trackCodeDetailTableModel.pagination"
                :rowKey="(record) => record.trackCode"
                size="small"
                :scroll="{ y: 'calc(100vh - 400px)' }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'isDisassembled'">
                    <span v-if="record.isDisassembled">是</span>
                    <span v-else>否</span>
                  </template>
                  <template v-else-if="column.dataIndex === 'action'">
                    <!-- 如果追溯码已被使用，显示"已使用"提示 -->
                    <span v-if="record.used" style="color: #52c41a; font-weight: bold;">已使用</span>
                    <!-- 如果追溯码未被使用或未标记，显示删除按钮 -->
                    <Button
                      v-else
                      type="link"
                      size="small"
                      danger
                      @click="onDelArt(record)"
                      :disabled="isViewRef"
                    >
                      删除
                    </Button>
                  </template>
                </template>
              </Table>
            </div>
          </Col>
        </Row>
      </div>

      <!-- 当前选中药品信息和追溯码输入区域 - 固定在底部 -->
      <div class="bills-track-code-input-area">
        <div class="current-art-info">
          <div style="font-size: large;">{{ currentArtInfo.artName }} {{ currentArtInfo.artSpec }} {{ currentArtInfo.producer }}</div>
          <div class="track-code-form-container">
            <Form
                layout="inline"
                :model="searchFormModel"
                v-if="!isViewRef"
                class="track-code-form"
                autocomplete="off"
            >
              <Form.Item label="拆零" name="isDisassembled" v-if="currentArtInfo.totalCells > 0 || currentArtInfo.artIsDisassembled">
                <Checkbox ref="isDisassembledRef" v-model:checked="searchFormModel.isDisassembled" @change="changeIsDisassembled"></Checkbox>
              </Form.Item>
              <Form.Item label="拆零数量" name="totalCells" v-if="(currentArtInfo.totalCells > 0 || currentArtInfo.artIsDisassembled) && searchFormModel.isDisassembled">
                <InputNumber v-model:value="searchFormModel.totalCells" ref="totalCellsRef" @pressEnter="trackCodeRef.focus()" autocomplete="off"/>
              </Form.Item>
              <Form.Item label="追溯码" name="trackCode">
                <Input
                  v-model:value="searchFormModel.trackCode"
                  ref="trackCodeRef"
                  @pressEnter="onAddArt"
                  style="width: 400px"
                  placeholder="请输入或粘贴追溯码，支持多行文本"
                  @paste="handlePaste"
                  autocomplete="off"
                />
              </Form.Item>
              <Form.Item name="onlyAddRecognized" v-if="props.enableOnlyAddRecognizedTrackCodeOption" class="only-add-recognized-item">
                <div class="switch-container">
                  <Switch v-model:checked="searchFormModel.onlyAddRecognized" @change="changeOnlyAddRecognized" />
                  <span class="switch-label">只添加识别追溯码</span>
                </div>
              </Form.Item>
            </Form>
            <div class="action-buttons-container">
              <!-- 全部清除按钮，功能未考虑好，暂时隐藏 -->
<!--              <div class="button-group">-->
<!--                <Button @click="onDelAllArt" :disabled="!hasAnyTrackCodes">全部清除</Button>-->
<!--              </div>-->

              <!-- 拆零上报按钮组 -->
              <div class="button-group" v-if="currentArtInfo.totalPacks > 0 && (!currentArtInfo.packCells || currentArtInfo.packCells === 1)">
                <Button
                  v-if="!currentArtInfo.artIsDisassembled"
                  danger
                  @click="onSetDisassembled"
                  :disabled="!currentAggregatedKey"
                >拆零上报</Button>
                <Button
                  v-if="currentArtInfo.artIsDisassembled"
                  @click="onClearDisassembled"
                  :disabled="!currentAggregatedKey"
                >取消拆零上报</Button>
              </div>

              <!-- 无追溯码按钮组 -->
              <div class="button-group">
                <Button
                  v-if="!currentArtInfo.noTrackCode"
                  @click="onSetNoTrackCode"
                  danger
                  :disabled="!currentAggregatedKey"
                >设置为无追溯码</Button>
                <Button
                  v-if="currentArtInfo.noTrackCode"
                  @click="onClearNoTrackCode"
                  :disabled="!currentAggregatedKey"
                >设置为有追溯码</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.bills-track-code-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
}

.bills-track-code-input-area {
  flex-shrink: 0;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
  margin-top: 20px;
  /* 固定在底部 */
  order: 2;
}

.current-art-info {
  margin-bottom: 8px;
}

.track-code-form-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.track-code-form {
  display: flex;
  flex-wrap: wrap;
  flex: 1;

  :deep(.ant-form-item) {
    margin-right: 16px;
  }

  .only-add-recognized-item {
    margin-left: 30px;
  }

  .switch-container {
    display: flex;
    align-items: center;

    .switch-label {
      margin-left: 8px;
      font-weight: bold;
      color: #1890ff;
    }
  }
}

.action-buttons-container {
  margin-left: 20px;
  display: flex;
  justify-content: flex-end;
  min-width: 240px;

  .button-group {
    margin-left: 10px;

    &:first-child {
      margin-left: 0;
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  /* 确保表格区域在上方 */
  order: 1;
}

.table-section {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  flex-shrink: 0;
}

.table-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.table-header-right {
  display: flex;
  align-items: center;
}

.table-count {
  font-size: 12px;
  color: #666;
}

/* 动画效果 */
@keyframes count-change {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
    color: #52c41a;
  }
  100% {
    transform: scale(1);
  }
}

.collected-count {
  font-weight: bold;
}

/* 表格样式优化 */
:deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
  background-color: #e6f7ff;
}

:deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

/* 输入框样式 */
:deep(.ant-input:focus) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表格容器样式，确保表格能够正确填充空间 */
:deep(.ant-table-wrapper) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.ant-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table-container) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.ant-table-body) {
  flex: 1;
  overflow: auto !important;
}

/* Row和Col的样式调整 */
:deep(.ant-row) {
  flex: 1;
  display: flex;
  overflow: hidden;
}

:deep(.ant-col) {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
