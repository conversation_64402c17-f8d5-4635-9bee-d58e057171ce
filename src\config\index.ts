/**
 * 将 .env 的配置注入到 @idmy/core 中
 */
import { appConfig, base64Encode, httpConfig, loginConfig, rbacConfig } from '@idmy/core'
import { setLicenseKey } from '@surely-vue/table'
import { MD5 as md5 } from 'crypto-js'
// @ts-ignore
import { version } from '../../package.json'
import '@idmy/antd/dist/index.css'
import '@mh-base/core'
import '@surely-vue/table/dist/index.less'


/** httpConfig* */
httpConfig.isCache = false
/** authConfig* */
rbacConfig.root = import.meta.env.VITE_ROOT === 'true'
rbacConfig.permission = import.meta.env.VITE_PERMISSION === 'true'
rbacConfig.role = import.meta.env.VITE_ROLE === 'true'
/** appConfig* */
appConfig.env = import.meta.env.MODE === 'develop.local' ? 'dev' : import.meta.env.MODE
appConfig.security.encrypt = import.meta.env.VITE_SECURITY_ENCRYPT === 'true'

appConfig.security.secret = import.meta.env.VITE_SECURITY_SECRET
appConfig.app.key = import.meta.env.VITE_APP_KEY
appConfig.app.title = import.meta.env.VITE_APP_TITLE
appConfig.app.alias = import.meta.env.VITE_APP_ALIAS ?? ''
appConfig.app.version = parseInt(version.replaceAll(/\D/g, ''))

appConfig.app.keys = {
  oauth: import.meta.env.VITE_APP_URL_oauth,
  idm: import.meta.env.VITE_APP_URL_idm,
  amc: import.meta.env.VITE_APP_URL_amc,
  bcs: import.meta.env.VITE_APP_URL_bcs,
  sys: import.meta.env.VITE_APP_URL_sys,
  hip: import.meta.env.VITE_APP_URL_hip,
  hsd: import.meta.env.VITE_APP_URL_hsd,
  inpatientHsd: import.meta.env.VITE_APP_URL_inpatientHsd,
  wm: import.meta.env.VITE_APP_URL_wm,
  eam: import.meta.env.VITE_APP_URL_eam,
  mi: import.meta.env.VITE_APP_URL_mi,
  miLocal: import.meta.env.VITE_APP_URL_miLocal,
}
/** loginConfig* */
loginConfig.loginUrl = import.meta.env.VITE_LOGIN_URL
/** numberConfig* */
appConfig.number.currencyUnitValue = 1



interface HackLicenseKeyOptions {
  hostname?: string;
}

const hackLicenseKey = (options?: HackLicenseKeyOptions) => {
  const domain = options?.hostname ?? globalThis.location.hostname;
  const key = base64Encode(`ORDER:00001,EXPIRY=33227712000000,DOMAIN=${domain},ULTIMATE=1,KEYVERSION=1`);
  const sign = md5(key).toString().toLowerCase();
  setLicenseKey(`${sign}${key}`);
}
hackLicenseKey()
