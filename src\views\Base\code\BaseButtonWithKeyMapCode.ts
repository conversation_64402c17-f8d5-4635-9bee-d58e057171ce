import { wrapCodeExample } from '@/utils/codeUtils'

// 基础用法
export const basicUsage = wrapCodeExample(`<template>
  <div>
    <!-- 使用KeyMap组件管理快捷键 -->
    <KeyMap
      ref="keyMapRef"
      pageKey="example-page"
      :functionKeys="functionKeys"
      :editable="true"
      @update="handleKeyMapUpdate"
      @reset="handleKeyMapReset"
    />

    <!-- 使用btnKey和pageKey从KeyMap获取快捷键 -->
    <BaseButton
      type="primary"
      btnKey="SAVE_DATA"
      pageKey="example-page"
      @click="handleSave"
    >
      保存数据
    </BaseButton>

    <!-- 使用btnKey和pageKey从KeyMap获取快捷键，同时提供默认快捷键 -->
    <BaseButton
      type="primary"
      btnKey="REFRESH_DATA"
      pageKey="example-page"
      functionKey="F5"
      @click="handleRefresh"
    >
      刷新数据
    </BaseButton>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { BaseButton } from '@mh-base/core'
import KeyMap from '@mh-base/keymap'
import { message } from 'ant-design-vue'

// KeyMap组件引用
const keyMapRef = ref()

// 预定义的功能键配置
const functionKeys = [
  { btnKey: "SAVE_DATA", btnDesc: "保存数据", bindKey: "Ctrl + S" },
  { btnKey: "REFRESH_DATA", btnDesc: "刷新数据", bindKey: "F5" }
]

// 快捷键配置更新回调
const handleKeyMapUpdate = (keyMap) => {
  console.log('快捷键配置已更新:', keyMap)
}

// 快捷键配置重置回调
const handleKeyMapReset = () => {
  console.log('快捷键配置已重置为默认')
}

// 处理保存按钮点击
const handleSave = () => {
  message.success('保存成功')
}

// 处理刷新按钮点击
const handleRefresh = () => {
  message.success('刷新成功')
}
</script>`)

// 隐藏快捷键显示
export const hideKeyUsage = wrapCodeExample(`<template>
  <div>
    <!-- 使用KeyMap组件管理快捷键 -->
    <KeyMap
      ref="keyMapRef"
      pageKey="example-page"
      :functionKeys="functionKeys"
      :editable="true"
    />

    <!-- 显示快捷键 -->
    <BaseButton
      type="primary"
      btnKey="SAVE_DATA"
      pageKey="example-page"
      @click="handleSave"
    >
      保存数据
    </BaseButton>

    <!-- 隐藏快捷键 -->
    <BaseButton
      type="primary"
      btnKey="REFRESH_DATA"
      pageKey="example-page"
      :showFunctionKey="false"
      @click="handleRefresh"
    >
      刷新数据
    </BaseButton>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { BaseButton } from '@mh-base/core'
import KeyMap from '@mh-base/keymap'
import { message } from 'ant-design-vue'

// KeyMap组件引用
const keyMapRef = ref()

// 预定义的功能键配置
const functionKeys = [
  { btnKey: "SAVE_DATA", btnDesc: "保存数据", bindKey: "Ctrl + S" },
  { btnKey: "REFRESH_DATA", btnDesc: "刷新数据", bindKey: "F5" }
]

// 处理保存按钮点击
const handleSave = () => {
  message.success('保存成功')
}

// 处理刷新按钮点击
const handleRefresh = () => {
  message.success('刷新成功')
}
</script>`)

// 导入代码
export const importCode = `import { BaseButton, getBindKey } from '@mh-base/core'
import KeyMap from '@mh-base/keymap'`

// package.json依赖
export const packageJsonCode = `{
  "dependencies": {
    "@mh-base/core": "^1.0.22",
    "@mh-base/keymap": "^1.0.1"
  }
}`
