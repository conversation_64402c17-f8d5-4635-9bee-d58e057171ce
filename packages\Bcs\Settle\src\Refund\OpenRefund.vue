<script lang="ts" setup>
import { cfg, Data } from '@idmy/core'
import Refund from './index.vue'

const { data } = defineProps({
  data: { type: Object as PropType<Data>, required: true },
})

const emit = defineEmits(['ok'])

const [onRefund] = useLoading(async () => {
  Modal.open({
    component: Refund,
    onClose: () => {
      emit('ok')
    },
    props: { redCash: data },
    title: `退费#${data.cashId}【${data.payer}】`,
    width: cfg.setting?.partialRefundDisabled ? 4 : 5,
  })
})
</script>

<template>
  <a v-if="data.status === 'ING' && data.actionType === 'RED'" class="primary" @click="onRefund">继续退费</a>
</template>
