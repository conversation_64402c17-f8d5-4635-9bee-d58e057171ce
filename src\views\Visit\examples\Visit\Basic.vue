<script setup lang="ts">
import { Visit } from '@mh-hsd/visit'
import { Card, Typography, Divider } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref } from 'vue'
import { packageJsonCode, basicUsage, importCode } from '../code/VisitCode'

const { Title, Paragraph } = Typography
const visitId = ref(102)
const orgId = ref(1)
const canRef = ref(false)
const referenceType = ref('')
const referenceData = ref(null)


// 处理引用确认事件
function handleConfirm(refType, value) {
  referenceType.value = refType
  referenceData.value = value

  console.log('引用类型:', refType)
  console.log('引用数据:', value)
}

</script>

<template>
  <Card class="mb-16px">
    <Paragraph>
      该示例展示了诊疗查询组件的基本用法。在实际应用中，您需要引入 <code>@mh-hsd/visit</code> 包并使用其中的 Visit 组件。
    </Paragraph>

    <div class="mb-16px">
      <div><Visit ref="visitRef" :visitId="visitId" :orgId="orgId" :canRef="canRef" @confirm="handleConfirm" /></div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="basicUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
</style>
