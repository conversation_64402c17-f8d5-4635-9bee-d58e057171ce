<script setup lang="ts">
import { ReqComponent } from '@mh-wm/req-component'
import { Card, Typography, Divider, Button, message, Table, Form, Input, Select, DatePicker } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, reactive } from 'vue'
import { formUsage, importCode, packageJsonCode } from '../code/ReqComponentCode'
import dayjs from 'dayjs'

const { Title, Paragraph, Text } = Typography

// 组件引用
const reqComponentRef = ref()

// 科室列表
const deptList = ref([
  { deptCode: '000013', deptName: '西药库' },
  { deptCode: '000014', deptName: '中药库' },
  { deptCode: '000015', deptName: '耗材库' }
])

// 表单数据
const formState = reactive({
  reqNo: 'REQ' + Date.now(),
  deptCode: '000013',
  deptName: '西药库',
  reqUser: '',
  reqDate: dayjs(),
  items: [],
  remark: ''
})

// 表格列定义
const columns = [
  { title: '品种名称', dataIndex: 'artName', key: 'artName', ellipsis: true },
  { title: '规格', dataIndex: 'artSpec', key: 'artSpec', ellipsis: true },
  { title: '生产批号', dataIndex: 'batchNo', key: 'batchNo' },
  { title: '生产日期', dataIndex: 'dateManufactured', key: 'dateManufactured' },
  { title: '有效期至', dataIndex: 'expiry', key: 'expiry' },
  { title: '整包数量', dataIndex: 'totalPacks', key: 'totalPacks' },
  { title: '包装单位', dataIndex: 'packUnit', key: 'packUnit' },
  { 
    title: '操作', 
    key: 'action', 
    width: 80,
    customRender: ({ record }: any) => {
      return h(Button, {
        type: 'link',
        danger: true,
        onClick: () => removeItem(record.id)
      }, { default: () => '删除' })
    }
  }
]

// 科室变更处理
const handleDeptChange = (value: string) => {
  const dept = deptList.value.find(item => item.deptCode === value)
  if (dept) {
    formState.deptName = dept.deptName
  }
}

// 添加品种回调
const handleAddArt = (formData: any) => {
  // 添加唯一ID
  const newItem = {
    id: Date.now(),
    ...formData,
    artName: formData.artData.artName,
    artSpec: formData.artData.artSpec
  }

  // 添加到列表
  formState.items.push(newItem)

  // 提示添加成功
  message.success(`成功添加品种: ${newItem.artName}`)
}

// 删除品种
const removeItem = (id: number) => {
  const index = formState.items.findIndex(item => item.id === id)
  if (index !== -1) {
    formState.items.splice(index, 1)
    message.success('删除成功')
  }
}

// 清空品种列表
const clearItems = () => {
  formState.items = []
  message.success('品种列表已清空')
}

// 验证品种列表
const validateItems = (_rule: any, _value: any) => {
  if (formState.items.length === 0) {
    return Promise.reject('请至少添加一个品种')
  }
  return Promise.resolve()
}

// 提交表单
const handleSubmit = () => {
  if (formState.items.length === 0) {
    message.error('请至少添加一个品种')
    return
  }

  // 构建提交数据
  const submitData = {
    reqNo: formState.reqNo,
    deptCode: formState.deptCode,
    deptName: formState.deptName,
    reqUser: formState.reqUser,
    reqDate: formState.reqDate.format('YYYYMMDD'),
    items: formState.items.map(item => ({
      artId: item.artData.artId,
      artCode: item.artData.artCode,
      artName: item.artData.artName,
      artSpec: item.artData.artSpec,
      batchNo: item.batchNo,
      dateManufactured: item.dateManufactured,
      expiry: item.expiry,
      totalPacks: item.totalPacks,
      packUnit: item.packUnit,
      totalCells: item.totalCells,
      cellUnit: item.cellUnit
    })),
    remark: formState.remark
  }

  // 这里可以添加表单提交逻辑，例如调用API保存数据
  console.log('提交的表单数据:', submitData)
  message.success('申请单提交成功')
}

// 重置表单
const handleReset = () => {
  formState.reqUser = ''
  formState.reqDate = dayjs()
  formState.items = []
  formState.remark = ''
  message.info('表单已重置')
}
</script>

<template>
  <Card title="在表单中使用" class="mb-16px">
    <div class="form-container">
      <Form :model="formState" layout="vertical">
        <Form.Item label="申请单号" name="reqNo">
          <Input v-model:value="formState.reqNo" placeholder="系统自动生成" disabled />
        </Form.Item>

        <Form.Item label="申请科室" name="deptCode" :rules="[{ required: true, message: '请选择申请科室' }]">
          <Select v-model:value="formState.deptCode" @change="handleDeptChange" placeholder="请选择申请科室">
            <Select.Option v-for="dept in deptList" :key="dept.deptCode" :value="dept.deptCode">
              {{ dept.deptName }}
            </Select.Option>
          </Select>
        </Form.Item>

        <div class="form-row">
          <Form.Item label="申请人" name="reqUser" :rules="[{ required: true, message: '请输入申请人' }]" class="form-col">
            <Input v-model:value="formState.reqUser" placeholder="请输入申请人" />
          </Form.Item>
          <Form.Item label="申请日期" name="reqDate" :rules="[{ required: true, message: '请选择申请日期' }]" class="form-col">
            <DatePicker v-model:value="formState.reqDate" style="width: 100%" />
          </Form.Item>
        </div>

        <Form.Item label="品种信息" name="items" :rules="[{ validator: validateItems }]">
          <div class="req-component-wrapper">
            <ReqComponent
              ref="reqComponentRef"
              :deptCode="formState.deptCode"
              @addArt="handleAddArt"
            />
          </div>

          <!-- 已添加品种列表 -->
          <div v-if="formState.items.length > 0" class="items-table">
            <div class="table-header">
              <h3>已添加品种列表</h3>
              <Button type="primary" danger size="small" @click="clearItems">清空列表</Button>
            </div>
            <Table
              :dataSource="formState.items"
              :columns="columns"
              rowKey="id"
              :pagination="false"
              size="small"
            />
          </div>
        </Form.Item>

        <Form.Item label="备注" name="remark">
          <Input.TextArea v-model:value="formState.remark" :rows="3" placeholder="请输入备注信息" />
        </Form.Item>

        <Form.Item>
          <Button type="primary" @click="handleSubmit" style="margin-right: 8px">提交申请</Button>
          <Button @click="handleReset">重置</Button>
        </Form.Item>
      </Form>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue :usage="formUsage" :importCode="importCode" :packageJson="packageJsonCode" />
  </Card>
</template>

<style scoped>
.form-container {
  max-width: 100%;
  margin: 0 auto;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-col {
  flex: 1;
}

.req-component-wrapper {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 16px;
}

.items-table {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
}
</style>
