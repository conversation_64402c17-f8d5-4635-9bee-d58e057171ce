<script setup lang="ts">
 import { Maker } from '@mh-wm/maker'
import { Card, Typography, Divider, Button, message, Table, Space } from 'ant-design-vue'
import CodeDemoVue from '@/views/CodeDemo/Vue.vue'
import { ref, h, computed } from 'vue'
import { basicUsage, importCode, packageJsonCode, publishCommands, buildProcess } from '@/views/Wm/examples/code/MakerCode'

const { Title, Paragraph, Text } = Typography

// 移除了仓库编码参数，组件内部不再需要外部传入

// 已添加的品种列表
const addedItems = ref([])

// 编辑状态管理
const editingKey = ref('')

// Maker组件引用
const makerComponentRef = ref()

// 当前正在表单中编辑的记录ID
const formEditingId = ref('')

// 判断是否在编辑状态
const isEditing = (record: any) => record.id === editingKey.value

// 添加品种回调
const handleAddArt = (formData: any) => {
  console.log('添加品种成功，表单数据：', formData)

  // 如果是编辑状态，更新现有记录
  if (formEditingId.value) {
    const index = addedItems.value.findIndex((item: any) => item.id === formEditingId.value)
    if (index !== -1) {
      addedItems.value[index] = {
        ...addedItems.value[index],
        ...formData,
        id: formEditingId.value // 保持原有ID
      }
      message.success('品种信息更新成功')
      formEditingId.value = '' // 清空编辑状态
      return
    }
  }

  // 新增记录
  const newItem = {
    id: Date.now().toString(),
    ...formData,
    // 从artData中提取品种信息
    artName: formData.artData?.artName || '',
    artSpec: formData.artData?.artSpec || '',
    producer: formData.artData?.producer || '',
  }

  addedItems.value.push(newItem)
  message.success('品种添加成功')
}

// 编辑记录
const edit = (record: any) => {
  // 设置表单编辑状态
  formEditingId.value = record.id

  // 将记录数据回填到表单
  if (makerComponentRef.value) {
    makerComponentRef.value.setFormData(record)
  }

  message.info('请在上方表单中修改品种信息，然后点击添加按钮保存')
}

// 删除记录
const deleteRecord = (record: any) => {
  const index = addedItems.value.findIndex((item: any) => item.id === record.id)
  if (index !== -1) {
    addedItems.value.splice(index, 1)
    message.success('删除成功')
  }
}

// 取消编辑
const cancelEdit = () => {
  formEditingId.value = ''
  if (makerComponentRef.value) {
    makerComponentRef.value.clearFormData()
  }
  message.info('已取消编辑')
}

// 表格列定义
const columns = [
  {
    title: '品名',
    dataIndex: 'artName',
    key: 'artName',
    width: 150,
  },
  {
    title: '规格',
    dataIndex: 'artSpec',
    key: 'artSpec',
    width: 120,
  },
  {
    title: '生产厂家',
    dataIndex: 'producer',
    key: 'producer',
    width: 120,
  },
  {
    title: '原产地',
    dataIndex: 'originPlace',
    key: 'originPlace',
    width: 100,
  },
  {
    title: '生产批号',
    dataIndex: 'batchNo',
    key: 'batchNo',
    width: 120,
  },
  {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    key: 'dateManufactured',
    width: 100,
  },
  {
    title: '有效期至',
    dataIndex: 'expiry',
    key: 'expiry',
    width: 100,
  },
  {
    title: '整包单价',
    dataIndex: 'packPrice',
    key: 'packPrice',
    width: 100,
  },
  {
    title: '整包数量',
    dataIndex: 'totalPacks',
    key: 'totalPacks',
    width: 100,
    customRender: ({ record }: any) => {
      return `${record.totalPacks || 0} ${record.packUnit || ''}`
    }
  },
  {
    title: '拆零数量',
    dataIndex: 'totalCells',
    key: 'totalCells',
    width: 100,
    customRender: ({ record }: any) => {
      if (record.packCells && record.packCells > 1) {
        return `${record.totalCells || 0} ${record.cellUnit || ''}`
      }
      return '-'
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    customRender: ({ record }: any) => {
      return h(Space, {}, [
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => edit(record)
        }, '编辑'),
        h(Button, {
          type: 'link',
          size: 'small',
          onClick: () => copyRecord(record)
        }, '复制'),
        h(Button, {
          type: 'link',
          size: 'small',
          danger: true,
          onClick: () => deleteRecord(record)
        }, '删除')
      ])
    }
  }
]

// 清空所有数据
const clearAll = () => {
  addedItems.value = []
  if (makerComponentRef.value) {
    makerComponentRef.value.clearFormData()
  }
  formEditingId.value = ''
  message.success('已清空所有数据')
}

// 批量删除选中的记录
const selectedRowKeys = ref<string[]>([])
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

const onSelectChange = (keys: string[]) => {
  selectedRowKeys.value = keys
}

const deleteSelected = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择要删除的记录')
    return
  }

  const deleteCount = selectedRowKeys.value.length
  addedItems.value = addedItems.value.filter((item: any) =>
    !selectedRowKeys.value.includes(item.id)
  )
  selectedRowKeys.value = []
  message.success(`已删除 ${deleteCount} 条记录`)
}

// 复制记录
const copyRecord = (record: any) => {
  const copiedRecord = {
    ...record,
    id: Date.now().toString(), // 生成新的ID
    batchNo: record.batchNo + '_copy', // 修改批号避免重复
  }

  addedItems.value.push(copiedRecord)
  message.success('记录复制成功')
}

// 快速设置测试数据
const setTestData = () => {
  const testData = [
    {
      id: 'test1',
      artData: {
        artId: 1001,
        artName: '阿莫西林胶囊',
        artSpec: '0.25g*24粒',
        producer: '哈药集团制药总厂',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 24,
        splittable: 1
      },
      artName: '阿莫西林胶囊',
      artSpec: '0.25g*24粒',
      producer: '哈药集团制药总厂',
      originPlace: '中国',
      batchNo: 'B20240101',
      dateManufactured: '20240101',
      expiry: '20261231',
      packPrice: 15.50,
      totalPacks: 10,
      totalCells: 5,
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 24,
      splittable: 1
    },
    {
      id: 'test2',
      artData: {
        artId: 1002,
        artName: '头孢克肟胶囊',
        artSpec: '0.1g*12粒',
        producer: '石药集团',
        packUnit: '盒',
        cellUnit: '粒',
        packCells: 12,
        splittable: 1
      },
      artName: '头孢克肟胶囊',
      artSpec: '0.1g*12粒',
      producer: '石药集团',
      originPlace: '河北',
      batchNo: 'C20240201',
      dateManufactured: '20240201',
      expiry: '20270131',
      packPrice: 28.80,
      totalPacks: 5,
      totalCells: 8,
      packUnit: '盒',
      cellUnit: '粒',
      packCells: 12,
      splittable: 1
    },
    {
      id: 'test3',
      artData: {
        artId: 1003,
        artName: '胰岛素注射液',
        artSpec: '3ml:300IU',
        producer: '诺和诺德',
        packUnit: '支',
        cellUnit: '支',
        packCells: 1,
        splittable: 0
      },
      artName: '胰岛素注射液',
      artSpec: '3ml:300IU',
      producer: '诺和诺德',
      originPlace: '丹麦',
      batchNo: 'D20240301',
      dateManufactured: '20240301',
      expiry: '20270228',
      packPrice: 68.50,
      totalPacks: 20,
      totalCells: 0,
      packUnit: '支',
      cellUnit: '支',
      packCells: 1,
      splittable: 0
    }
  ]

  addedItems.value = testData
  message.success('测试数据已加载')
}

// 导出数据
const exportData = () => {
  if (addedItems.value.length === 0) {
    message.warning('没有数据可导出')
    return
  }

  const dataStr = JSON.stringify(addedItems.value, null, 2)
  const blob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `maker-data-${new Date().getTime()}.json`
  link.click()
  URL.revokeObjectURL(url)
  message.success('数据导出成功')
}

// 测试校验功能
const testValidation = () => {
  // 清空表单，然后尝试提交，触发校验
  if (makerComponentRef.value) {
    makerComponentRef.value.clearFormData()
  }

  message.info('表单已清空，现在点击"添加"按钮可以体验各种校验提示')

  // 延迟一秒后自动聚焦到品种选择框
  setTimeout(() => {
    if (makerComponentRef.value) {
      // 尝试获取组件内部的品种选择框引用并聚焦
      const artSelectRef = makerComponentRef.value.$refs?.artSelectRef
      if (artSelectRef && typeof artSelectRef.focus === 'function') {
        artSelectRef.focus()
      }
    }
  }, 1000)
}
</script>

<template>
  <Card title="基础用法 - 采购制单组件" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">制单录入组件</Title>
      <Paragraph>
        采购制单组件是一个功能完整的制单录入组件，支持品种选择、信息填写、数据验证等功能。
        该组件基于采购组件开发，提供了更好的用户体验和更完善的功能。
      </Paragraph>

      <!-- 采购制单录入组件 -->
      <div class="maker-form-section">
        <Title :level="5">采购制单录入</Title>
        <Paragraph v-if="formEditingId">
          <Text type="warning">当前正在编辑记录，修改完成后点击"添加"按钮保存更改。</Text>
          <Button type="link" size="small" @click="cancelEdit">取消编辑</Button>
        </Paragraph>
        <Maker
          ref="makerComponentRef"
          @addArt="handleAddArt"
        />
      </div>

      <Divider />

      <!-- 已添加品种列表 -->
      <div class="added-items-section">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <Title :level="5" style="margin: 0;">已添加品种列表 ({{ addedItems.length }})</Title>
          <Space>
            <Button @click="setTestData" type="primary" ghost>加载测试数据</Button>
            <Button v-if="addedItems.length > 0" @click="exportData">导出数据</Button>
            <Button v-if="addedItems.length > 0" @click="clearAll" danger>清空所有</Button>
          </Space>
        </div>

        <!-- 批量操作工具栏 -->
        <div v-if="addedItems.length > 0" style="margin-bottom: 16px; padding: 12px; background: #fafafa; border-radius: 6px;">
          <Space>
            <span>已选择 {{ selectedRowKeys.length }} 项</span>
            <Button
              v-if="hasSelected"
              @click="deleteSelected"
              danger
              size="small"
            >
              批量删除
            </Button>
            <Button
              v-if="selectedRowKeys.length > 0"
              @click="selectedRowKeys = []"
              size="small"
            >
              取消选择
            </Button>
          </Space>
        </div>

        <Table
          v-if="addedItems.length > 0"
          :dataSource="addedItems"
          :columns="columns"
          rowKey="id"
          :scroll="{ x: 1500 }"
          :pagination="{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            type: 'checkbox'
          }"
        />

        <div v-else style="text-align: center; padding: 40px; color: #999;">
          <div style="margin-bottom: 16px;">暂无数据，请使用上方表单添加品种</div>
          <Button @click="setTestData" type="primary" ghost>或点击加载测试数据</Button>
        </div>
      </div>

      <!-- 功能说明 -->
      <div class="feature-description">
        <Title :level="5">功能特性</Title>
        <div class="feature-list">
          <div class="feature-item">
            <Text strong>✨ 品种选择：</Text>
            <Text>支持通过输入框搜索和选择品种，自动获取品种详细信息</Text>
          </div>
          <div class="feature-item">
            <Text strong>📝 表单验证：</Text>
            <Text>内置完整的表单验证规则，确保数据的准确性和完整性</Text>
          </div>
          <div class="feature-item">
            <Text strong>⌨️ 快捷操作：</Text>
            <Text>支持回车键导航，提高录入效率</Text>
          </div>
          <div class="feature-item">
            <Text strong>🔄 编辑功能：</Text>
            <Text>支持记录的编辑、复制、删除等操作</Text>
          </div>
          <div class="feature-item">
            <Text strong>📊 批量操作：</Text>
            <Text>支持批量选择和删除记录</Text>
          </div>
          <div class="feature-item">
            <Text strong>💾 数据管理：</Text>
            <Text>支持测试数据加载和数据导出功能</Text>
          </div>
          <div class="feature-item">
            <Text strong>⚙️ 自动设置：</Text>
            <Text>当品种信息为空时会自动弹出机构商品设置窗口</Text>
          </div>
        </div>
      </div>

      <!-- 数据校验说明 -->
      <div class="validation-description">
        <Title :level="5">数据校验规则</Title>
        <div class="validation-list">
          <div class="validation-item">
            <Text strong type="danger">* 品种查询：</Text>
            <Text>必须选择一个品种才能提交</Text>
          </div>
          <div class="validation-item">
            <Text strong type="danger">* 生产批号：</Text>
            <Text>不能为空，用于标识产品批次</Text>
          </div>
          <div class="validation-item">
            <Text strong type="danger">* 生产日期：</Text>
            <Text>必须填写，格式为YYYYMMDD（如：20240101）</Text>
          </div>
          <div class="validation-item">
            <Text strong type="danger">* 有效期至：</Text>
            <Text>必须填写，格式为YYYYMMDD，且必须大于生产日期</Text>
          </div>
          <div class="validation-item">
            <Text strong type="danger">* 整包单价：</Text>
            <Text>必须填写且大于0，支持小数点后2位</Text>
          </div>
          <div class="validation-item">
            <Text strong type="danger">* 数量验证：</Text>
            <Text>整包数量和拆零数量至少填写一个且必须大于0</Text>
          </div>
          <div class="validation-item">
            <Text strong>日期格式：</Text>
            <Text>年份范围1900-2100，月份1-12，日期根据月份自动验证</Text>
          </div>
        </div>

        <div class="validation-demo">
          <Title :level="5">校验演示</Title>
          <Space>
            <Button @click="testValidation" type="primary" ghost size="small">
              测试校验功能
            </Button>
            <Text type="secondary">点击按钮可以体验各种校验提示</Text>
          </Space>
        </div>
      </div>

      <div class="mt-8px tip-text">
        <i class="tip-icon">💡</i>
        点击"加载测试数据"可以快速体验组件的各项功能，包括编辑、复制、删除等操作。
      </div>
    </div>

    <Divider />

    <Title :level="4">代码示例</Title>
    <CodeDemoVue
      :usage="basicUsage"
      :importCode="importCode"
      :packageJson="packageJsonCode"
    />
  </Card>

  <!-- 打包发布指令 -->
  <Card title="打包与发布" class="mb-16px">
    <div class="mb-16px">
      <Title :level="4">组件打包指令</Title>
      <Paragraph>
        在开发完成后，需要打包组件以便发布和使用。以下是Maker组件的打包和发布指令：
      </Paragraph>

      <div class="code-section">
        <Title :level="5">打包发布指令</Title>
        <pre class="code-block">{{ publishCommands }}</pre>
      </div>

      <div class="code-section">
        <Title :level="5">打包流程说明</Title>
        <pre class="code-block">{{ buildProcess }}</pre>
      </div>

      <div class="tips-section">
        <Title :level="5">使用说明</Title>
        <ul class="tips-list">
          <li><strong>正式版本：</strong>用于生产环境，版本号会自动递增</li>
          <li><strong>测试版本：</strong>用于测试环境，版本号带有beta标识</li>
          <li><strong>开发版本：</strong>用于开发环境，版本号带有alpha标识</li>
          <li><strong>安装组件：</strong>在其他项目中使用pnpm add命令安装组件</li>
        </ul>
      </div>

      <div class="warning-section">
        <Paragraph type="warning">
          <strong>注意：</strong>打包前请确保组件代码已经完成开发和测试，并且所有依赖项都已正确配置。
        </Paragraph>
      </div>
    </div>
  </Card>
</template>

<style scoped>
.mb-16px {
  margin-bottom: 16px;
}

.mt-8px {
  margin-top: 8px;
}

.maker-form-section {
  margin-bottom: 24px;
}

.added-items-section {
  margin-top: 24px;
}

.tip-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  display: inline-flex;
  align-items: center;
  max-width: 600px;
}

.tip-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  text-align: center;
  line-height: 16px;
  font-size: 12px;
  font-style: normal;
  margin-right: 8px;
  flex-shrink: 0;
}

/* 打包指令相关样式 */
.code-section {
  margin-bottom: 24px;
}

.code-block {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 14px;
  line-height: 1.45;
  color: #24292e;
  overflow-x: auto;
  white-space: pre;
  margin: 0;
}

.tips-section {
  margin-bottom: 24px;
}

.tips-list {
  margin: 8px 0;
  padding-left: 20px;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.tips-list strong {
  color: #1890ff;
}

.warning-section {
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
  padding: 12px 16px;
  margin-top: 16px;
}

/* 功能说明样式 */
.feature-description {
  margin: 24px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.feature-list {
  margin-top: 16px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.6;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.feature-item .ant-typography {
  margin-right: 8px;
}

.feature-item .ant-typography:last-child {
  flex: 1;
  margin-right: 0;
}

/* 校验说明样式 */
.validation-description {
  margin: 24px 0;
  padding: 20px;
  background: #fff2f0;
  border-radius: 8px;
  border: 1px solid #ffccc7;
}

.validation-list {
  margin-top: 16px;
}

.validation-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  line-height: 1.6;
}

.validation-item:last-child {
  margin-bottom: 0;
}

.validation-item .ant-typography {
  margin-right: 8px;
}

.validation-item .ant-typography:last-child {
  flex: 1;
  margin-right: 0;
}

.validation-demo {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ffccc7;
}
</style>
