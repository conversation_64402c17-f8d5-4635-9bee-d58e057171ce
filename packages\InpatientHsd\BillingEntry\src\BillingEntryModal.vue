<script setup lang="ts">
import BillingEntry from './BillingEntry.vue'
import { Button, Modal } from 'ant-design-vue'

const props = defineProps({
  inOperating: {
    type: Number,
    default: null,
  },
  sectionId: {
    type: Number,
    default: null,
  },
  sectionName: {
    type: String,
    default: null
  },
  genderId: {
    type: Number,
    default: null,
  },
  defaultFreqCode: {
    type: String,
    default: 'qd',
  }
})

const emit = defineEmits(['oeChange'])
const billingEntryRef = ref<InstanceType<typeof BillingEntry>>()
const visible = ref(false)
const visitId = ref()


const open = (initVisitId: any) => {
  visitId.value = initVisitId
  setTimeout(() => {
    billingEntryRef.value.init()
  }, 500)
  visible.value = true
}

function handleCancel() {
  visible.value = false
}

const handleOeChange = () => {
  emit('oeChange')
}

defineExpose({
  open,
})
</script>

<template>
  <Modal v-model:open="visible" title="计费医嘱" width="1400px" @cancel="handleCancel" :mask-closable="false" style="top: 20px">
    <template #footer>
      <Button type="dashed" @click="handleCancel"> 关闭 </Button>
    </template>
    <div class="content-req">
      <billing-entry ref="billingEntryRef" :section-id="props.sectionId" :visit-id="visitId" :gender-id="props.genderId" :default-freq-code="props.defaultFreqCode" @oe-change="handleOeChange" />
    </div>
  </Modal>
</template>

<style lang="less" scoped>
.content-req {
  height: calc(100vh - 160px);
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
